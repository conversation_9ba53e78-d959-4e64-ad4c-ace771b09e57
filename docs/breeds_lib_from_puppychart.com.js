$(document).ready(function() {
    var a = -0.2;
    var K = [10, 25, 45, 77];
    var B = [9.19814356, 10.987186, 11.4973043, 12.05979376];
    var c = 0.12;

    function b(w) {
      var bc = [[0.11927, 8.00545], [0.025506, 10.349538], [0.017578, 10.706303]];
      if (w < K[1]) {
        return bc[0][0]*w + bc[0][1];
      } else if (w < K[2]) {
        return bc[1][0]*w + bc[1][1];
      } else {
        return bc[2][0]*w + bc[2][1];
      }
    }

    function d(w) {
      return 1-0.0014443*w;
    }

    function mgompertz(t, w, a, b, c, d) {
      return Math.max(0, (w-a)*Math.exp(-Math.exp(-c*(Math.pow(t/7.0, d)-b))) + a);
    }

    function mgompertz_inv(t, y, a, c, d, w_init) {
      var w = w_init;
      var iterations = 100;
      for (i = 0; i < iterations; i++) {
        w = (y-a)/Math.exp(-Math.exp(-c*(Math.pow(t/7.0, d(w))-b(w)))) + a;
      }
      return w;
    }

    function fgompertz(t, w) {
      return mgompertz(t, w, a, b(w), c, d(w));
    }

    function fgompertz_inv(t, y) {
      var w_init = 0;
      if (y < fgompertz(t, K[1], a, b(K[1]), c, d(K[1]))) {
        w_init = K[0];
      } else if (y < fgompertz(t, K[2], a, b(K[2]), c, d(K[2]))) {
        w_init = K[1];
      } else {
        w_init = K[2];
      }

      return mgompertz_inv(t, y, a, c, d, w_init);
    }

    function kgToLb(value) {
      return value/0.4536;
    }

    function lbToKg(value) {
      return 0.4536*value;
    }

    function convertFromUnit(value) {
      if ($('#unit').val() == 'lb') {
        return lbToKg(value);
      } else {
        return value;
      }
    }

    function convertToUnit(value) {
      if ($('#unit').val() == 'lb') {
        return kgToLb(value);
      } else {
        return value;
      }
    }

    function convertCurveToUnit(curve) {
      if ($('#unit').val() == 'lb') {
        for (var i = 0; i < curve.length; i++) {
          curve[i][1] = kgToLb(curve[i][1]);
        }
      }
    }

    function getCurve(t, w) {
      curve = [];
      for (var i = 0; i <= t.length; i += 1) {
        curve.push([t[i], fgompertz(t[i], w)]);
      }
      return curve;
    }

    function daysBetween(date1, date2) {
      return Math.floor(( Date.parse(date2) - Date.parse(date1) ) / 86400000);
    }

    function xTickGenerator(axis) {
      var units = [
        {days: 360, singular: 'year', plural: 'years', from: 84, to: 100000},
        {days: 30, singular: 'month', plural: 'months', from: 84, to: 100000},
        {days: 7, singular: 'week', plural: 'weeks', from: 0, to: 84}
      ];

      var wantedTicks = 8;
      var minTicks = 5;

      // Use fewer ticks on phones
      if ($(window).width() < 768) {
        wantedTicks = 4;
        minTicks = 3;
      }

      units = $.map(units, function(unit, i) {
        if (axis.max > unit.from && axis.max <= unit.to) {
          var step = Math.ceil(((axis.max - axis.min)/unit.days)/wantedTicks);
          var ticks = Math.ceil((axis.max - axis.min)/(step*unit.days));
          return $.extend(true, unit, {step: step, ticks: ticks});
        }
      });

      var res = [[0, 'birth']];

      for (u = 0; u < units.length; u++) {
        var unit = units[u];
        var step = unit.step;
        var i = Math.floor(axis.min / unit.days);
        var v;
        do {
          v = i * unit.days;
          if (i !== 0) {
            var str = '';
            if (i == 1) {
              str = i + ' ' + unit.singular;
            } else {
              str = i + ' ' + unit.plural;
            }

            var useThis = true;
            for (k = 0; k < res.length; k++) {
              if (res[k][0] > v - step*unit.days && res[k][0] < v + step*unit.days) {
                // Conflicting tick already exists, so drop this one
                useThis = false;
              }
            }
            if (useThis) res.push([v, str]);
          }
          i = i + step;
        } while (v < axis.max);
        if (unit.ticks >= minTicks) break;
      }

      return res;
    }

    // Initial plot settings
    var color1 = '#ad3700';
    var color2 = '#fc832f';
    var color3 = '#e59e2e';
    var color4 = '#fed83b';
    var color5 = '#aeb23b';
    var bgFill = 0.4;
    var dogSizes = [
      {name: 'Giant', from: 50, to: 75, color: color5},
      {name: 'Large', from: 30, to: 50, color: color4},
      {name: 'Medium', from: 15, to: 30, color: color3},
      {name: 'Small', from: 5, to: 15, color: color2}
    ];
    var novalueText = $('.adultweight').html();

    var tmin = 0;
    var tmax = 720;
    var tstep = 4.0;
    var t = [];
    for (var i = tmin; i <= tmax; i += tstep) {
      t.push(i);
    }

    // Plot options
    var options = {
      legend: { show: true, container: $("#legend") },
      xaxis: { ticks: xTickGenerator },
      yaxis: { ticks: 10, tickDecimals: 2, tickFormatter: function (v) { return Math.round(v*100)/100 + " " + $('#unit').val(); } },
      selection: { mode: "xy" }
    };

    // Initial plot
    var datasetSizes = $.map(dogSizes, function(val, i) {
      var lower = getCurve(t, val.from);
      var upper = getCurve(t, val.to);
      var area = upper.reverse().concat(lower);
      return {label: val.name, data: area, lines: { show: true, lineWidth: 0.0, fill: bgFill}, color: val.color };
    });
    var bgData = datasetSizes;
    var fgData = [];

    // Function to plot data
    function drawPlot() {
      var dataset = bgData.concat(fgData);
      var croppedData = $.extend(true, [], dataset);
      if (typeof currentZoom.yaxis === "undefined") {
        // Crop the data to the current X selection to auto-scale Y axis
        var xmin = currentZoom.xaxis.min;
        var xmax = currentZoom.xaxis.max;
        for (i = 0; i < dataset.length; i++) {
          for (k = croppedData[i].data.length - 1; k >= 0; k--) {
            if ((croppedData[i].data[k][0] < xmin) || (croppedData[i].data[k][0] > xmax)) {
              croppedData[i].data.splice(k, 1);
            }
          }
        }
      }

      // Convert to correct unit
      for (var k = 0; k < croppedData.length; k++) {
        convertCurveToUnit(croppedData[k].data);
      }

      // Plot it
      plot = $.plot(placeholder, croppedData, $.extend(true, {}, options, currentZoom));
      $('#legend').find('table').addClass('condensed-table');
    }

    // Redraw when window changes
    window.onresize = function(event) {
      drawPlot();
    };

    // Set up zoom handler and plot it
    var placeholder = $("#placeholder");
    var currentZoom = {xaxis: {from: 0, to: 24}};
    var plot;
    drawPlot();
    placeholder.bind("plotselected", function (event, ranges) {
      plotZoom(ranges);
    });

    // Zoom function
    function plotZoom(ranges) {
      currentZoom = {xaxis: { min: ranges.xaxis.from, max: ranges.xaxis.to }};
      if (typeof ranges.yaxis !== "undefined") {
        currentZoom = $.extend(true, currentZoom, {yaxis: { min: ranges.yaxis.from, max: ranges.yaxis.to }});
      }
      drawPlot();
    }

    $('.zoom').click(function () {
      var to = parseFloat($(this).data('to'));
      var from = parseFloat($(this).data('from'));
      plotZoom({xaxis: {from: from, to: to}});
      return false;
    });

    // Update plot when form values changed
    function update() {
      var age = daysBetween($('#birth').val(), $('#timeOfWeight').val());
      var weight = convertFromUnit($('#weight').val());
      var w = fgompertz_inv(parseFloat(age), parseFloat(weight));
      if (!$.isNaN(w)) {
        $(".adultweight").html(convertToUnit(w).toFixed(1) + ' ' + $('#unit').val());
        $(".adultweight").removeClass('novalue');
        curve = getCurve(t, w);
        fgData = [
        { label: 'Your puppy', data: curve, lines: { show: true, fill: false }, color: color1 },
        { data: [[age, weight]], points: { show: true }, color: color1 }
        ];
        drawPlot();
        // Track event
        _gaq.push(['_trackEvent', 'Weight', 'Age', $('#breed').val(), age]);
      } else {
        $(".adultweight").html(novalueText);
        $(".adultweight").addClass('novalue');
        fgData = [];
        drawPlot();
      }
    }

    function updateBreed() {
      // Default if no data avaiable
      bgData = datasetSizes;
      $('#dogsizes').show();

      // Check if we have breed data
      var selected = $('#breed').val();
      if (selected > 0) {
        var breed = breeds[selected];
        if (typeof breed.weightMax !== "undefined") {
          var from, to;
          if (typeof breed.weightMin !== "undefined") {
            from = parseFloat(breed.weightMin);
            to = parseFloat(breed.weightMax);
          } else {
            to = 0.9*parseFloat(breed.weightMax);
            to = 1.1*parseFloat(breed.weightMax);
          }
          if (breed.weightUnit == 'lb') {
            to = lbToKg(to);
            from = lbToKg(from);
          }
          var lower = getCurve(t, from);
          var upper = getCurve(t, to);
          var area = upper.reverse().concat(lower);
          bgData = [
          { label: breed.name, data: area, lines: { show: true, lineWidth: 0.0, fill: bgFill }, color: color3 }
            ];
          $('#dogsizes').hide();
        }
      }

      drawPlot();
    }

    $('input').keyup(update);
    $('input').change(update);
    $('#breed').change(updateBreed);
    $('#unit').change(update);
    $('input.date').datepicker();

    $.map(breeds, function(breed, i) {
      $('<option value="'+i+'">'+breed.name+'</option>').appendTo($('#breed'));
    });
});

