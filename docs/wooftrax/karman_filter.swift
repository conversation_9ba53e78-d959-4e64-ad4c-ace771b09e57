//Callback to CLLocationManagerDelegate for IOS
    public override void LocationsUpdated(CLLocationManager manager, CLLocation[] locations)
	{
		// see https://github.com/slodge/MvvmCross/issues/92 and http://stackoverflow.com/questions/13262385/monotouch-cllocationmanagerdelegate-updatedlocation
		if (locations.Length == 0)
		{
			Console.WriteLine("iOS has passed LocationsUpdated an empty array - this should never happen");
			return;
		}

		CLLocation mostRecent = null;
		if (locations.Length > 0)
		{
			mostRecent = locations[locations.Length - 1];
		}
		if (mostRecent != null)
		{
			/* Kalman Filter */
			double Qvalue;
			var timestamp = NSDateToDateTime(mostRecent.Timestamp);
			long elapsedTimeInMillis = (long)timestamp.Subtract(_runStartTimeInMillis).TotalMilliseconds;

			if (currentSpeed == 0.0f)
			{
				Qvalue = 3.0f; //3 meters per second
			}
			else
			{
				Qvalue = currentSpeed; // meters per second
			}
			currentSpeed = mostRecent.Speed;

			kalmanFilter.Process(mostRecent.Coordinate.Latitude, mostRecent.Coordinate.Longitude, (float)mostRecent.HorizontalAccuracy, elapsedTimeInMillis, (float)Qvalue);
			double predictedLat = kalmanFilter.get_lat();
			double predictedLng = kalmanFilter.get_lng();

			CLLocation predictedLocation = new CLLocation(new CLLocationCoordinate2D(predictedLat, predictedLng), mostRecent.Altitude, mostRecent.HorizontalAccuracy, mostRecent.VerticalAccuracy, mostRecent.Course, mostRecent.Speed, mostRecent.Timestamp);

			double predictedDeltaInMeters = predictedLocation.DistanceFrom(mostRecent);

			if (predictedDeltaInMeters > 60)
			{
				kalmanFilter.consecutiveRejectCount += 1;

				if (kalmanFilter.consecutiveRejectCount > 3)
				{
					kalmanFilter = new KalmanLatLong(3); //reset Kalman Filter if it rejects more than 3 times in raw.
				}
			}
			else
			{
				kalmanFilter.consecutiveRejectCount = 0;
			}
			var converted = CreateLocation(predictedLocation, _previousLocation, _lastSeenHeading);
			_owner.SendLocation(converted);

			_previousLocation = predictedLocation;

		}
		else
		{
			_owner.SendTimerEvent();
		}
		_owner.SetupDeferredLocationUpdates();
	}

	//Implementation was transcoded from
	// https://github.com/mizutori/AndroidLocationStarterKit/blob/master/app/src/main/java/com/goldrushcomputing/androidlocationstarterkit/KalmanLatLong.java
	public class KalmanLatLong
    {
		private readonly float MinAccuracy = 1;

		private float Q_metres_per_second;
		private long TimeStamp_milliseconds;
		private double lat;
		private double lng;
		private float variance; // P matrix. Negative means object uninitialised.
								// NB: units irrelevant, as long as same units used
								// throughout
		public int consecutiveRejectCount;

		public KalmanLatLong(float Q_metres_per_second)
		{
			this.Q_metres_per_second = Q_metres_per_second;
			variance = -1;
			consecutiveRejectCount = 0;
		}

		public long get_TimeStamp()
		{
			return TimeStamp_milliseconds;
		}

		public double get_lat()
		{
			return lat;
		}

		public double get_lng()
		{
			return lng;
		}

		public float get_accuracy()
		{
			return (float)Math.Sqrt(variance);
		}

		public void SetState(double lat, double lng, float accuracy,
				long TimeStamp_milliseconds)
		{
			this.lat = lat;
			this.lng = lng;
			variance = accuracy * accuracy;
			this.TimeStamp_milliseconds = TimeStamp_milliseconds;
		}

		// / <summary>
		// / Kalman filter processing for lattitude and longitude
		// / </summary>
		// / <param name="lat_measurement_degrees">new measurement of
		// lattidude</param>
		// / <param name="lng_measurement">new measurement of longitude</param>
		// / <param name="accuracy">measurement of 1 standard deviation error in
		// metres</param>
		// / <param name="TimeStamp_milliseconds">time of measurement</param>
		// / <returns>new state</returns>
		public void Process(double lat_measurement, double lng_measurement,
				float accuracy, long TimeStamp_milliseconds, float Q_metres_per_second)
		{
			this.Q_metres_per_second = Q_metres_per_second;

			if (accuracy < MinAccuracy)
				accuracy = MinAccuracy;
			if (variance < 0)
			{
				// if variance < 0, object is unitialised, so initialise with
				// current values
				this.TimeStamp_milliseconds = TimeStamp_milliseconds;
				lat = lat_measurement;
				lng = lng_measurement;
				variance = accuracy * accuracy;
			}
			else
			{
				// else apply Kalman filter methodology

				long TimeInc_milliseconds = TimeStamp_milliseconds
						- this.TimeStamp_milliseconds;
				if (TimeInc_milliseconds > 0)
				{
					// time has moved on, so the uncertainty in the current position
					// increases
					variance += TimeInc_milliseconds * Q_metres_per_second
							* Q_metres_per_second / 1000;
					this.TimeStamp_milliseconds = TimeStamp_milliseconds;
					// TO DO: USE VELOCITY INFORMATION HERE TO GET A BETTER ESTIMATE
					// OF CURRENT POSITION
				}

				// Kalman gain matrix K = Covarariance * Inverse(Covariance +
				// MeasurementVariance)
				// NB: because K is dimensionless, it doesn't matter that variance
				// has different units to lat and lng
				float K = variance / (variance + accuracy * accuracy);
				// apply K
				lat += K * (lat_measurement - lat);
				lng += K * (lng_measurement - lng);
				// new Covarariance matrix is (IdentityMatrix - K) * Covarariance
				variance = (1 - K) * variance;
			}
		}

		public int GetConsecutiveRejectCount()
		{
			return consecutiveRejectCount;
		}

		public void SetConsecutiveRejectCount(int consecutiveRejectCount)
		{
			this.consecutiveRejectCount = consecutiveRejectCount;
		}
	}