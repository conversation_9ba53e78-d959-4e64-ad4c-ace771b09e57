//test pour tester toJson à mettre dans le fromJson

    String jsonStart = jsonEncode(parsedJson);
    debugPrint(jsonStart);
    String jsonEnd = jsonEncode(toJson());
    debugPrint(jsonEnd);

    bool ok = true;
    diff(jsonStart, jsonEnd)
    // diff(jsonEnd, jsonReEnd)
        .forEach((element) {
      if (element.operation != DIFF_EQUAL) {
        debugPrint('❌' + element.text);
        ok = false;
      }
    });
    if (ok) {
      debugPrint('✅');
    }