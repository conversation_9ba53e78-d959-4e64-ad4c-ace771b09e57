name: mybuddy
description: myBuddy pet app®

version: 6.9.22+799022

publish_to: none

environment:
  sdk: ">=2.12.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  app_settings: ^4.1.8
  archive: ^3.2.0
  cached_network_image: ^3.2.3
  # cached_video_player: ^2.0.0-dev.1  replaced by video_player: duplicate namespace, check regularly (readonly_video_view.dart)
  video_player: ^2.2.18
  carousel_slider: ^4.0.0
  charts_flutter: ^0.12.0
  contacts_service: ^0.6.3
  image_cropper: ^3.0.1
  datetime_picker_formfield: ^2.0.0
  device_info: ^2.0.3
  dio: ^4.0.4
  font_awesome_flutter: ^9.2.0
  flutter_native_timezone: ^2.0.0
  flutter_speed_dial: ^5.0.0+1
  flutter_svg: ^1.0.3
  flutter_widget_from_html: ^0.9.0+1
  google_mobile_ads: ^3.1.0
  gpx: ^2.1.0
  grouped_list: ^4.2.0
  html_unescape: ^2.0.0
  image: ^3.1.1
  image_picker: ^0.8.4+8
  ios_utsname_ext: ^2.1.0
  intl: ^0.17.0
  introduction_screen: ^3.1.4
  json_api: ^5.0.5
  linkwell: ^2.0.6
  lottie: ^1.2.2
  multi_select_flutter: ^4.0.0
  package_info_plus: ^1.3.0
  path_provider: ^2.0.9
  photo_view:
    git:
      url: https://github.com/bluefireteam/photo_view
      ref: master
  permission_handler: ^9.0.1
  phone_form_field: ^4.4.0
  pin_code_fields: ^7.3.0
  pretty_json: ^2.0.0
  rxdart: ^0.27.3
  sign_in_with_apple: 4.0.0
  signal_strength_indicator: ^0.4.1
  share_plus: ^5.0.0
  shared_preferences: ^2.0.13
  shimmer: ^2.0.0
  sliding_up_panel: ^2.0.0+1
  url_launcher: ^6.0.20
  latlong2: ^0.8.1
  sqflite: ^2.2.2
  path: ^1.8.0
  google_maps_flutter: ^2.1.8
  firebase_dynamic_links: ^5.3.4
  firebase_core: ^2.15.0
  fluttertoast: ^8.0.9
  get: ^4.6.5
  sticky_headers: ^0.2.0
  firebase_crashlytics: ^3.3.4
  location:
    git:
      url: https://github.com/akhatriST/flutterlocation
      path: packages/location
      ref: fixing_issues
  move_to_background: ^1.0.2
  appsflyer_sdk: ^6.8.2
  flutter_neumorphic: ^3.2.0
  # flutter_facebook_auth: ^5.0.7  # Temporarily disabled to resolve Facebook SDK conflict
  google_sign_in: ^5.4.4
  flutter_custom_month_picker: ^0.1.3
  # customer_io: ^1.3.2  # Temporarily disabled to resolve build issues
  firebase_messaging: ^14.6.5
  timezone: ^0.9.2
  awesome_notifications: ^0.7.4+1
  dart_ipify: ^1.1.1
  flutter_native_splash:
  sqflite_common_ffi: ^2.2.5
  flutter_staggered_grid_view: ^0.6.0
  qr_flutter: ^4.0.0
  syncfusion_flutter_datepicker: ^20.4.48
  jiffy: ^5.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^1.0.4
  dependency_validator: ^3.1.2
  test: ^1.19.5

dependency_overrides:
  # override fix for flutter 3.3.10
  syncfusion_flutter_datepicker: "20.2.43"
  # override for intl version conflicts
  intl: ^0.17.0

flutter:
  uses-material-design: true
  fonts:
    - family: Montserrat
      fonts:
        - asset: assets/fonts/Montserrat-Regular.ttf
        - asset: assets/fonts/Montserrat-Italic.ttf
          style: italic
        - asset: assets/fonts/Montserrat-Bold.ttf
          weight: 700
  assets:
    - assets/images/
    - assets/json/ref.json
    - assets/lottieFiles/
    - assets/avatars/
    - assets/badges/
    - assets/icon/
#flutter_icons:
#  android: "launcher_icon"
#  ios: true
#  image_path: "assets/icon/launcher_icon.png"
#
#flavorizr:
#  app:
#    android:
#      flavorDimensions: "app"
#    ios:
#  flavors:
#    mybuddy:
#      app:
#        name: "myBuddy pet app®"
#      android:
#        applicationId: "com.mediproductions.mybuddy"
#      ios:
#        bundleId: "com.mediproductions.mybuddy"
#        generateDummyAssets: true
#    wooftrax:
#      app:
#        name: "WoofTrax"
#      android:
#        applicationId: "com.mediproductions.wooftrax"
#      ios:
#        bundleId: "com.mediproductions.wooftrax"
#        generateDummyAssets: true
#
