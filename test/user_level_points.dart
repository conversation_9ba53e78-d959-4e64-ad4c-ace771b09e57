import 'package:flutter_test/flutter_test.dart';
import 'package:mybuddy/extensions/number_extensions.dart';

void main() {
  int getLevelPoints(int level) {

    int points = level.toLevelPoints();

    return points;
  }

  test('For Level 1, User points should be 0', () {
    int level = 1;
    int points = getLevelPoints(level);

    expect(points, 0);
  });

  test('For Level 2, User points should be 10', () {
    int level = 2;
    int points = getLevelPoints(level);

    expect(points, 10);
  });

  test('For Level 3, User points should be 40', () {
    int level = 3;
    int points = getLevelPoints(level);

    expect(points, 40);
  });

  test('For Level 4, User points should be 70', () {
    int level = 4;
    int points = getLevelPoints(level);

    expect(points, 70);
  });

  test('For Level 5, User points should be 120', () {
    int level = 5;
    int points = getLevelPoints(level);

    expect(points, 120);
  });

  test('For Level 6, User points should be 170', () {
    int level = 6;
    int points = getLevelPoints(level);

    expect(points, 170);
  });

  test('For Level 7, User points should be 220', () {
    int level = 7;
    int points = getLevelPoints(level);

    expect(points, 220);
  });

  test('For Level 8, User points should be 300', () {
    int level = 8;
    int points = getLevelPoints(level);

    expect(points, 300);
  });

  test('For Level 9, User points should be 380', () {
    int level = 9;
    int points = getLevelPoints(level);

    expect(points, 380);
  });

  test('For Level 10, User points should be 460', () {
    int level = 10;
    int points = getLevelPoints(level);

    expect(points, 460);
  });

  test('For Level 11, User points should be 560', () {
    int level = 11;
    int points = getLevelPoints(level);

    expect(points, 560);
  });

  test('For Level 12, User points should be 660', () {
    int level = 12;
    int points = getLevelPoints(level);

    expect(points, 660);
  });

  test('For Level 13, User points should be 760', () {
    int level = 13;
    int points = getLevelPoints(level);

    expect(points, 760);
  });
}
