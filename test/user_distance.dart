// Import the test package and Counter class
import 'package:flutter_test/flutter_test.dart';
import 'package:mybuddy/extensions/number_extensions.dart';

void main() {

  test('Duration is 0.07, Time should be 0 min 4 sec', () {
    double duration = 0.07;
    String level = duration.toENTimeString();

    expect(level, "0 min 4 sec");
  });

  test('Duration is 61.0, Time should be 1 hr 1 min', () {
    double duration = 61.0;
    String level = duration.toENTimeString();

    expect(level, "1 hr 1 min");
  });

  test('Duration is 0.025, Time should be 0 min 1 sec', () {
    double duration = 0.025;
    String level = duration.toENTimeString();

    expect(level, "0 min 1 sec");
  });
}
