// Import the test package and Counter class
import 'package:flutter_test/flutter_test.dart';
import 'package:mybuddy/extensions/number_extensions.dart';

void main() {
  int getLevel(int points) {
    Map<String, dynamic> buildLevel = points.toLevel();

    int level = buildLevel['level'];

    return level;
  }

  test('Points are 0, User level should be 1', () {
    int points = 0;
    int level = getLevel(points);

    expect(level, 1);
  });

  test('Points are 10, User level should be 2', () {
    int points = 10;
    int level = getLevel(points);

    expect(level, 2);
  });

  test('Points are 30, User level should be 2', () {
    int points = 30;
    int level = getLevel(points);

    expect(level, 2);
  });

  test('Points are 40, User level should be 3', () {
    int points = 40;
    int level = getLevel(points);

    expect(level, 3);
  });

  test('Points are 50, User level should be 3', () {
    int points = 50;
    int level = getLevel(points);

    expect(level, 3);
  });

  test('Points are 60, User level should be 3', () {
    int points = 60;
    Map<String, dynamic> buildLevel = points.toLevel();

    int level = buildLevel['level'];

    expect(level, 3);
  });

  test('Points are 70, User level should be 4', () {
    int points = 70;
    int level = getLevel(points);

    expect(level, 4);
  });

  test('Points are 80, User level should be 4', () {
    int points = 80;
    int level = getLevel(points);

    expect(level, 4);
  });

  test('Points are 100, User level should be 4', () {
    int points = 100;
    int level = getLevel(points);

    expect(level, 4);
  });

  test('Points are 120, User level should be 5', () {
    int points = 120;
    int level = getLevel(points);

    expect(level, 5);
  });

  test('Points are 150, User level should be 5', () {
    int points = 150;
    int level = getLevel(points);

    expect(level, 5);
  });

  test('Points are 170, User level should be 6', () {
    int points = 170;
    int level = getLevel(points);

    expect(level, 6);
  });

  test('Points are 200, User level should be 6', () {
    int points = 200;
    int level = getLevel(points);

    expect(level, 6);
  });

  test('Points are 220, User level should be 7', () {
    int points = 220;
    int level = getLevel(points);

    expect(level, 7);
  });

  test('Points are 250, User level should be 7', () {
    int points = 250;
    int level = getLevel(points);

    expect(level, 7);
  });

  test('Points are 290, User level should be 7', () {
    int points = 290;
    int level = getLevel(points);

    expect(level, 7);
  });

  test('Points are 300, User level should be 8', () {
    int points = 300;
    int level = getLevel(points);

    expect(level, 8);
  });

  test('Points are 320, User level should be 8', () {
    int points = 320;
    int level = getLevel(points);

    expect(level, 8);
  });

  test('Points are 350, User level should be 8', () {
    int points = 350;
    int level = getLevel(points);

    expect(level, 8);
  });

  test('Points are 380, User level should be 9', () {
    int points = 380;
    int level = getLevel(points);

    expect(level, 9);
  });

  test('Points are 400, User level should be 9', () {
    int points = 400;
    int level = getLevel(points);

    expect(level, 9);
  });

  test('Points are 420, User level should be 9', () {
    int points = 420;
    int level = getLevel(points);

    expect(level, 9);
  });

  test('Points are 450, User level should be 9', () {
    int points = 450;
    int level = getLevel(points);

    expect(level, 9);
  });

  test('Points are 460, User level should be 10', () {
    int points = 460;
    int level = getLevel(points);

    expect(level, 10);
  });

  test('Points are 480, User level should be 10', () {
    int points = 480;
    int level = getLevel(points);

    expect(level, 10);
  });

  test('Points are 500, User level should be 10', () {
    int points = 500;
    int level = getLevel(points);

    expect(level, 10);
  });

  test('Points are 540, User level should be 10', () {
    int points = 540;
    int level = getLevel(points);

    expect(level, 10);
  });

  test('Points are 560, User level should be 11', () {
    int points = 560;
    int level = getLevel(points);

    expect(level, 11);
  });

  test('Points are 600, User level should be 11', () {
    int points = 600;
    int level = getLevel(points);

    expect(level, 11);
  });

  test('Points are 640, User level should be 11', () {
    int points = 640;
    int level = getLevel(points);

    expect(level, 11);
  });

  test('Points are 660, User level should be 12', () {
    int points = 660;
    int level = getLevel(points);

    expect(level, 12);
  });

  test('Points are 670, User level should be 12', () {
    int points = 670;
    int level = getLevel(points);

    expect(level, 12);
  });

  test('Points are 690, User level should be 12', () {
    int points = 690;
    int level = getLevel(points);

    expect(level, 12);
  });

  test('Points are 710, User level should be 12', () {
    int points = 710;
    int level = getLevel(points);

    expect(level, 12);
  });

  test('Points are 750, User level should be 12', () {
    int points = 750;
    int level = getLevel(points);

    expect(level, 12);
  });

  test('Points are 760, User level should be 13', () {
    int points = 760;
    int level = getLevel(points);

    expect(level, 13);
  });

  test('Points are 800, User level should be 13', () {
    int points = 800;
    int level = getLevel(points);

    expect(level, 13);
  });

  test('Points are 860, User level should be 14', () {
    int points = 860;
    int level = getLevel(points);

    expect(level, 14);
  });

  test('Points are 8600, User level should be 91', () {
    int points = 8600;
    int level = getLevel(points);

    expect(level, 91);
  });
}
