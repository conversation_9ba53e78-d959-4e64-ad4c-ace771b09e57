// Import the test package and Counter class
import 'package:flutter_test/flutter_test.dart';
import 'package:mybuddy/db/dao/workout_dao.dart';
import 'package:mybuddy/db/dao/workout_dog_dao.dart';
import 'package:mybuddy/db/database_service.dart';
import 'package:mybuddy/db/models/workout_db.dart';
import 'package:mybuddy/db/models/workout_dogs_db.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

Future<void> main() async {
  // Init ffi loader if needed.
  sqfliteFfiInit();

  var databaseFactory = databaseFactoryFfi;
  late Database _database;

  _database = await databaseFactory.openDatabase(
    inMemoryDatabasePath,
    options: OpenDatabaseOptions(
      onCreate: (db, version) async {
        await db.execute(
          'CREATE TABLE ${DatabaseService.TABLE_WORKOUT}('
          'id INTEGER PRIMARY KEY AUTOINCREMENT, '
          'workoutId INTEGER UNIQUE, '
          'ownerId INTEGER, '
          'status INTEGER, '
          'distance NUMERIC, '
          'speed NUMERIC, '
          'duration NUMERIC, '
          'moodType INTEGER, '
          'charityId INTEGER, '
          'charityName TEXT, '
          'points INTEGER, '
          'startLat NUMERIC, '
          'startLong NUMERIC, '
          'endLat NUMERIC, '
          'endLong NUMERIC, '
          'gpxFileName TEXT, '
          'isUploaded INTEGER, '
          'autoEndReason INTEGER, '
          'createdAt TEXT, '
          'updatedAt TEXT '
          ')',
        );
        // Run the CREATE {WorkoutDogs} TABLE statement on the database.
        await db.execute(
          'CREATE TABLE ${DatabaseService.TABLE_WORKOUT_DOGS}('
          'id INTEGER PRIMARY KEY AUTOINCREMENT, '
          'workoutId INTEGER, '
          'dogId INTEGER, '
          'FOREIGN KEY(workoutId) REFERENCES workouts(id)'
          ')',
        );
        // Run the CREATE {HomeSummary} TABLE statement on the database.
        await db.execute('''
          create table ${DatabaseService.TABLE_HOME_SUMMARY} (
            id integer primary key,
            totalPoints integer,
            totalWalks integer,
            totalDistance numeric,
            totalMinutes numeric,
            totalMigratedCharityPoints integer
          )
        ''');
      },
      version: 1,
      onConfigure: (db) async => await db.execute('PRAGMA foreign_keys = ON'),
    ),
  );

  test('When id is null', () async {
    WorkoutDao _workoutDao = WorkoutDao(db: _database);
    Map<String, dynamic> data = {
      "id": null,
      "workoutId": 36809074,
      "ownerId": 1194663,
      "status": 1,
      "distance": 0.004,
      "points": 0,
      "duration": 0.03,
      "speed": 11.1168,
      "moodType": 1,
      "charityId": 8521,
      "charityName": "100Plus Animal Rescue Inc.",
      "startLat": 37.32712677,
      "startLong": -122.02684563,
      "endLat": 37.32716294403848,
      "endLong": -122.02684847156645,
      "gpxFileName": "2023092815320048632921.gpx",
      "isUploaded": 0,
      "createdAt": "2023-09-28T15:33:32.522302",
      "updatedAt": "2023-09-28T15:33:36.385476"
    };
    WorkoutDb workout = WorkoutDb.fromMap(data);
    int? id = await _workoutDao.insertWorkout(workout);
    print(id);

    bool isAdded = id != null && id > 0;

    expect(isAdded, true);
  });

  test('When id and workoutId are null', () async {
    WorkoutDao _workoutDao = WorkoutDao(db: _database);
    Map<String, dynamic> data = {
      "id": null,
      "workoutId": null,
      "ownerId": 1194663,
      "status": 1,
      "distance": 0.004,
      "points": 0,
      "duration": 0.03,
      "speed": 11.1168,
      "moodType": 1,
      "charityId": 8521,
      "charityName": "100Plus Animal Rescue Inc.",
      "startLat": 37.32712677,
      "startLong": -122.02684563,
      "endLat": 37.32716294403848,
      "endLong": -122.02684847156645,
      "gpxFileName": "2023092815320048632921.gpx",
      "isUploaded": 0,
      "createdAt": "2023-09-28T15:33:32.522302",
      "updatedAt": "2023-09-28T15:33:36.385476"
    };
    WorkoutDb workout = WorkoutDb.fromMap(data);
    int? id = await _workoutDao.insertWorkout(workout);

    bool isAdded = id != null && id > 0;

    expect(isAdded, true);
  });

  test('When speed, distance and duration are zero', () async {
    WorkoutDao _workoutDao = WorkoutDao(db: _database);
    Map<String, dynamic> data = {
      "id": null,
      "workoutId": null,
      "ownerId": 1194663,
      "status": 1,
      "distance": 0,
      "points": 0,
      "duration": 0,
      "speed": 0,
      "moodType": 1,
      "charityId": 8521,
      "charityName": "100Plus Animal Rescue Inc.",
      "startLat": 37.32712677,
      "startLong": -122.02684563,
      "endLat": 37.32716294403848,
      "endLong": -122.02684847156645,
      "gpxFileName": "2023092815320048632921.gpx",
      "isUploaded": 0,
      "createdAt": "2023-09-28T15:33:32.522302",
      "updatedAt": "2023-09-28T15:33:36.385476"
    };
    WorkoutDb workout = WorkoutDb.fromMap(data);
    int? id = await _workoutDao.insertWorkout(workout);

    bool isAdded = id != null && id > 0;

    expect(isAdded, true);
  });

  test('When duplicate data is inserted', () async {
    WorkoutDao _workoutDao = WorkoutDao(db: _database);
    WorkoutDogDao _workoutDogDao = WorkoutDogDao(db: _database);
    Map<String, dynamic> data = {
      "id": null,
      "workoutId": 36809074,
      "ownerId": 1194663,
      "status": 1,
      "distance": 0.004,
      "points": 0,
      "duration": 0.03,
      "speed": 11.1168,
      "moodType": 1,
      "charityId": 8521,
      "charityName": "100Plus Animal Rescue Inc.",
      "startLat": 37.32712677,
      "startLong": -122.02684563,
      "endLat": 37.32716294403848,
      "endLong": -122.02684847156645,
      "gpxFileName": "2023092815320048632921.gpx",
      "isUploaded": 0,
      "createdAt": "2023-09-28T15:33:32.522302",
      "updatedAt": "2023-09-28T15:33:36.385476"
    };
    WorkoutDb workout = WorkoutDb.fromMap(data);
    int? id = await _workoutDao.insertWorkout(workout);
    print(id);

    await _workoutDogDao.insertWorkoutDogs([
      WorkoutDogDb(workoutId: id!, dogId: 1),
      WorkoutDogDb(workoutId: id, dogId: 2)
    ]);

    // workout.workoutId = 36809075;
    id = await _workoutDao.insertWorkout(workout);
    print(id);

    bool isAdded = id != null && id > 0;

    var result = await _database.query(DatabaseService.TABLE_WORKOUT);
    print(result);

    expect(isAdded, true);
  });

  test('When owner id are null', () async {
    WorkoutDao _workoutDao = WorkoutDao(db: _database);
    Map<String, dynamic> data = {
      "id": null,
      "workoutId": 36809074,
      "ownerId": null,
      "status": 1,
      "distance": 0.004,
      "points": 0,
      "duration": 0.03,
      "speed": 11.1168,
      "moodType": 1,
      "charityId": 8521,
      "charityName": "100Plus Animal Rescue Inc.",
      "startLat": 37.32712677,
      "startLong": -122.02684563,
      "endLat": 37.32716294403848,
      "endLong": -122.02684847156645,
      "gpxFileName": "2023092815320048632921.gpx",
      "isUploaded": 0,
      "createdAt": "2023-09-28T15:33:32.522302",
      "updatedAt": "2023-09-28T15:33:36.385476"
    };
    WorkoutDb workout = WorkoutDb.fromMap(data);
    int? id = await _workoutDao.insertWorkout(workout);

    bool isAdded = id != null && id > 0;

    expect(isAdded, true);
  });
}
