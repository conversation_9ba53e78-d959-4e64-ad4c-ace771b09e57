# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: "36a321c3d2cbe01cbcb3540a87b8843846e0206df3e691fa7b23e19e78de6d49"
      url: "https://pub.dev"
    source: hosted
    version: "65.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "5dce45a06d386358334eb1689108db6455d90ceb0d75848d5f4819283d4ee2b8"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.4"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: dfe03b90ec022450e22513b5e5ca1f01c0c01de9c3fba2f7fd233cb57a6b9a07
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  app_settings:
    dependency: "direct main"
    description:
      name: app_settings
      sha256: "7a5b880e2dd41dba8877108180380a1d28d874c231f7c0f9022127a4061b88e1"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.8"
  appsflyer_sdk:
    dependency: "direct main"
    description:
      name: appsflyer_sdk
      sha256: "2a960c9be41af9a21c4e8cab45989847d20d5342888af37648c044f826fcb32d"
      url: "https://pub.dev"
    source: hosted
    version: "6.8.2"
  archive:
    dependency: "direct main"
    description:
      name: archive
      sha256: eb33140ede1b4039f4ad631f7bf3cfa58e24514e8bf87184bc32f17541af87fc
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  args:
    dependency: transitive
    description:
      name: args
      sha256: c372bb384f273f0c2a8aaaa226dad84dc27c8519a691b888725dec59518ad53a
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  async:
    dependency: transitive
    description:
      name: async
      sha256: d2872f9c19731c2e5f10444b14686eb7cc85c76274bd6c16e1816bff9a3bab63
      url: "https://pub.dev"
    source: hosted
    version: "2.12.0"
  audio_session:
    dependency: transitive
    description:
      name: audio_session
      sha256: d8234185733d80133249143d67bdee29dcedc4588ce6ea232d88b083e79bfaf0
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6+1"
  awesome_notifications:
    dependency: "direct main"
    description:
      name: awesome_notifications
      sha256: "2b430c75cc879d6cfd52bb6eb2b5c1591ed425347816408cdcbd3f6916bba14c"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4+1"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "8aab1771e1243a5063b8b0ff68042d67334e3feab9e95b9490f9a6ebf73b42ea"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: ad77deb6e9c143a3f550fbb4c5c1e0c6aadabe24274898d06b9526c61b9cf4fb
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      sha256: fd3d0dc1d451f9a252b32d95d3f0c3c487bc41a75eba2e6097cb0b9c71491b15
      url: "https://pub.dev"
    source: hosted
    version: "3.2.3"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: bb2b8403b4ccdc60ef5f25c70dead1f3d32d24b9d6117cfc087f496b178594a7
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: b8eb814ebfcb4dea049680f8c1ffb2df399e4d03bf7a352c775e26fa06e02fa0
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: "5a1c1dd80ce7cbd69752fb283635b67b934528c87a031ac91646c0a9ba767705"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb98c0f6d12c920a02ee2d998da788bca066ca5f148492b7085ee23372b12306
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  charts_common:
    dependency: transitive
    description:
      name: charts_common
      sha256: "7b8922f9b0d9b134122756a787dab1c3946ae4f3fc5022ff323ba0014998ea02"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.0"
  charts_flutter:
    dependency: "direct main"
    description:
      name: charts_flutter
      sha256: "4172c3f4b85322fdffe1896ffbed79ae4689ae72cb6fe6690dcaaea620a9c558"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: dd007e4fb8270916820a0d66e24f619266b60773cddd082c6439341645af2659
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  chewie:
    dependency: transitive
    description:
      name: chewie
      sha256: da986fc618e01f22fa39da99da42152d8e0b572c767f8a6db9beaa3f6853d386
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  circle_flags:
    dependency: transitive
    description:
      name: circle_flags
      sha256: "****************************************c6e2f9aaec93b7592813fe05"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  contacts_service:
    dependency: "direct main"
    description:
      name: contacts_service
      sha256: f6d5ea33b31dfcdcd2e65d8abdc836502e04ddb0f66a96aa726fa9891ea9671e
      url: "https://pub.dev"
    source: hosted
    version: "0.6.3"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "196284f26f69444b7f5c50692b55ec25da86d9e500451dc09333bf2e3ad69259"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  coverage:
    dependency: transitive
    description:
      name: coverage
      sha256: "3945034e86ea203af7a056d98e98e42a5518fff200d6e8e6647e1886b07e936e"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.0"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: f71079978789bc2fe78d79227f1f8cfe195b31bbd8db2399b0d15a4b96fb843b
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: cf75650c66c0316274e21d7c43d3dea246273af5955bd94e8184837cd577575c
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: d1cd6d6e4b39a4ad295204722b8608f19981677b223f3e942c0b5a33dcf57ec0
      url: "https://pub.dev"
    source: hosted
    version: "0.17.1"
  cupertino_icons:
    dependency: transitive
    description:
      name: cupertino_icons
      sha256: "1989d917fbe8e6b39806207df5a3fdd3d816cbd090fac2ce26fb45e9a71476e5"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  dart_countries:
    dependency: transitive
    description:
      name: dart_countries
      sha256: "9bfc266284bfedd88226a32903781ab237020c7d9bf0089da288c6d677b192bc"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  dart_ipify:
    dependency: "direct main"
    description:
      name: dart_ipify
      sha256: "3b70d589504126107e81ad0703d91394cc8e2039cb0a11ebd92b9b824a5e9561"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  datetime_picker_formfield:
    dependency: "direct main"
    description:
      name: datetime_picker_formfield
      sha256: fcf49dc9917f769f1819cf0e15d843bf5e6df2ee53641b1cd344141796ff8fd0
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  dependency_validator:
    dependency: "direct dev"
    description:
      name: dependency_validator
      sha256: f289a3f79d05ae6e0601c29c8d1fae612c10abc904749493c3c3d6fb2f1adbcb
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  device_info:
    dependency: "direct main"
    description:
      name: device_info
      sha256: f4a8156cb7b7480d969cb734907d18b333c8f0bc0b1ad0b342cdcecf30d62c48
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  device_info_platform_interface:
    dependency: transitive
    description:
      name: device_info_platform_interface
      sha256: b148e0bf9640145d09a4f8dea96614076f889e7f7f8b5ecab1c7e5c2dbc73c1b
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "5fd6e152bdbc876bc6e81910e16a2bd36e0c68e23d87316e8da67a1ec8fd7b1c"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.4"
  dots_indicator:
    dependency: transitive
    description:
      name: dots_indicator
      sha256: e59dfc90030ee5a4fd4c53144a8ce97cc7a823c2067b8fb9814960cd1ae63f89
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "6a95e56b2449df2273fd8c45a662d6947ce1ebb7aafe80e550a3f68297f3cacc"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: a38574032c5f1dd06c4aee541789906c12ccaab8ba01446e800d9c5b79c4a978
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  file:
    dependency: transitive
    description:
      name: file
      sha256: b69516f2c26a5bcac4eee2e32512e1a5205ab312b3536c1c1227b2b942b5f9ad
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: "2e9324f719e90200dc7d3c4f5d2abc26052f9f2b995d3b6626c47a0dfe1c8192"
      url: "https://pub.dev"
    source: hosted
    version: "2.15.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: b63e3be6c96ef5c33bdec1aab23c91eb00696f6452f0519401d640938c94cba2
      url: "https://pub.dev"
    source: hosted
    version: "4.8.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "0fd5c4b228de29b55fac38aed0d9e42514b3d3bd47675de52bf7f8fccaf922fa"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  firebase_crashlytics:
    dependency: "direct main"
    description:
      name: firebase_crashlytics
      sha256: "3607b46342537f98df18b130b6f5ab25cee6981a3a782e1a7b121d04dfea3caa"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.4"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: c63abeb87b18f6e6d4bf6bb3977f15d2d9281a049d93fe098e83e56dcbf7da06
      url: "https://pub.dev"
    source: hosted
    version: "3.6.4"
  firebase_dynamic_links:
    dependency: "direct main"
    description:
      name: firebase_dynamic_links
      sha256: "4872f4d7e94736041398bc3490c2ddd87ee159d6b051ba01ca2708e5260a7ebe"
      url: "https://pub.dev"
    source: hosted
    version: "5.3.4"
  firebase_dynamic_links_platform_interface:
    dependency: transitive
    description:
      name: firebase_dynamic_links_platform_interface
      sha256: "946fccfefb67e26bf63e392f1b3917d79ea031d3071488f0c5e8ab72de8219ab"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.6+4"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      sha256: "8ac91d83a028eef050de770f1dc98421e215714d245f34de7b154d436676fbd0"
      url: "https://pub.dev"
    source: hosted
    version: "14.6.5"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: b2995e3640efb646e9ebf0e2fa50dea84895f0746a31d7e3af0e5e009a533a1a
      url: "https://pub.dev"
    source: hosted
    version: "4.5.4"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: "5d8446a28339124a2cb4f57a6ca454a3aca7d0c5c0cdfa5707afb192f7c830a7"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.4"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      sha256: "3525f13a7c280886d8119d7b8cbb88918ff0542bb7c41acc5674c2be93d1f8c0"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "32cd900555219333326a2d0653aaaf8671264c29befa65bbd9856d204a4c9fb3"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  flutter_custom_month_picker:
    dependency: "direct main"
    description:
      name: flutter_custom_month_picker
      sha256: c287dc6bac11fe8625ecf48dae64cb8b59d9f0ce01b9d58b72f50e5545c076f5
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: b543301ad291598523947dc534aaddc5aaad597b709d2426d3a0e0d44c5cb493
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_native_splash:
    dependency: "direct main"
    description:
      name: flutter_native_splash
      sha256: "06e82dcc3aae28085a385f8d6e2c6753199041b2001e6fc1dcce997f6e6ff463"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.11"
  flutter_native_timezone:
    dependency: "direct main"
    description:
      name: flutter_native_timezone
      sha256: ed7bfb982f036243de1c068e269182a877100c994f05143c8b26a325e28c1b02
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_neumorphic:
    dependency: "direct main"
    description:
      name: flutter_neumorphic
      sha256: "02606d937a3ceaa497b8a7c25f3efa95188bf93d77ebf0bd6552e432db4c2ec6"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "5c574d21b98ec92adab05ead10afd2b13ff5856c7ca79696edb338a9dd8ed387"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  flutter_speed_dial:
    dependency: "direct main"
    description:
      name: flutter_speed_dial
      sha256: "5e0e5c553877527d95a740f481bc2445166867a017427e52e419427681ca25f0"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0+1"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      sha256: "1312314293acceb65b92754298754801b0e1f26a1845833b740b30415bbbcf07"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.2"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: "6ff9fa12892ae074092de2fa6a9938fb21dbabfdaa2ff57dc697ff912fc8d4b2"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.6"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_widget_from_html:
    dependency: "direct main"
    description:
      name: flutter_widget_from_html
      sha256: bc92fa62986a1d14bec92d26225bb7b37d581bd91efdad55158efa2eeb200b85
      url: "https://pub.dev"
    source: hosted
    version: "0.9.1"
  flutter_widget_from_html_core:
    dependency: transitive
    description:
      name: flutter_widget_from_html_core
      sha256: "296485e685b44ad7c6883372aebc56038a28b84f75e7a668bcd8bf01de881739"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.1"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      sha256: b528e78a4e69957bb8a33d9e8ceaa728801bb7c6ce599e811e49cf6d94d17fef
      url: "https://pub.dev"
    source: hosted
    version: "8.0.9"
  font_awesome_flutter:
    dependency: "direct main"
    description:
      name: font_awesome_flutter
      sha256: "1f93e5799f0e6c882819e8393a05c6ca5226010f289190f2242ec19f3f0fdba5"
      url: "https://pub.dev"
    source: hosted
    version: "9.2.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: f64a0333a82f30b0cca061bc3d143813a486dc086b574bfb233b7c1372427694
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  fwfh_cached_network_image:
    dependency: transitive
    description:
      name: fwfh_cached_network_image
      sha256: "2b6b1f07ce04f52dbaa84258c5c613f1862e81d501cdf0b9208ddd6f7100d6b2"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0+3"
  fwfh_chewie:
    dependency: transitive
    description:
      name: fwfh_chewie
      sha256: "6474630c084cc90fbd348cea007d3cb41d62478460f75364e591f2baf26abccd"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.1+2"
  fwfh_just_audio:
    dependency: transitive
    description:
      name: fwfh_just_audio
      sha256: e2721732cfb8d3da0d5e74bc82af48100312fd4edc305567f2b9b85d53b63a1c
      url: "https://pub.dev"
    source: hosted
    version: "0.9.0"
  fwfh_svg:
    dependency: transitive
    description:
      name: fwfh_svg
      sha256: "556df67faf76b98c76dba144daf9fa9b3e874670e35b0a02d918402f8b8e98f5"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2+1"
  fwfh_text_style:
    dependency: transitive
    description:
      name: fwfh_text_style
      sha256: "37806ee0222f79b6e8d4c698c322c897eae6a817258156f40aeece4e588fac60"
      url: "https://pub.dev"
    source: hosted
    version: "2.22.08+1"
  fwfh_url_launcher:
    dependency: transitive
    description:
      name: fwfh_url_launcher
      sha256: cf3a537d74bf01b65d91dc8eac8c889d94b4176002fdc7069138f166f22d3411
      url: "https://pub.dev"
    source: hosted
    version: "0.9.0"
  fwfh_webview:
    dependency: transitive
    description:
      name: fwfh_webview
      sha256: "7ebd3dff551d7281dd3fbb9f5523a8455592665ff75459ca84871ebbebe864a8"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0+2"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: "2ba20a47c8f1f233bed775ba2dd0d3ac97b4cf32fc17731b3dfc672b06b0e92a"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.5"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "8321dd2c0ab0683a91a51307fa844c6db4aa8e3981219b78961672aaab434658"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  google_maps_flutter:
    dependency: "direct main"
    description:
      name: google_maps_flutter
      sha256: d2a1f357f6ceed27486fb1af3fd6f37a997527e595d8d42b245ac95fcbb1dd88
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      sha256: "39eb667f960064428f1db78e184fbb0f2e8a4b40e254ae0275162d6d57878322"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  google_mobile_ads:
    dependency: "direct main"
    description:
      name: google_mobile_ads
      sha256: "7b8915f0ad358f49ba7d547b5187204cfe72ca668fb83aa303f96a2eacdc4033"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  google_sign_in:
    dependency: "direct main"
    description:
      name: google_sign_in
      sha256: "821f354c053d51a2d417b02d42532a19a6ea8057d2f9ebb8863c07d81c98aaf9"
      url: "https://pub.dev"
    source: hosted
    version: "5.4.4"
  google_sign_in_android:
    dependency: transitive
    description:
      name: google_sign_in_android
      sha256: "197228076289e506d281328c6846b5986d929d316fc1d2c92a88bb13784faccc"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.5"
  google_sign_in_ios:
    dependency: transitive
    description:
      name: google_sign_in_ios
      sha256: "1116aff5e87f89837b052a81abe6259be7c4dd418275786864d27b74cb2a4e70"
      url: "https://pub.dev"
    source: hosted
    version: "5.5.1"
  google_sign_in_platform_interface:
    dependency: transitive
    description:
      name: google_sign_in_platform_interface
      sha256: "61306213c76bb8170c3aa20017df296c0131c24d7f6c0cc7e2eeaeac34c9f457"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  google_sign_in_web:
    dependency: transitive
    description:
      name: google_sign_in_web
      sha256: "75cc41ebc53b1756320ee14d9c3018ad3e6cea298147dbcd86e9d0c8d6720b40"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.2+1"
  gpx:
    dependency: "direct main"
    description:
      name: gpx
      sha256: b745a513c97e70dbc3e6c661cb1d0ed37e6096e2da7c005af58c12d2337f8d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  grouped_list:
    dependency: "direct main"
    description:
      name: grouped_list
      sha256: "1b5bce73ae7bfed03302c90dd485dced53b0d2b52213fe2a392a041dafaff4af"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  html:
    dependency: transitive
    description:
      name: html
      sha256: bfef906cbd4e78ef49ae511d9074aebd1d2251482ef601a280973e8b58b51bbf
      url: "https://pub.dev"
    source: hosted
    version: "0.15.0"
  html_unescape:
    dependency: "direct main"
    description:
      name: html_unescape
      sha256: "15362d7a18f19d7b742ef8dcb811f5fd2a2df98db9f80ea393c075189e0b61e3"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "6aa2946395183537c8b880962d935877325d6a09a2867c3970c05c0fed6ac482"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.5"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: e362d639ba3bc07d5a71faebb98cde68c05bfbcfbbb444b60b6f60bb67719185
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  image:
    dependency: "direct main"
    description:
      name: image
      sha256: f6ffe2895e3c86c6ad5a27e6302cf807403463e397cb2f0c580f619ac2fa588b
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  image_cropper:
    dependency: "direct main"
    description:
      name: image_cropper
      sha256: "85324928ee8a8be35a529446435ca53067865b9353c8681983472eeec66d780f"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  image_cropper_for_web:
    dependency: transitive
    description:
      name: image_cropper_for_web
      sha256: "09e93a8ec0435adcaa23622ac090442872f18145d70b9ff605ffedcf97d56255"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  image_cropper_platform_interface:
    dependency: transitive
    description:
      name: image_cropper_platform_interface
      sha256: "62349e3aab63873ea9b9ab9f69d036ab8a0d74b3004beec4303981386cb9273f"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: "4a82ddeb36277b633454ce8ad8b80f25989442b781ec8b0171cf5f141a66f8c7"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.4+8"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "324147043b5bb9032d0e187e30f6e97e50a8e8c2451ad1a9d44e6075724f4ce2"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "7f88edc438b61292a9ce4f4fc6af8cf808eaa36abd6643d5675194572714bbd8"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "910f85bce16fb5c6f614e117efa303e85a1731bb0081edf3604a2ae6e9a3cc91"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.0"
  introduction_screen:
    dependency: "direct main"
    description:
      name: introduction_screen
      sha256: "73965475d6b271846f81c5fce5b459546a4ea36c285408691522437fd6bbeb69"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "0d4c73c3653ab85bf696d51a9657604c900a370549196a91f33e4c39af760852"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  ios_utsname_ext:
    dependency: "direct main"
    description:
      name: ios_utsname_ext
      sha256: "45d7e97ac38700022775ca8460ae224b35ac037967ec7d58a39a2c59c13cb6dc"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  jiffy:
    dependency: "direct main"
    description:
      name: jiffy
      sha256: "85172c4fc975a50224521c05bf43abc845288863b19d91bd3c221a96a8785dd3"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  js:
    dependency: transitive
    description:
      name: js
      sha256: a5e201311cb08bf3912ebbe9a2be096e182d703f881136ec1e81a2338a9e120d
      url: "https://pub.dev"
    source: hosted
    version: "0.6.4"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "2639efc0237c7b71c6584696c0847ea4e4733ddaf571ae9c79d5295e8ae17272"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.0"
  json_api:
    dependency: "direct main"
    description:
      name: json_api
      sha256: baa7e55a65188cff4499e846f77032d9ae5c5f7a1877705019ca5129f45a7436
      url: "https://pub.dev"
    source: hosted
    version: "5.0.5"
  just_audio:
    dependency: transitive
    description:
      name: just_audio
      sha256: "6d898a3d863aafb3e17cc9d138faec93910d81c17650c500717a5909e8dacf50"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.18"
  just_audio_platform_interface:
    dependency: transitive
    description:
      name: just_audio_platform_interface
      sha256: "9953c5c6604c940c2a78f9ac6441da3551f61174a5dac9bda4dd92de73b11555"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  just_audio_web:
    dependency: transitive
    description:
      name: just_audio_web
      sha256: "8601e26d993e217b89fc4e335e9ef3759367def176afbc7fb97ee739a411a7ee"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  latlong2:
    dependency: "direct main"
    description:
      name: latlong2
      sha256: "408993a0e3f46e79ce1f129e4cb0386eef6d48dfa6394939ecacfbd7049154ec"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.1"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: c35baad643ba394b40aac41080300150a4f08fd0fd6a10378f8f7c6bc161acec
      url: "https://pub.dev"
    source: hosted
    version: "10.0.8"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: f8b613e7e6a13ec79cfdc0e97638fddb3ab848452eff057653abd3edba760573
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  linkwell:
    dependency: "direct main"
    description:
      name: linkwell
      sha256: "10b8453666a9b2f7a112af4b4cc832b3f82788a7867e114469402aa8614bf159"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.6"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: a2c3d198cb5ea2e179926622d433331d8b58374ab8f29cdda6e863bd62fd369c
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  location:
    dependency: "direct main"
    description:
      path: "packages/location"
      ref: fixing_issues
      resolved-ref: d730a507779d196aab7709c65b3d7b6ebaea38c8
      url: "https://github.com/akhatriST/flutterlocation"
    source: git
    version: "4.4.0"
  location_platform_interface:
    dependency: transitive
    description:
      name: location_platform_interface
      sha256: "62eeaf1658e92e4459b727f55a3c328eccbac8ba043fa6d262ac5286ad48384c"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  location_web:
    dependency: transitive
    description:
      name: location_web
      sha256: "6c08c408a040534c0269c4ff9fe17eebb5a36dea16512fbaf116b9c8bc21545b"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "293ae2d49fd79d4c04944c3a26dfd313382d5f52e821ec57119230ae16031ad4"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: c24e6c9ec37e07810b6242e143d06f219a5c5d1fb8491c242a0d421812541704
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "52e38f7e1143ef39daf532117d6b8f8f617bf4bcd6044ed8c29040d20d269630"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  move_to_background:
    dependency: "direct main"
    description:
      name: move_to_background
      sha256: "00caad17a6ce149910777131503f43f8ed80025681f94684e3a6a87d979b914c"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  multi_select_flutter:
    dependency: "direct main"
    description:
      name: multi_select_flutter
      sha256: "942b8ecac2de6884098b6dd9720e824adc7ee4619909a7a9dbdb43fb4204610f"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  node_preamble:
    dependency: transitive
    description:
      name: node_preamble
      sha256: "8ebdbaa3b96d5285d068f80772390d27c21e1fa10fb2df6627b1b9415043608d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: a4d69f0677d742a5c76b26430aeae9420656226f8b6add0f3d9a7c6309f35012
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: a4d5ede5ca9c3d88a2fef1147a078570c861714c806485c596b109819135bc12
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: f62d7253edc197fe3c88d7c2ddab82d68f555e778d55390ccc3537eca8e8d637
      url: "https://pub.dev"
    source: hosted
    version: "1.4.3+1"
  package_info_plus_linux:
    dependency: transitive
    description:
      name: package_info_plus_linux
      sha256: "04b575f44233d30edbb80a94e57cad9107aada334fc02aabb42b6becd13c43fc"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  package_info_plus_macos:
    dependency: transitive
    description:
      name: package_info_plus_macos
      sha256: a2ad8b4acf4cd479d4a0afa5a74ea3f5b1c7563b77e52cc32b3ee6956d5482a6
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: f7a0c8f1e7e981bc65f8b64137a53fd3c195b18d429fba960babc59a5a1c7ae8
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  package_info_plus_web:
    dependency: transitive
    description:
      name: package_info_plus_web
      sha256: f0829327eb534789e0a16ccac8936a80beed4e2401c4d3a74f3f39094a822d3b
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  package_info_plus_windows:
    dependency: transitive
    description:
      name: package_info_plus_windows
      sha256: "79524f11c42dd9078b96d797b3cf79c0a2883a50c4920dc43da8562c115089bc"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  path:
    dependency: "direct main"
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "050e8e85e4b7fecdf2bb3682c1c64c4887a183720c802d323de8a5fd76d372dd"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.11"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: c69109bae02c6116bd8ac81319b13eb73dfae02ef74690d2a1a98c1ddd3aaefc
      url: "https://pub.dev"
    source: hosted
    version: "2.0.11"
  path_provider_ios:
    dependency: transitive
    description:
      name: path_provider_ios
      sha256: "038d0141ff5d08c60ed071eee2758b68c50c42a1c10066a1fb6c28ab32fac84c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: ab0987bf95bc591da42dffb38c77398fc43309f0b9b894dcc5d6f40c4b26c379
      url: "https://pub.dev"
    source: hosted
    version: "2.1.7"
  path_provider_macos:
    dependency: transitive
    description:
      name: path_provider_macos
      sha256: "96c359ca47dfb7d0154fddcd47e251c00355c0eb647825af28b91ab16f8d7d83"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: c2af5a8a6369992d915f8933dfc23172071001359d17896e83db8be57db8a397
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bcabbe399d4042b8ee687e17548d5d3f527255253b4a639f5f8d2094a9c2b45c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      sha256: "67fc27ed9639506c856c840ccce7594d0bdcd91bc8d53d6e52359449a1d50602"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "14c4f2b9b04ce5b65c11a4541ef81af402bac6af883105fd32da13b61cd86391"
      url: "https://pub.dev"
    source: hosted
    version: "9.0.1"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: ca16451bfdc6d26693d10b37b2d81370bdf3f0318422f3eecfd6004f5bd7d21f
      url: "https://pub.dev"
    source: hosted
    version: "3.7.0"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "49392a45ced973e8d94a85fdb21293fbb40ba805fc49f2965101ae748a3683b4"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  phone_form_field:
    dependency: "direct main"
    description:
      name: phone_form_field
      sha256: "0244708f51e1c9b8fcd9e2ff6250cdf411a2ea6484a582ecfc8ba4877110c04c"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.0"
  phone_number_metadata:
    dependency: transitive
    description:
      name: phone_number_metadata
      sha256: "32d42fac690951941fe9ddd7fd62e79ea98fab46141ddc9e5f8322885b68f9b0"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  phone_numbers_parser:
    dependency: transitive
    description:
      name: phone_numbers_parser
      sha256: "3fc01dfa447b8ed275aeaaf6f7a0faae1c8f5740eec0ee753c8503c2a89c8bb9"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  photo_view:
    dependency: "direct main"
    description:
      path: "."
      ref: master
      resolved-ref: "7e8c335ad718c71e22402a920c939d949106d508"
      url: "https://github.com/bluefireteam/photo_view"
    source: git
    version: "0.14.0"
  pin_code_fields:
    dependency: "direct main"
    description:
      name: pin_code_fields
      sha256: c54d2016f77447990b8b2116a02f7eb445bda297d43bc7277f86a496a5ec58fe
      url: "https://pub.dev"
    source: hosted
    version: "7.3.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "4a451831508d7d6ca779f7ac6e212b4023dd5a7d08a27a63da33756410e32b76"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: dbf0f707c78beedc9200146ad3cb0ab4d5da13c246336987be6940f026500d3a
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  pretty_json:
    dependency: "direct main"
    description:
      name: pretty_json
      sha256: f1e9395d2c4c2bafacbcac0aedc77dbc25a29f80b690438e5f0cb5be8d8626c2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "53fd8db9cec1d37b0574e12f07520d582019cb6c44abf5479a01505099a34a09"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: "59471e0a4595e264625d3496af567ac85bdae1148ec985aff1e0555786f53ecf"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: b5a5fcc6425ea43704852ba4453ba94b08c2226c63418a260240c3a054579014
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "3686efe4a4613a4449b1a4ae08670aadbd3376f2e78d93e3f8f0919db02a7256"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "5c4208b4dc0d55c3184d10d83ee0ded6212dc2b5e2ba17c5a0c0aab279128d21"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  qr_flutter:
    dependency: "direct main"
    description:
      name: qr_flutter
      sha256: c5c121c54cb6dd837b9b9d57eb7bc7ec6df4aee741032060c8833a678c80b87e
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: "616b691d1c8f5c53b7b39ce3542f6a25308d7900bf689d0210e72a644a10387e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1+1"
  rxdart:
    dependency: "direct main"
    description:
      name: rxdart
      sha256: bc2d2b17b87fab32e2dca53ca3066d3147de6f96c74d76cfe1a379a24239c46d
      url: "https://pub.dev"
    source: hosted
    version: "0.27.3"
  share_plus:
    dependency: "direct main"
    description:
      name: share_plus
      sha256: "4a4f54a8be58496a9c6d167e8a337fb95510ac7decccfda054ef6903e90cb4bc"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  share_plus_linux:
    dependency: transitive
    description:
      name: share_plus_linux
      sha256: dc32bf9f1151b9864bb86a997c61a487967a08f2e0b4feaa9a10538712224da4
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  share_plus_macos:
    dependency: transitive
    description:
      name: share_plus_macos
      sha256: "44daa946f2845045ecd7abb3569b61cd9a55ae9cc4cbec9895b2067b270697ae"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: "82ddd4ab9260c295e6e39612d4ff00390b9a7a21f1bb1da771e2f232d80ab8a1"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  share_plus_web:
    dependency: transitive
    description:
      name: share_plus_web
      sha256: eaef05fa8548b372253e772837dd1fbe4ce3aca30ea330765c945d7d4f7c9935
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  share_plus_windows:
    dependency: transitive
    description:
      name: share_plus_windows
      sha256: f0bf3109d8cc4722b279b1f54bb16cfe339091b1262c2849263e42a4661897d2
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "1cd0c3c0be0826eb52362ab018a81eed13b616ad9a52548c6ceb1bb349e6b6eb"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.13"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "54eead0b53ed93d0c5d45dd1d39298d497489e0f1246aa8fb3d94372c9be9b35"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.9"
  shared_preferences_ios:
    dependency: transitive
    description:
      name: shared_preferences_ios
      sha256: "67b49e37034d692c101e0cf7e74b2391bce2f62534b37b85e393ecac3319a9a5"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.8"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: d772f65137c79c6a2021995b34dc4147edf60b7e62367081a075c8b430c55632
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  shared_preferences_macos:
    dependency: transitive
    description:
      name: shared_preferences_macos
      sha256: d8c36fedba519cba905d30833b818cde3d52d4e9c7438da4664bd523468bb93f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "992f0fdc46d0a3c0ac2e5859f2de0e577bbe51f78a77ee8f357cbe626a2ad32d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: ed98e2d4cf610e023975ff8b59d05d7fdf2d3ea038273c9dfb0cbc29094c93f2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "11cfe1f9f70b3215ca54593e80c9e2f6d35d46235cffbe7432f9bdd332bbab03"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: "8ec607599dd0a78931a5114cdac7d609b6dbbf479a38acc9a6dba024b2a30ea0"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  shelf_packages_handler:
    dependency: transitive
    description:
      name: shelf_packages_handler
      sha256: aef74dc9195746a384843102142ab65b6a4735bb3beea791e63527b88cc83306
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  shelf_static:
    dependency: transitive
    description:
      name: shelf_static
      sha256: e792b76b96a36d4a41b819da593aff4bdd413576b3ba6150df5d8d9996d2e74c
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "6db16374bc3497d21aa0eebe674d3db9fdf82082aac0f04dc7b44e4af5b08afc"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  shimmer:
    dependency: "direct main"
    description:
      name: shimmer
      sha256: "1f1009b5845a1f88f1c5630212279540486f97409e9fc3f63883e71070d107bf"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  sign_in_with_apple:
    dependency: "direct main"
    description:
      name: sign_in_with_apple
      sha256: "4fb4ec953a6cfa45630f83d783a436a0e35c3aeee0d82d4737118a7d7f5fcb3f"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  sign_in_with_apple_platform_interface:
    dependency: transitive
    description:
      name: sign_in_with_apple_platform_interface
      sha256: a5883edee09ed6be19de19e7d9f618a617fe41a6fa03f76d082dfb787e9ea18d
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  sign_in_with_apple_web:
    dependency: transitive
    description:
      name: sign_in_with_apple_web
      sha256: "44b66528f576e77847c14999d5e881e17e7223b7b0625a185417829e5306f47a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  signal_strength_indicator:
    dependency: "direct main"
    description:
      name: signal_strength_indicator
      sha256: "076f84fbffc69694f0df3d376822d806cc0de67d379aa57ebcdab09384ab839b"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  sliding_up_panel:
    dependency: "direct main"
    description:
      name: sliding_up_panel
      sha256: "578e90956a6212d1e406373250b2436a0f3afece29aee3c24c8360094d6cf968"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0+1"
  source_map_stack_trace:
    dependency: transitive
    description:
      name: source_map_stack_trace
      sha256: "8c463326277f68a628abab20580047b419c2ff66756fd0affd451f73f9508c11"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  source_maps:
    dependency: transitive
    description:
      name: source_maps
      sha256: "52de2200bb098de739794c82d09c41ac27b2e42fd7e23cce7b9c74bf653c7296"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.10"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "254ee5351d6cb365c859e20ee823c3bb479bf4a293c22d17a9f1bf144ce86f7c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.1"
  sqflite:
    dependency: "direct main"
    description:
      name: sqflite
      sha256: "2b1697c7b78576fdc722c358f16f62171bd56e92dc13422d9e44be3fc446c276"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "8f7603f3f8f126740bc55c4ca2d1027aab4b74a1267a3e31ce51fe40e3b65b8f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.5+1"
  sqflite_common_ffi:
    dependency: "direct main"
    description:
      name: sqflite_common_ffi
      sha256: f86de82d37403af491b21920a696b19f01465b596f545d1acd4d29a0a72418ad
      url: "https://pub.dev"
    source: hosted
    version: "2.2.5"
  sqlite3:
    dependency: transitive
    description:
      name: sqlite3
      sha256: "281b672749af2edf259fc801f0fcba092257425bcd32a0ce1c8237130bc934c7"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.2"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  sticky_headers:
    dependency: "direct main"
    description:
      name: sticky_headers
      sha256: e932bf2afbfd6587779cd445a9a252ba1c610db9ba33942b6d902d738dd205ca
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ed464977cb26a1f41537e177e190c67223dbd9f4f683489b6ab2e5d211ec564e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "921cd31725b72fe181906c6a94d987c78e3b98c2e205b397ea399d4054872b43"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      sha256: "3979f0b1c5a97422cadae52d476c21fa3e0fb671ef51de6cae1d646d8b99fe1f"
      url: "https://pub.dev"
    source: hosted
    version: "20.4.54"
  syncfusion_flutter_datepicker:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_datepicker
      sha256: "84f6dce915dfceb74c63b2f7b43a572a31c49f82deb79bab690c6c964a6e1f5c"
      url: "https://pub.dev"
    source: hosted
    version: "20.2.43"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "271977ff1e9e82ceefb4f08424b8839f577c1852e0726b5ce855311b46d3ef83"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: "7f554798625ea768a7518313e58f83891c7f5024f88e46e7182a4558850a4b8e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  test:
    dependency: "direct dev"
    description:
      name: test
      sha256: "301b213cd241ca982e9ba50266bd3f5bd1ea33f1455554c5abb85d1be0e2d87e"
      url: "https://pub.dev"
    source: hosted
    version: "1.25.15"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: fb31f383e2ee25fbbfe06b40fe21e1e458d14080e3c67e7ba0acfde4df4e0bbd
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  test_core:
    dependency: transitive
    description:
      name: test_core
      sha256: "84d17c3486c8dfdbe5e12a50c8ae176d15e2a771b96909a9442b40173649ccaa"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.8"
  timezone:
    dependency: "direct main"
    description:
      name: timezone
      sha256: "1cfd8ddc2d1cfd836bc93e67b9be88c3adaeca6f40a00ca999104c30693cdca0"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: "53bdf7e979cfbf3e28987552fd72f637e63f3c8724c9e56d9246942dc2fa36ee"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      sha256: "06866290206d196064fd61df4c7aea1ffe9a4e7c4ccaa8fcded42dd41948005d"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "3c92b0efb5e9dcb8f846aefabf9f0f739f91682ed486b991ceda51c288e60896"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.7"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "55b921b486e0153aacd57aef18940c4ff427faa469a108af31d24a098a43ebd4"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.13"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "31bbb50e00b80848767c4e8fd72143ca14df677e8c827ce0dc84a7a49dd1eb77"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.13"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "360fa359ab06bcb4f7c5cd3123a2a9a4d3364d4575d27c4b33468bd4497dd094"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: c116ae76b578e0403c2c65630715c0c81a79ef22a11ff9192674b5233be88735
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "4eae912628763eb48fc214522e58e942fd16ce195407dbf45638239523c759a6"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "5669882643b96bb6d5786637cac727c6e918a790053b09245fd4513b8a07df2a"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.13"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: e3c3b16d3104260c10eea3b0e34272aaa57921f83148b0619f74c2eced9b7ef1
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: "9550ef42803203cd729e34313142b7a1ee2a90b395105a7e38e32a9139fa28ad"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.18"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: "6178b89f1af07054b49f0e7d18ad57b189ee2af946df0c93aab1f6c1b51beaa0"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.17"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: "57ed9871b08a2e422cd0d8f74ae3a6423effa5d387b22451286403fdbe91808f"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.18"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: "2432273a3f277d1a3dcf98f266af4ef52aa14ae7117cabb43022f5c96704f974"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "0c4c05b3275b9ec219b2d55c24424bdc469ab0ba676184ac8c5d2f5d1c67f0fe"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "0968250880a6c5fe7edc067ed0a13d4bae1577fe2771dcf3010d52c4a9d3ca14"
      url: "https://pub.dev"
    source: hosted
    version: "14.3.1"
  wakelock:
    dependency: "direct overridden"
    description:
      name: wakelock
      sha256: "769ecf42eb2d07128407b50cb93d7c10bd2ee48f0276ef0119db1d25cc2f87db"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.2"
  wakelock_macos:
    dependency: transitive
    description:
      name: wakelock_macos
      sha256: "047c6be2f88cb6b76d02553bca5a3a3b95323b15d30867eca53a19a0a319d4cd"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  wakelock_platform_interface:
    dependency: transitive
    description:
      name: wakelock_platform_interface
      sha256: "1f4aeb81fb592b863da83d2d0f7b8196067451e4df91046c26b54a403f9de621"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  wakelock_web:
    dependency: transitive
    description:
      name: wakelock_web
      sha256: "1b256b811ee3f0834888efddfe03da8d18d0819317f20f6193e2922b41a501b5"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  wakelock_windows:
    dependency: transitive
    description:
      name: wakelock_windows
      sha256: "108b1b73711f1664ee462e73af34a9286ff496e27d4d8371e2fb4da8fde4cdac"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: e42dfcc48f67618344da967b10f62de57e04bae01d9d3af4c2596f3712a88c99
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: "3a969ddcc204a3e34e863d204b29c0752716f78b6f9cc8235083208d268a4ccd"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  webkit_inspection_protocol:
    dependency: transitive
    description:
      name: webkit_inspection_protocol
      sha256: "67d3a8b6c79e1987d19d848b0892e582dbb0c66c57cc1fef58a177dd2aa2823d"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: "789d52bd789373cc1e100fb634af2127e86c99cf9abde09499743270c5de8d00"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: bca797abba472868655b5f1a6029c1132385685ee9db4713cb0e7f33076210c6
      url: "https://pub.dev"
    source: hosted
    version: "3.9.3"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: "0ca3cfcc6781a7de701d580917af4a9efc4e3e129f8ead95a80587f0a749480a"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: ed749f94ac9e814d04a258a9255cf69cfa4cc6006ff59542aea7fb4590144972
      url: "https://pub.dev"
    source: hosted
    version: "3.7.3"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "6b75ac2ddd42f5c226fdaf4498a2b04071c06f1f2b8f7ab1c3f77cc7f2285ff1"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "11541eedefbcaec9de35aa82650b695297ce668662bbd6e3911a7fabdbde589f"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0+2"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: ac0e3f4bf00ba2708c33fbabbbe766300e509f8c82dbd4ab6525039813f7e2fb
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "23812a9b125b48d4007117254bca50abb6c712352927eece9e155207b1db2370"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
sdks:
  dart: ">=3.7.0-0 <4.0.0"
  flutter: ">=3.18.0-18.0.pre.54"
