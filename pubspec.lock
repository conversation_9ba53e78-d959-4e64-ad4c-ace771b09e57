# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      url: "https://pub.dartlang.org"
    source: hosted
    version: "47.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.4"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.7.0"
  app_settings:
    dependency: "direct main"
    description:
      name: app_settings
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.8"
  appsflyer_sdk:
    dependency: "direct main"
    description:
      name: appsflyer_sdk
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.8.2"
  archive:
    dependency: "direct main"
    description:
      name: archive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.3.0"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.4.1"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.9.0"
  audio_session:
    dependency: transitive
    description:
      name: audio_session
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.6+1"
  awesome_notifications:
    dependency: "direct main"
    description:
      name: awesome_notifications
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.4+1"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  build_config:
    dependency: transitive
    description:
      name: build_config
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.3"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  charts_common:
    dependency: transitive
    description:
      name: charts_common
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.0"
  charts_flutter:
    dependency: "direct main"
    description:
      name: charts_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  chewie:
    dependency: transitive
    description:
      name: chewie
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.2"
  circle_flags:
    dependency: transitive
    description:
      name: circle_flags
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.2"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.1"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.16.0"
  contacts_service:
    dependency: "direct main"
    description:
      name: contacts_service
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.3"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  coverage:
    dependency: transitive
    description:
      name: coverage
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.3"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.3+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  csslib:
    dependency: transitive
    description:
      name: csslib
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.1"
  cupertino_icons:
    dependency: transitive
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  customer_io:
    dependency: "direct main"
    description:
      name: customer_io
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.2"
  dart_countries:
    dependency: transitive
    description:
      name: dart_countries
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  dart_ipify:
    dependency: "direct main"
    description:
      name: dart_ipify
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.1"
  datetime_picker_formfield:
    dependency: "direct main"
    description:
      name: datetime_picker_formfield
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  dependency_validator:
    dependency: "direct dev"
    description:
      name: dependency_validator
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.2"
  device_info:
    dependency: "direct main"
    description:
      name: device_info
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.3"
  device_info_platform_interface:
    dependency: transitive
    description:
      name: device_info_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  dio:
    dependency: "direct main"
    description:
      name: dio
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.4"
  dots_indicator:
    dependency: transitive
    description:
      name: dots_indicator
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  facebook_auth_desktop:
    dependency: transitive
    description:
      name: facebook_auth_desktop
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.8"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  file:
    dependency: transitive
    description:
      name: file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.2"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.15.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.8.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.6.0"
  firebase_crashlytics:
    dependency: "direct main"
    description:
      name: firebase_crashlytics
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.3.4"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.6.4"
  firebase_dynamic_links:
    dependency: "direct main"
    description:
      name: firebase_dynamic_links
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.3.4"
  firebase_dynamic_links_platform_interface:
    dependency: transitive
    description:
      name: firebase_dynamic_links_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.6+4"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      url: "https://pub.dartlang.org"
    source: hosted
    version: "14.6.5"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.5.4"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.5.4"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.0"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.3.0"
  flutter_custom_month_picker:
    dependency: "direct main"
    description:
      name: flutter_custom_month_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.3"
  flutter_facebook_auth:
    dependency: "direct main"
    description:
      name: flutter_facebook_auth
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.7"
  flutter_facebook_auth_platform_interface:
    dependency: transitive
    description:
      name: flutter_facebook_auth_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.1"
  flutter_facebook_auth_web:
    dependency: transitive
    description:
      name: flutter_facebook_auth_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.1"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_native_splash:
    dependency: "direct main"
    description:
      name: flutter_native_splash
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.11"
  flutter_native_timezone:
    dependency: "direct main"
    description:
      name: flutter_native_timezone
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  flutter_neumorphic:
    dependency: "direct main"
    description:
      name: flutter_neumorphic
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  flutter_secure_storage:
    dependency: transitive
    description:
      name: flutter_secure_storage
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.0.1"
  flutter_secure_storage_linux:
    dependency: transitive
    description:
      name: flutter_secure_storage_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.2"
  flutter_secure_storage_macos:
    dependency: transitive
    description:
      name: flutter_secure_storage_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  flutter_secure_storage_platform_interface:
    dependency: transitive
    description:
      name: flutter_secure_storage_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  flutter_secure_storage_web:
    dependency: transitive
    description:
      name: flutter_secure_storage_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.1"
  flutter_secure_storage_windows:
    dependency: transitive
    description:
      name: flutter_secure_storage_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.3"
  flutter_speed_dial:
    dependency: "direct main"
    description:
      name: flutter_speed_dial
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.0+1"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.2"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.6"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_widget_from_html:
    dependency: "direct main"
    description:
      name: flutter_widget_from_html
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.1"
  flutter_widget_from_html_core:
    dependency: transitive
    description:
      name: flutter_widget_from_html_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.1"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      url: "https://pub.dartlang.org"
    source: hosted
    version: "8.0.9"
  font_awesome_flutter:
    dependency: "direct main"
    description:
      name: font_awesome_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "9.2.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  fwfh_cached_network_image:
    dependency: transitive
    description:
      name: fwfh_cached_network_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.0+3"
  fwfh_chewie:
    dependency: transitive
    description:
      name: fwfh_chewie
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.1+2"
  fwfh_just_audio:
    dependency: transitive
    description:
      name: fwfh_just_audio
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.0"
  fwfh_svg:
    dependency: transitive
    description:
      name: fwfh_svg
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.2+1"
  fwfh_text_style:
    dependency: transitive
    description:
      name: fwfh_text_style
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.22.08+1"
  fwfh_url_launcher:
    dependency: transitive
    description:
      name: fwfh_url_launcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.0"
  fwfh_webview:
    dependency: transitive
    description:
      name: fwfh_webview
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.0+2"
  get:
    dependency: "direct main"
    description:
      name: get
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.6.5"
  glob:
    dependency: transitive
    description:
      name: glob
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  google_maps_flutter:
    dependency: "direct main"
    description:
      name: google_maps_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.8"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  google_mobile_ads:
    dependency: "direct main"
    description:
      name: google_mobile_ads
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  google_sign_in:
    dependency: "direct main"
    description:
      name: google_sign_in
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.4.4"
  google_sign_in_android:
    dependency: transitive
    description:
      name: google_sign_in_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.5"
  google_sign_in_ios:
    dependency: transitive
    description:
      name: google_sign_in_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.5.1"
  google_sign_in_platform_interface:
    dependency: transitive
    description:
      name: google_sign_in_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.0"
  google_sign_in_web:
    dependency: transitive
    description:
      name: google_sign_in_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.10.2+1"
  gpx:
    dependency: "direct main"
    description:
      name: gpx
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  grouped_list:
    dependency: "direct main"
    description:
      name: grouped_list
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.0"
  html:
    dependency: transitive
    description:
      name: html
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.15.0"
  html_unescape:
    dependency: "direct main"
    description:
      name: html_unescape
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  http:
    dependency: transitive
    description:
      name: http
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.13.5"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  image:
    dependency: "direct main"
    description:
      name: image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.2"
  image_cropper:
    dependency: "direct main"
    description:
      name: image_cropper
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  image_cropper_for_web:
    dependency: transitive
    description:
      name: image_cropper_for_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.3"
  image_cropper_platform_interface:
    dependency: transitive
    description:
      name: image_cropper_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.3"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.4+8"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.4"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.4.1"
  intl:
    dependency: "direct main"
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.0"
  introduction_screen:
    dependency: "direct main"
    description:
      name: introduction_screen
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.4"
  io:
    dependency: transitive
    description:
      name: io
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.3"
  ios_utsname_ext:
    dependency: "direct main"
    description:
      name: ios_utsname_ext
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  jiffy:
    dependency: "direct main"
    description:
      name: jiffy
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.0"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.4"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.4.0"
  json_api:
    dependency: "direct main"
    description:
      name: json_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.5"
  just_audio:
    dependency: transitive
    description:
      name: just_audio
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.18"
  just_audio_platform_interface:
    dependency: transitive
    description:
      name: just_audio_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  just_audio_web:
    dependency: transitive
    description:
      name: just_audio_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.2"
  latlong2:
    dependency: "direct main"
    description:
      name: latlong2
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.1"
  linkwell:
    dependency: "direct main"
    description:
      name: linkwell
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.6"
  lints:
    dependency: transitive
    description:
      name: lints
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  location:
    dependency: "direct main"
    description:
      path: "packages/location"
      ref: fixing_issues
      resolved-ref: d730a507779d196aab7709c65b3d7b6ebaea38c8
      url: "https://github.com/akhatriST/flutterlocation"
    source: git
    version: "4.4.0"
  location_platform_interface:
    dependency: transitive
    description:
      name: location_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.0"
  location_web:
    dependency: transitive
    description:
      name: location_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.1"
  logging:
    dependency: transitive
    description:
      name: logging
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.2"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.12"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.5"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0"
  mime:
    dependency: transitive
    description:
      name: mime
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.3"
  move_to_background:
    dependency: "direct main"
    description:
      name: move_to_background
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  multi_select_flutter:
    dependency: "direct main"
    description:
      name: multi_select_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  nested:
    dependency: transitive
    description:
      name: nested
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  node_preamble:
    dependency: transitive
    description:
      name: node_preamble
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  package_config:
    dependency: transitive
    description:
      name: package_config
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.3+1"
  package_info_plus_linux:
    dependency: transitive
    description:
      name: package_info_plus_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  package_info_plus_macos:
    dependency: transitive
    description:
      name: package_info_plus_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  package_info_plus_web:
    dependency: transitive
    description:
      name: package_info_plus_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.6"
  package_info_plus_windows:
    dependency: transitive
    description:
      name: package_info_plus_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  path:
    dependency: "direct main"
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.2"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.11"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.11"
  path_provider_ios:
    dependency: transitive
    description:
      name: path_provider_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.7"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.7"
  path_provider_macos:
    dependency: transitive
    description:
      name: path_provider_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.11.1"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      url: "https://pub.dartlang.org"
    source: hosted
    version: "9.0.1"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.7.0"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.1.0"
  phone_form_field:
    dependency: "direct main"
    description:
      name: phone_form_field
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.4.0"
  phone_number_metadata:
    dependency: transitive
    description:
      name: phone_number_metadata
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.2"
  phone_numbers_parser:
    dependency: transitive
    description:
      name: phone_numbers_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.0"
  photo_view:
    dependency: "direct main"
    description:
      path: "."
      ref: master
      resolved-ref: "7e8c335ad718c71e22402a920c939d949106d508"
      url: "https://github.com/bluefireteam/photo_view"
    source: git
    version: "0.14.0"
  pin_code_fields:
    dependency: "direct main"
    description:
      name: pin_code_fields
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.3.0"
  platform:
    dependency: transitive
    description:
      name: platform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  pool:
    dependency: transitive
    description:
      name: pool
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.1"
  pretty_json:
    dependency: "direct main"
    description:
      name: pretty_json
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  process:
    dependency: transitive
    description:
      name: process
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.4"
  provider:
    dependency: transitive
    description:
      name: provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.0"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  qr:
    dependency: transitive
    description:
      name: qr
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  qr_flutter:
    dependency: "direct main"
    description:
      name: qr_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  quiver:
    dependency: transitive
    description:
      name: quiver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1+1"
  rxdart:
    dependency: "direct main"
    description:
      name: rxdart
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.27.3"
  share_plus:
    dependency: "direct main"
    description:
      name: share_plus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.0"
  share_plus_linux:
    dependency: transitive
    description:
      name: share_plus_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  share_plus_macos:
    dependency: transitive
    description:
      name: share_plus_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.0"
  share_plus_web:
    dependency: transitive
    description:
      name: share_plus_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  share_plus_windows:
    dependency: transitive
    description:
      name: share_plus_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.13"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.9"
  shared_preferences_ios:
    dependency: transitive
    description:
      name: shared_preferences_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.8"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.3"
  shared_preferences_macos:
    dependency: transitive
    description:
      name: shared_preferences_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.3"
  shelf:
    dependency: transitive
    description:
      name: shelf
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.2"
  shelf_packages_handler:
    dependency: transitive
    description:
      name: shelf_packages_handler
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  shelf_static:
    dependency: transitive
    description:
      name: shelf_static
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  shimmer:
    dependency: "direct main"
    description:
      name: shimmer
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  sign_in_with_apple:
    dependency: "direct main"
    description:
      name: sign_in_with_apple
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  sign_in_with_apple_platform_interface:
    dependency: transitive
    description:
      name: sign_in_with_apple_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  sign_in_with_apple_web:
    dependency: transitive
    description:
      name: sign_in_with_apple_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  signal_strength_indicator:
    dependency: "direct main"
    description:
      name: signal_strength_indicator
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sliding_up_panel:
    dependency: "direct main"
    description:
      name: sliding_up_panel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0+1"
  source_map_stack_trace:
    dependency: transitive
    description:
      name: source_map_stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  source_maps:
    dependency: transitive
    description:
      name: source_maps
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.10.10"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.9.0"
  sqflite:
    dependency: "direct main"
    description:
      name: sqflite
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.2"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.4.5+1"
  sqflite_common_ffi:
    dependency: "direct main"
    description:
      name: sqflite_common_ffi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.5"
  sqlite3:
    dependency: transitive
    description:
      name: sqlite3
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.11.2"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0"
  sticky_headers:
    dependency: "direct main"
    description:
      name: sticky_headers
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.1"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "20.4.54"
  syncfusion_flutter_datepicker:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_datepicker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "20.2.43"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  test:
    dependency: "direct dev"
    description:
      name: test
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.21.4"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.12"
  test_core:
    dependency: transitive
    description:
      name: test_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.16"
  timezone:
    dependency: "direct main"
    description:
      name: timezone
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.7"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.13"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.13"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.13"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  uuid:
    dependency: transitive
    description:
      name: uuid
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.7"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.18"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.17"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.18"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.0"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.5.0"
  wakelock:
    dependency: transitive
    description:
      name: wakelock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.6"
  wakelock_macos:
    dependency: transitive
    description:
      name: wakelock_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.0"
  wakelock_platform_interface:
    dependency: transitive
    description:
      name: wakelock_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.0"
  wakelock_web:
    dependency: transitive
    description:
      name: wakelock_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.0"
  wakelock_windows:
    dependency: transitive
    description:
      name: wakelock_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  webkit_inspection_protocol:
    dependency: transitive
    description:
      name: webkit_inspection_protocol
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.2"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.9.3"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.5.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.7.3"
  win32:
    dependency: transitive
    description:
      name: win32
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.7.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0+2"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.1"
sdks:
  dart: ">=2.18.6 <3.0.0"
  flutter: ">=3.3.0"
