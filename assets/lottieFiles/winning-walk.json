{"v": "5.6.9", "fr": 24, "ip": 0, "op": 96, "w": 600, "h": 800, "nm": "anim-petsmile", "ddd": 0, "assets": [{"id": "image_0", "w": 32, "h": 32, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAB7UlEQVRYhcWWMS8EQRTHf28KdxESCo2Gb6A+q6CgUgoFhUItV2r0Eo0PoJK4RKFTylGw2+pEIdFQK7DX7D3FDM7hbndn3f6b2ZnNzv/3dt+8t+Ahjdj0eR7AeAHAlkZMlwYAzABBKQB6wRgw0obFUgCoMA8g2HHgACpsuMuJUgCAJTcOa8T6QAE0ZBmofM6F3cECCIfA0NcCUw7q/wGSkH1gvGu5qkLDnYz/A9CQZRG26Yz+S6Na5SwrgGQxV+H0D/NOXZsac4UCJCH7PSL/TU8CgdR48AJwUR9iv3la8w+1FE5Mi7os8JwKQC8Yo8K8KzJL2KOW1fgHCHArQoOEcwm4+QagIXUVVrCNZcTTrJ8UG/S9KmdGOBAA11KDNiy62j4BDBdoHAOvwJ0qTSM0pcYl9MgBjVhXYRdlCqjmNH5R4dgoe38lZN9T4BKxAYxmMI5VODIxO70SMBUA2OR0RSbNz8ebKGsym64opS5EAO2Iqz4Qb9Im6M70wgAcxCMw+cutWJTVtJF/KHMzEvsGWt3rKhxlNc8HUONB4aRr+cXE7GTdKxcAgGlRp+MtqHDcL9sLBXBmt24aG2Uvzz65AQDE1gaA1zRdr3AAEs6xtf0u9x4+AO6siyrNUgCc7o34AXgpCTkozRw+27iX3gGtbJicGFjZiAAAAABJRU5ErkJggg==", "e": 1}, {"id": "image_1", "w": 18, "h": 18, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAABJklEQVQ4jaWSP0vDQBiHnzdGuUXIXBBcu0kHRV1KcS4d+in6IRyc7DfwC3TrXCgUEhByXevcQewsCE5Gcq9DUpCG9lLy2+7ud8/7FzzSlIHGRD5f4DM4oY/hyucLfQaBnhMyIGmUEdAS5dpnOgjSJSPgFGj7+rQXpDGRKs/l0ahhfjRIYyI1vAHn/647uWVSG6RLRmr4AC52nkKBobOs1dLd/SeaMnBCX6AHtCh64tMvoMBGYRrAS8gPSWD4ckJWTqcNGA/oG1iJMJOchdzzLpXSiv7MgQ7VPctUGZ/c8VgpbV/I3DIRGAJnW4gEPMgNr55sq3KWtbOos2ie8nQ0YCu1dJ0lc5ZPn/fgZsstCcV0Vo1ApTYizBqDFKbkLGoE9IAsl3V8f6vSYIBPS/58AAAAAElFTkSuQmCC", "e": 1}, {"id": "image_2", "w": 197, "h": 59, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_3", "w": 276, "h": 60, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_4", "w": 39, "h": 31, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACcAAAAfCAYAAABkitT1AAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAACtklEQVRYhc2XT2rbUBDGf6MYXBqInUWgC4NVsisUO4tCQhdxT5DcoLqBfQP7Bs0N4t4gPUG8sbVss+mu4H2gjRcNDVSaLuR/st97khw35AMtNBrNfG++efMkYUvQED+GwIu5kvd820ZM2UYQgDhkDNSBifzBlw/cPTamBxCF9OIQjUZcbBJEQ/wpMYAKZYLHEpuTE+gCiNDWEZ3i7DhP3coGMWzkVgL3ppXIDZW1StU1pFUoxpBmHDKORlzoNVUjOaCi0M8dNFlIY9Uek19aHdJUjwFQF6FNOVmYiRzAaQF5WyajwMdZBZzErqlOiVVmtlhousjllldX+i2FHBtDX6SJLcNzEMiUV6+pIpxZn6/3YgrT6bDWEnNy4KyOW94XjqolaOgwkWgVGhKI0Ha9bJV1HsQhr1PSKWJvfXE6pKlkz9RMciTyXq0lyJB0BmFlBiYboI+lz4qSA2hEIb2UJVvSGSoaLnpPy/Rx9BmAaMZuXXsBusv9k0fSVV8d0clTbcQ+hO1JEjlyS7qU7ExDAhU+FclXwr1bV9GIRlwgxT+JFC6LvlOKwS/y3SRCW2FSNNEmKCTrEjJ32jawKbn/DR+SnjNjt4nsn6MPY7jtPw2lBepgq9xBgLz9CrUucniJvBm4Q+02kaMxcqzIscJB4HSXWg95d5dcvv2gMJKTWi9t2DuFXeMRufAv1xf3h5dQ9s3OBwHUurBTSa5XbetizAf/UqI5dhyfZvuGkWchJwa77LWMvp6YyP36kr6PJnDvGG33N+u2h7HdP6evUVb9ESwI3t+g31vw1/6np+NOsoAZbj9bE+ptP+0bTRKbARKHDIBTa+a8KFXhZROiO/idcYCUqos++3llXIjEHG2P3JYh8Pq5DmHkhPGzJQfgSUyHJzrIi0Jg/mPsbyNgbPmPLQIPBnLC4B+PkOAfhZHCJQAAAABJRU5ErkJggg==", "e": 1}, {"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Null 13", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [300, 400, 0], "to": [0, -5, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 1, "y": 0}, "t": 7.5, "s": [300, 370, 0], "to": [0, 0, 0], "ti": [0, -5, 0]}, {"t": 15, "s": [300, 400, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "board-face", "parent": 1, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [300, 400, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 600, "h": 800, "ip": 4, "op": 224, "st": 4, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "shadow", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [10]}, {"t": 24, "s": [20]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 4, "s": [100, 100, 100]}, {"t": 10, "s": [48, 100, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [109, 59.876], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.2, 0, 0, 0, 0.6, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 111], "ix": 5}, "e": {"a": 0, "k": [100, 0], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-58.5, -28.062], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 4, "op": 224, "st": 4, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "board-right", "parent": 1, "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [300, 400, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 600, "h": 800, "ip": 8, "op": 228, "st": 8, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "shadow 2", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 14, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16, "s": [10]}, {"t": 28, "s": [20]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [109, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [109, 59.876], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.2, 0, 0, 0, 0.6, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 111], "ix": 5}, "e": {"a": 0, "k": [100, 0], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-58.5, -28.062], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 8, "op": 228, "st": 8, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "box/gift.ai", "cl": "ai", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-4.255, 25.871, 0], "ix": 2}, "a": {"a": 0, "k": [108.995, 83.621, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[36.575, 79.716], [36.573, 79.716]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 2", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.104, 0], [0.095, -0.245], [-0.409, -0.197], [-0.084, 0], [-0.094, 0.053], [-0.015, 0.252], [0.049, 0.138], [0.165, 0.098]], "o": [[-0.237, 0], [-0.162, 0.424], [0.084, 0.041], [0.096, 0], [0.236, -0.133], [0.005, -0.152], [-0.066, -0.183], [-0.1, -0.058]], "v": [[-0.028, -0.804], [-0.583, -0.416], [-0.124, 0.742], [0.129, 0.804], [0.414, 0.725], [0.74, 0.12], [0.637, -0.305], [0.284, -0.718]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.906, 70.832], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.005, -0.152], [0.236, -0.132], [0.181, 0.088], [-0.162, 0.423], [-0.328, -0.192], [-0.065, -0.183]], "o": [[-0.015, 0.252], [-0.176, 0.1], [-0.409, -0.197], [0.136, -0.354], [0.165, 0.097], [0.05, 0.138]], "v": [[0.74, 0.16], [0.414, 0.764], [-0.124, 0.782], [-0.583, -0.376], [0.284, -0.678], [0.636, -0.265]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.906, 70.792], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.104, 0], [0.15, -0.098], [0.13, -0.281], [0.011, -0.023], [0.004, 0.001], [0, 0], [0.256, 0], [0.158, 0.009], [0, 0], [-0.019, -0.002], [-0.048, -0.296], [0, -0.086], [0.001, -0.732], [0.007, -0.078], [0.213, -0.035], [0.071, -0.008], [0, 0], [0, 0], [0, 0], [0.131, 0.032], [0.04, 0.198], [0, 0.065], [-0.012, 0.524], [-0.34, 0.323], [-0.156, 0.021], [-0.036, 0], [-0.042, -0.057], [0.044, -0.051], [-0.281, -0.073], [-0.037, 0], [-0.059, 0.189], [0.289, 0.115]], "o": [[-0.163, 0], [-0.261, 0.172], [-0.01, 0.024], [-0.005, -0.001], [0, 0], [-0.257, 0.078], [-0.158, 0], [0, 0], [0.025, 0.003], [0.308, 0.028], [0.013, 0.084], [0.002, 0.732], [0, 0.079], [-0.021, 0.215], [-0.072, 0.012], [0, 0], [0, 0], [0, 0], [-0.143, -0.023], [-0.188, -0.047], [-0.013, -0.064], [-0.001, -0.525], [0.01, -0.466], [0.114, -0.107], [0.036, -0.005], [0.057, 0], [-0.052, 0.048], [-0.193, 0.227], [0.038, 0.011], [0.182, 0], [0.089, -0.279], [-0.108, -0.043]], "v": [[0.789, -1.959], [0.318, -1.809], [-0.253, -1.113], [-0.285, -1.042], [-0.299, -1.046], [-0.299, -1.93], [-1.068, -1.829], [-1.541, -1.847], [-1.541, -1.701], [-1.477, -1.696], [-0.988, -1.253], [-0.972, -0.997], [-0.971, 1.201], [-0.983, 1.436], [-1.325, 1.8], [-1.541, 1.825], [-1.541, 1.959], [0.491, 1.959], [0.491, 1.834], [0.079, 1.766], [-0.278, 1.403], [-0.298, 1.208], [-0.296, -0.367], [0.235, -1.554], [0.638, -1.753], [0.747, -1.765], [0.899, -1.693], [0.749, -1.553], [0.937, -0.915], [1.051, -0.898], [1.451, -1.204], [1.107, -1.894]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.879, 77.489], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.414, 0.125], [0, 0], [-0.005, -0.002], [-0.011, 0.023], [-0.262, 0.172], [-0.276, -0.11], [0.089, -0.28], [0.227, 0.06], [-0.193, 0.227], [-0.053, 0.049], [0.093, -0.012], [0.113, -0.107], [0.011, -0.465], [-0.001, -0.525], [-0.013, -0.064], [-0.188, -0.047], [-0.144, -0.023], [0, 0], [0, 0], [0, 0], [-0.072, 0.012], [-0.021, 0.215], [0, 0.078], [0.001, 0.732], [0.014, 0.085], [0.307, 0.029], [0.025, 0.002], [0, 0]], "o": [[0, 0], [0.005, 0.002], [0.011, -0.023], [0.13, -0.281], [0.246, -0.162], [0.289, 0.115], [-0.071, 0.227], [-0.28, -0.074], [0.044, -0.051], [-0.07, -0.091], [-0.155, 0.021], [-0.341, 0.322], [-0.011, 0.525], [0, 0.066], [0.04, 0.197], [0.132, 0.033], [0, 0], [0, 0], [0, 0], [0.072, -0.008], [0.214, -0.035], [0.007, -0.078], [0.001, -0.733], [0, -0.086], [-0.047, -0.296], [-0.02, -0.001], [0, 0], [0.416, 0.024]], "v": [[-0.298, -1.907], [-0.298, -1.024], [-0.285, -1.019], [-0.251, -1.089], [0.319, -1.786], [1.109, -1.871], [1.452, -1.18], [0.938, -0.891], [0.749, -1.529], [0.901, -1.671], [0.638, -1.731], [0.236, -1.531], [-0.296, -0.344], [-0.298, 1.23], [-0.277, 1.426], [0.08, 1.788], [0.492, 1.856], [0.492, 1.981], [-1.54, 1.981], [-1.54, 1.848], [-1.324, 1.823], [-0.982, 1.459], [-0.971, 1.224], [-0.971, -0.973], [-0.988, -1.231], [-1.475, -1.673], [-1.54, -1.678], [-1.54, -1.824]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.879, 77.465], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.001, 0.014], [0, 0], [0, 0], [-0.092, -0.032], [0.036, -0.119], [0.027, -0.043], [0.082, -0.116], [0.151, -0.208], [0.015, 0.021], [0.248, 0.358], [-0.121, 0.046], [-0.063, 0.015], [0.001, 0.023], [0.001, 0.015], [0, 0], [0, 0], [-0.092, -0.131], [-0.365, -0.529], [0.014, -0.019], [0.083, -0.106], [0.26, -0.324], [0.158, -0.051], [-0.031, -0.106], [0, 0], [0, 0], [0.037, 0.011], [0.089, 0.044], [-0.048, 0.138], [-0.055, 0.076], [-0.147, 0.191], [-0.059, 0.076], [-0.005, -0.007], [-0.277, -0.4], [0.107, -0.045], [0.063, -0.016], [0, 0], [0, 0], [0, 0], [0.086, 0.109], [0.045, 0.065], [0.352, 0.502], [-0.028, 0.038], [-0.247, 0.332], [-0.298, 0.07], [0, 0.023]], "o": [[0, 0], [0, 0], [0.101, 0.023], [0.117, 0.041], [-0.014, 0.049], [-0.074, 0.121], [-0.143, 0.205], [-0.019, -0.026], [-0.248, -0.358], [-0.074, -0.107], [0.056, -0.02], [0, -0.023], [-0.001, -0.021], [0, 0], [0, 0], [0.176, 0.01], [0.369, 0.527], [0.013, 0.019], [-0.079, 0.109], [-0.256, 0.327], [-0.101, 0.125], [-0.107, 0.034], [0, 0], [0, 0], [-0.042, -0.005], [-0.096, -0.028], [-0.129, -0.065], [0.031, -0.089], [0.14, -0.196], [0.058, -0.075], [0.012, 0.012], [0.277, 0.399], [0.066, 0.095], [-0.059, 0.025], [0, 0], [0, 0], [0, 0], [-0.144, -0.03], [-0.049, -0.061], [-0.35, -0.503], [-0.028, -0.041], [0.245, -0.333], [0.17, -0.228], [0, -0.027], [-0.001, -0.021]], "v": [[1.679, -1.9], [0.486, -1.9], [0.486, -1.775], [0.777, -1.706], [0.897, -1.458], [0.84, -1.317], [0.609, -0.961], [0.167, -0.346], [0.118, -0.412], [-0.627, -1.485], [-0.554, -1.725], [-0.375, -1.769], [-0.375, -1.838], [-0.379, -1.896], [-1.794, -1.896], [-1.794, -1.755], [-1.417, -1.502], [-0.315, 0.083], [-0.32, 0.166], [-0.565, 0.487], [-1.334, 1.467], [-1.716, 1.746], [-1.8, 1.9], [-0.391, 1.9], [-0.391, 1.769], [-0.509, 1.753], [-0.795, 1.661], [-0.924, 1.332], [-0.796, 1.076], [-0.359, 0.501], [-0.183, 0.276], [-0.16, 0.3], [0.672, 1.498], [0.609, 1.718], [0.422, 1.769], [0.422, 1.897], [1.831, 1.897], [1.831, 1.759], [1.493, 1.544], [1.348, 1.358], [0.297, -0.151], [0.299, -0.255], [1.032, -1.255], [1.683, -1.77], [1.683, -1.843]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [71.711, 77.549], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.144, -0.03], [0, 0], [0, 0], [0, 0], [-0.059, 0.025], [0.065, 0.095], [0.278, 0.399], [0.012, 0.012], [0.057, -0.076], [0.14, -0.195], [0.031, -0.089], [-0.129, -0.065], [-0.096, -0.028], [-0.043, -0.005], [0, 0], [0, 0], [-0.107, 0.034], [-0.1, 0.125], [-0.256, 0.327], [-0.078, 0.109], [0.012, 0.018], [0.369, 0.527], [0.175, 0.011], [0, 0], [0, 0], [0, -0.022], [0, -0.024], [0.055, -0.021], [-0.074, -0.107], [-0.248, -0.358], [-0.02, -0.026], [-0.144, 0.204], [-0.074, 0.12], [-0.014, 0.048], [0.118, 0.041], [0.1, 0.023], [0, 0], [0, 0], [-0.001, -0.021], [0, -0.028], [0.171, -0.228], [0.245, -0.333], [-0.028, -0.041], [-0.35, -0.503], [-0.049, -0.062]], "o": [[0, 0], [0, 0], [0, 0], [0.063, -0.017], [0.106, -0.045], [-0.277, -0.4], [-0.004, -0.006], [-0.06, 0.075], [-0.147, 0.19], [-0.056, 0.077], [-0.048, 0.138], [0.089, 0.044], [0.036, 0.01], [0, 0], [0, 0], [-0.031, -0.106], [0.158, -0.05], [0.261, -0.323], [0.083, -0.106], [0.014, -0.02], [-0.366, -0.53], [-0.092, -0.131], [0, 0], [0, 0], [0.001, 0.014], [0.001, 0.023], [-0.063, 0.015], [-0.122, 0.045], [0.248, 0.358], [0.014, 0.02], [0.15, -0.209], [0.081, -0.116], [0.026, -0.043], [0.036, -0.12], [-0.091, -0.032], [0, 0], [0, 0], [0.001, 0.014], [0, 0.023], [-0.299, 0.07], [-0.246, 0.332], [-0.029, 0.038], [0.351, 0.503], [0.045, 0.064], [0.086, 0.108]], "v": [[1.831, 1.759], [1.831, 1.898], [0.421, 1.898], [0.421, 1.77], [0.609, 1.718], [0.672, 1.498], [-0.161, 0.3], [-0.183, 0.277], [-0.359, 0.502], [-0.796, 1.076], [-0.924, 1.332], [-0.795, 1.661], [-0.509, 1.754], [-0.391, 1.769], [-0.391, 1.9], [-1.8, 1.9], [-1.716, 1.746], [-1.335, 1.467], [-0.565, 0.487], [-0.32, 0.167], [-0.315, 0.084], [-1.417, -1.502], [-1.794, -1.755], [-1.794, -1.895], [-0.379, -1.895], [-0.376, -1.837], [-0.375, -1.768], [-0.554, -1.724], [-0.627, -1.485], [0.118, -0.411], [0.167, -0.345], [0.609, -0.96], [0.84, -1.317], [0.897, -1.457], [0.776, -1.706], [0.486, -1.775], [0.486, -1.9], [1.679, -1.9], [1.683, -1.843], [1.683, -1.769], [1.031, -1.255], [0.299, -0.255], [0.297, -0.151], [1.348, 1.359], [1.493, 1.545]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [71.711, 77.549], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.194, 0], [0.102, -0.021], [0.126, -0.466], [0, -0.326], [0, -0.039], [0, 0], [0, 0], [0, 0], [0, 0], [0.001, -0.932], [0.007, -0.076], [0.217, -0.036], [0.071, -0.009], [0, 0], [0, 0], [0, 0], [0.087, 0.01], [0.054, 0.229], [0.001, 0.07], [0, 0.938], [-0.002, 0.034], [0, 0], [0, 0], [0, 0], [-0.07, 0.439], [-0.331, 0], [-0.023, -0.002], [-0.067, -0.121], [-0.014, -0.23], [-0.211, -0.018], [-0.012, 0], [-0.041, 0.165], [0.236, 0.142]], "o": [[-0.101, 0], [-0.489, 0.103], [-0.086, 0.318], [-0.001, 0.03], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.932], [0, 0.076], [-0.021, 0.22], [-0.071, 0.012], [0, 0], [0, 0], [0, 0], [-0.092, -0.009], [-0.283, -0.034], [-0.016, -0.069], [-0.001, -0.937], [0, -0.026], [0, 0], [0, 0], [0, 0], [0.003, -0.453], [0.059, -0.367], [0.022, 0], [0.114, 0.007], [-0.247, 0.089], [0.012, 0.191], [0.012, 0.001], [0.18, 0], [0.059, -0.235], [-0.179, -0.107]], "v": [[0.491, -2.897], [0.187, -2.864], [-0.706, -1.971], [-0.801, -0.999], [-0.803, -0.901], [-1.404, -0.901], [-1.404, -0.756], [-0.803, -0.756], [-0.803, -0.656], [-0.803, 2.141], [-0.816, 2.369], [-1.158, 2.739], [-1.372, 2.765], [-1.372, 2.897], [0.661, 2.897], [0.661, 2.764], [0.395, 2.739], [-0.107, 2.349], [-0.13, 2.138], [-0.13, -0.675], [-0.126, -0.762], [0.672, -0.762], [0.672, -0.907], [-0.132, -0.907], [-0.078, -2.244], [0.506, -2.783], [0.574, -2.78], [0.866, -2.626], [0.553, -2.193], [0.926, -1.841], [0.962, -1.84], [1.344, -2.122], [1.053, -2.745]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [57.33, 76.549], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.247, 0.089], [0.114, 0.007], [0.062, -0.391], [0.003, -0.453], [0, 0], [0, 0], [0, 0], [0, -0.026], [-0.001, -0.937], [-0.016, -0.069], [-0.283, -0.034], [-0.092, -0.009], [0, 0], [0, 0], [0, 0], [-0.072, 0.012], [-0.021, 0.22], [0, 0.076], [0, 0.932], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.03], [-0.085, 0.318], [-0.489, 0.103], [-0.271, -0.163], [0.059, -0.235], [0.193, 0.016], [0.012, 0.192]], "o": [[-0.067, -0.121], [-0.373, -0.025], [-0.071, 0.439], [0, 0], [0, 0], [0, 0], [-0.001, 0.034], [0, 0.938], [0, 0.07], [0.054, 0.229], [0.088, 0.01], [0, 0], [0, 0], [0, 0], [0.07, -0.009], [0.217, -0.036], [0.007, -0.076], [0, -0.933], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.039], [0.001, -0.327], [0.126, -0.466], [0.3, -0.062], [0.236, 0.142], [-0.043, 0.177], [-0.211, -0.018], [-0.014, -0.23]], "v": [[0.865, -2.612], [0.573, -2.766], [-0.078, -2.229], [-0.131, -0.892], [0.672, -0.892], [0.672, -0.747], [-0.127, -0.747], [-0.131, -0.66], [-0.13, 2.152], [-0.107, 2.363], [0.394, 2.754], [0.662, 2.779], [0.662, 2.911], [-1.372, 2.911], [-1.372, 2.78], [-1.158, 2.754], [-0.815, 2.384], [-0.803, 2.156], [-0.803, -0.642], [-0.803, -0.741], [-1.403, -0.741], [-1.403, -0.887], [-0.802, -0.887], [-0.802, -0.984], [-0.707, -1.956], [0.186, -2.849], [1.053, -2.73], [1.344, -2.107], [0.927, -1.827], [0.553, -2.178]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [57.33, 76.535], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.074, 0], [0.017, 0.392], [-0.305, 0.184], [-0.145, 0.071], [-0.161, 0.182], [0, -0.022], [0.004, -0.428], [0.028, -0.041], [0.259, -0.074]], "o": [[-0.339, 0], [-0.015, -0.359], [0.138, -0.084], [0.198, -0.097], [0.003, 0.035], [0.001, 0.428], [0, 0.049], [-0.144, 0.213], [-0.082, 0.024]], "v": [[-0.455, 1.699], [-1.048, 1.068], [-0.611, 0.25], [-0.176, 0.033], [0.4, -0.31], [0.405, -0.231], [0.402, 1.053], [0.358, 1.199], [-0.221, 1.664]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.187, 0], [0.307, -0.171], [0.086, -0.111], [-0.1, -0.187], [-0.139, 0], [-0.025, 0.003], [-0.041, 0.152], [0.156, 0.109], [0.057, 0.029], [-0.099, 0.04], [-0.15, 0], [-0.06, -0.007], [-0.017, -0.37], [-0.008, -0.179], [0.164, -0.079], [0.134, -0.053], [0.261, -0.105], [0.133, -0.132], [-0.034, -0.293], [-0.163, -0.136], [-0.18, -0.014], [-0.056, 0], [-0.272, 0.417], [-0.025, 0.033], [-0.002, -0.033], [-0.21, -0.059], [-0.091, 0], [-0.205, 0.184], [0.027, 0.041], [0.045, -0.024], [0.05, -0.011], [0.022, 0], [0.024, 0.124], [0, 0.055], [0.004, 0.669], [0.021, 0.172], [0.381, 0.11]], "o": [[-0.33, 0], [-0.122, 0.069], [-0.13, 0.169], [0.075, 0.138], [0.024, 0], [0.187, -0.02], [0.051, -0.191], [-0.047, -0.032], [0.068, -0.086], [0.143, -0.057], [0.059, 0], [0.369, 0.048], [0.008, 0.179], [0.009, 0.188], [-0.129, 0.062], [-0.261, 0.103], [-0.173, 0.071], [-0.223, 0.221], [0.025, 0.205], [0.142, 0.117], [0.059, 0.005], [0.457, 0], [0.014, -0.021], [0.003, 0.054], [0.019, 0.25], [0.097, 0.027], [0.248, 0], [-0.025, -0.038], [-0.05, 0.034], [-0.047, 0.024], [-0.025, 0.005], [-0.11, 0], [-0.009, -0.054], [-0.001, -0.669], [-0.001, -0.174], [-0.047, -0.393], [-0.193, -0.056]], "v": [[-0.163, -1.995], [-1.12, -1.739], [-1.443, -1.458], [-1.467, -0.906], [-1.132, -0.721], [-1.057, -0.725], [-0.701, -0.998], [-0.874, -1.496], [-1.033, -1.58], [-0.776, -1.762], [-0.335, -1.844], [-0.155, -1.833], [0.398, -1.228], [0.407, -0.689], [0.17, -0.292], [-0.227, -0.12], [-1.012, 0.183], [-1.466, 0.496], [-1.697, 1.282], [-1.43, 1.803], [-0.938, 1.983], [-0.766, 1.99], [0.351, 1.413], [0.406, 1.34], [0.413, 1.46], [0.772, 1.955], [1.053, 1.995], [1.73, 1.713], [1.655, 1.596], [1.52, 1.686], [1.372, 1.743], [1.302, 1.751], [1.096, 1.56], [1.082, 1.394], [1.079, -0.613], [1.049, -1.135], [0.408, -1.911]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [68.081, 77.522], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 4, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.002, 0.035], [0.198, -0.097], [0.138, -0.084], [-0.015, -0.359], [-0.46, 0.131], [-0.145, 0.213], [0, 0.05], [0, 0.427]], "o": [[-0.162, 0.182], [-0.145, 0.071], [-0.305, 0.184], [0.021, 0.476], [0.259, -0.074], [0.027, -0.041], [0.004, -0.427], [0, -0.022]], "v": [[0.401, -0.306], [-0.177, 0.037], [-0.611, 0.254], [-1.048, 1.072], [-0.221, 1.668], [0.358, 1.203], [0.402, 1.056], [0.405, -0.227]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.05, 0.034], [-0.025, -0.038], [0.362, 0.101], [0.019, 0.25], [0.003, 0.055], [0.014, -0.021], [0.533, 0.043], [0.142, 0.118], [0.025, 0.205], [-0.223, 0.22], [-0.173, 0.07], [-0.261, 0.103], [-0.129, 0.062], [0.009, 0.188], [0.008, 0.179], [0.369, 0.048], [0.2, -0.079], [0.068, -0.085], [-0.047, -0.032], [0.051, -0.191], [0.187, -0.02], [0.089, 0.163], [-0.13, 0.169], [-0.122, 0.069], [-0.529, -0.152], [-0.047, -0.392], [-0.001, -0.175], [-0.001, -0.669], [-0.009, -0.055], [-0.149, 0.032], [-0.047, 0.024]], "o": [[0.027, 0.041], [-0.28, 0.252], [-0.21, -0.059], [-0.002, -0.033], [-0.025, 0.034], [-0.306, 0.468], [-0.18, -0.015], [-0.163, -0.135], [-0.034, -0.293], [0.133, -0.133], [0.261, -0.105], [0.134, -0.053], [0.164, -0.08], [-0.008, -0.18], [-0.017, -0.371], [-0.21, -0.027], [-0.099, 0.039], [0.057, 0.03], [0.156, 0.108], [-0.041, 0.151], [-0.17, 0.019], [-0.1, -0.186], [0.086, -0.111], [0.483, -0.269], [0.381, 0.111], [0.021, 0.173], [0.004, 0.669], [0, 0.055], [0.028, 0.149], [0.05, -0.011], [0.045, -0.024]], "v": [[1.655, 1.6], [1.73, 1.716], [0.771, 1.959], [0.413, 1.464], [0.405, 1.343], [0.35, 1.417], [-0.939, 1.987], [-1.43, 1.806], [-1.697, 1.285], [-1.466, 0.5], [-1.013, 0.187], [-0.227, -0.116], [0.169, -0.288], [0.406, -0.685], [0.397, -1.224], [-0.156, -1.829], [-0.776, -1.758], [-1.033, -1.577], [-0.875, -1.492], [-0.702, -0.994], [-1.057, -0.722], [-1.467, -0.903], [-1.444, -1.454], [-1.121, -1.735], [0.408, -1.908], [1.048, -1.132], [1.079, -0.609], [1.082, 1.398], [1.095, 1.564], [1.372, 1.747], [1.52, 1.69]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [68.081, 77.519], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 4, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.2, 0], [0.157, 0.196], [0.044, 0.267], [-0.11, 0.578], [-0.165, 0.198], [-0.218, 0], [-0.164, -0.229], [-0.037, -0.28], [-0.021, -0.247], [0.117, -0.368], [0.136, -0.142]], "o": [[-0.224, 0], [-0.173, -0.216], [-0.096, -0.578], [0.046, -0.248], [0.164, -0.198], [0.24, 0], [0.167, 0.236], [0.032, 0.245], [-0.013, 0.384], [-0.059, 0.186], [-0.156, 0.161]], "v": [[0.027, 1.872], [-0.577, 1.576], [-0.883, 0.841], [-0.87, -0.895], [-0.573, -1.576], [0.029, -1.871], [0.671, -1.524], [0.953, -0.74], [1.018, 0], [0.865, 1.133], [0.583, 1.631]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.255, 0], [0.117, -0.019], [0.204, -0.558], [-0.22, -0.667], [-0.453, -0.164], [-0.204, 0], [-0.102, 0.014], [-0.241, 0.524], [-0.018, 0.382], [0.041, 0.221], [0.503, 0.238]], "o": [[-0.115, 0], [-0.584, 0.097], [-0.241, 0.661], [0.15, 0.458], [0.196, 0.072], [0.101, 0], [0.566, -0.078], [0.161, -0.35], [-0.026, -0.223], [-0.101, -0.532], [-0.239, -0.112]], "v": [[0.053, -2.018], [-0.297, -1.988], [-1.508, -1.018], [-1.525, 0.986], [-0.6, 1.916], [0.001, 2.018], [0.307, 1.997], [1.533, 1.107], [1.749, 0.001], [1.669, -0.669], [0.796, -1.856]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.566, 77.549], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 4, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.013, 0.384], [0.032, 0.245], [0.167, 0.235], [0.346, -0.414], [0.047, -0.248], [-0.096, -0.578], [-0.173, -0.216], [-0.33, 0.343], [-0.058, 0.186]], "o": [[-0.021, -0.247], [-0.037, -0.28], [-0.312, -0.44], [-0.165, 0.198], [-0.11, 0.578], [0.044, 0.267], [0.297, 0.371], [0.137, -0.142], [0.117, -0.368]], "v": [[1.019, 0.005], [0.954, -0.735], [0.671, -1.518], [-0.572, -1.571], [-0.869, -0.89], [-0.882, 0.846], [-0.577, 1.581], [0.583, 1.636], [0.865, 1.138]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.026, -0.223], [0.161, -0.35], [0.565, -0.078], [0.294, 0.107], [0.15, 0.458], [-0.242, 0.662], [-0.584, 0.097], [-0.348, -0.165], [-0.101, -0.533]], "o": [[-0.018, 0.382], [-0.242, 0.524], [-0.308, 0.043], [-0.452, -0.165], [-0.22, -0.667], [0.204, -0.558], [0.376, -0.062], [0.503, 0.237], [0.041, 0.22]], "v": [[1.749, 0.006], [1.535, 1.112], [0.309, 2.002], [-0.6, 1.922], [-1.525, 0.991], [-1.507, -1.013], [-0.296, -1.983], [0.796, -1.85], [1.669, -0.663]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.566, 77.544], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 4, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.2, 0], [0.158, 0.196], [0.045, 0.266], [-0.109, 0.578], [-0.141, 0.192], [-0.234, 0], [-0.165, -0.227], [-0.037, -0.282], [-0.021, -0.249], [0.103, -0.354], [0.147, -0.153]], "o": [[-0.224, 0], [-0.174, -0.215], [-0.096, -0.579], [0.043, -0.23], [0.165, -0.225], [0.236, 0], [0.172, 0.236], [0.033, 0.248], [-0.013, 0.366], [-0.059, 0.201], [-0.157, 0.163]], "v": [[0.02, 1.87], [-0.585, 1.574], [-0.891, 0.84], [-0.878, -0.897], [-0.614, -1.536], [0.021, -1.873], [0.658, -1.533], [0.946, -0.744], [1.012, 0.004], [0.873, 1.087], [0.578, 1.627]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.317, 0], [0.071, -0.006], [0.198, -0.627], [-0.183, -0.613], [-0.501, -0.169], [-0.192, 0], [-0.104, 0.015], [-0.24, 0.518], [-0.018, 0.382], [0.041, 0.223], [0.418, 0.253]], "o": [[-0.07, 0], [-0.657, 0.055], [-0.194, 0.612], [0.15, 0.505], [0.186, 0.062], [0.104, 0], [0.56, -0.081], [0.162, -0.35], [-0.026, -0.226], [-0.087, -0.468], [-0.28, -0.17]], "v": [[0.033, -2.017], [-0.178, -2.007], [-1.549, -0.933], [-1.558, 0.913], [-0.57, 1.927], [-0.003, 2.017], [0.31, 1.994], [1.525, 1.11], [1.743, 0.004], [1.661, -0.673], [0.934, -1.78]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [54.242, 77.552], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 4, "cix": 2, "bm": 0, "ix": 14, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.013, 0.367], [0.032, 0.248], [0.172, 0.236], [0.331, -0.45], [0.044, -0.23], [-0.097, -0.579], [-0.174, -0.215], [-0.332, 0.343], [-0.059, 0.201]], "o": [[-0.021, -0.249], [-0.038, -0.282], [-0.329, -0.452], [-0.141, 0.192], [-0.11, 0.577], [0.044, 0.267], [0.3, 0.372], [0.147, -0.153], [0.103, -0.354]], "v": [[1.011, 0.005], [0.946, -0.743], [0.657, -1.532], [-0.614, -1.535], [-0.878, -0.895], [-0.89, 0.842], [-0.585, 1.575], [0.577, 1.63], [0.872, 1.088]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.026, -0.226], [0.162, -0.35], [0.56, -0.08], [0.287, 0.097], [0.151, 0.504], [-0.193, 0.612], [-0.656, 0.055], [-0.342, -0.208], [-0.087, -0.468]], "o": [[-0.018, 0.382], [-0.24, 0.518], [-0.296, 0.043], [-0.501, -0.169], [-0.182, -0.614], [0.199, -0.626], [0.394, -0.033], [0.418, 0.253], [0.041, 0.223]], "v": [[1.742, 0.005], [1.524, 1.111], [0.309, 1.995], [-0.57, 1.928], [-1.558, 0.915], [-1.549, -0.932], [-0.178, -2.006], [0.933, -1.779], [1.66, -0.671]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [54.242, 77.55], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 15", "np": 4, "cix": 2, "bm": 0, "ix": 15, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.058, 0.572], [0, 0], [0.046, -0.575], [0, 0], [-0.008, 0.044], [-0.069, 0.263], [-0.293, 0.075], [-0.201, 0], [-0.131, 0], [-0.011, -0.007], [0, 0], [0.001, -1.488], [0.008, -0.134], [0.31, -0.051], [0.128, -0.015], [0, 0], [0, 0], [0, 0], [0.042, 0.005], [0.112, 0.021], [0.033, 0.192], [0, 0.106], [0, 1.573], [-0.003, 0.042], [-0.293, -0.036], [-0.09, -0.364], [-0.034, -0.189], [-0.014, -0.111], [0, 0], [-0.022, 0], [-0.019, 0.002]], "o": [[0, 0], [0.057, 0.575], [0, 0], [0.006, -0.05], [0.05, -0.268], [0.075, -0.288], [0.198, -0.05], [0.131, 0], [0.004, 0], [0, 0], [0, 1.487], [0, 0.134], [-0.02, 0.316], [-0.126, 0.021], [0, 0], [0, 0], [0, 0], [-0.047, -0.004], [-0.113, -0.015], [-0.194, -0.037], [-0.017, -0.104], [-0.002, -1.573], [0, -0.022], [0.3, 0.016], [0.367, 0.045], [0.046, 0.186], [0.018, 0.106], [0, 0], [0.022, 0], [0.012, 0], [-0.038, -0.577]], "v": [[2.36, -2.921], [-2.36, -2.921], [-2.327, -1.199], [-2.138, -1.199], [-2.12, -1.339], [-1.965, -2.142], [-1.397, -2.686], [-0.798, -2.746], [-0.406, -2.742], [-0.387, -2.729], [-0.387, -2.636], [-0.388, 1.826], [-0.401, 2.228], [-0.851, 2.732], [-1.233, 2.778], [-1.233, 2.921], [1.234, 2.921], [1.234, 2.774], [1.102, 2.762], [0.763, 2.715], [0.423, 2.364], [0.393, 2.047], [0.391, -2.673], [0.396, -2.759], [1.286, -2.708], [1.986, -2.084], [2.097, -1.519], [2.139, -1.194], [2.217, -1.194], [2.283, -1.194], [2.326, -1.198]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [61.343, 76.527], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 16", "np": 2, "cix": 2, "bm": 0, "ix": 16, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.038, -0.577], [0.012, 0], [0.053, 0], [0.018, 0.106], [0.046, 0.186], [0.367, 0.045], [0.3, 0.016], [0, -0.022], [-0.002, -1.573], [-0.017, -0.104], [-0.194, -0.037], [-0.114, -0.015], [-0.047, -0.004], [0, 0], [0, 0], [0, 0], [-0.126, 0.021], [-0.02, 0.317], [0, 0.134], [0, 1.487], [0, 0], [0.004, 0], [0.327, -0.083], [0.075, -0.288], [0.05, -0.268], [0.006, -0.05], [0, 0], [0.057, 0.575]], "o": [[-0.058, 0.572], [-0.019, 0.002], [-0.046, 0], [-0.014, -0.111], [-0.034, -0.189], [-0.09, -0.364], [-0.293, -0.036], [-0.003, 0.042], [0, 1.573], [0, 0.106], [0.033, 0.192], [0.112, 0.021], [0.042, 0.005], [0, 0], [0, 0], [0, 0], [0.128, -0.015], [0.31, -0.051], [0.008, -0.134], [0.001, -1.488], [0, 0], [-0.011, -0.007], [-0.331, 0], [-0.293, 0.075], [-0.069, 0.263], [-0.008, 0.044], [0, 0], [0.046, -0.575], [0, 0]], "v": [[2.36, -2.921], [2.326, -1.198], [2.283, -1.194], [2.139, -1.194], [2.097, -1.519], [1.986, -2.084], [1.286, -2.708], [0.396, -2.759], [0.391, -2.673], [0.393, 2.047], [0.423, 2.364], [0.763, 2.715], [1.102, 2.762], [1.234, 2.774], [1.234, 2.921], [-1.233, 2.921], [-1.233, 2.778], [-0.851, 2.732], [-0.401, 2.228], [-0.388, 1.826], [-0.387, -2.636], [-0.387, -2.729], [-0.406, -2.742], [-1.397, -2.686], [-1.965, -2.142], [-2.12, -1.339], [-2.138, -1.199], [-2.327, -1.199], [-2.36, -2.921]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [61.343, 76.527], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 17", "np": 2, "cix": 2, "bm": 0, "ix": 17, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.053, 0], [0.073, -0.002], [0.086, -0.006], [0.18, -0.015], [0.021, -0.007], [0.026, -0.006], [0.004, 0], [0.015, 0], [0.003, 0], [0.014, 0], [0, 0], [0.039, 0], [0.021, -0.004], [0.309, -0.151], [-0.045, 0], [-0.042, 0.042], [-0.036, 0], [-0.036, 0.022], [-0.012, 0], [-0.051, 0], [-0.017, 0.001], [-0.246, 0], [0, 0], [-0.243, -0.008], [-0.175, -0.044], [-0.082, -0.011], [-0.021, 0], [-0.046, 0], [0.015, -0.006], [-0.155, -0.037], [-0.004, 0], [-0.013, 0], [-0.006, -0.002], [-0.076, -0.022], [-0.061, -0.07], [-0.009, -0.004], [-0.216, -0.09], [-0.035, -0.021], [-0.14, -0.084], [-0.059, -0.04], [-0.092, -0.069], [-0.039, -0.014], [-0.132, -0.023], [-0.153, -0.099], [-0.103, -0.058], [-0.081, -0.049], [-0.121, -0.089], [-0.037, -0.004], [-0.043, -0.015], [-0.087, 0], [-0.025, 0.01], [-0.012, -0.004], [-0.05, -0.012], [-0.037, -0.015], [-0.02, -0.005], [-0.092, -0.019], [-0.043, -0.011], [-0.038, -0.007], [-0.118, -0.026], [-0.121, -0.022], [-0.14, 0], [0, 0], [-0.106, 0.015], [-0.004, 0], [-0.015, -0.002], [-0.042, 0], [-0.004, 0], [-0.067, 0.007], [-0.164, 0.026], [-0.07, 0.029], [-0.016, 0], [-0.044, 0.02], [-0.004, 0], [0, 0], [-0.014, 0], [-0.097, 0.068], [-0.006, 0], [-0.003, 0], [-0.004, 0], [-0.072, 0.021], [-0.063, 0.022], [-0.135, 0.047], [-0.113, 0.03], [-0.003, 0.066], [0.016, 0], [0.009, -0.002], [0.059, -0.014], [0.255, -0.026], [0.204, 0], [0.01, 0], [0.024, 0], [0.137, -0.007], [0.061, -0.004], [0.003, 0], [0.022, 0.005], [0.028, 0.003], [0.031, 0], [0.058, 0], [0.04, 0.009], [0.228, 0], [0.026, 0], [0.001, 0], [0.023, 0.012], [0.006, 0.001], [0.081, 0.015], [0.055, 0.016], [0.08, 0.025], [0.038, 0.013], [0.083, 0.029], [0.033, 0.031], [0.101, 0.048], [0.199, 0.091], [0.118, 0.061], [0.068, 0.042], [0, 0], [0.076, 0.062], [0.097, 0.1], [0.045, 0.029], [0.052, 0.043], [0.109, 0.015], [0.006, 0.009], [0.128, 0.06], [0.223, 0.088], [0.043, 0.006], [0.019, 0.005], [0.135, 0.03], [0.019, 0.007], [0.087, 0], [0.029, -0.008], [0.003, 0], [0.019, 0.007], [0.028, 0.005], [0.105, 0.008], [0.26, 0.012]], "o": [[-0.073, 0], [-0.086, 0.001], [-0.179, 0.014], [-0.022, 0.002], [-0.025, 0.007], [-0.004, 0.001], [-0.015, 0], [-0.002, 0], [-0.014, 0.001], [0, 0], [-0.04, 0], [-0.022, 0], [-0.33, 0.063], [0.018, 0.032], [0.055, 0], [0.036, 0.012], [0.037, 0], [0.01, -0.006], [0.051, 0], [0.016, 0], [0.245, -0.011], [0, 0], [0.243, 0.001], [0.179, 0.006], [0.08, 0.019], [0.019, 0.002], [0.034, 0], [-0.033, 0.011], [0.158, 0.039], [0.005, 0.001], [0.014, 0], [0.005, 0], [0.074, 0.028], [0.078, 0.022], [0.006, 0.007], [0.216, 0.091], [0.039, 0.016], [0.14, 0.083], [0.061, 0.038], [0.096, 0.064], [0.035, 0.025], [0.121, 0.043], [0.113, 0.153], [0.1, 0.064], [0.083, 0.047], [0.125, 0.075], [0.026, 0.019], [0.059, 0.006], [0.083, 0.028], [0.025, 0], [0.01, 0.026], [0.049, 0.014], [0.038, 0.009], [0.019, 0.008], [0.09, 0.023], [0.044, 0.009], [0.039, 0.009], [0.119, 0.023], [0.119, 0.025], [0.136, 0.024], [0, 0], [0.105, -0.001], [0.005, -0.001], [0.015, 0], [0.042, 0.005], [0.004, 0], [0.067, -0.004], [0.166, -0.019], [0.064, -0.009], [0.025, 0.014], [0.018, 0], [0.004, -0.002], [0, 0], [0.014, 0.001], [0.115, 0], [0.004, -0.003], [0.003, 0], [0.004, 0], [0.074, -0.014], [0.064, -0.019], [0.136, -0.047], [0.111, -0.039], [0.072, -0.02], [-0.019, 0], [-0.01, 0], [-0.06, 0.014], [-0.248, 0.061], [-0.202, 0.02], [-0.011, 0], [-0.025, 0], [-0.137, 0], [-0.061, 0.003], [-0.002, 0.001], [-0.021, 0], [-0.028, -0.007], [-0.032, -0.005], [-0.058, 0], [-0.041, 0], [-0.223, -0.044], [-0.027, 0], [-0.001, 0.001], [-0.024, 0], [-0.007, -0.004], [-0.081, -0.018], [-0.057, -0.011], [-0.081, -0.022], [-0.039, -0.012], [-0.083, -0.031], [-0.051, -0.018], [-0.088, -0.083], [-0.197, -0.095], [-0.121, -0.056], [-0.07, -0.036], [0, 0], [-0.087, -0.044], [-0.104, -0.086], [-0.036, -0.038], [-0.058, -0.036], [-0.081, -0.066], [-0.012, -0.002], [-0.089, -0.125], [-0.217, -0.101], [-0.035, -0.014], [-0.01, -0.007], [-0.134, -0.031], [-0.019, -0.005], [-0.081, -0.03], [-0.027, 0], [-0.003, 0.001], [-0.019, 0], [-0.027, -0.009], [-0.104, -0.017], [-0.26, -0.018], [-0.054, -0.002]], "v": [[-4.244, -1.65], [-4.463, -1.647], [-4.721, -1.633], [-5.258, -1.59], [-5.323, -1.58], [-5.397, -1.551], [-5.41, -1.55], [-5.456, -1.554], [-5.463, -1.554], [-5.505, -1.539], [-5.515, -1.539], [-5.634, -1.543], [-5.699, -1.538], [-6.677, -1.297], [-6.574, -1.25], [-6.415, -1.314], [-6.307, -1.294], [-6.197, -1.324], [-6.161, -1.333], [-6.007, -1.331], [-5.957, -1.332], [-5.221, -1.367], [-5.215, -1.367], [-4.487, -1.329], [-3.956, -1.265], [-3.713, -1.221], [-3.654, -1.218], [-3.538, -1.22], [-3.598, -1.199], [-3.13, -1.085], [-3.117, -1.084], [-3.077, -1.087], [-3.06, -1.084], [-2.84, -0.996], [-2.612, -0.916], [-2.585, -0.903], [-1.939, -0.63], [-1.817, -0.597], [-1.401, -0.338], [-1.209, -0.237], [-0.93, -0.03], [-0.824, 0.042], [-0.511, 0.244], [-0.07, 0.567], [0.246, 0.731], [0.486, 0.885], [0.881, 1.075], [0.99, 1.091], [1.117, 1.176], [1.367, 1.254], [1.443, 1.24], [1.469, 1.305], [1.62, 1.333], [1.733, 1.372], [1.788, 1.407], [2.061, 1.47], [2.194, 1.489], [2.304, 1.53], [2.661, 1.592], [3.022, 1.62], [3.439, 1.65], [3.49, 1.65], [3.807, 1.606], [3.821, 1.605], [3.866, 1.61], [3.992, 1.626], [4.003, 1.626], [4.203, 1.599], [4.701, 1.542], [4.895, 1.464], [4.949, 1.486], [5.032, 1.457], [5.045, 1.452], [5.046, 1.452], [5.087, 1.454], [5.405, 1.354], [5.422, 1.35], [5.431, 1.35], [5.442, 1.35], [5.663, 1.308], [5.849, 1.23], [6.259, 1.097], [6.589, 0.971], [6.677, 0.848], [6.625, 0.847], [6.598, 0.849], [6.42, 0.896], [5.669, 1.051], [5.067, 1.121], [5.036, 1.121], [4.961, 1.12], [4.55, 1.135], [4.367, 1.156], [4.36, 1.157], [4.295, 1.144], [4.212, 1.121], [4.118, 1.115], [3.943, 1.12], [3.822, 1.109], [3.148, 1.016], [3.069, 1.017], [3.067, 1.018], [2.995, 0.989], [2.982, 0.961], [2.738, 0.911], [2.566, 0.883], [2.328, 0.802], [2.208, 0.779], [1.961, 0.68], [1.806, 0.642], [1.502, 0.498], [0.905, 0.226], [0.546, 0.052], [0.341, -0.071], [0.339, -0.07], [0.089, -0.222], [-0.254, -0.429], [-0.386, -0.521], [-0.56, -0.627], [-0.827, -0.777], [-0.86, -0.794], [-1.219, -1.024], [-1.884, -1.296], [-2.004, -1.314], [-2.048, -1.347], [-2.451, -1.435], [-2.507, -1.455], [-2.756, -1.518], [-2.84, -1.507], [-2.849, -1.506], [-2.909, -1.524], [-2.989, -1.557], [-3.302, -1.601], [-4.083, -1.647]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.234, 79.787], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 18", "np": 2, "cix": 2, "bm": 0, "ix": 18, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.071, -0.036], [-0.12, -0.056], [-0.197, -0.095], [-0.088, -0.083], [-0.05, -0.017], [-0.084, -0.031], [-0.038, -0.012], [-0.08, -0.022], [-0.057, -0.011], [-0.081, -0.018], [-0.008, -0.004], [-0.024, 0.002], [-0.248, -0.049], [-0.129, -0.018], [-0.029, -0.007], [-0.024, 0.002], [-0.062, 0.002], [-0.162, -0.004], [-0.211, 0.021], [-0.249, 0.061], [-0.06, 0.014], [-0.03, 0], [0.073, -0.02], [0.111, -0.039], [0.136, -0.047], [0.064, -0.018], [0.074, -0.013], [0.009, -0.007], [0.131, 0.008], [0.005, -0.002], [0.052, 0.031], [0.064, -0.009], [0.166, -0.018], [0.068, -0.004], [0.046, 0.006], [0.019, -0.003], [0.106, -0.001], [0.153, 0.028], [0.119, 0.025], [0.12, 0.023], [0.038, 0.009], [0.044, 0.009], [0.091, 0.023], [0.02, 0.009], [0.039, 0.009], [0.049, 0.015], [0.01, 0.026], [0.106, 0.035], [0.059, 0.005], [0.026, 0.019], [0.125, 0.075], [0.083, 0.047], [0.099, 0.064], [0.113, 0.154], [0.121, 0.043], [0.034, 0.025], [0.096, 0.064], [0.062, 0.038], [0.14, 0.083], [0.039, 0.017], [0.215, 0.091], [0.007, 0.007], [0.078, 0.022], [0.074, 0.028], [0.022, 0.005], [0.158, 0.039], [-0.033, 0.011], [0.051, 0.006], [0.08, 0.019], [0.18, 0.006], [0.243, 0], [0.248, -0.011], [0.068, 0], [0.011, -0.006], [0.074, 0.023], [0.039, 0.07], [-0.329, 0.063], [-0.065, 0.001], [-0.014, 0.001], [-0.021, 0.004], [-0.024, 0.007], [-0.022, 0.001], [-0.179, 0.014], [-0.086, 0.001], [-0.126, -0.006], [-0.26, -0.018], [-0.104, -0.017], [-0.027, -0.009], [-0.02, 0.005], [-0.108, -0.039], [-0.019, -0.005], [-0.133, -0.031], [-0.01, -0.007], [-0.036, -0.014], [-0.216, -0.101], [-0.088, -0.125], [-0.012, -0.001], [-0.081, -0.066], [-0.057, -0.036], [-0.037, -0.038], [-0.104, -0.086], [-0.088, -0.043]], "o": [[0.068, 0.042], [0.118, 0.061], [0.199, 0.091], [0.1, 0.048], [0.033, 0.031], [0.084, 0.03], [0.037, 0.013], [0.08, 0.025], [0.056, 0.016], [0.082, 0.015], [0.006, 0.002], [0.023, 0.012], [0.256, -0.013], [0.13, 0.026], [0.028, 0.004], [0.023, 0.006], [0.061, -0.004], [0.162, -0.008], [0.215, 0.005], [0.256, -0.026], [0.059, -0.014], [0.024, -0.005], [-0.002, 0.066], [-0.113, 0.03], [-0.135, 0.047], [-0.064, 0.022], [-0.072, 0.021], [-0.012, 0.003], [-0.107, 0.076], [-0.004, 0], [-0.084, 0.038], [-0.071, 0.029], [-0.165, 0.026], [-0.067, 0.008], [-0.045, 0.002], [-0.019, -0.002], [-0.106, 0.014], [-0.156, 0.001], [-0.12, -0.022], [-0.118, -0.026], [-0.037, -0.008], [-0.043, -0.011], [-0.092, -0.019], [-0.02, -0.005], [-0.037, -0.015], [-0.049, -0.012], [-0.012, -0.003], [-0.118, 0.044], [-0.044, -0.015], [-0.037, -0.004], [-0.12, -0.089], [-0.081, -0.049], [-0.104, -0.058], [-0.153, -0.099], [-0.133, -0.022], [-0.039, -0.014], [-0.093, -0.07], [-0.06, -0.04], [-0.139, -0.085], [-0.035, -0.021], [-0.215, -0.089], [-0.01, -0.004], [-0.061, -0.071], [-0.075, -0.022], [-0.02, -0.008], [-0.155, -0.037], [0.014, -0.006], [-0.073, 0], [-0.081, -0.011], [-0.175, -0.044], [-0.242, -0.008], [-0.248, -0.001], [-0.068, 0.004], [-0.012, 0], [-0.071, 0.042], [-0.076, 0.076], [0.309, -0.151], [0.063, -0.012], [0.014, 0], [0.022, -0.002], [0.025, -0.006], [0.021, -0.007], [0.179, -0.015], [0.085, -0.006], [0.127, -0.003], [0.26, 0.012], [0.105, 0.008], [0.028, 0.005], [0.022, 0.009], [0.12, -0.032], [0.019, 0.007], [0.134, 0.03], [0.02, 0.005], [0.043, 0.006], [0.223, 0.088], [0.128, 0.059], [0.007, 0.009], [0.109, 0.016], [0.051, 0.043], [0.046, 0.029], [0.096, 0.1], [0.076, 0.062], [0, 0]], "v": [[0.342, -0.07], [0.546, 0.053], [0.904, 0.227], [1.503, 0.499], [1.807, 0.643], [1.96, 0.68], [2.208, 0.78], [2.327, 0.803], [2.566, 0.884], [2.738, 0.912], [2.982, 0.962], [2.995, 0.99], [3.07, 1.018], [3.822, 1.11], [4.212, 1.121], [4.296, 1.145], [4.367, 1.157], [4.551, 1.136], [5.036, 1.122], [5.669, 1.052], [6.421, 0.897], [6.598, 0.85], [6.676, 0.849], [6.589, 0.972], [6.258, 1.098], [5.85, 1.231], [5.663, 1.308], [5.441, 1.35], [5.404, 1.355], [5.046, 1.453], [5.031, 1.458], [4.895, 1.465], [4.701, 1.543], [4.204, 1.599], [4.003, 1.627], [3.865, 1.61], [3.807, 1.607], [3.49, 1.651], [3.021, 1.62], [2.661, 1.593], [2.303, 1.531], [2.195, 1.49], [2.061, 1.471], [1.788, 1.408], [1.732, 1.373], [1.62, 1.334], [1.469, 1.305], [1.443, 1.241], [1.117, 1.177], [0.99, 1.092], [0.88, 1.076], [0.486, 0.886], [0.247, 0.732], [-0.07, 0.568], [-0.51, 0.244], [-0.824, 0.043], [-0.93, -0.029], [-1.208, -0.236], [-1.402, -0.337], [-1.818, -0.596], [-1.939, -0.63], [-2.585, -0.902], [-2.613, -0.915], [-2.84, -0.995], [-3.061, -1.083], [-3.13, -1.084], [-3.598, -1.198], [-3.538, -1.219], [-3.714, -1.22], [-3.956, -1.264], [-4.488, -1.328], [-5.215, -1.365], [-5.958, -1.331], [-6.161, -1.332], [-6.197, -1.323], [-6.415, -1.313], [-6.676, -1.296], [-5.699, -1.537], [-5.505, -1.538], [-5.462, -1.553], [-5.396, -1.55], [-5.324, -1.579], [-5.258, -1.589], [-4.721, -1.632], [-4.464, -1.646], [-4.083, -1.646], [-3.303, -1.6], [-2.99, -1.556], [-2.908, -1.523], [-2.84, -1.506], [-2.507, -1.454], [-2.451, -1.434], [-2.049, -1.346], [-2.003, -1.313], [-1.883, -1.295], [-1.219, -1.023], [-0.861, -0.793], [-0.827, -0.777], [-0.559, -0.626], [-0.387, -0.52], [-0.253, -0.428], [0.089, -0.221], [0.339, -0.07]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.234, 79.786], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 19", "np": 2, "cix": 2, "bm": 0, "ix": 19, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-0.098, -0.276], [-0.002, -0.008], [-0.57, -1.672], [-0.055, 0], [-0.034, 0.004], [-0.444, 1.421], [-0.478, -1.398], [-0.017, 0], [-0.03, 0.095], [0, 0], [-0.286, 0.893], [-0.341, 0.102], [-0.002, 0.015], [0, 0.037], [0, 0], [0, 0], [-0.092, -0.016], [0.02, -0.296], [0.057, -0.184], [0.336, -1.042], [0.017, -0.045], [0.006, 0.018], [0.391, 1.201], [0.015, 0.104], [-0.175, 0.036], [-0.113, 0.017], [0, 0], [0, 0], [0, 0], [-0.084, -0.179], [-0.017, -0.048], [-0.066, -0.204], [0.014, -0.044], [0.32, -1.001], [0.015, -0.041], [0.008, 0.024], [0.352, 1.068], [0.057, 0.235], [-0.174, 0.041], [-0.06, 0.011]], "o": [[0, 0], [0, 0], [0.329, 0.032], [0.003, 0.007], [0.571, 1.671], [0.031, 0.09], [0.019, 0], [0.437, -1.399], [0.486, 1.423], [0.025, 0.001], [0.07, 0], [0, 0], [0.283, -0.893], [0.1, -0.313], [0.014, -0.005], [0.004, -0.039], [0, 0], [0, 0], [0.099, 0.012], [0.293, 0.05], [-0.013, 0.192], [-0.329, 1.043], [-0.009, 0.031], [-0.013, -0.034], [-0.393, -1.201], [-0.033, -0.1], [-0.024, -0.178], [0.111, -0.022], [0, 0], [0, 0], [0, 0], [0.217, 0.03], [0.021, 0.046], [0.071, 0.202], [0.014, 0.045], [-0.317, 1.002], [-0.008, 0.025], [-0.014, -0.043], [-0.354, -1.067], [-0.076, -0.23], [-0.042, -0.175], [0.06, -0.014], [0, 0]], "v": [[-2.016, -2.917], [-3.79, -2.917], [-3.79, -2.77], [-3.221, -2.233], [-3.214, -2.211], [-1.504, 2.803], [-1.41, 2.917], [-1.331, 2.911], [-0.012, -1.309], [1.432, 2.914], [1.494, 2.915], [1.6, 2.803], [2.296, 0.598], [3.145, -2.083], [3.753, -2.758], [3.786, -2.8], [3.787, -2.917], [2.223, -2.917], [2.223, -2.774], [2.507, -2.738], [2.915, -2.243], [2.807, -1.671], [1.803, 1.454], [1.764, 1.561], [1.736, 1.492], [0.559, -2.111], [0.488, -2.421], [0.705, -2.725], [1.042, -2.775], [1.042, -2.917], [-0.81, -2.917], [-0.81, -2.77], [-0.395, -2.414], [-0.338, -2.273], [-0.129, -1.665], [-0.128, -1.52], [-1.085, 1.484], [-1.118, 1.575], [-1.148, 1.484], [-2.208, -1.718], [-2.411, -2.415], [-2.199, -2.74], [-2.016, -2.773]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.182, 76.583], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 20", "np": 2, "cix": 2, "bm": 0, "ix": 20, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.004, -0.039], [0.014, -0.004], [0.101, -0.313], [0.283, -0.893], [0, 0], [0.13, 0.006], [0.486, 1.423], [0.437, -1.399], [0.042, 0.122], [0.57, 1.671], [0.003, 0.007], [0.33, 0.032], [0, 0], [0, 0], [0, 0], [0.06, -0.014], [-0.042, -0.175], [-0.076, -0.23], [-0.354, -1.067], [-0.014, -0.043], [-0.008, 0.025], [-0.317, 1.002], [0.014, 0.045], [0.07, 0.202], [0.022, 0.046], [0.218, 0.03], [0, 0], [0, 0], [0, 0], [0.111, -0.022], [-0.024, -0.178], [-0.033, -0.1], [-0.393, -1.2], [-0.014, -0.034], [-0.01, 0.031], [-0.33, 1.043], [-0.013, 0.192], [0.293, 0.05], [0.099, 0.012], [0, 0]], "o": [[0, 0.037], [-0.002, 0.015], [-0.34, 0.103], [-0.285, 0.893], [0, 0], [-0.037, 0.117], [-0.478, -1.398], [-0.444, 1.421], [-0.131, 0.015], [-0.57, -1.672], [-0.002, -0.008], [-0.098, -0.276], [0, 0], [0, 0], [0, 0], [-0.06, 0.011], [-0.174, 0.041], [0.056, 0.235], [0.351, 1.068], [0.008, 0.024], [0.015, -0.041], [0.32, -1.001], [0.014, -0.044], [-0.066, -0.204], [-0.018, -0.048], [-0.084, -0.179], [0, 0], [0, 0], [0, 0], [-0.112, 0.017], [-0.174, 0.036], [0.014, 0.104], [0.391, 1.201], [0.006, 0.019], [0.017, -0.045], [0.336, -1.042], [0.057, -0.184], [0.02, -0.296], [-0.093, -0.016], [0, 0], [0, 0]], "v": [[3.788, -2.921], [3.787, -2.805], [3.753, -2.764], [3.145, -2.088], [2.296, 0.593], [1.6, 2.798], [1.433, 2.909], [-0.011, -1.314], [-1.33, 2.906], [-1.503, 2.799], [-3.213, -2.216], [-3.22, -2.238], [-3.791, -2.775], [-3.791, -2.921], [-2.016, -2.921], [-2.016, -2.778], [-2.198, -2.744], [-2.411, -2.42], [-2.208, -1.723], [-1.147, 1.479], [-1.117, 1.57], [-1.084, 1.48], [-0.127, -1.524], [-0.128, -1.67], [-0.337, -2.278], [-0.395, -2.419], [-0.81, -2.775], [-0.81, -2.921], [1.042, -2.921], [1.042, -2.78], [0.705, -2.73], [0.488, -2.425], [0.56, -2.116], [1.737, 1.486], [1.765, 1.556], [1.804, 1.449], [2.807, -1.676], [2.915, -2.248], [2.508, -2.743], [2.223, -2.779], [2.223, -2.921]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.182, 76.588], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 21", "np": 2, "cix": 2, "bm": 0, "ix": 21, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.027, 0], [0.028, -0.033], [0.022, -0.062], [0.047, -0.147], [-0.168, -0.232], [-0.21, -0.131], [-0.124, -0.088], [-0.017, -0.273], [0.044, -0.236], [0.302, -0.445], [0.296, -0.133], [0.127, 0], [0.12, 0.081], [0.07, 0.03], [0.037, 0.006], [0.009, 0], [0.031, -0.048], [-0.033, -0.047], [-0.056, -0.04], [-0.175, -0.071], [-0.208, 0], [-0.235, 0.157], [-0.12, 0.084], [-0.161, 0.088], [-0.075, 0], [-0.107, -0.083], [-0.063, -0.154], [-0.013, -0.297], [-0.009, -0.345], [-0.08, -0.101], [-0.048, 0], [-0.036, 0.059], [-0.012, 0.048], [0.051, 0.473], [0.061, 0.578], [-0.089, 0.466], [-0.039, 0.147], [-0.008, -0.001], [-0.021, -0.055], [-0.265, -0.288], [-0.131, -0.12], [-0.077, 0], [-0.025, 0.007], [-0.01, 0], [-0.022, -0.025], [-0.272, -0.287], [-0.601, -0.297], [-0.013, -0.012], [0.027, -0.003], [0.053, 0], [0, 0], [0.429, 0.205], [0.036, 0.038], [0.126, 0.264], [-0.192, 0.089], [0.004, 0.012], [0.022, 0.006], [0.009, 0], [0.019, -0.006], [-0.047, -0.196], [-0.056, -0.088], [0.02, -0.371], [0.035, -0.22], [0.101, -0.104], [0.063, -0.054], [0.15, -0.12], [-0.025, -0.077], [-0.06, 0], [-0.019, 0.007], [-0.101, 0.075], [-0.223, 0.179], [-0.094, -0.239], [-0.066, -0.124], [-0.032, 0], [0, 0], [0.034, 0.108], [0.047, 0.115], [0.079, 0.193], [-0.015, 0], [-0.008, -0.002], [-0.304, -0.077], [-0.155, -0.062], [0.035, -0.061], [0.038, -0.06], [0.085, -0.149], [-0.001, -0.066], [-0.04, -0.025], [-0.003, 0], [-0.02, 0.026], [-0.11, 0.145], [-0.123, 0.156], [-0.066, -0.089], [-0.154, -0.208], [-0.068, -0.072], [-0.02, 0], [-0.019, 0.017], [0.01, 0.027], [0.034, 0.052], [0.137, 0.185], [-0.021, 0.224], [-0.013, 0.135], [-0.128, 0.212], [-0.056, 0], [-0.091, -0.047], [-0.002, -0.001], [-0.134, 0], [-0.157, 0.145], [0.213, 0.146], [0.353, 0.247], [0.006, 0.024], [0.033, 0.125], [0.015, 0.037], [0.009, -0.001], [0.03, -0.132], [0.044, 0], [0.033, 0.026], [0.043, 0.046], [0.083, 0.086], [0.006, -0.004], [-0.041, -0.14], [0.079, -0.171], [0.15, -0.133], [0.043, 0], [0.031, 0.014], [0.413, 0.411], [0.339, 0.354], [-0.009, 0.022], [0.044, 0.06], [0.054, 0.06], [0.076, 0.118], [0.076, 0.35], [0.031, 0.107], [0.54, 0.108], [0.459, 0.076], [0.203, 0.117], [-0.097, 0.251], [-0.016, 0.092], [0.066, 0.029]], "o": [[-0.037, 0], [-0.043, 0.052], [-0.055, 0.144], [-0.086, 0.277], [0.147, 0.204], [0.129, 0.08], [0.218, 0.157], [0.014, 0.242], [-0.099, 0.525], [-0.179, 0.263], [-0.128, 0.057], [-0.121, 0], [-0.062, -0.042], [-0.034, -0.015], [-0.01, -0.002], [-0.054, 0], [-0.036, 0.054], [0.04, 0.058], [0.156, 0.112], [0.21, 0.086], [0.248, 0], [0.122, -0.082], [0.151, -0.105], [0.078, -0.042], [0.117, 0], [0.134, 0.105], [0.115, 0.278], [0.016, 0.344], [0.003, 0.132], [0.038, 0.049], [0.054, 0], [0.027, -0.042], [0.118, -0.461], [-0.061, -0.579], [-0.05, -0.471], [0.028, -0.149], [0.009, 0], [0.023, 0.055], [0.134, 0.363], [0.119, 0.13], [0.057, 0.053], [0.025, 0], [0.012, -0.003], [0.029, 0], [0.269, 0.29], [0.457, 0.483], [0.01, 0.005], [-0.035, 0.008], [-0.053, 0.007], [0, 0], [-0.482, 0], [-0.047, -0.022], [-0.199, -0.215], [-0.09, -0.191], [0.018, -0.009], [-0.007, -0.023], [-0.008, -0.003], [-0.02, 0], [-0.193, 0.059], [0.024, 0.101], [0.195, 0.308], [-0.012, 0.221], [-0.021, 0.135], [-0.058, 0.061], [-0.147, 0.123], [-0.052, 0.042], [0.016, 0.048], [0.019, 0], [0.119, -0.047], [0.226, -0.166], [0.149, 0.197], [0.053, 0.13], [0.014, 0.028], [0, 0], [0.112, -0.001], [-0.036, -0.118], [-0.076, -0.185], [0.027, 0], [0.01, 0], [0.306, 0.064], [0.16, 0.041], [0.065, 0.026], [-0.035, 0.061], [-0.091, 0.146], [-0.033, 0.059], [0.001, 0.043], [0.003, 0.001], [0.027, 0], [0.112, -0.144], [0.115, -0.151], [0.081, 0.091], [0.154, 0.208], [0.059, 0.078], [0.019, 0.019], [0.023, 0], [0.019, -0.017], [-0.02, -0.059], [-0.128, -0.192], [-0.139, -0.186], [0.012, -0.136], [0.024, -0.242], [0.072, -0.121], [0.041, 0], [0.002, 0.001], [0.116, 0.059], [0.222, 0], [0.191, -0.176], [-0.356, -0.243], [-0.02, -0.014], [-0.034, -0.124], [-0.01, -0.039], [-0.009, 0], [-0.03, 0.132], [-0.016, 0.068], [-0.026, 0], [-0.049, -0.039], [-0.083, -0.086], [-0.006, 0.004], [0.037, 0.141], [0.051, 0.177], [-0.082, 0.179], [-0.038, 0.035], [-0.024, 0], [-0.532, -0.242], [-0.347, -0.346], [-0.019, -0.02], [0.031, -0.074], [-0.048, -0.066], [-0.095, -0.106], [-0.19, -0.297], [-0.023, -0.108], [-0.153, -0.52], [-0.456, -0.091], [-0.229, -0.038], [-0.255, -0.147], [0.034, -0.086], [0.011, -0.063], [-0.027, -0.013]], "v": [[-3.984, -4.875], [-4.084, -4.828], [-4.193, -4.657], [-4.337, -4.218], [-4.204, -3.456], [-3.652, -2.975], [-3.269, -2.728], [-2.913, -2.087], [-2.965, -1.37], [-3.545, 0.094], [-4.234, 0.718], [-4.618, 0.812], [-4.98, 0.698], [-5.184, 0.597], [-5.293, 0.569], [-5.321, 0.566], [-5.447, 0.644], [-5.439, 0.8], [-5.301, 0.96], [-4.819, 1.273], [-4.189, 1.408], [-3.463, 1.182], [-3.107, 0.92], [-2.646, 0.615], [-2.416, 0.553], [-2.079, 0.683], [-1.793, 1.078], [-1.626, 1.946], [-1.597, 2.979], [-1.467, 3.328], [-1.333, 3.401], [-1.189, 3.312], [-1.131, 3.173], [-1.021, 1.772], [-1.217, 0.038], [-1.181, -1.37], [-1.066, -1.811], [-1.04, -1.809], [-0.969, -1.645], [-0.407, -0.646], [-0.034, -0.268], [0.173, -0.182], [0.248, -0.193], [0.281, -0.198], [0.35, -0.157], [1.158, 0.712], [2.716, 1.922], [2.746, 1.949], [2.658, 1.968], [2.498, 1.982], [2.492, 1.982], [1.135, 1.634], [1.008, 1.538], [0.516, 0.82], [0.656, 0.453], [0.697, 0.403], [0.645, 0.347], [0.618, 0.343], [0.557, 0.354], [0.287, 0.804], [0.416, 1.091], [0.738, 2.094], [0.646, 2.754], [0.494, 3.126], [0.322, 3.308], [-0.129, 3.668], [-0.209, 3.833], [-0.081, 3.915], [-0.023, 3.904], [0.32, 3.734], [0.983, 3.206], [1.328, 3.875], [1.51, 4.257], [1.604, 4.311], [1.604, 4.311], [1.731, 4.142], [1.591, 3.799], [1.36, 3.235], [1.421, 3.234], [1.447, 3.236], [2.367, 3.434], [2.832, 3.612], [2.876, 3.747], [2.77, 3.93], [2.495, 4.367], [2.442, 4.566], [2.528, 4.688], [2.538, 4.69], [2.637, 4.636], [2.962, 4.197], [3.318, 3.743], [3.544, 4.002], [3.995, 4.632], [4.197, 4.848], [4.255, 4.875], [4.32, 4.846], [4.339, 4.754], [4.255, 4.586], [3.863, 4.016], [3.701, 3.4], [3.748, 2.994], [3.951, 2.308], [4.111, 2.136], [4.298, 2.21], [4.304, 2.213], [4.688, 2.301], [5.291, 2.079], [5.253, 1.571], [4.191, 0.834], [4.147, 0.77], [4.051, 0.396], [4.007, 0.284], [3.979, 0.285], [3.889, 0.681], [3.798, 0.785], [3.708, 0.747], [3.576, 0.611], [3.328, 0.352], [3.309, 0.364], [3.421, 0.787], [3.387, 1.303], [3.069, 1.788], [2.952, 1.843], [2.869, 1.822], [1.447, 0.847], [0.426, -0.208], [0.397, -0.294], [0.378, -0.495], [0.22, -0.68], [-0.064, -0.995], [-0.488, -1.955], [-0.577, -2.276], [-1.598, -3.247], [-2.972, -3.488], [-3.626, -3.697], [-3.896, -4.434], [-3.827, -4.705], [-3.903, -4.855]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.86, 75.128], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 22", "np": 2, "cix": 2, "bm": 0, "ix": 22, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.149, 0.197], [0.226, -0.166], [0.119, -0.047], [0.02, 0.064], [-0.053, 0.042], [-0.147, 0.124], [-0.058, 0.06], [-0.021, 0.136], [-0.012, 0.221], [0.194, 0.307], [0.024, 0.1], [-0.192, 0.059], [-0.026, -0.008], [-0.007, -0.023], [0.019, -0.009], [-0.09, -0.191], [-0.199, -0.215], [-0.047, -0.022], [-0.485, 0.001], [-0.053, 0.006], [-0.035, 0.007], [0.01, 0.005], [0.457, 0.484], [0.269, 0.291], [0.05, -0.014], [0.077, 0.07], [0.12, 0.13], [0.135, 0.363], [0.023, 0.055], [0.008, 0.001], [0.029, -0.149], [-0.049, -0.47], [-0.061, -0.578], [0.117, -0.46], [0.027, -0.042], [0.082, 0.103], [0.004, 0.132], [0.015, 0.344], [0.114, 0.277], [0.134, 0.104], [0.197, -0.107], [0.151, -0.106], [0.121, -0.082], [0.461, 0.188], [0.156, 0.112], [0.04, 0.057], [-0.035, 0.054], [-0.069, -0.011], [-0.034, -0.016], [-0.062, -0.042], [-0.25, 0.112], [-0.179, 0.263], [-0.099, 0.525], [0.014, 0.243], [0.217, 0.156], [0.129, 0.081], [0.146, 0.203], [-0.087, 0.276], [-0.054, 0.144], [-0.043, 0.052], [-0.066, -0.029], [0.011, -0.063], [0.033, -0.086], [-0.255, -0.147], [-0.229, -0.038], [-0.456, -0.091], [-0.152, -0.521], [-0.024, -0.109], [-0.191, -0.297], [-0.095, -0.105], [-0.048, -0.065], [0.031, -0.073], [-0.02, -0.02], [-0.346, -0.345], [-0.532, -0.242], [-0.062, 0.055], [-0.083, 0.178], [0.051, 0.177], [0.036, 0.141], [-0.006, 0.004], [-0.083, -0.087], [-0.049, -0.04], [-0.025, 0.109], [-0.03, 0.132], [-0.008, 0], [-0.011, -0.038], [-0.034, -0.124], [-0.02, -0.013], [-0.356, -0.243], [0.191, -0.176], [0.306, 0.157], [0.002, 0.001], [0.125, -0.208], [0.024, -0.242], [0.012, -0.136], [-0.138, -0.186], [-0.128, -0.192], [-0.021, -0.059], [0.019, -0.018], [0.041, 0.043], [0.059, 0.079], [0.153, 0.208], [0.081, 0.092], [0.114, -0.15], [0.112, -0.143], [0.019, 0.012], [0.001, 0.043], [-0.033, 0.058], [-0.092, 0.146], [-0.035, 0.061], [0.065, 0.026], [0.16, 0.04], [0.307, 0.064], [0.041, 0], [-0.076, -0.186], [-0.036, -0.118], [0.111, -0.001], [0.015, 0.028], [0.052, 0.131]], "o": [[-0.224, 0.18], [-0.102, 0.074], [-0.08, 0.031], [-0.025, -0.076], [0.15, -0.12], [0.064, -0.053], [0.1, -0.104], [0.034, -0.219], [0.02, -0.371], [-0.056, -0.088], [-0.048, -0.196], [0.029, -0.009], [0.022, 0.007], [0.003, 0.011], [-0.191, 0.09], [0.126, 0.264], [0.036, 0.038], [0.432, 0.206], [0.053, -0.001], [0.027, -0.003], [-0.013, -0.013], [-0.602, -0.299], [-0.272, -0.287], [-0.029, -0.032], [-0.105, 0.029], [-0.13, -0.119], [-0.265, -0.289], [-0.021, -0.056], [-0.009, 0], [-0.039, 0.147], [-0.088, 0.467], [0.062, 0.579], [0.05, 0.474], [-0.012, 0.049], [-0.07, 0.112], [-0.08, -0.102], [-0.008, -0.344], [-0.014, -0.296], [-0.063, -0.155], [-0.176, -0.138], [-0.162, 0.087], [-0.12, 0.084], [-0.434, 0.291], [-0.174, -0.071], [-0.056, -0.04], [-0.034, -0.047], [0.037, -0.056], [0.037, 0.007], [0.069, 0.03], [0.245, 0.166], [0.297, -0.134], [0.301, -0.445], [0.044, -0.237], [-0.017, -0.273], [-0.124, -0.088], [-0.211, -0.131], [-0.168, -0.233], [0.047, -0.147], [0.023, -0.062], [0.048, -0.058], [0.066, 0.029], [-0.017, 0.092], [-0.098, 0.252], [0.203, 0.116], [0.459, 0.076], [0.539, 0.108], [0.032, 0.106], [0.075, 0.349], [0.075, 0.118], [0.054, 0.06], [0.044, 0.061], [-0.009, 0.022], [0.338, 0.354], [0.414, 0.411], [0.085, 0.039], [0.15, -0.134], [0.078, -0.171], [-0.041, -0.14], [0.006, -0.005], [0.083, 0.087], [0.043, 0.046], [0.088, 0.069], [0.031, -0.132], [0.009, 0], [0.016, 0.037], [0.033, 0.125], [0.006, 0.024], [0.353, 0.247], [0.213, 0.147], [-0.253, 0.233], [-0.003, -0.001], [-0.218, -0.112], [-0.128, 0.211], [-0.014, 0.136], [-0.02, 0.223], [0.138, 0.185], [0.034, 0.053], [0.009, 0.026], [-0.033, 0.03], [-0.068, -0.071], [-0.154, -0.207], [-0.066, -0.088], [-0.124, 0.157], [-0.11, 0.145], [-0.024, 0.03], [-0.041, -0.024], [-0.001, -0.067], [0.085, -0.15], [0.037, -0.06], [0.035, -0.061], [-0.155, -0.062], [-0.303, -0.078], [-0.021, -0.004], [0.079, 0.193], [0.047, 0.114], [0.033, 0.107], [-0.033, 0], [-0.066, -0.125], [-0.095, -0.238]], "v": [[0.984, 3.203], [0.32, 3.732], [-0.023, 3.901], [-0.208, 3.83], [-0.129, 3.666], [0.32, 3.305], [0.494, 3.124], [0.646, 2.751], [0.738, 2.091], [0.417, 1.089], [0.287, 0.802], [0.557, 0.351], [0.645, 0.344], [0.697, 0.401], [0.656, 0.45], [0.516, 0.818], [1.008, 1.535], [1.134, 1.631], [2.499, 1.98], [2.658, 1.966], [2.746, 1.947], [2.716, 1.92], [1.158, 0.709], [0.35, -0.16], [0.249, -0.195], [-0.035, -0.271], [-0.408, -0.648], [-0.971, -1.647], [-1.04, -1.812], [-1.066, -1.814], [-1.182, -1.373], [-1.219, 0.035], [-1.021, 1.769], [-1.131, 3.17], [-1.189, 3.309], [-1.467, 3.326], [-1.598, 2.976], [-1.626, 1.943], [-1.792, 1.076], [-2.079, 0.681], [-2.646, 0.613], [-3.107, 0.918], [-3.463, 1.179], [-4.82, 1.271], [-5.301, 0.957], [-5.439, 0.798], [-5.448, 0.641], [-5.292, 0.566], [-5.184, 0.595], [-4.98, 0.695], [-4.236, 0.716], [-3.544, 0.092], [-2.965, -1.372], [-2.913, -2.09], [-3.269, -2.73], [-3.652, -2.978], [-4.204, -3.458], [-4.337, -4.22], [-4.193, -4.66], [-4.083, -4.83], [-3.902, -4.858], [-3.827, -4.707], [-3.896, -4.437], [-3.626, -3.699], [-2.973, -3.49], [-1.598, -3.25], [-0.577, -2.278], [-0.488, -1.957], [-0.064, -0.998], [0.221, -0.683], [0.378, -0.498], [0.397, -0.297], [0.426, -0.211], [1.447, 0.844], [2.869, 1.819], [3.07, 1.786], [3.387, 1.301], [3.421, 0.785], [3.309, 0.362], [3.327, 0.349], [3.576, 0.609], [3.708, 0.745], [3.889, 0.678], [3.979, 0.282], [4.005, 0.281], [4.051, 0.393], [4.147, 0.767], [4.191, 0.831], [5.253, 1.568], [5.292, 2.076], [4.305, 2.21], [4.298, 2.207], [3.951, 2.306], [3.749, 2.991], [3.701, 3.398], [3.863, 4.013], [4.255, 4.583], [4.339, 4.752], [4.32, 4.844], [4.197, 4.845], [3.995, 4.629], [3.544, 3.999], [3.318, 3.74], [2.962, 4.194], [2.637, 4.633], [2.529, 4.685], [2.443, 4.564], [2.495, 4.365], [2.77, 3.927], [2.876, 3.744], [2.833, 3.61], [2.365, 3.432], [1.447, 3.233], [1.361, 3.233], [1.591, 3.797], [1.731, 4.14], [1.604, 4.308], [1.509, 4.255], [1.328, 3.872]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.861, 75.13], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 23", "np": 2, "cix": 2, "bm": 0, "ix": 23, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.104, 0], [0, 0], [0, -1.104], [0, 0], [1.104, 0], [0, 0], [0, 1.104], [0, 0]], "o": [[0, 0], [1.104, 0], [0, 0], [0, 1.104], [0, 0], [-1.104, 0], [0, 0], [0, -1.104]], "v": [[-24.184, -9.814], [24.184, -9.814], [26.184, -7.814], [26.184, 7.815], [24.184, 9.814], [-24.184, 9.814], [-26.184, 7.815], [-26.184, -7.814]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [51.55, 75.732], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 24", "np": 2, "cix": 2, "bm": 0, "ix": 24, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[14.499, 83.371], [-14.499, 83.371], [-14.499, -83.371], [14.499, -83.371]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.819999964097, 0.438999998803, 0.294000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [54.622, 83.621], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 25", "np": 2, "cix": 2, "bm": 0, "ix": 25, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[14.499, 83.371], [-14.499, 83.371], [-14.499, -83.371], [14.499, -83.371]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [163.367, 83.621], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 26", "np": 2, "cix": 2, "bm": 0, "ix": 26, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.523, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 5.522]], "v": [[-44.372, 83.371], [54.372, 83.371], [54.372, -83.371], [-54.372, -83.371], [-54.372, 73.371]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952999997606, 0.588000009574, 0.294000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [54.622, 83.621], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 27", "np": 2, "cix": 2, "bm": 0, "ix": 27, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.522, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 5.522]], "v": [[44.373, 83.371], [-54.373, 83.371], [-54.373, -83.371], [54.373, -83.371], [54.373, 73.371]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.819999964097, 0.438999998803, 0.294000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [163.367, 83.621], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 28", "np": 2, "cix": 2, "bm": 0, "ix": 28, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "board-left", "parent": 1, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [300, 400, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "w": 600, "h": 800, "ip": 8, "op": 228, "st": 8, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Shape Layer 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [5]}, {"t": 19, "s": [10]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [296, 516, 0], "ix": 2}, "a": {"a": 0, "k": [-4, 190, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 4, "s": [94, 94, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 11, "s": [85, 85, 100]}, {"t": 19, "s": [94, 94, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [214.647, 17.17], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-5.677, 181.415], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 4, "op": 224, "st": 4, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "board-back", "parent": 1, "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-10.5, 0, 0], "ix": 2}, "a": {"a": 0, "k": [300, 400, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "w": 600, "h": 800, "ip": 4, "op": 224, "st": 4, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 2, "ty": 3, "nm": "rebord-face/gift.ai: Path 1 [1.0]", "cl": "ai 0", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.135, "y": 0}, "t": 0, "s": [370.604, 341.4, 0], "to": [0, -23.494, 0], "ti": [35.778, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 1.728, "s": [305.822, 276.618, 0], "to": [-35.778, 0, 0], "ti": [0, -35.778, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4.579, "s": [241.041, 341.4, 0], "to": [0, 21.661, 0], "ti": [-22.295, -22.744, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.197, "y": 0}, "t": 13.333, "s": [267.084, 397.262, 0], "to": [0.291, 0.296, 0], "ti": [-0.352, -0.254, 0]}, {"t": 20, "s": [268, 394, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 3, "nm": "rebord-face/gift.ai: Path 1 [1.3]", "cl": "ai 3", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.135, "y": 0}, "t": 0, "s": [252.781, 340.5, 0], "to": [0, -23.494, 0], "ti": [35.778, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 1.728, "s": [188, 275.719, 0], "to": [-35.778, 0, 0], "ti": [0, -35.778, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4.579, "s": [123.219, 340.5, 0], "to": [0, 21.661, 0], "ti": [-22.295, -22.744, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.197, "y": 0}, "t": 13.333, "s": [149.261, 396.362, 0], "to": [0.291, 0.296, 0], "ti": [-0.352, -0.254, 0]}, {"t": 20, "s": [150.178, 393.1, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "rebord-face/gift.ai", "cl": "ai", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [223.799, 368.065, 0], "ix": 2}, "a": {"a": 0, "k": [72.799, 26.315, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.0]", "np": 3, "mn": "ADBE Layer Control", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.1]", "np": 3, "mn": "ADBE Layer Control", "ix": 2, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 4, "ix": 1}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.2]", "np": 3, "mn": "ADBE Layer Control", "ix": 3, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 4, "ix": 1}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.3]", "np": 3, "mn": "ADBE Layer Control", "ix": 4, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[117.335, 52.38], [145.349, 0.25], [36.278, 0.25], [0.25, 52.141]], "c": true}, "ix": 2, "x": "var $bm_rt;\nvar nullLayerNames = [\n        'rebord-face/gift.ai: Path 1 [1.0]',\n        'rebord-face/gift.ai: Path 1 [1.1]',\n        'rebord-face/gift.ai: Path 1 [1.2]',\n        'rebord-face/gift.ai: Path 1 [1.3]'\n    ];\nvar origPath = thisProperty;\nvar origPoints = origPath.points();\nvar origInTang = origPath.inTangents();\nvar origOutTang = origPath.outTangents();\nvar getNullLayers = [];\nfor (var i = 0, il = nullLayerNames.length; i < il; i++) {\n    try {\n        getNullLayers.push(effect(nullLayerNames[i])('ADBE Layer Control-0001'));\n    } catch (err) {\n        getNullLayers.push(null);\n    }\n}\nfor (var i = 0, il = getNullLayers.length; i < il; i++) {\n    if (getNullLayers[i] != null && getNullLayers[i].index != thisLayer.index) {\n        origPoints[i] = fromCompToSurface(getNullLayers[i].toComp(getNullLayers[i].anchorPoint));\n    }\n}\n$bm_rt = createPath(origPoints, origInTang, origOutTang, origPath.isClosed());"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.972999961703, 0.722000002394, 0.419999994016, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 220, "st": 0, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "reboard-cote/gift.ai: Path 1 [1.0]", "cl": "ai 0", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.376, "y": 0}, "t": 0, "s": [228.585, 343.439, 0], "to": [0, -32.231, 0], "ti": [-33.745, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3.275, "s": [289.685, 282.338, 0], "to": [33.745, 0, 0], "ti": [0, -33.745, 0]}, {"i": {"x": 0.045, "y": 1}, "o": {"x": 0.167, "y": 0.397}, "t": 5.434, "s": [350.785, 343.439, 0], "to": [0, 21.024, 0], "ti": [12.703, -14.391, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0, "y": 0}, "t": 13.333, "s": [325.017, 400.1, 0], "to": [-0.233, 0.729, 0], "ti": [0.725, -0.493, 0]}, {"t": 16, "s": [324, 394, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "reboard-cote/gift.ai: Path 1 [1.3]", "cl": "ai 3", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.376, "y": 0}, "t": 0, "s": [344.079, 342.312, 0], "to": [0, -32.231, 0], "ti": [-33.745, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3.275, "s": [405.179, 281.212, 0], "to": [33.745, 0, 0], "ti": [0, -33.745, 0]}, {"i": {"x": 0.045, "y": 1}, "o": {"x": 0.167, "y": 0.397}, "t": 5.434, "s": [466.279, 342.312, 0], "to": [0, 21.024, 0], "ti": [12.703, -14.391, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0, "y": 0}, "t": 13.333, "s": [440.511, 398.974, 0], "to": [-0.233, 0.729, 0], "ti": [0.725, -0.493, 0]}, {"t": 16, "s": [439.494, 394, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "reboard-cote/gift.ai", "cl": "ai", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [368.549, 368.315, 0], "ix": 2}, "a": {"a": 0, "k": [72.799, 26.315, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.0]", "np": 3, "mn": "ADBE Layer Control", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}]}, {"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.1]", "np": 3, "mn": "ADBE Layer Control", "ix": 2, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}, {"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.2]", "np": 3, "mn": "ADBE Layer Control", "ix": 3, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}, {"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.3]", "np": 3, "mn": "ADBE Layer Control", "ix": 4, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[28.264, 52.38], [0.25, 0.25], [109.321, 0.25], [145.349, 52.141]], "c": true}, "ix": 2, "x": "var $bm_rt;\nvar nullLayerNames = [\n        'reboard-cote/gift.ai: Path 1 [1.0]',\n        'reboard-cote/gift.ai: Path 1 [1.1]',\n        'reboard-cote/gift.ai: Path 1 [1.2]',\n        'reboard-cote/gift.ai: Path 1 [1.3]'\n    ];\nvar origPath = thisProperty;\nvar origPoints = origPath.points();\nvar origInTang = origPath.inTangents();\nvar origOutTang = origPath.outTangents();\nvar getNullLayers = [];\nfor (var i = 0, il = nullLayerNames.length; i < il; i++) {\n    try {\n        getNullLayers.push(effect(nullLayerNames[i])('ADBE Layer Control-0001'));\n    } catch (err) {\n        getNullLayers.push(null);\n    }\n}\nfor (var i = 0, il = getNullLayers.length; i < il; i++) {\n    if (getNullLayers[i] != null && getNullLayers[i].index != thisLayer.index) {\n        origPoints[i] = fromCompToSurface(getNullLayers[i].toComp(getNullLayers[i].anchorPoint));\n    }\n}\n$bm_rt = createPath(origPoints, origInTang, origOutTang, origPath.isClosed());"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952999997606, 0.588000009574, 0.294000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 220, "st": 0, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "reboard-cote/gift.ai: Path 1 [1.0]", "cl": "ai 0", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.376, "y": 0}, "t": 0, "s": [228.585, 343.439, 0], "to": [0, -32.231, 0], "ti": [-33.745, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3.275, "s": [289.685, 282.338, 0], "to": [33.745, 0, 0], "ti": [0, -33.745, 0]}, {"i": {"x": 0.045, "y": 1}, "o": {"x": 0.167, "y": 0.397}, "t": 5.434, "s": [350.785, 343.439, 0], "to": [0, 21.024, 0], "ti": [12.703, -14.391, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0, "y": 0}, "t": 13.333, "s": [325.017, 400.1, 0], "to": [-0.233, 0.729, 0], "ti": [0.725, -0.493, 0]}, {"t": 16, "s": [324, 394, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "reboard-cote/gift.ai: Path 1 [1.3]", "cl": "ai 3", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.376, "y": 0}, "t": 0, "s": [344.079, 342.312, 0], "to": [0, -32.231, 0], "ti": [-33.745, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3.275, "s": [405.179, 281.212, 0], "to": [33.745, 0, 0], "ti": [0, -33.745, 0]}, {"i": {"x": 0.045, "y": 1}, "o": {"x": 0.167, "y": 0.397}, "t": 5.434, "s": [466.279, 342.312, 0], "to": [0, 21.024, 0], "ti": [12.703, -14.391, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0, "y": 0}, "t": 13.333, "s": [440.511, 398.974, 0], "to": [-0.233, 0.729, 0], "ti": [0.725, -0.493, 0]}, {"t": 16, "s": [439.494, 394, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "reboard-cote/gift.ai", "cl": "ai", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [368.549, 368.315, 0], "ix": 2}, "a": {"a": 0, "k": [72.799, 26.315, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.0]", "np": 3, "mn": "ADBE Layer Control", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}]}, {"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.1]", "np": 3, "mn": "ADBE Layer Control", "ix": 2, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}, {"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.2]", "np": 3, "mn": "ADBE Layer Control", "ix": 3, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}, {"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.3]", "np": 3, "mn": "ADBE Layer Control", "ix": 4, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 5, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [0.992156863213, 0.784313738346, 0.529411792755, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[28.264, 52.38], [0.25, 0.25], [109.321, 0.25], [145.349, 52.141]], "c": true}, "ix": 2, "x": "var $bm_rt;\nvar nullLayerNames = [\n        'reboard-cote/gift.ai: Path 1 [1.0]',\n        'reboard-cote/gift.ai: Path 1 [1.1]',\n        'reboard-cote/gift.ai: Path 1 [1.2]',\n        'reboard-cote/gift.ai: Path 1 [1.3]'\n    ];\nvar origPath = thisProperty;\nvar origPoints = origPath.points();\nvar origInTang = origPath.inTangents();\nvar origOutTang = origPath.outTangents();\nvar getNullLayers = [];\nfor (var i = 0, il = nullLayerNames.length; i < il; i++) {\n    try {\n        getNullLayers.push(effect(nullLayerNames[i])('ADBE Layer Control-0001'));\n    } catch (err) {\n        getNullLayers.push(null);\n    }\n}\nfor (var i = 0, il = getNullLayers.length; i < il; i++) {\n    if (getNullLayers[i] != null && getNullLayers[i].index != thisLayer.index) {\n        origPoints[i] = fromCompToSurface(getNullLayers[i].toComp(getNullLayers[i].anchorPoint));\n    }\n}\n$bm_rt = createPath(origPoints, origInTang, origOutTang, origPath.isClosed());"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952999997606, 0.588000009574, 0.294000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 220, "st": 0, "bm": 0}]}, {"id": "comp_4", "layers": [{"ddd": 0, "ind": 2, "ty": 3, "nm": "rebord-face/gift.ai: Path 1 [1.0]", "cl": "ai 0", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.135, "y": 0}, "t": 0, "s": [370.604, 341.4, 0], "to": [0, -23.494, 0], "ti": [35.778, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 1.728, "s": [305.822, 276.618, 0], "to": [-35.778, 0, 0], "ti": [0, -35.778, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4.579, "s": [241.041, 341.4, 0], "to": [0, 21.661, 0], "ti": [-22.295, -22.744, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.197, "y": 0}, "t": 13.333, "s": [267.084, 397.262, 0], "to": [0.291, 0.296, 0], "ti": [-0.352, -0.254, 0]}, {"t": 20, "s": [268, 394, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 3, "nm": "rebord-face/gift.ai: Path 1 [1.3]", "cl": "ai 3", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.135, "y": 0}, "t": 0, "s": [252.781, 340.5, 0], "to": [0, -23.494, 0], "ti": [35.778, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 1.728, "s": [188, 275.719, 0], "to": [-35.778, 0, 0], "ti": [0, -35.778, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4.579, "s": [123.219, 340.5, 0], "to": [0, 21.661, 0], "ti": [-22.295, -22.744, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.197, "y": 0}, "t": 13.333, "s": [149.261, 396.362, 0], "to": [0.291, 0.296, 0], "ti": [-0.352, -0.254, 0]}, {"t": 20, "s": [150.178, 393.1, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "rebord-face/gift.ai", "cl": "ai", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [223.799, 368.065, 0], "ix": 2}, "a": {"a": 0, "k": [72.799, 26.315, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [0.993257403374, 0.78356564045, 0.531280219555, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.0]", "np": 3, "mn": "ADBE Layer Control", "ix": 2, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.1]", "np": 3, "mn": "ADBE Layer Control", "ix": 3, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 4, "ix": 1}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.2]", "np": 3, "mn": "ADBE Layer Control", "ix": 4, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 4, "ix": 1}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.3]", "np": 3, "mn": "ADBE Layer Control", "ix": 5, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[117.335, 52.38], [145.349, 0.25], [36.278, 0.25], [0.25, 52.141]], "c": true}, "ix": 2, "x": "var $bm_rt;\nvar nullLayerNames = [\n        'rebord-face/gift.ai: Path 1 [1.0]',\n        'rebord-face/gift.ai: Path 1 [1.1]',\n        'rebord-face/gift.ai: Path 1 [1.2]',\n        'rebord-face/gift.ai: Path 1 [1.3]'\n    ];\nvar origPath = thisProperty;\nvar origPoints = origPath.points();\nvar origInTang = origPath.inTangents();\nvar origOutTang = origPath.outTangents();\nvar getNullLayers = [];\nfor (var i = 0, il = nullLayerNames.length; i < il; i++) {\n    try {\n        getNullLayers.push(effect(nullLayerNames[i])('ADBE Layer Control-0001'));\n    } catch (err) {\n        getNullLayers.push(null);\n    }\n}\nfor (var i = 0, il = getNullLayers.length; i < il; i++) {\n    if (getNullLayers[i] != null && getNullLayers[i].index != thisLayer.index) {\n        origPoints[i] = fromCompToSurface(getNullLayers[i].toComp(getNullLayers[i].anchorPoint));\n    }\n}\n$bm_rt = createPath(origPoints, origInTang, origOutTang, origPath.isClosed());"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.972999961703, 0.722000002394, 0.419999994016, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 220, "st": 0, "bm": 0}]}, {"id": "comp_5", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "flash 11", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [0]}, {"t": 121, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [396.561, 270.797, 0], "ix": 2}, "a": {"a": 0, "k": [15.723, 15.724, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 97, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 107, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 117, "s": [100, 100, 100]}, {"t": 122, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 97, "op": 122, "st": 69, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "flash 10", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 72, "s": [0]}, {"t": 96, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [310.811, 332.547, 0], "ix": 2}, "a": {"a": 0, "k": [15.723, 15.724, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 72, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 82, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 92, "s": [100, 100, 100]}, {"t": 97, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 72, "op": 97, "st": 44, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "flash 9", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [0]}, {"t": 71, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [222.811, 259.547, 0], "ix": 2}, "a": {"a": 0, "k": [15.723, 15.724, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 47, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 57, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 67, "s": [100, 100, 100]}, {"t": 72, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 47, "op": 72, "st": 19, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "flash 5", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [0]}, {"t": 46, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [373.061, 314.797, 0], "ix": 2}, "a": {"a": 0, "k": [15.723, 15.724, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 22, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 32, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 42, "s": [100, 100, 100]}, {"t": 47, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 22, "op": 47, "st": -6, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "flash 8", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 92, "s": [0]}, {"t": 116, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [237.098, 304.165, 0], "ix": 2}, "a": {"a": 0, "k": [8.849, 8.848, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 92, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 102, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 112, "s": [100, 100, 100]}, {"t": 117, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 92, "op": 117, "st": 65, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "flash 7", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [0]}, {"t": 100, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [221.348, 300.665, 0], "ix": 2}, "a": {"a": 0, "k": [8.849, 8.848, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 76, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 86, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 96, "s": [100, 100, 100]}, {"t": 101, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 76, "op": 101, "st": 49, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "flash 6", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 51, "s": [0]}, {"t": 75, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [326.348, 312.165, 0], "ix": 2}, "a": {"a": 0, "k": [8.849, 8.848, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 51, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 61, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 71, "s": [100, 100, 100]}, {"t": 76, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 51, "op": 76, "st": 24, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 2, "nm": "flash 2", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [0]}, {"t": 50, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [276.348, 207.165, 0], "ix": 2}, "a": {"a": 0, "k": [8.849, 8.848, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 26, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 36, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 46, "s": [100, 100, 100]}, {"t": 51, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 26, "op": 51, "st": -1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 2, "nm": "walk", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.904]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0.605]}, {"i": {"x": [0.833], "y": [1.364]}, "o": {"x": [0.167], "y": [0.636]}, "t": 1, "s": [0.886]}, {"i": {"x": [0.833], "y": [0.768]}, "o": {"x": [0.167], "y": [0.068]}, "t": 2, "s": [0.928]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.13]}, "t": 3, "s": [0.701]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.174]}, "t": 4, "s": [0.294]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.227]}, "t": 5, "s": [-0.08]}, {"i": {"x": [0.833], "y": [0.908]}, "o": {"x": [0.167], "y": [0.196]}, "t": 6, "s": [-0.297]}, {"i": {"x": [0.833], "y": [1.638]}, "o": {"x": [0.167], "y": [0.877]}, "t": 7, "s": [-0.458]}, {"i": {"x": [0.833], "y": [0.772]}, "o": {"x": [0.167], "y": [0.074]}, "t": 8, "s": [-0.475]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.131]}, "t": 9, "s": [-0.328]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.174]}, "t": 10, "s": [-0.073]}, {"i": {"x": [0.833], "y": [1.052]}, "o": {"x": [0.167], "y": [0.413]}, "t": 11, "s": [0.162]}, {"i": {"x": [0.833], "y": [0.764]}, "o": {"x": [0.167], "y": [0.032]}, "t": 12, "s": [0.221]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.129]}, "t": 13, "s": [0.125]}, {"i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.164]}, "t": 14, "s": [-0.052]}, {"i": {"x": [0.833], "y": [0.931]}, "o": {"x": [0.167], "y": [0.214]}, "t": 15, "s": [-0.233]}, {"i": {"x": [0.833], "y": [0.007]}, "o": {"x": [0.167], "y": [-0.394]}, "t": 16, "s": [-0.349]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.091]}, "t": 17, "s": [-0.329]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.14]}, "t": 18, "s": [-0.108]}, {"i": {"x": [0.833], "y": [0.876]}, "o": {"x": [0.167], "y": [0.182]}, "t": 19, "s": [0.218]}, {"i": {"x": [0.833], "y": [0.933]}, "o": {"x": [0.167], "y": [0.253]}, "t": 20, "s": [0.494]}, {"i": {"x": [0.833], "y": [0.472]}, "o": {"x": [0.167], "y": [-0.341]}, "t": 21, "s": [0.629]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.099]}, "t": 22, "s": [0.603]}, {"i": {"x": [0.833], "y": [0.909]}, "o": {"x": [0.167], "y": [0.191]}, "t": 23, "s": [0.461]}, {"i": {"x": [0.833], "y": [1.047]}, "o": {"x": [0.167], "y": [1.024]}, "t": 24, "s": [0.351]}, {"i": {"x": [0.833], "y": [1.016]}, "o": {"x": [0.167], "y": [0.03]}, "t": 25, "s": [0.342]}, {"i": {"x": [0.833], "y": [0.497]}, "o": {"x": [0.167], "y": [0.013]}, "t": 26, "s": [0.357]}, {"i": {"x": [0.833], "y": [0.747]}, "o": {"x": [0.167], "y": [0.1]}, "t": 27, "s": [0.339]}, {"i": {"x": [0.833], "y": [0.794]}, "o": {"x": [0.167], "y": [0.124]}, "t": 28, "s": [0.247]}, {"i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.14]}, "t": 29, "s": [0.062]}, {"i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.175]}, "t": 30, "s": [-0.212]}, {"i": {"x": [0.833], "y": [0.95]}, "o": {"x": [0.167], "y": [0.26]}, "t": 31, "s": [-0.461]}, {"i": {"x": [0.833], "y": [0.617]}, "o": {"x": [0.167], "y": [-0.124]}, "t": 32, "s": [-0.579]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.107]}, "t": 33, "s": [-0.532]}, {"i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.165]}, "t": 34, "s": [-0.362]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.211]}, "t": 35, "s": [-0.188]}, {"i": {"x": [0.833], "y": [0.907]}, "o": {"x": [0.167], "y": [0.177]}, "t": 36, "s": [-0.075]}, {"i": {"x": [0.833], "y": [1.659]}, "o": {"x": [0.167], "y": [0.802]}, "t": 37, "s": [0.027]}, {"i": {"x": [0.833], "y": [0.762]}, "o": {"x": [0.167], "y": [0.074]}, "t": 38, "s": [0.039]}, {"i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.128]}, "t": 39, "s": [-0.066]}, {"i": {"x": [0.833], "y": [0.875]}, "o": {"x": [0.167], "y": [0.163]}, "t": 40, "s": [-0.261]}, {"i": {"x": [0.833], "y": [0.96]}, "o": {"x": [0.167], "y": [0.252]}, "t": 41, "s": [-0.465]}, {"i": {"x": [0.833], "y": [0.592]}, "o": {"x": [0.167], "y": [-0.079]}, "t": 42, "s": [-0.566]}, {"i": {"x": [0.833], "y": [0.79]}, "o": {"x": [0.167], "y": [0.105]}, "t": 43, "s": [-0.514]}, {"i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.138]}, "t": 44, "s": [-0.311]}, {"i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.163]}, "t": 45, "s": [-0.003]}, {"i": {"x": [0.833], "y": [0.944]}, "o": {"x": [0.167], "y": [0.215]}, "t": 46, "s": [0.319]}, {"i": {"x": [0.833], "y": [0.596]}, "o": {"x": [0.167], "y": [-0.174]}, "t": 47, "s": [0.524]}, {"i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.105]}, "t": 48, "s": [0.457]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.163]}, "t": 49, "s": [0.203]}, {"i": {"x": [0.833], "y": [0.926]}, "o": {"x": [0.167], "y": [0.225]}, "t": 50, "s": [-0.064]}, {"i": {"x": [0.833], "y": [-0.045]}, "o": {"x": [0.167], "y": [-0.67]}, "t": 51, "s": [-0.221]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.091]}, "t": 52, "s": [-0.203]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.151]}, "t": 53, "s": [-0.003]}, {"i": {"x": [0.833], "y": [0.962]}, "o": {"x": [0.167], "y": [0.293]}, "t": 54, "s": [0.246]}, {"i": {"x": [0.833], "y": [0.666]}, "o": {"x": [0.167], "y": [-0.07]}, "t": 55, "s": [0.344]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.111]}, "t": 56, "s": [0.291]}, {"i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.159]}, "t": 57, "s": [0.13]}, {"i": {"x": [0.833], "y": [1.007]}, "o": {"x": [0.167], "y": [0.327]}, "t": 58, "s": [-0.049]}, {"i": {"x": [0.833], "y": [0.899]}, "o": {"x": [0.167], "y": [0.007]}, "t": 59, "s": [-0.109]}, {"i": {"x": [0.833], "y": [0.987]}, "o": {"x": [0.167], "y": [0.479]}, "t": 60, "s": [-0.043]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [-0.016]}, "t": 61, "s": [-0.029]}, {"i": {"x": [0.833], "y": [1.074]}, "o": {"x": [0.167], "y": [0.186]}, "t": 62, "s": [-0.041]}, {"i": {"x": [0.833], "y": [0.601]}, "o": {"x": [0.167], "y": [0.039]}, "t": 63, "s": [-0.05]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.105]}, "t": 64, "s": [-0.032]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.145]}, "t": 65, "s": [0.035]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.224]}, "t": 66, "s": [0.126]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.223]}, "t": 67, "s": [0.18]}, {"i": {"x": [0.833], "y": [0.792]}, "o": {"x": [0.167], "y": [0.18]}, "t": 68, "s": [0.212]}, {"i": {"x": [0.833], "y": [0.767]}, "o": {"x": [0.167], "y": [0.139]}, "t": 69, "s": [0.24]}, {"i": {"x": [0.833], "y": [0.773]}, "o": {"x": [0.167], "y": [0.13]}, "t": 70, "s": [0.282]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.132]}, "t": 71, "s": [0.357]}, {"i": {"x": [0.833], "y": [0.923]}, "o": {"x": [0.167], "y": [0.182]}, "t": 72, "s": [0.485]}, {"i": {"x": [0.833], "y": [-0.641]}, "o": {"x": [0.167], "y": [-1.002]}, "t": 73, "s": [0.595]}, {"i": {"x": [0.833], "y": [0.774]}, "o": {"x": [0.167], "y": [0.088]}, "t": 74, "s": [0.586]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.132]}, "t": 75, "s": [0.43]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.165]}, "t": 76, "s": [0.162]}, {"i": {"x": [0.833], "y": [0.894]}, "o": {"x": [0.167], "y": [0.226]}, "t": 77, "s": [-0.112]}, {"i": {"x": [0.833], "y": [1.027]}, "o": {"x": [0.167], "y": [0.388]}, "t": 78, "s": [-0.271]}, {"i": {"x": [0.833], "y": [0.744]}, "o": {"x": [0.167], "y": [0.02]}, "t": 79, "s": [-0.315]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.123]}, "t": 80, "s": [-0.257]}, {"i": {"x": [0.833], "y": [0.898]}, "o": {"x": [0.167], "y": [0.168]}, "t": 81, "s": [-0.137]}, {"i": {"x": [0.833], "y": [1.446]}, "o": {"x": [0.167], "y": [0.46]}, "t": 82, "s": [-0.019]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.07]}, "t": 83, "s": [0.007]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.138]}, "t": 84, "s": [-0.159]}, {"i": {"x": [0.833], "y": [0.919]}, "o": {"x": [0.167], "y": [0.208]}, "t": 85, "s": [-0.413]}, {"i": {"x": [0.833], "y": [-2.192]}, "o": {"x": [0.167], "y": [-2.743]}, "t": 86, "s": [-0.583]}, {"i": {"x": [0.833], "y": [0.786]}, "o": {"x": [0.167], "y": [0.086]}, "t": 87, "s": [-0.578]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.137]}, "t": 88, "s": [-0.391]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.17]}, "t": 89, "s": [-0.099]}, {"i": {"x": [0.833], "y": [0.933]}, "o": {"x": [0.167], "y": [0.224]}, "t": 90, "s": [0.182]}, {"i": {"x": [0.833], "y": [0.338]}, "o": {"x": [0.167], "y": [-0.331]}, "t": 91, "s": [0.348]}, {"i": {"x": [0.833], "y": [0.794]}, "o": {"x": [0.167], "y": [0.095]}, "t": 92, "s": [0.315]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.14]}, "t": 93, "s": [0.082]}, {"i": {"x": [0.833], "y": [0.871]}, "o": {"x": [0.167], "y": [0.188]}, "t": 94, "s": [-0.259]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.234]}, "t": 95, "s": [-0.531]}, {"i": {"x": [0.833], "y": [0.895]}, "o": {"x": [0.167], "y": [0.165]}, "t": 96, "s": [-0.681]}, {"i": {"x": [0.833], "y": [1.16]}, "o": {"x": [0.167], "y": [0.397]}, "t": 97, "s": [-0.834]}, {"i": {"x": [0.833], "y": [0.74]}, "o": {"x": [0.167], "y": [0.055]}, "t": 98, "s": [-0.875]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.123]}, "t": 99, "s": [-0.756]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.157]}, "t": 100, "s": [-0.505]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.181]}, "t": 101, "s": [-0.222]}, {"i": {"x": [0.833], "y": [0.892]}, "o": {"x": [0.167], "y": [0.186]}, "t": 102, "s": [0.02]}, {"i": {"x": [0.833], "y": [1.061]}, "o": {"x": [0.167], "y": [0.362]}, "t": 103, "s": [0.216]}, {"i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0.035]}, "t": 104, "s": [0.275]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.123]}, "t": 105, "s": [0.174]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.172]}, "t": 106, "s": [-0.037]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.193]}, "t": 107, "s": [-0.235]}, {"i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.155]}, "t": 108, "s": [-0.385]}, {"i": {"x": [0.833], "y": [0.978]}, "o": {"x": [0.167], "y": [0.258]}, "t": 109, "s": [-0.56]}, {"i": {"x": [0.833], "y": [0.656]}, "o": {"x": [0.167], "y": [-0.03]}, "t": 110, "s": [-0.644]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.11]}, "t": 111, "s": [-0.583]}, {"i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.15]}, "t": 112, "s": [-0.391]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.159]}, "t": 113, "s": [-0.151]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.158]}, "t": 114, "s": [0.113]}, {"i": {"x": [0.833], "y": [0.925]}, "o": {"x": [0.167], "y": [0.226]}, "t": 115, "s": [0.408]}, {"i": {"x": [0.833], "y": [0.04]}, "o": {"x": [0.167], "y": [-0.725]}, "t": 116, "s": [0.581]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.091]}, "t": 117, "s": [0.564]}, {"i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.147]}, "t": 118, "s": [0.376]}, {"i": {"x": [0.833], "y": [0.956]}, "o": {"x": [0.167], "y": [0.259]}, "t": 119, "s": [0.128]}, {"i": {"x": [0.833], "y": [0.694]}, "o": {"x": [0.167], "y": [-0.094]}, "t": 120, "s": [0.011]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.115]}, "t": 121, "s": [0.066]}, {"i": {"x": [0.833], "y": [0.87]}, "o": {"x": [0.167], "y": [0.161]}, "t": 122, "s": [0.213]}, {"i": {"x": [0.833], "y": [0.977]}, "o": {"x": [0.167], "y": [0.233]}, "t": 123, "s": [0.371]}, {"i": {"x": [0.833], "y": [0.623]}, "o": {"x": [0.167], "y": [-0.032]}, "t": 124, "s": [0.459]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.107]}, "t": 125, "s": [0.396]}, {"i": {"x": [0.833], "y": [0.87]}, "o": {"x": [0.167], "y": [0.168]}, "t": 126, "s": [0.173]}, {"i": {"x": [0.833], "y": [0.92]}, "o": {"x": [0.167], "y": [0.231]}, "t": 127, "s": [-0.046]}, {"i": {"x": [0.833], "y": [-1.038]}, "o": {"x": [0.167], "y": [-2.199]}, "t": 128, "s": [-0.17]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.087]}, "t": 129, "s": [-0.165]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.156]}, "t": 130, "s": [-0.06]}, {"i": {"x": [0.833], "y": [-18.086]}, "o": {"x": [0.167], "y": [-16.687]}, "t": 131, "s": [0.062]}, {"i": {"x": [0.833], "y": [0.797]}, "o": {"x": [0.167], "y": [0.084]}, "t": 132, "s": [0.061]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.141]}, "t": 133, "s": [-0.076]}, {"i": {"x": [0.833], "y": [0.865]}, "o": {"x": [0.167], "y": [0.171]}, "t": 134, "s": [-0.274]}, {"i": {"x": [0.833], "y": [0.921]}, "o": {"x": [0.167], "y": [0.217]}, "t": 135, "s": [-0.461]}, {"i": {"x": [0.833], "y": [-2.41]}, "o": {"x": [0.167], "y": [-1.706]}, "t": 136, "s": [-0.578]}, {"i": {"x": [0.833], "y": [0.771]}, "o": {"x": [0.167], "y": [0.085]}, "t": 137, "s": [-0.573]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.131]}, "t": 138, "s": [-0.355]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.174]}, "t": 139, "s": [0.026]}, {"i": {"x": [0.833], "y": [0.916]}, "o": {"x": [0.167], "y": [0.23]}, "t": 140, "s": [0.378]}, {"i": {"x": [0.833], "y": [6.476]}, "o": {"x": [0.167], "y": [6.764]}, "t": 141, "s": [0.578]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.082]}, "t": 142, "s": [0.58]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.126]}, "t": 143, "s": [0.414]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.147]}, "t": 144, "s": [0.089]}, {"i": {"x": [0.833], "y": [0.882]}, "o": {"x": [0.167], "y": [0.189]}, "t": 145, "s": [-0.339]}, {"i": {"x": [0.833], "y": [0.955]}, "o": {"x": [0.167], "y": [0.282]}, "t": 146, "s": [-0.675]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.1]}, "t": 147, "s": [-0.816]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.112]}, "t": 148, "s": [-0.752]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.154]}, "t": 149, "s": [-0.565]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.165]}, "t": 150, "s": [-0.343]}, {"i": {"x": [0.833], "y": [0.913]}, "o": {"x": [0.167], "y": [0.223]}, "t": 151, "s": [-0.116]}, {"i": {"x": [0.833], "y": [2.365]}, "o": {"x": [0.167], "y": [2.002]}, "t": 152, "s": [0.02]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.079]}, "t": 153, "s": [0.026]}, {"i": {"x": [0.833], "y": [0.899]}, "o": {"x": [0.167], "y": [0.149]}, "t": 154, "s": [-0.077]}, {"i": {"x": [0.833], "y": [1.105]}, "o": {"x": [0.167], "y": [0.471]}, "t": 155, "s": [-0.206]}, {"i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.047]}, "t": 156, "s": [-0.234]}, {"i": {"x": [0.833], "y": [0.904]}, "o": {"x": [0.167], "y": [0.163]}, "t": 157, "s": [-0.171]}, {"i": {"x": [0.833], "y": [1.576]}, "o": {"x": [0.167], "y": [0.647]}, "t": 158, "s": [-0.105]}, {"i": {"x": [0.833], "y": [0.737]}, "o": {"x": [0.167], "y": [0.073]}, "t": 159, "s": [-0.095]}, {"i": {"x": [0.833], "y": [0.783]}, "o": {"x": [0.167], "y": [0.122]}, "t": 160, "s": [-0.172]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.135]}, "t": 161, "s": [-0.338]}, {"i": {"x": [0.833], "y": [0.881]}, "o": {"x": [0.167], "y": [0.164]}, "t": 162, "s": [-0.606]}, {"i": {"x": [0.833], "y": [0.988]}, "o": {"x": [0.167], "y": [0.276]}, "t": 163, "s": [-0.883]}, {"i": {"x": [0.833], "y": [0.683]}, "o": {"x": [0.167], "y": [-0.014]}, "t": 164, "s": [-1.003]}, {"i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.113]}, "t": 165, "s": [-0.9]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.155]}, "t": 166, "s": [-0.611]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.165]}, "t": 167, "s": [-0.277]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.152]}, "t": 168, "s": [0.063]}, {"i": {"x": [0.833], "y": [0.895]}, "o": {"x": [0.167], "y": [0.201]}, "t": 169, "s": [0.475]}, {"i": {"x": [0.833], "y": [1.069]}, "o": {"x": [0.167], "y": [0.41]}, "t": 170, "s": [0.766]}, {"i": {"x": [0.833], "y": [0.772]}, "o": {"x": [0.167], "y": [0.038]}, "t": 171, "s": [0.84]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.131]}, "t": 172, "s": [0.705]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.165]}, "t": 173, "s": [0.469]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.162]}, "t": 174, "s": [0.228]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.199]}, "t": 175, "s": [-0.028]}, {"i": {"x": [0.833], "y": [0.945]}, "o": {"x": [0.167], "y": [0.294]}, "t": 176, "s": [-0.212]}, {"i": {"x": [0.833], "y": [0.724]}, "o": {"x": [0.167], "y": [-0.159]}, "t": 177, "s": [-0.285]}, {"i": {"x": [0.833], "y": [0.969]}, "o": {"x": [0.167], "y": [0.119]}, "t": 178, "s": [-0.26]}, {"i": {"x": [0.833], "y": [0.616]}, "o": {"x": [0.167], "y": [-0.048]}, "t": 179, "s": [-0.202]}, {"i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.106]}, "t": 180, "s": [-0.239]}, {"i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.175]}, "t": 181, "s": [-0.372]}, {"i": {"x": [0.833], "y": [1.055]}, "o": {"x": [0.167], "y": [0.329]}, "t": 182, "s": [-0.493]}, {"i": {"x": [0.833], "y": [0.713]}, "o": {"x": [0.167], "y": [0.033]}, "t": 183, "s": [-0.534]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.117]}, "t": 184, "s": [-0.466]}, {"i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.172]}, "t": 185, "s": [-0.3]}, {"i": {"x": [0.833], "y": [0.897]}, "o": {"x": [0.167], "y": [0.317]}, "t": 186, "s": [-0.145]}, {"i": {"x": [0.833], "y": [0.772]}, "o": {"x": [0.167], "y": [0.442]}, "t": 187, "s": [-0.09]}, {"i": {"x": [0.833], "y": [0.632]}, "o": {"x": [0.167], "y": [0.131]}, "t": 188, "s": [-0.077]}, {"i": {"x": [0.833], "y": [0.734]}, "o": {"x": [0.167], "y": [0.108]}, "t": 189, "s": [-0.055]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.121]}, "t": 190, "s": [0.021]}, {"i": {"x": [0.833], "y": [0.895]}, "o": {"x": [0.167], "y": [0.159]}, "t": 191, "s": [0.189]}, {"i": {"x": [0.833], "y": [1.032]}, "o": {"x": [0.167], "y": [0.406]}, "t": 192, "s": [0.374]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.023]}, "t": 193, "s": [0.422]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.126]}, "t": 194, "s": [0.356]}, {"i": {"x": [0.833], "y": [0.914]}, "o": {"x": [0.167], "y": [0.176]}, "t": 195, "s": [0.226]}, {"i": {"x": [0.833], "y": [3.901]}, "o": {"x": [0.167], "y": [3.194]}, "t": 196, "s": [0.109]}, {"i": {"x": [0.833], "y": [0.873]}, "o": {"x": [0.167], "y": [0.081]}, "t": 197, "s": [0.106]}, {"i": {"x": [0.833], "y": [0.933]}, "o": {"x": [0.167], "y": [0.241]}, "t": 198, "s": [0.218]}, {"i": {"x": [0.833], "y": [0.393]}, "o": {"x": [0.167], "y": [-0.354]}, "t": 199, "s": [0.277]}, {"i": {"x": [0.833], "y": [0.806]}, "o": {"x": [0.167], "y": [0.097]}, "t": 200, "s": [0.266]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.146]}, "t": 201, "s": [0.195]}, {"i": {"x": [0.833], "y": [0.911]}, "o": {"x": [0.167], "y": [0.228]}, "t": 202, "s": [0.102]}, {"i": {"x": [0.833], "y": [0.175]}, "o": {"x": [0.167], "y": [1.216]}, "t": 203, "s": [0.048]}, {"i": {"x": [0.833], "y": [0.785]}, "o": {"x": [0.167], "y": [0.093]}, "t": 204, "s": [0.044]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.136]}, "t": 205, "s": [0.009]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.158]}, "t": 206, "s": [-0.047]}, {"i": {"x": [0.833], "y": [0.878]}, "o": {"x": [0.167], "y": [0.183]}, "t": 207, "s": [-0.109]}, {"i": {"x": [0.833], "y": [1.183]}, "o": {"x": [0.167], "y": [0.264]}, "t": 208, "s": [-0.162]}, {"i": {"x": [0.833], "y": [0.723]}, "o": {"x": [0.167], "y": [0.057]}, "t": 209, "s": [-0.186]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.119]}, "t": 210, "s": [-0.109]}, {"i": {"x": [0.833], "y": [0.892]}, "o": {"x": [0.167], "y": [0.181]}, "t": 211, "s": [0.07]}, {"i": {"x": [0.833], "y": [1.075]}, "o": {"x": [0.167], "y": [0.36]}, "t": 212, "s": [0.222]}, {"i": {"x": [0.833], "y": [0.73]}, "o": {"x": [0.167], "y": [0.04]}, "t": 213, "s": [0.268]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.121]}, "t": 214, "s": [0.181]}, {"i": {"x": [0.833], "y": [0.892]}, "o": {"x": [0.167], "y": [0.173]}, "t": 215, "s": [-0.014]}, {"i": {"x": [0.833], "y": [0.989]}, "o": {"x": [0.167], "y": [0.362]}, "t": 216, "s": [-0.195]}, {"i": {"x": [0.833], "y": [0.739]}, "o": {"x": [0.167], "y": [-0.012]}, "t": 217, "s": [-0.249]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.123]}, "t": 218, "s": [-0.202]}, {"i": {"x": [0.833], "y": [0.937]}, "o": {"x": [0.167], "y": [0.182]}, "t": 219, "s": [-0.102]}, {"i": {"x": [0.833], "y": [0.437]}, "o": {"x": [0.167], "y": [-0.258]}, "t": 220, "s": [-0.018]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.098]}, "t": 221, "s": [-0.039]}, {"i": {"x": [0.833], "y": [0.923]}, "o": {"x": [0.167], "y": [0.246]}, "t": 222, "s": [-0.157]}, {"i": {"x": [0.833], "y": [-0.097]}, "o": {"x": [0.167], "y": [-1.065]}, "t": 223, "s": [-0.218]}, {"i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.09]}, "t": 224, "s": [-0.214]}, {"i": {"x": [0.833], "y": [0.891]}, "o": {"x": [0.167], "y": [0.151]}, "t": 225, "s": [-0.16]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.349]}, "t": 226, "s": [-0.094]}, {"i": {"x": [0.833], "y": [0.453]}, "o": {"x": [0.167], "y": [0.149]}, "t": 227, "s": [-0.073]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.098]}, "t": 228, "s": [-0.046]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.153]}, "t": 229, "s": [0.101]}, {"i": {"x": [0.833], "y": [0.891]}, "o": {"x": [0.167], "y": [0.194]}, "t": 230, "s": [0.276]}, {"i": {"x": [0.833], "y": [1.072]}, "o": {"x": [0.167], "y": [0.356]}, "t": 231, "s": [0.408]}, {"i": {"x": [0.833], "y": [0.716]}, "o": {"x": [0.167], "y": [0.039]}, "t": 232, "s": [0.449]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.118]}, "t": 233, "s": [0.373]}, {"i": {"x": [0.833], "y": [0.878]}, "o": {"x": [0.167], "y": [0.166]}, "t": 234, "s": [0.192]}, {"i": {"x": [0.833], "y": [0.969]}, "o": {"x": [0.167], "y": [0.265]}, "t": 235, "s": [0.008]}, {"i": {"x": [0.833], "y": [0.658]}, "o": {"x": [0.167], "y": [-0.049]}, "t": 236, "s": [-0.076]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.11]}, "t": 237, "s": [-0.023]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.157]}, "t": 238, "s": [0.141]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.196]}, "t": 239, "s": [0.328]}, {"i": {"x": [0.833], "y": [0.904]}, "o": {"x": [0.167], "y": [0.197]}, "t": 240, "s": [0.466]}, {"i": {"x": [0.833], "y": [1.341]}, "o": {"x": [0.167], "y": [0.627]}, "t": 241, "s": [0.567]}, {"i": {"x": [0.833], "y": [0.769]}, "o": {"x": [0.167], "y": [0.067]}, "t": 242, "s": [0.583]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.131]}, "t": 243, "s": [0.504]}, {"i": {"x": [0.833], "y": [0.926]}, "o": {"x": [0.167], "y": [0.177]}, "t": 244, "s": [0.364]}, {"i": {"x": [0.833], "y": [0.283]}, "o": {"x": [0.167], "y": [-0.644]}, "t": 245, "s": [0.24]}, {"i": {"x": [0.833], "y": [0.873]}, "o": {"x": [0.167], "y": [0.094]}, "t": 246, "s": [0.254]}, {"i": {"x": [0.833], "y": [1.028]}, "o": {"x": [0.167], "y": [0.241]}, "t": 247, "s": [0.362]}, {"i": {"x": [0.833], "y": [0.668]}, "o": {"x": [0.167], "y": [0.021]}, "t": 248, "s": [0.419]}, {"i": {"x": [0.833], "y": [0.795]}, "o": {"x": [0.167], "y": [0.111]}, "t": 249, "s": [0.343]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.14]}, "t": 250, "s": [0.117]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.163]}, "t": 251, "s": [-0.214]}, {"i": {"x": [0.833], "y": [0.914]}, "o": {"x": [0.167], "y": [0.209]}, "t": 252, "s": [-0.557]}, {"i": {"x": [0.833], "y": [3.56]}, "o": {"x": [0.167], "y": [2.833]}, "t": 253, "s": [-0.785]}, {"i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.081]}, "t": 254, "s": [-0.792]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.136]}, "t": 255, "s": [-0.573]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.186]}, "t": 256, "s": [-0.224]}, {"i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.332]}, "t": 257, "s": [0.059]}, {"i": {"x": [0.833], "y": [1.01]}, "o": {"x": [0.167], "y": [0.316]}, "t": 258, "s": [0.153]}, {"i": {"x": [0.833], "y": [0.701]}, "o": {"x": [0.167], "y": [0.009]}, "t": 259, "s": [0.187]}, {"i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.115]}, "t": 260, "s": [0.149]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.151]}, "t": 261, "s": [0.051]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.209]}, "t": 262, "s": [-0.071]}, {"i": {"x": [0.833], "y": [0.745]}, "o": {"x": [0.167], "y": [0.166]}, "t": 263, "s": [-0.152]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.124]}, "t": 264, "s": [-0.233]}, {"i": {"x": [0.833], "y": [0.9]}, "o": {"x": [0.167], "y": [0.188]}, "t": 265, "s": [-0.4]}, {"i": {"x": [0.833], "y": [1.222]}, "o": {"x": [0.167], "y": [0.493]}, "t": 266, "s": [-0.533]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.061]}, "t": 267, "s": [-0.56]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.126]}, "t": 268, "s": [-0.461]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.157]}, "t": 269, "s": [-0.267]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.179]}, "t": 270, "s": [-0.047]}, {"i": {"x": [0.833], "y": [0.871]}, "o": {"x": [0.167], "y": [0.202]}, "t": 271, "s": [0.145]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.234]}, "t": 272, "s": [0.279]}, {"i": {"x": [0.833], "y": [0.801]}, "o": {"x": [0.167], "y": [0.246]}, "t": 273, "s": [0.353]}, {"i": {"x": [0.833], "y": [0.678]}, "o": {"x": [0.167], "y": [0.143]}, "t": 274, "s": [0.391]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.112]}, "t": 275, "s": [0.444]}, {"i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.153]}, "t": 276, "s": [0.595]}, {"i": {"x": [0.833], "y": [1.062]}, "o": {"x": [0.167], "y": [0.315]}, "t": 277, "s": [0.775]}, {"i": {"x": [0.833], "y": [0.717]}, "o": {"x": [0.167], "y": [0.036]}, "t": 278, "s": [0.84]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.118]}, "t": 279, "s": [0.727]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.153]}, "t": 280, "s": [0.456]}, {"i": {"x": [0.833], "y": [0.919]}, "o": {"x": [0.167], "y": [0.223]}, "t": 281, "s": [0.131]}, {"i": {"x": [0.833], "y": [-1.402]}, "o": {"x": [0.167], "y": [-3.043]}, "t": 282, "s": [-0.063]}, {"i": {"x": [0.833], "y": [0.8]}, "o": {"x": [0.167], "y": [0.086]}, "t": 283, "s": [-0.058]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.143]}, "t": 284, "s": [0.086]}, {"i": {"x": [0.833], "y": [0.923]}, "o": {"x": [0.167], "y": [0.191]}, "t": 285, "s": [0.287]}, {"i": {"x": [0.833], "y": [-1.256]}, "o": {"x": [0.167], "y": [-1.07]}, "t": 286, "s": [0.443]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.087]}, "t": 287, "s": [0.432]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.142]}, "t": 288, "s": [0.138]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.196]}, "t": 289, "s": [-0.28]}, {"i": {"x": [0.833], "y": [1.105]}, "o": {"x": [0.167], "y": [0.426]}, "t": 290, "s": [-0.588]}, {"i": {"x": [0.833], "y": [0.762]}, "o": {"x": [0.167], "y": [0.047]}, "t": 291, "s": [-0.663]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.128]}, "t": 292, "s": [-0.493]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.187]}, "t": 293, "s": [-0.178]}, {"i": {"x": [0.833], "y": [0.932]}, "o": {"x": [0.167], "y": [0.296]}, "t": 294, "s": [0.076]}, {"i": {"x": [0.833], "y": [0.568]}, "o": {"x": [0.167], "y": [-0.37]}, "t": 295, "s": [0.175]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.103]}, "t": 296, "s": [0.157]}, {"i": {"x": [0.833], "y": [1.035]}, "o": {"x": [0.167], "y": [0.204]}, "t": 297, "s": [0.08]}, {"i": {"x": [0.833], "y": [0.672]}, "o": {"x": [0.167], "y": [0.025]}, "t": 298, "s": [0.028]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.112]}, "t": 299, "s": [0.102]}, {"i": {"x": [0.833], "y": [0.88]}, "o": {"x": [0.167], "y": [0.184]}, "t": 300, "s": [0.322]}, {"i": {"x": [0.833], "y": [0.953]}, "o": {"x": [0.167], "y": [0.274]}, "t": 301, "s": [0.504]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [-0.109]}, "t": 302, "s": [0.584]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.11]}, "t": 303, "s": [0.55]}, {"i": {"x": [0.833], "y": [0.806]}, "o": {"x": [0.167], "y": [0.182]}, "t": 304, "s": [0.44]}, {"i": {"x": [0.833], "y": [0.731]}, "o": {"x": [0.167], "y": [0.146]}, "t": 305, "s": [0.348]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.121]}, "t": 306, "s": [0.226]}, {"i": {"x": [0.833], "y": [0.876]}, "o": {"x": [0.167], "y": [0.173]}, "t": 307, "s": [-0.047]}, {"i": {"x": [0.833], "y": [0.952]}, "o": {"x": [0.167], "y": [0.254]}, "t": 308, "s": [-0.3]}, {"i": {"x": [0.833], "y": [0.583]}, "o": {"x": [0.167], "y": [-0.114]}, "t": 309, "s": [-0.423]}, {"i": {"x": [0.833], "y": [0.804]}, "o": {"x": [0.167], "y": [0.104]}, "t": 310, "s": [-0.371]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.145]}, "t": 311, "s": [-0.163]}, {"i": {"x": [0.833], "y": [0.872]}, "o": {"x": [0.167], "y": [0.181]}, "t": 312, "s": [0.119]}, {"i": {"x": [0.833], "y": [0.915]}, "o": {"x": [0.167], "y": [0.239]}, "t": 313, "s": [0.36]}, {"i": {"x": [0.833], "y": [4.113]}, "o": {"x": [0.167], "y": [5.134]}, "t": 314, "s": [0.488]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.081]}, "t": 315, "s": [0.49]}, {"i": {"x": [0.833], "y": [0.925]}, "o": {"x": [0.167], "y": [0.188]}, "t": 316, "s": [0.409]}, {"i": {"x": [0.833], "y": [1.001]}, "o": {"x": [0.167], "y": [-0.798]}, "t": 317, "s": [0.344]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.001]}, "t": 318, "s": [0.35]}, {"i": {"x": [0.833], "y": [0.935]}, "o": {"x": [0.167], "y": [0.157]}, "t": 319, "s": [0.344]}, {"i": {"x": [0.833], "y": [0.097]}, "o": {"x": [0.167], "y": [-0.299]}, "t": 320, "s": [0.337]}, {"i": {"x": [0.833], "y": [0.755]}, "o": {"x": [0.167], "y": [0.092]}, "t": 321, "s": [0.339]}, {"i": {"x": [0.833], "y": [0.785]}, "o": {"x": [0.167], "y": [0.126]}, "t": 322, "s": [0.354]}, {"i": {"x": [0.833], "y": [0.898]}, "o": {"x": [0.167], "y": [0.136]}, "t": 323, "s": [0.383]}, {"i": {"x": [0.833], "y": [1.654]}, "o": {"x": [0.167], "y": [0.448]}, "t": 324, "s": [0.428]}, {"i": {"x": [0.833], "y": [0.727]}, "o": {"x": [0.167], "y": [0.074]}, "t": 325, "s": [0.438]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.12]}, "t": 326, "s": [0.347]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.144]}, "t": 327, "s": [0.138]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.17]}, "t": 328, "s": [-0.15]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.192]}, "t": 329, "s": [-0.425]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.186]}, "t": 330, "s": [-0.637]}, {"i": {"x": [0.833], "y": [0.976]}, "o": {"x": [0.167], "y": [0.293]}, "t": 331, "s": [-0.809]}, {"i": {"x": [0.833], "y": [0.7]}, "o": {"x": [0.167], "y": [-0.034]}, "t": 332, "s": [-0.878]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.115]}, "t": 333, "s": [-0.829]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.178]}, "t": 334, "s": [-0.702]}, {"i": {"x": [0.833], "y": [0.749]}, "o": {"x": [0.167], "y": [0.161]}, "t": 335, "s": [-0.59]}, {"i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.125]}, "t": 336, "s": [-0.47]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.17]}, "t": 337, "s": [-0.23]}, {"i": {"x": [0.833], "y": [0.914]}, "o": {"x": [0.167], "y": [0.224]}, "t": 338, "s": [0.003]}, {"i": {"x": [0.833], "y": [2.907]}, "o": {"x": [0.167], "y": [2.384]}, "t": 339, "s": [0.141]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.08]}, "t": 340, "s": [0.146]}, {"i": {"x": [0.833], "y": [0.891]}, "o": {"x": [0.167], "y": [0.151]}, "t": 341, "s": [0.026]}, {"i": {"x": [0.833], "y": [1.05]}, "o": {"x": [0.167], "y": [0.356]}, "t": 342, "s": [-0.12]}, {"i": {"x": [0.833], "y": [0.727]}, "o": {"x": [0.167], "y": [0.031]}, "t": 343, "s": [-0.165]}, {"i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.12]}, "t": 344, "s": [-0.093]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.155]}, "t": 345, "s": [0.071]}, {"i": {"x": [0.833], "y": [0.902]}, "o": {"x": [0.167], "y": [0.223]}, "t": 346, "s": [0.261]}, {"i": {"x": [0.833], "y": [0.723]}, "o": {"x": [0.167], "y": [0.561]}, "t": 347, "s": [0.375]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.119]}, "t": 348, "s": [0.395]}, {"i": {"x": [0.833], "y": [0.863]}, "o": {"x": [0.167], "y": [0.164]}, "t": 349, "s": [0.441]}, {"i": {"x": [0.833], "y": [0.906]}, "o": {"x": [0.167], "y": [0.213]}, "t": 350, "s": [0.489]}, {"i": {"x": [0.833], "y": [1.452]}, "o": {"x": [0.167], "y": [0.758]}, "t": 351, "s": [0.52]}, {"i": {"x": [0.833], "y": [0.421]}, "o": {"x": [0.167], "y": [0.07]}, "t": 352, "s": [0.524]}, {"i": {"x": [0.833], "y": [0.749]}, "o": {"x": [0.167], "y": [0.097]}, "t": 353, "s": [0.499]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.125]}, "t": 354, "s": [0.353]}, {"i": {"x": [0.833], "y": [0.881]}, "o": {"x": [0.167], "y": [0.178]}, "t": 355, "s": [0.059]}, {"i": {"x": [0.833], "y": [0.975]}, "o": {"x": [0.167], "y": [0.278]}, "t": 356, "s": [-0.201]}, {"i": {"x": [0.833], "y": [0.665]}, "o": {"x": [0.167], "y": [-0.036]}, "t": 357, "s": [-0.312]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.111]}, "t": 358, "s": [-0.234]}, {"i": {"x": [0.833], "y": [0.86]}, "o": {"x": [0.167], "y": [0.15]}, "t": 359, "s": [0]}, {"i": {"x": [0.833], "y": [0.92]}, "o": {"x": [0.167], "y": [0.206]}, "t": 360, "s": [0.291]}, {"i": {"x": [0.833], "y": [-1.501]}, "o": {"x": [0.167], "y": [-2.025]}, "t": 361, "s": [0.489]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.086]}, "t": 362, "s": [0.481]}, {"t": 363, "s": [0.255]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.094, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [292.307, 484.058, 0], "to": [0, -24.01, 0], "ti": [0, 24.01, 0]}, {"t": 14, "s": [292.307, 340, 0]}], "ix": 2}, "a": {"a": 0, "k": [98.137, 29.055, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 4, "s": [25, 25, 100]}, {"t": 14, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 364, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 2, "nm": "wining", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.87]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0.56]}, {"i": {"x": [0.833], "y": [0.894]}, "o": {"x": [0.167], "y": [0.231]}, "t": 1, "s": [0.254]}, {"i": {"x": [0.833], "y": [0.716]}, "o": {"x": [0.167], "y": [0.389]}, "t": 2, "s": [0.081]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.118]}, "t": 3, "s": [0.034]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.16]}, "t": 4, "s": [-0.079]}, {"i": {"x": [0.833], "y": [0.885]}, "o": {"x": [0.167], "y": [0.195]}, "t": 5, "s": [-0.203]}, {"i": {"x": [0.833], "y": [0.994]}, "o": {"x": [0.167], "y": [0.302]}, "t": 6, "s": [-0.296]}, {"i": {"x": [0.833], "y": [0.905]}, "o": {"x": [0.167], "y": [-0.006]}, "t": 7, "s": [-0.331]}, {"i": {"x": [0.833], "y": [2.349]}, "o": {"x": [0.167], "y": [0.661]}, "t": 8, "s": [-0.298]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.078]}, "t": 9, "s": [-0.294]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.165]}, "t": 10, "s": [-0.375]}, {"i": {"x": [0.833], "y": [1.097]}, "o": {"x": [0.167], "y": [0.337]}, "t": 11, "s": [-0.458]}, {"i": {"x": [0.833], "y": [0.707]}, "o": {"x": [0.167], "y": [0.045]}, "t": 12, "s": [-0.485]}, {"i": {"x": [0.833], "y": [0.752]}, "o": {"x": [0.167], "y": [0.116]}, "t": 13, "s": [-0.426]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.126]}, "t": 14, "s": [-0.277]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.149]}, "t": 15, "s": [0.017]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.196]}, "t": 16, "s": [0.393]}, {"i": {"x": [0.833], "y": [1.009]}, "o": {"x": [0.167], "y": [0.34]}, "t": 17, "s": [0.672]}, {"i": {"x": [0.833], "y": [0.746]}, "o": {"x": [0.167], "y": [0.008]}, "t": 18, "s": [0.763]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.124]}, "t": 19, "s": [0.662]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.166]}, "t": 20, "s": [0.456]}, {"i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.192]}, "t": 21, "s": [0.248]}, {"i": {"x": [0.833], "y": [0.993]}, "o": {"x": [0.167], "y": [0.318]}, "t": 22, "s": [0.087]}, {"i": {"x": [0.833], "y": [0.729]}, "o": {"x": [0.167], "y": [-0.007]}, "t": 23, "s": [0.03]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.12]}, "t": 24, "s": [0.083]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.194]}, "t": 25, "s": [0.2]}, {"i": {"x": [0.833], "y": [0.68]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [0.289]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.113]}, "t": 27, "s": [0.2]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.154]}, "t": 28, "s": [-0.054]}, {"i": {"x": [0.833], "y": [0.872]}, "o": {"x": [0.167], "y": [0.183]}, "t": 29, "s": [-0.352]}, {"i": {"x": [0.833], "y": [0.935]}, "o": {"x": [0.167], "y": [0.241]}, "t": 30, "s": [-0.603]}, {"i": {"x": [0.833], "y": [0.5]}, "o": {"x": [0.167], "y": [-0.295]}, "t": 31, "s": [-0.735]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.1]}, "t": 32, "s": [-0.706]}, {"i": {"x": [0.833], "y": [0.913]}, "o": {"x": [0.167], "y": [0.192]}, "t": 33, "s": [-0.56]}, {"i": {"x": [0.833], "y": [3.079]}, "o": {"x": [0.167], "y": [2.117]}, "t": 34, "s": [-0.448]}, {"i": {"x": [0.833], "y": [0.775]}, "o": {"x": [0.167], "y": [0.08]}, "t": 35, "s": [-0.443]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.132]}, "t": 36, "s": [-0.562]}, {"i": {"x": [0.833], "y": [0.898]}, "o": {"x": [0.167], "y": [0.173]}, "t": 37, "s": [-0.765]}, {"i": {"x": [0.833], "y": [1.234]}, "o": {"x": [0.167], "y": [0.447]}, "t": 38, "s": [-0.954]}, {"i": {"x": [0.833], "y": [0.732]}, "o": {"x": [0.167], "y": [0.061]}, "t": 39, "s": [-0.997]}, {"i": {"x": [0.833], "y": [0.803]}, "o": {"x": [0.167], "y": [0.121]}, "t": 40, "s": [-0.832]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.144]}, "t": 41, "s": [-0.466]}, {"i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.165]}, "t": 42, "s": [0.036]}, {"i": {"x": [0.833], "y": [0.912]}, "o": {"x": [0.167], "y": [0.21]}, "t": 43, "s": [0.546]}, {"i": {"x": [0.833], "y": [1.919]}, "o": {"x": [0.167], "y": [1.708]}, "t": 44, "s": [0.88]}, {"i": {"x": [0.833], "y": [0.786]}, "o": {"x": [0.167], "y": [0.076]}, "t": 45, "s": [0.898]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.136]}, "t": 46, "s": [0.691]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.164]}, "t": 47, "s": [0.368]}, {"i": {"x": [0.833], "y": [0.907]}, "o": {"x": [0.167], "y": [0.201]}, "t": 48, "s": [0.034]}, {"i": {"x": [0.833], "y": [1.384]}, "o": {"x": [0.167], "y": [0.805]}, "t": 49, "s": [-0.202]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.068]}, "t": 50, "s": [-0.229]}, {"i": {"x": [0.833], "y": [0.905]}, "o": {"x": [0.167], "y": [0.187]}, "t": 51, "s": [-0.077]}, {"i": {"x": [0.833], "y": [1.449]}, "o": {"x": [0.167], "y": [0.692]}, "t": 52, "s": [0.046]}, {"i": {"x": [0.833], "y": [0.767]}, "o": {"x": [0.167], "y": [0.07]}, "t": 53, "s": [0.062]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.13]}, "t": 54, "s": [-0.045]}, {"i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.171]}, "t": 55, "s": [-0.236]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.221]}, "t": 56, "s": [-0.418]}, {"i": {"x": [0.833], "y": [0.92]}, "o": {"x": [0.167], "y": [0.228]}, "t": 57, "s": [-0.528]}, {"i": {"x": [0.833], "y": [-0.954]}, "o": {"x": [0.167], "y": [-1.833]}, "t": 58, "s": [-0.591]}, {"i": {"x": [0.833], "y": [0.799]}, "o": {"x": [0.167], "y": [0.087]}, "t": 59, "s": [-0.589]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.142]}, "t": 60, "s": [-0.527]}, {"i": {"x": [0.833], "y": [0.939]}, "o": {"x": [0.167], "y": [0.222]}, "t": 61, "s": [-0.439]}, {"i": {"x": [0.833], "y": [0.965]}, "o": {"x": [0.167], "y": [-0.233]}, "t": 62, "s": [-0.387]}, {"i": {"x": [0.833], "y": [0.005]}, "o": {"x": [0.167], "y": [-0.061]}, "t": 63, "s": [-0.401]}, {"i": {"x": [0.833], "y": [0.744]}, "o": {"x": [0.167], "y": [0.091]}, "t": 64, "s": [-0.393]}, {"i": {"x": [0.833], "y": [0.803]}, "o": {"x": [0.167], "y": [0.124]}, "t": 65, "s": [-0.305]}, {"i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.145]}, "t": 66, "s": [-0.124]}, {"i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.169]}, "t": 67, "s": [0.123]}, {"i": {"x": [0.833], "y": [0.895]}, "o": {"x": [0.167], "y": [0.211]}, "t": 68, "s": [0.363]}, {"i": {"x": [0.833], "y": [1.105]}, "o": {"x": [0.167], "y": [0.405]}, "t": 69, "s": [0.521]}, {"i": {"x": [0.833], "y": [0.74]}, "o": {"x": [0.167], "y": [0.046]}, "t": 70, "s": [0.561]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.123]}, "t": 71, "s": [0.469]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.156]}, "t": 72, "s": [0.275]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.228]}, "t": 73, "s": [0.052]}, {"i": {"x": [0.833], "y": [0.747]}, "o": {"x": [0.167], "y": [0.341]}, "t": 74, "s": [-0.076]}, {"i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.124]}, "t": 75, "s": [-0.118]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.156]}, "t": 76, "s": [-0.203]}, {"i": {"x": [0.833], "y": [0.863]}, "o": {"x": [0.167], "y": [0.178]}, "t": 77, "s": [-0.301]}, {"i": {"x": [0.833], "y": [0.898]}, "o": {"x": [0.167], "y": [0.212]}, "t": 78, "s": [-0.387]}, {"i": {"x": [0.833], "y": [0.923]}, "o": {"x": [0.167], "y": [0.454]}, "t": 79, "s": [-0.443]}, {"i": {"x": [0.833], "y": [-0.706]}, "o": {"x": [0.167], "y": [-0.945]}, "t": 80, "s": [-0.456]}, {"i": {"x": [0.833], "y": [0.531]}, "o": {"x": [0.167], "y": [0.088]}, "t": 81, "s": [-0.455]}, {"i": {"x": [0.833], "y": [0.755]}, "o": {"x": [0.167], "y": [0.101]}, "t": 82, "s": [-0.435]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.126]}, "t": 83, "s": [-0.343]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.146]}, "t": 84, "s": [-0.166]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.168]}, "t": 85, "s": [0.071]}, {"i": {"x": [0.833], "y": [0.904]}, "o": {"x": [0.167], "y": [0.202]}, "t": 86, "s": [0.305]}, {"i": {"x": [0.833], "y": [1.478]}, "o": {"x": [0.167], "y": [0.616]}, "t": 87, "s": [0.469]}, {"i": {"x": [0.833], "y": [0.747]}, "o": {"x": [0.167], "y": [0.071]}, "t": 88, "s": [0.494]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.124]}, "t": 89, "s": [0.321]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.152]}, "t": 90, "s": [-0.03]}, {"i": {"x": [0.833], "y": [0.876]}, "o": {"x": [0.167], "y": [0.193]}, "t": 91, "s": [-0.46]}, {"i": {"x": [0.833], "y": [0.876]}, "o": {"x": [0.167], "y": [0.253]}, "t": 92, "s": [-0.787]}, {"i": {"x": [0.833], "y": [0.977]}, "o": {"x": [0.167], "y": [0.255]}, "t": 93, "s": [-0.947]}, {"i": {"x": [0.833], "y": [0.644]}, "o": {"x": [0.167], "y": [-0.031]}, "t": 94, "s": [-1.025]}, {"i": {"x": [0.833], "y": [0.804]}, "o": {"x": [0.167], "y": [0.109]}, "t": 95, "s": [-0.969]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.145]}, "t": 96, "s": [-0.783]}, {"i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.192]}, "t": 97, "s": [-0.532]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.215]}, "t": 98, "s": [-0.339]}, {"i": {"x": [0.833], "y": [0.88]}, "o": {"x": [0.167], "y": [0.166]}, "t": 99, "s": [-0.216]}, {"i": {"x": [0.833], "y": [0.978]}, "o": {"x": [0.167], "y": [0.271]}, "t": 100, "s": [-0.093]}, {"i": {"x": [0.833], "y": [0.673]}, "o": {"x": [0.167], "y": [-0.03]}, "t": 101, "s": [-0.038]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.112]}, "t": 102, "s": [-0.079]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.157]}, "t": 103, "s": [-0.197]}, {"i": {"x": [0.833], "y": [0.894]}, "o": {"x": [0.167], "y": [0.223]}, "t": 104, "s": [-0.332]}, {"i": {"x": [0.833], "y": [1.032]}, "o": {"x": [0.167], "y": [0.393]}, "t": 105, "s": [-0.412]}, {"i": {"x": [0.833], "y": [0.747]}, "o": {"x": [0.167], "y": [0.023]}, "t": 106, "s": [-0.434]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.124]}, "t": 107, "s": [-0.404]}, {"i": {"x": [0.833], "y": [0.907]}, "o": {"x": [0.167], "y": [0.171]}, "t": 108, "s": [-0.343]}, {"i": {"x": [0.833], "y": [0.193]}, "o": {"x": [0.167], "y": [0.768]}, "t": 109, "s": [-0.285]}, {"i": {"x": [0.833], "y": [0.632]}, "o": {"x": [0.167], "y": [0.093]}, "t": 110, "s": [-0.278]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.108]}, "t": 111, "s": [-0.218]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.159]}, "t": 112, "s": [-0.009]}, {"i": {"x": [0.833], "y": [0.899]}, "o": {"x": [0.167], "y": [0.203]}, "t": 113, "s": [0.221]}, {"i": {"x": [0.833], "y": [1.202]}, "o": {"x": [0.167], "y": [0.479]}, "t": 114, "s": [0.382]}, {"i": {"x": [0.833], "y": [0.794]}, "o": {"x": [0.167], "y": [0.059]}, "t": 115, "s": [0.416]}, {"i": {"x": [0.833], "y": [0.881]}, "o": {"x": [0.167], "y": [0.14]}, "t": 116, "s": [0.3]}, {"i": {"x": [0.833], "y": [0.973]}, "o": {"x": [0.167], "y": [0.281]}, "t": 117, "s": [0.129]}, {"i": {"x": [0.833], "y": [0.665]}, "o": {"x": [0.167], "y": [-0.04]}, "t": 118, "s": [0.057]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.111]}, "t": 119, "s": [0.106]}, {"i": {"x": [0.833], "y": [0.87]}, "o": {"x": [0.167], "y": [0.152]}, "t": 120, "s": [0.253]}, {"i": {"x": [0.833], "y": [0.971]}, "o": {"x": [0.167], "y": [0.231]}, "t": 121, "s": [0.433]}, {"i": {"x": [0.833], "y": [0.749]}, "o": {"x": [0.167], "y": [-0.045]}, "t": 122, "s": [0.534]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.125]}, "t": 123, "s": [0.468]}, {"i": {"x": [0.833], "y": [0.913]}, "o": {"x": [0.167], "y": [0.197]}, "t": 124, "s": [0.336]}, {"i": {"x": [0.833], "y": [3.095]}, "o": {"x": [0.167], "y": [2.138]}, "t": 125, "s": [0.239]}, {"i": {"x": [0.833], "y": [0.771]}, "o": {"x": [0.167], "y": [0.08]}, "t": 126, "s": [0.235]}, {"i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.131]}, "t": 127, "s": [0.338]}, {"i": {"x": [0.833], "y": [1.192]}, "o": {"x": [0.167], "y": [0.329]}, "t": 128, "s": [0.517]}, {"i": {"x": [0.833], "y": [0.763]}, "o": {"x": [0.167], "y": [0.058]}, "t": 129, "s": [0.578]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.129]}, "t": 130, "s": [0.377]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.156]}, "t": 131, "s": [0.008]}, {"i": {"x": [0.833], "y": [0.891]}, "o": {"x": [0.167], "y": [0.187]}, "t": 132, "s": [-0.416]}, {"i": {"x": [0.833], "y": [1.145]}, "o": {"x": [0.167], "y": [0.359]}, "t": 133, "s": [-0.758]}, {"i": {"x": [0.833], "y": [0.767]}, "o": {"x": [0.167], "y": [0.053]}, "t": 134, "s": [-0.861]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.13]}, "t": 135, "s": [-0.578]}, {"i": {"x": [0.833], "y": [0.865]}, "o": {"x": [0.167], "y": [0.171]}, "t": 136, "s": [-0.071]}, {"i": {"x": [0.833], "y": [0.905]}, "o": {"x": [0.167], "y": [0.219]}, "t": 137, "s": [0.411]}, {"i": {"x": [0.833], "y": [1.332]}, "o": {"x": [0.167], "y": [0.702]}, "t": 138, "s": [0.708]}, {"i": {"x": [0.833], "y": [0.762]}, "o": {"x": [0.167], "y": [0.067]}, "t": 139, "s": [0.748]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.128]}, "t": 140, "s": [0.549]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.158]}, "t": 141, "s": [0.18]}, {"i": {"x": [0.833], "y": [0.872]}, "o": {"x": [0.167], "y": [0.189]}, "t": 142, "s": [-0.234]}, {"i": {"x": [0.833], "y": [0.902]}, "o": {"x": [0.167], "y": [0.239]}, "t": 143, "s": [-0.56]}, {"i": {"x": [0.833], "y": [1.01]}, "o": {"x": [0.167], "y": [0.553]}, "t": 144, "s": [-0.734]}, {"i": {"x": [0.833], "y": [0.806]}, "o": {"x": [0.167], "y": [0.009]}, "t": 145, "s": [-0.765]}, {"i": {"x": [0.833], "y": [0.797]}, "o": {"x": [0.167], "y": [0.146]}, "t": 146, "s": [-0.731]}, {"i": {"x": [0.833], "y": [0.871]}, "o": {"x": [0.167], "y": [0.141]}, "t": 147, "s": [-0.685]}, {"i": {"x": [0.833], "y": [0.958]}, "o": {"x": [0.167], "y": [0.235]}, "t": 148, "s": [-0.619]}, {"i": {"x": [0.833], "y": [0.586]}, "o": {"x": [0.167], "y": [-0.084]}, "t": 149, "s": [-0.583]}, {"i": {"x": [0.833], "y": [0.801]}, "o": {"x": [0.167], "y": [0.104]}, "t": 150, "s": [-0.601]}, {"i": {"x": [0.833], "y": [0.905]}, "o": {"x": [0.167], "y": [0.144]}, "t": 151, "s": [-0.673]}, {"i": {"x": [0.833], "y": [1.688]}, "o": {"x": [0.167], "y": [0.694]}, "t": 152, "s": [-0.772]}, {"i": {"x": [0.833], "y": [0.792]}, "o": {"x": [0.167], "y": [0.074]}, "t": 153, "s": [-0.785]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.139]}, "t": 154, "s": [-0.66]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.171]}, "t": 155, "s": [-0.472]}, {"i": {"x": [0.833], "y": [0.938]}, "o": {"x": [0.167], "y": [0.225]}, "t": 156, "s": [-0.294]}, {"i": {"x": [0.833], "y": [0.293]}, "o": {"x": [0.167], "y": [-0.242]}, "t": 157, "s": [-0.188]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.094]}, "t": 158, "s": [-0.215]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.15]}, "t": 159, "s": [-0.417]}, {"i": {"x": [0.833], "y": [0.931]}, "o": {"x": [0.167], "y": [0.225]}, "t": 160, "s": [-0.668]}, {"i": {"x": [0.833], "y": [0.313]}, "o": {"x": [0.167], "y": [-0.386]}, "t": 161, "s": [-0.816]}, {"i": {"x": [0.833], "y": [0.801]}, "o": {"x": [0.167], "y": [0.095]}, "t": 162, "s": [-0.79]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.144]}, "t": 163, "s": [-0.6]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.148]}, "t": 164, "s": [-0.336]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.15]}, "t": 165, "s": [0.002]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.187]}, "t": 166, "s": [0.422]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.247]}, "t": 167, "s": [0.761]}, {"i": {"x": [0.833], "y": [-10.078]}, "o": {"x": [0.167], "y": [-18.239]}, "t": 168, "s": [0.934]}, {"i": {"x": [0.833], "y": [0.777]}, "o": {"x": [0.167], "y": [0.084]}, "t": 169, "s": [0.933]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.133]}, "t": 170, "s": [0.829]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.144]}, "t": 171, "s": [0.655]}, {"i": {"x": [0.833], "y": [0.871]}, "o": {"x": [0.167], "y": [0.181]}, "t": 172, "s": [0.416]}, {"i": {"x": [0.833], "y": [0.912]}, "o": {"x": [0.167], "y": [0.235]}, "t": 173, "s": [0.211]}, {"i": {"x": [0.833], "y": [1.877]}, "o": {"x": [0.167], "y": [1.621]}, "t": 174, "s": [0.099]}, {"i": {"x": [0.833], "y": [0.879]}, "o": {"x": [0.167], "y": [0.076]}, "t": 175, "s": [0.093]}, {"i": {"x": [0.833], "y": [1.159]}, "o": {"x": [0.167], "y": [0.267]}, "t": 176, "s": [0.163]}, {"i": {"x": [0.833], "y": [0.741]}, "o": {"x": [0.167], "y": [0.055]}, "t": 177, "s": [0.195]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.123]}, "t": 178, "s": [0.102]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.148]}, "t": 179, "s": [-0.093]}, {"i": {"x": [0.833], "y": [0.872]}, "o": {"x": [0.167], "y": [0.172]}, "t": 180, "s": [-0.344]}, {"i": {"x": [0.833], "y": [0.96]}, "o": {"x": [0.167], "y": [0.237]}, "t": 181, "s": [-0.582]}, {"i": {"x": [0.833], "y": [0.642]}, "o": {"x": [0.167], "y": [-0.076]}, "t": 182, "s": [-0.71]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.109]}, "t": 183, "s": [-0.643]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.145]}, "t": 184, "s": [-0.422]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.167]}, "t": 185, "s": [-0.127]}, {"i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.194]}, "t": 186, "s": [0.169]}, {"i": {"x": [0.833], "y": [0.954]}, "o": {"x": [0.167], "y": [0.323]}, "t": 187, "s": [0.39]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [-0.104]}, "t": 188, "s": [0.467]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.156]}, "t": 189, "s": [0.433]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.16]}, "t": 190, "s": [0.394]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.164]}, "t": 191, "s": [0.351]}, {"i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.169]}, "t": 192, "s": [0.306]}, {"i": {"x": [0.833], "y": [0.705]}, "o": {"x": [0.167], "y": [0.176]}, "t": 193, "s": [0.263]}, {"i": {"x": [0.833], "y": [0.772]}, "o": {"x": [0.167], "y": [0.116]}, "t": 194, "s": [0.223]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.131]}, "t": 195, "s": [0.123]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.177]}, "t": 196, "s": [-0.05]}, {"i": {"x": [0.833], "y": [0.935]}, "o": {"x": [0.167], "y": [0.246]}, "t": 197, "s": [-0.205]}, {"i": {"x": [0.833], "y": [0.439]}, "o": {"x": [0.167], "y": [-0.302]}, "t": 198, "s": [-0.284]}, {"i": {"x": [0.833], "y": [0.778]}, "o": {"x": [0.167], "y": [0.098]}, "t": 199, "s": [-0.267]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.133]}, "t": 200, "s": [-0.169]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.158]}, "t": 201, "s": [-0.005]}, {"i": {"x": [0.833], "y": [0.893]}, "o": {"x": [0.167], "y": [0.204]}, "t": 202, "s": [0.178]}, {"i": {"x": [0.833], "y": [1.023]}, "o": {"x": [0.167], "y": [0.376]}, "t": 203, "s": [0.304]}, {"i": {"x": [0.833], "y": [0.776]}, "o": {"x": [0.167], "y": [0.018]}, "t": 204, "s": [0.34]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.133]}, "t": 205, "s": [0.294]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.198]}, "t": 206, "s": [0.216]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.179]}, "t": 207, "s": [0.16]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.164]}, "t": 208, "s": [0.111]}, {"i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.156]}, "t": 209, "s": [0.06]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.155]}, "t": 210, "s": [0.002]}, {"i": {"x": [0.833], "y": [0.879]}, "o": {"x": [0.167], "y": [0.158]}, "t": 211, "s": [-0.066]}, {"i": {"x": [0.833], "y": [0.959]}, "o": {"x": [0.167], "y": [0.266]}, "t": 212, "s": [-0.141]}, {"i": {"x": [0.833], "y": [0.875]}, "o": {"x": [0.167], "y": [-0.08]}, "t": 213, "s": [-0.176]}, {"i": {"x": [0.833], "y": [1.284]}, "o": {"x": [0.167], "y": [0.251]}, "t": 214, "s": [-0.158]}, {"i": {"x": [0.833], "y": [0.697]}, "o": {"x": [0.167], "y": [0.064]}, "t": 215, "s": [-0.149]}, {"i": {"x": [0.833], "y": [0.787]}, "o": {"x": [0.167], "y": [0.115]}, "t": 216, "s": [-0.188]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.137]}, "t": 217, "s": [-0.29]}, {"i": {"x": [0.833], "y": [0.926]}, "o": {"x": [0.167], "y": [0.191]}, "t": 218, "s": [-0.448]}, {"i": {"x": [0.833], "y": [0.105]}, "o": {"x": [0.167], "y": [-0.658]}, "t": 219, "s": [-0.57]}, {"i": {"x": [0.833], "y": [0.788]}, "o": {"x": [0.167], "y": [0.092]}, "t": 220, "s": [-0.556]}, {"i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.137]}, "t": 221, "s": [-0.422]}, {"i": {"x": [0.833], "y": [0.882]}, "o": {"x": [0.167], "y": [0.169]}, "t": 222, "s": [-0.216]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.286]}, "t": 223, "s": [-0.016]}, {"i": {"x": [0.833], "y": [0.637]}, "o": {"x": [0.167], "y": [0.208]}, "t": 224, "s": [0.066]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.108]}, "t": 225, "s": [0.121]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.158]}, "t": 226, "s": [0.306]}, {"i": {"x": [0.833], "y": [0.897]}, "o": {"x": [0.167], "y": [0.201]}, "t": 227, "s": [0.511]}, {"i": {"x": [0.833], "y": [1.159]}, "o": {"x": [0.167], "y": [0.44]}, "t": 228, "s": [0.656]}, {"i": {"x": [0.833], "y": [0.704]}, "o": {"x": [0.167], "y": [0.055]}, "t": 229, "s": [0.69]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.116]}, "t": 230, "s": [0.592]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.15]}, "t": 231, "s": [0.341]}, {"i": {"x": [0.833], "y": [0.886]}, "o": {"x": [0.167], "y": [0.194]}, "t": 232, "s": [0.028]}, {"i": {"x": [0.833], "y": [0.979]}, "o": {"x": [0.167], "y": [0.311]}, "t": 233, "s": [-0.207]}, {"i": {"x": [0.833], "y": [0.728]}, "o": {"x": [0.167], "y": [-0.029]}, "t": 234, "s": [-0.292]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.12]}, "t": 235, "s": [-0.228]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.148]}, "t": 236, "s": [-0.084]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.149]}, "t": 237, "s": [0.102]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.173]}, "t": 238, "s": [0.337]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.195]}, "t": 239, "s": [0.557]}, {"i": {"x": [0.833], "y": [0.881]}, "o": {"x": [0.167], "y": [0.226]}, "t": 240, "s": [0.721]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.28]}, "t": 241, "s": [0.817]}, {"i": {"x": [0.833], "y": [0.932]}, "o": {"x": [0.167], "y": [0.161]}, "t": 242, "s": [0.857]}, {"i": {"x": [0.833], "y": [-0.79]}, "o": {"x": [0.167], "y": [-0.38]}, "t": 243, "s": [0.901]}, {"i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0.087]}, "t": 244, "s": [0.893]}, {"i": {"x": [0.833], "y": [0.804]}, "o": {"x": [0.167], "y": [0.123]}, "t": 245, "s": [0.733]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.145]}, "t": 246, "s": [0.398]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.171]}, "t": 247, "s": [-0.052]}, {"i": {"x": [0.833], "y": [0.871]}, "o": {"x": [0.167], "y": [0.195]}, "t": 248, "s": [-0.481]}, {"i": {"x": [0.833], "y": [0.948]}, "o": {"x": [0.167], "y": [0.235]}, "t": 249, "s": [-0.802]}, {"i": {"x": [0.833], "y": [0.537]}, "o": {"x": [0.167], "y": [-0.14]}, "t": 250, "s": [-0.979]}, {"i": {"x": [0.833], "y": [0.8]}, "o": {"x": [0.167], "y": [0.102]}, "t": 251, "s": [-0.913]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.143]}, "t": 252, "s": [-0.613]}, {"i": {"x": [0.833], "y": [0.885]}, "o": {"x": [0.167], "y": [0.193]}, "t": 253, "s": [-0.193]}, {"i": {"x": [0.833], "y": [0.895]}, "o": {"x": [0.167], "y": [0.303]}, "t": 254, "s": [0.127]}, {"i": {"x": [0.833], "y": [1.149]}, "o": {"x": [0.167], "y": [0.401]}, "t": 255, "s": [0.249]}, {"i": {"x": [0.833], "y": [0.735]}, "o": {"x": [0.167], "y": [0.053]}, "t": 256, "s": [0.281]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.121]}, "t": 257, "s": [0.192]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.152]}, "t": 258, "s": [-0.002]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.2]}, "t": 259, "s": [-0.239]}, {"i": {"x": [0.833], "y": [0.786]}, "o": {"x": [0.167], "y": [0.168]}, "t": 260, "s": [-0.408]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.137]}, "t": 261, "s": [-0.574]}, {"i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.188]}, "t": 262, "s": [-0.833]}, {"i": {"x": [0.833], "y": [1.012]}, "o": {"x": [0.167], "y": [0.324]}, "t": 263, "s": [-1.04]}, {"i": {"x": [0.833], "y": [0.723]}, "o": {"x": [0.167], "y": [0.011]}, "t": 264, "s": [-1.112]}, {"i": {"x": [0.833], "y": [0.768]}, "o": {"x": [0.167], "y": [0.119]}, "t": 265, "s": [-1.029]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.13]}, "t": 266, "s": [-0.838]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.141]}, "t": 267, "s": [-0.495]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.171]}, "t": 268, "s": [-0.002]}, {"i": {"x": [0.833], "y": [0.879]}, "o": {"x": [0.167], "y": [0.2]}, "t": 269, "s": [0.469]}, {"i": {"x": [0.833], "y": [0.922]}, "o": {"x": [0.167], "y": [0.265]}, "t": 270, "s": [0.804]}, {"i": {"x": [0.833], "y": [-0.174]}, "o": {"x": [0.167], "y": [-1.145]}, "t": 271, "s": [0.957]}, {"i": {"x": [0.833], "y": [0.778]}, "o": {"x": [0.167], "y": [0.09]}, "t": 272, "s": [0.947]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.133]}, "t": 273, "s": [0.811]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.159]}, "t": 274, "s": [0.584]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.177]}, "t": 275, "s": [0.332]}, {"i": {"x": [0.833], "y": [0.881]}, "o": {"x": [0.167], "y": [0.202]}, "t": 276, "s": [0.109]}, {"i": {"x": [0.833], "y": [0.996]}, "o": {"x": [0.167], "y": [0.279]}, "t": 277, "s": [-0.048]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.004]}, "t": 278, "s": [-0.114]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.112]}, "t": 279, "s": [-0.051]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.15]}, "t": 280, "s": [0.132]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.173]}, "t": 281, "s": [0.363]}, {"i": {"x": [0.833], "y": [0.901]}, "o": {"x": [0.167], "y": [0.208]}, "t": 282, "s": [0.577]}, {"i": {"x": [0.833], "y": [1.472]}, "o": {"x": [0.167], "y": [0.524]}, "t": 283, "s": [0.719]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.071]}, "t": 284, "s": [0.746]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.126]}, "t": 285, "s": [0.566]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.163]}, "t": 286, "s": [0.216]}, {"i": {"x": [0.833], "y": [0.883]}, "o": {"x": [0.167], "y": [0.197]}, "t": 287, "s": [-0.149]}, {"i": {"x": [0.833], "y": [0.968]}, "o": {"x": [0.167], "y": [0.289]}, "t": 288, "s": [-0.417]}, {"i": {"x": [0.833], "y": [0.788]}, "o": {"x": [0.167], "y": [-0.052]}, "t": 289, "s": [-0.526]}, {"i": {"x": [0.833], "y": [0.91]}, "o": {"x": [0.167], "y": [0.137]}, "t": 290, "s": [-0.459]}, {"i": {"x": [0.833], "y": [1.306]}, "o": {"x": [0.167], "y": [1.076]}, "t": 291, "s": [-0.355]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.065]}, "t": 292, "s": [-0.347]}, {"i": {"x": [0.833], "y": [0.916]}, "o": {"x": [0.167], "y": [0.161]}, "t": 293, "s": [-0.387]}, {"i": {"x": [0.833], "y": [16.181]}, "o": {"x": [0.167], "y": [7.578]}, "t": 294, "s": [-0.431]}, {"i": {"x": [0.833], "y": [0.773]}, "o": {"x": [0.167], "y": [0.083]}, "t": 295, "s": [-0.432]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.132]}, "t": 296, "s": [-0.343]}, {"i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.185]}, "t": 297, "s": [-0.189]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.21]}, "t": 298, "s": [-0.063]}, {"i": {"x": [0.833], "y": [0.871]}, "o": {"x": [0.167], "y": [0.246]}, "t": 299, "s": [0.019]}, {"i": {"x": [0.833], "y": [0.761]}, "o": {"x": [0.167], "y": [0.235]}, "t": 300, "s": [0.062]}, {"i": {"x": [0.833], "y": [0.976]}, "o": {"x": [0.167], "y": [0.128]}, "t": 301, "s": [0.085]}, {"i": {"x": [0.833], "y": [0.459]}, "o": {"x": [0.167], "y": [-0.033]}, "t": 302, "s": [0.128]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.099]}, "t": 303, "s": [0.097]}, {"i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.165]}, "t": 304, "s": [-0.074]}, {"i": {"x": [0.833], "y": [0.975]}, "o": {"x": [0.167], "y": [0.258]}, "t": 305, "s": [-0.247]}, {"i": {"x": [0.833], "y": [0.622]}, "o": {"x": [0.167], "y": [-0.036]}, "t": 306, "s": [-0.33]}, {"i": {"x": [0.833], "y": [0.771]}, "o": {"x": [0.167], "y": [0.107]}, "t": 307, "s": [-0.273]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.131]}, "t": 308, "s": [-0.069]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.156]}, "t": 309, "s": [0.287]}, {"i": {"x": [0.833], "y": [0.88]}, "o": {"x": [0.167], "y": [0.193]}, "t": 310, "s": [0.699]}, {"i": {"x": [0.833], "y": [0.934]}, "o": {"x": [0.167], "y": [0.272]}, "t": 311, "s": [1.01]}, {"i": {"x": [0.833], "y": [0.61]}, "o": {"x": [0.167], "y": [-0.31]}, "t": 312, "s": [1.147]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.106]}, "t": 313, "s": [1.118]}, {"i": {"x": [0.833], "y": [0.804]}, "o": {"x": [0.167], "y": [0.149]}, "t": 314, "s": [1.011]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.145]}, "t": 315, "s": [0.876]}, {"i": {"x": [0.833], "y": [0.863]}, "o": {"x": [0.167], "y": [0.176]}, "t": 316, "s": [0.692]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.212]}, "t": 317, "s": [0.527]}, {"i": {"x": [0.833], "y": [0.96]}, "o": {"x": [0.167], "y": [0.33]}, "t": 318, "s": [0.421]}, {"i": {"x": [0.833], "y": [1.012]}, "o": {"x": [0.167], "y": [-0.077]}, "t": 319, "s": [0.385]}, {"i": {"x": [0.833], "y": [0.367]}, "o": {"x": [0.167], "y": [0.01]}, "t": 320, "s": [0.404]}, {"i": {"x": [0.833], "y": [0.779]}, "o": {"x": [0.167], "y": [0.096]}, "t": 321, "s": [0.383]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.134]}, "t": 322, "s": [0.242]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.153]}, "t": 323, "s": [0.009]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.173]}, "t": 324, "s": [-0.269]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.225]}, "t": 325, "s": [-0.526]}, {"i": {"x": [0.833], "y": [0.79]}, "o": {"x": [0.167], "y": [0.2]}, "t": 326, "s": [-0.678]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.138]}, "t": 327, "s": [-0.787]}, {"i": {"x": [0.833], "y": [0.885]}, "o": {"x": [0.167], "y": [0.187]}, "t": 328, "s": [-0.952]}, {"i": {"x": [0.833], "y": [0.984]}, "o": {"x": [0.167], "y": [0.299]}, "t": 329, "s": [-1.085]}, {"i": {"x": [0.833], "y": [0.702]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 330, "s": [-1.137]}, {"i": {"x": [0.833], "y": [0.766]}, "o": {"x": [0.167], "y": [0.116]}, "t": 331, "s": [-1.095]}, {"i": {"x": [0.833], "y": [0.792]}, "o": {"x": [0.167], "y": [0.129]}, "t": 332, "s": [-0.987]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.139]}, "t": 333, "s": [-0.792]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.165]}, "t": 334, "s": [-0.499]}, {"i": {"x": [0.833], "y": [0.865]}, "o": {"x": [0.167], "y": [0.186]}, "t": 335, "s": [-0.2]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.219]}, "t": 336, "s": [0.042]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.343]}, "t": 337, "s": [0.191]}, {"i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0.166]}, "t": 338, "s": [0.238]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.123]}, "t": 339, "s": [0.286]}, {"i": {"x": [0.833], "y": [0.921]}, "o": {"x": [0.167], "y": [0.201]}, "t": 340, "s": [0.386]}, {"i": {"x": [0.833], "y": [-1.077]}, "o": {"x": [0.167], "y": [-1.507]}, "t": 341, "s": [0.457]}, {"i": {"x": [0.833], "y": [0.777]}, "o": {"x": [0.167], "y": [0.087]}, "t": 342, "s": [0.453]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.133]}, "t": 343, "s": [0.365]}, {"i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.158]}, "t": 344, "s": [0.215]}, {"i": {"x": [0.833], "y": [0.924]}, "o": {"x": [0.167], "y": [0.21]}, "t": 345, "s": [0.05]}, {"i": {"x": [0.833], "y": [-0.254]}, "o": {"x": [0.167], "y": [-0.872]}, "t": 346, "s": [-0.059]}, {"i": {"x": [0.833], "y": [0.788]}, "o": {"x": [0.167], "y": [0.089]}, "t": 347, "s": [-0.05]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.137]}, "t": 348, "s": [0.084]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.183]}, "t": 349, "s": [0.29]}, {"i": {"x": [0.833], "y": [-21.764]}, "o": {"x": [0.167], "y": [-25.917]}, "t": 350, "s": [0.462]}, {"i": {"x": [0.833], "y": [0.801]}, "o": {"x": [0.167], "y": [0.084]}, "t": 351, "s": [0.462]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.144]}, "t": 352, "s": [0.311]}, {"i": {"x": [0.833], "y": [0.873]}, "o": {"x": [0.167], "y": [0.177]}, "t": 353, "s": [0.104]}, {"i": {"x": [0.833], "y": [0.958]}, "o": {"x": [0.167], "y": [0.244]}, "t": 354, "s": [-0.082]}, {"i": {"x": [0.833], "y": [0.639]}, "o": {"x": [0.167], "y": [-0.086]}, "t": 355, "s": [-0.178]}, {"i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.108]}, "t": 356, "s": [-0.131]}, {"i": {"x": [0.833], "y": [0.962]}, "o": {"x": [0.167], "y": [0.219]}, "t": 357, "s": [0.028]}, {"i": {"x": [0.833], "y": [0.574]}, "o": {"x": [0.167], "y": [-0.071]}, "t": 358, "s": [0.125]}, {"i": {"x": [0.833], "y": [0.794]}, "o": {"x": [0.167], "y": [0.104]}, "t": 359, "s": [0.072]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.14]}, "t": 360, "s": [-0.144]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.176]}, "t": 361, "s": [-0.462]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.204]}, "t": 362, "s": [-0.747]}, {"t": 363, "s": [-0.943]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.165, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [291.905, 437.612, 0], "to": [0, -24.602, 0], "ti": [0, 24.602, 0]}, {"t": 10, "s": [291.905, 290, 0]}], "ix": 2}, "a": {"a": 0, "k": [137.851, 29.85, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [25, 25, 100]}, {"t": 10, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 364, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 2, "nm": " crown", "refId": "image_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\n$bm_rt = wiggle(2, 5);"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [297.261, 446.133, 0], "to": [0.123, -34.708, 0], "ti": [-0.123, 34.708, 0]}, {"t": 26, "s": [298, 237.883, 0]}], "ix": 2}, "a": {"a": 0, "k": [19.819, 31.104, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 19, "s": [0, 0, 100]}, {"t": 26, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 364, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "open", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [300, 488, 0], "ix": 2}, "a": {"a": 0, "k": [300, 400, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 600, "h": 800, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "winning-walk", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [300, 400, 0], "ix": 2}, "a": {"a": 0, "k": [300, 400, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 600, "h": 800, "ip": 5, "op": 369, "st": 5, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "flash 10", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 67, "s": [0]}, {"t": 91, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [293.061, 321.797, 0], "ix": 2}, "a": {"a": 0, "k": [15.723, 15.724, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 67, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 77, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 87, "s": [100, 100, 100]}, {"t": 92, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "ip": 67, "op": 92, "st": 39, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Light", "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [302, 397, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[53.5, 7], [-58, 9.5], [-113, 38], [105, 38]], "c": true}]}, {"t": 15, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[147, -178], [-166, -173], [-113, 38], [105, 38]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.072, 1, 1, 1, 0.536, 0.998, 0.89, 0.5, 1, 0.996, 0.78, 0, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-6.04, -91.303], "ix": 5}, "e": {"a": 0, "k": [-3.947, 31.021], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 5, "op": 101, "st": 5, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "ruban-left-anim", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [301.711, 406.899, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [-57, 57, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Turbulent Displace", "np": 16, "mn": "ADBE Turbulent Displace", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Displacement", "mn": "ADBE Turbulent Displace-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 0, "nm": "Amount", "mn": "ADBE Turbulent Displace-0002", "ix": 2, "v": {"a": 0, "k": 30, "ix": 2}}, {"ty": 0, "nm": "Size", "mn": "ADBE Turbulent Displace-0003", "ix": 3, "v": {"a": 0, "k": 100, "ix": 3}}, {"ty": 3, "nm": "Offset (Turbulence)", "mn": "ADBE Turbulent Displace-0004", "ix": 4, "v": {"a": 0, "k": [300, 400], "ix": 4}}, {"ty": 0, "nm": "Complexity", "mn": "ADBE Turbulent Displace-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 0, "nm": "Evolution", "mn": "ADBE Turbulent Displace-0006", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 6, "nm": "Evolution Options", "mn": "ADBE Turbulent Displace-0007", "ix": 7, "v": 0}, {"ty": 7, "nm": "Cycle Evolution", "mn": "ADBE Turbulent Displace-0008", "ix": 8, "v": {"a": 0, "k": 0, "ix": 8}}, {"ty": 0, "nm": "Cycle (in Revolutions)", "mn": "ADBE Turbulent Displace-0009", "ix": 9, "v": {"a": 0, "k": 1, "ix": 9}}, {"ty": 0, "nm": "Random Seed", "mn": "ADBE Turbulent Displace-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10}}, {"ty": 6, "nm": "Random Seed", "mn": "ADBE Turbulent Displace-0011", "ix": 11, "v": 0}, {"ty": 7, "nm": "<PERSON>nning", "mn": "ADBE Turbulent Displace-0012", "ix": 12, "v": {"a": 0, "k": 3, "ix": 12}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>er", "mn": "ADBE Turbulent Displace-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13}}, {"ty": 7, "nm": "Antialiasing for Best Quality", "mn": "ADBE Turbulent Displace-0014", "ix": 14, "v": {"a": 0, "k": 1, "ix": 14}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-3.509, 0]], "o": [[3.509, 0]], "v": [[197.738, 50.177]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.823529411765, 0.439215686275, 0.294117647059, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 30, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-114.035, -44.737], [29.5, 77.144], [-74.561, -44.737], [-140.489, -174.561]], "o": [[0, 0], [198.315, 77.8], [-69.437, -181.579], [74.561, 44.737], [20.301, 25.224]], "v": [[40.523, -33.313], [242.613, -142.805], [370.683, -232.279], [201.385, -113.858], [540.859, -273.507]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [0]}, {"t": 28.3076171875, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14.14, "s": [0]}, {"t": 30, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.823529411765, 0.439215686275, 0.294117647059, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 30, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 96, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "ruban-right-anim", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [301.711, 406.899, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [57, 57, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Turbulent Displace", "np": 16, "mn": "ADBE Turbulent Displace", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Displacement", "mn": "ADBE Turbulent Displace-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 0, "nm": "Amount", "mn": "ADBE Turbulent Displace-0002", "ix": 2, "v": {"a": 0, "k": 30, "ix": 2}}, {"ty": 0, "nm": "Size", "mn": "ADBE Turbulent Displace-0003", "ix": 3, "v": {"a": 0, "k": 100, "ix": 3}}, {"ty": 3, "nm": "Offset (Turbulence)", "mn": "ADBE Turbulent Displace-0004", "ix": 4, "v": {"a": 0, "k": [300, 400], "ix": 4}}, {"ty": 0, "nm": "Complexity", "mn": "ADBE Turbulent Displace-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 0, "nm": "Evolution", "mn": "ADBE Turbulent Displace-0006", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 6, "nm": "Evolution Options", "mn": "ADBE Turbulent Displace-0007", "ix": 7, "v": 0}, {"ty": 7, "nm": "Cycle Evolution", "mn": "ADBE Turbulent Displace-0008", "ix": 8, "v": {"a": 0, "k": 0, "ix": 8}}, {"ty": 0, "nm": "Cycle (in Revolutions)", "mn": "ADBE Turbulent Displace-0009", "ix": 9, "v": {"a": 0, "k": 1, "ix": 9}}, {"ty": 0, "nm": "Random Seed", "mn": "ADBE Turbulent Displace-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10}}, {"ty": 6, "nm": "Random Seed", "mn": "ADBE Turbulent Displace-0011", "ix": 11, "v": 0}, {"ty": 7, "nm": "<PERSON>nning", "mn": "ADBE Turbulent Displace-0012", "ix": 12, "v": {"a": 0, "k": 3, "ix": 12}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>er", "mn": "ADBE Turbulent Displace-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13}}, {"ty": 7, "nm": "Antialiasing for Best Quality", "mn": "ADBE Turbulent Displace-0014", "ix": 14, "v": {"a": 0, "k": 1, "ix": 14}}]}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-114.035, -44.737], [31.579, 76.316], [-74.561, -44.737], [-4.824, 32.018]], "o": [[0, 0], [198.315, 77.8], [-31.579, -76.316], [74.561, 44.737], [4.825, -32.018]], "v": [[40.523, -33.313], [169.806, -113.858], [187.35, -279.647], [111.911, -224.384], [550.508, -391.928]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 4, "s": [0]}, {"t": 23.384765625, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 9.86, "s": [0]}, {"t": 25, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.823529411765, 0.439215686275, 0.294117647059, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 30, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 2, "op": 96, "st": 2, "bm": 0}], "markers": []}