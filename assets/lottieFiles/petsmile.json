{"v": "5.6.9", "fr": 24, "ip": 0, "op": 96, "w": 600, "h": 800, "nm": "anim-petsmile", "ddd": 0, "assets": [{"id": "image_0", "w": 32, "h": 32, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAB7UlEQVRYhcWWMS8EQRTHf28KdxESCo2Gb6A+q6CgUgoFhUItV2r0Eo0PoJK4RKFTylGw2+pEIdFQK7DX7D3FDM7hbndn3f6b2ZnNzv/3dt+8t+Ahjdj0eR7AeAHAlkZMlwYAzABBKQB6wRgw0obFUgCoMA8g2HHgACpsuMuJUgCAJTcOa8T6QAE0ZBmofM6F3cECCIfA0NcCUw7q/wGSkH1gvGu5qkLDnYz/A9CQZRG26Yz+S6Na5SwrgGQxV+H0D/NOXZsac4UCJCH7PSL/TU8CgdR48AJwUR9iv3la8w+1FE5Mi7os8JwKQC8Yo8K8KzJL2KOW1fgHCHArQoOEcwm4+QagIXUVVrCNZcTTrJ8UG/S9KmdGOBAA11KDNiy62j4BDBdoHAOvwJ0qTSM0pcYl9MgBjVhXYRdlCqjmNH5R4dgoe38lZN9T4BKxAYxmMI5VODIxO70SMBUA2OR0RSbNz8ebKGsym64opS5EAO2Iqz4Qb9Im6M70wgAcxCMw+cutWJTVtJF/KHMzEvsGWt3rKhxlNc8HUONB4aRr+cXE7GTdKxcAgGlRp+MtqHDcL9sLBXBmt24aG2Uvzz65AQDE1gaA1zRdr3AAEs6xtf0u9x4+AO6siyrNUgCc7o34AXgpCTkozRw+27iX3gGtbJicGFjZiAAAAABJRU5ErkJggg==", "e": 1}, {"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Null 13", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [300, 400, 0], "to": [0, -5, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 1, "y": 0}, "t": 7.5, "s": [300, 370, 0], "to": [0, 0, 0], "ti": [0, -5, 0]}, {"t": 15, "s": [300, 400, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "board-face", "parent": 1, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [300, 400, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 600, "h": 800, "ip": 4, "op": 224, "st": 4, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "shadow", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [10]}, {"t": 24, "s": [20]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 4, "s": [100, 100, 100]}, {"t": 10, "s": [48, 100, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [109, 59.876], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.2, 0, 0, 0, 0.6, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 111], "ix": 5}, "e": {"a": 0, "k": [100, 0], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-58.5, -28.062], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 4, "op": 224, "st": 4, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "board-right", "parent": 1, "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [300, 400, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 600, "h": 800, "ip": 8, "op": 228, "st": 8, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "shadow 2", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 14, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16, "s": [10]}, {"t": 28, "s": [20]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [109, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [109, 59.876], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.2, 0, 0, 0, 0.6, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 111], "ix": 5}, "e": {"a": 0, "k": [100, 0], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-58.5, -28.062], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 8, "op": 228, "st": 8, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "box/gift.ai", "cl": "ai", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-4.255, 25.871, 0], "ix": 2}, "a": {"a": 0, "k": [108.995, 83.621, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[36.575, 79.716], [36.573, 79.716]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 2", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.104, 0], [0.095, -0.245], [-0.409, -0.197], [-0.084, 0], [-0.094, 0.053], [-0.015, 0.252], [0.049, 0.138], [0.165, 0.098]], "o": [[-0.237, 0], [-0.162, 0.424], [0.084, 0.041], [0.096, 0], [0.236, -0.133], [0.005, -0.152], [-0.066, -0.183], [-0.1, -0.058]], "v": [[-0.028, -0.804], [-0.583, -0.416], [-0.124, 0.742], [0.129, 0.804], [0.414, 0.725], [0.74, 0.12], [0.637, -0.305], [0.284, -0.718]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.906, 70.832], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.005, -0.152], [0.236, -0.132], [0.181, 0.088], [-0.162, 0.423], [-0.328, -0.192], [-0.065, -0.183]], "o": [[-0.015, 0.252], [-0.176, 0.1], [-0.409, -0.197], [0.136, -0.354], [0.165, 0.097], [0.05, 0.138]], "v": [[0.74, 0.16], [0.414, 0.764], [-0.124, 0.782], [-0.583, -0.376], [0.284, -0.678], [0.636, -0.265]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.906, 70.792], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.104, 0], [0.15, -0.098], [0.13, -0.281], [0.011, -0.023], [0.004, 0.001], [0, 0], [0.256, 0], [0.158, 0.009], [0, 0], [-0.019, -0.002], [-0.048, -0.296], [0, -0.086], [0.001, -0.732], [0.007, -0.078], [0.213, -0.035], [0.071, -0.008], [0, 0], [0, 0], [0, 0], [0.131, 0.032], [0.04, 0.198], [0, 0.065], [-0.012, 0.524], [-0.34, 0.323], [-0.156, 0.021], [-0.036, 0], [-0.042, -0.057], [0.044, -0.051], [-0.281, -0.073], [-0.037, 0], [-0.059, 0.189], [0.289, 0.115]], "o": [[-0.163, 0], [-0.261, 0.172], [-0.01, 0.024], [-0.005, -0.001], [0, 0], [-0.257, 0.078], [-0.158, 0], [0, 0], [0.025, 0.003], [0.308, 0.028], [0.013, 0.084], [0.002, 0.732], [0, 0.079], [-0.021, 0.215], [-0.072, 0.012], [0, 0], [0, 0], [0, 0], [-0.143, -0.023], [-0.188, -0.047], [-0.013, -0.064], [-0.001, -0.525], [0.01, -0.466], [0.114, -0.107], [0.036, -0.005], [0.057, 0], [-0.052, 0.048], [-0.193, 0.227], [0.038, 0.011], [0.182, 0], [0.089, -0.279], [-0.108, -0.043]], "v": [[0.789, -1.959], [0.318, -1.809], [-0.253, -1.113], [-0.285, -1.042], [-0.299, -1.046], [-0.299, -1.93], [-1.068, -1.829], [-1.541, -1.847], [-1.541, -1.701], [-1.477, -1.696], [-0.988, -1.253], [-0.972, -0.997], [-0.971, 1.201], [-0.983, 1.436], [-1.325, 1.8], [-1.541, 1.825], [-1.541, 1.959], [0.491, 1.959], [0.491, 1.834], [0.079, 1.766], [-0.278, 1.403], [-0.298, 1.208], [-0.296, -0.367], [0.235, -1.554], [0.638, -1.753], [0.747, -1.765], [0.899, -1.693], [0.749, -1.553], [0.937, -0.915], [1.051, -0.898], [1.451, -1.204], [1.107, -1.894]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.879, 77.489], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.414, 0.125], [0, 0], [-0.005, -0.002], [-0.011, 0.023], [-0.262, 0.172], [-0.276, -0.11], [0.089, -0.28], [0.227, 0.06], [-0.193, 0.227], [-0.053, 0.049], [0.093, -0.012], [0.113, -0.107], [0.011, -0.465], [-0.001, -0.525], [-0.013, -0.064], [-0.188, -0.047], [-0.144, -0.023], [0, 0], [0, 0], [0, 0], [-0.072, 0.012], [-0.021, 0.215], [0, 0.078], [0.001, 0.732], [0.014, 0.085], [0.307, 0.029], [0.025, 0.002], [0, 0]], "o": [[0, 0], [0.005, 0.002], [0.011, -0.023], [0.13, -0.281], [0.246, -0.162], [0.289, 0.115], [-0.071, 0.227], [-0.28, -0.074], [0.044, -0.051], [-0.07, -0.091], [-0.155, 0.021], [-0.341, 0.322], [-0.011, 0.525], [0, 0.066], [0.04, 0.197], [0.132, 0.033], [0, 0], [0, 0], [0, 0], [0.072, -0.008], [0.214, -0.035], [0.007, -0.078], [0.001, -0.733], [0, -0.086], [-0.047, -0.296], [-0.02, -0.001], [0, 0], [0.416, 0.024]], "v": [[-0.298, -1.907], [-0.298, -1.024], [-0.285, -1.019], [-0.251, -1.089], [0.319, -1.786], [1.109, -1.871], [1.452, -1.18], [0.938, -0.891], [0.749, -1.529], [0.901, -1.671], [0.638, -1.731], [0.236, -1.531], [-0.296, -0.344], [-0.298, 1.23], [-0.277, 1.426], [0.08, 1.788], [0.492, 1.856], [0.492, 1.981], [-1.54, 1.981], [-1.54, 1.848], [-1.324, 1.823], [-0.982, 1.459], [-0.971, 1.224], [-0.971, -0.973], [-0.988, -1.231], [-1.475, -1.673], [-1.54, -1.678], [-1.54, -1.824]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.879, 77.465], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.001, 0.014], [0, 0], [0, 0], [-0.092, -0.032], [0.036, -0.119], [0.027, -0.043], [0.082, -0.116], [0.151, -0.208], [0.015, 0.021], [0.248, 0.358], [-0.121, 0.046], [-0.063, 0.015], [0.001, 0.023], [0.001, 0.015], [0, 0], [0, 0], [-0.092, -0.131], [-0.365, -0.529], [0.014, -0.019], [0.083, -0.106], [0.26, -0.324], [0.158, -0.051], [-0.031, -0.106], [0, 0], [0, 0], [0.037, 0.011], [0.089, 0.044], [-0.048, 0.138], [-0.055, 0.076], [-0.147, 0.191], [-0.059, 0.076], [-0.005, -0.007], [-0.277, -0.4], [0.107, -0.045], [0.063, -0.016], [0, 0], [0, 0], [0, 0], [0.086, 0.109], [0.045, 0.065], [0.352, 0.502], [-0.028, 0.038], [-0.247, 0.332], [-0.298, 0.07], [0, 0.023]], "o": [[0, 0], [0, 0], [0.101, 0.023], [0.117, 0.041], [-0.014, 0.049], [-0.074, 0.121], [-0.143, 0.205], [-0.019, -0.026], [-0.248, -0.358], [-0.074, -0.107], [0.056, -0.02], [0, -0.023], [-0.001, -0.021], [0, 0], [0, 0], [0.176, 0.01], [0.369, 0.527], [0.013, 0.019], [-0.079, 0.109], [-0.256, 0.327], [-0.101, 0.125], [-0.107, 0.034], [0, 0], [0, 0], [-0.042, -0.005], [-0.096, -0.028], [-0.129, -0.065], [0.031, -0.089], [0.14, -0.196], [0.058, -0.075], [0.012, 0.012], [0.277, 0.399], [0.066, 0.095], [-0.059, 0.025], [0, 0], [0, 0], [0, 0], [-0.144, -0.03], [-0.049, -0.061], [-0.35, -0.503], [-0.028, -0.041], [0.245, -0.333], [0.17, -0.228], [0, -0.027], [-0.001, -0.021]], "v": [[1.679, -1.9], [0.486, -1.9], [0.486, -1.775], [0.777, -1.706], [0.897, -1.458], [0.84, -1.317], [0.609, -0.961], [0.167, -0.346], [0.118, -0.412], [-0.627, -1.485], [-0.554, -1.725], [-0.375, -1.769], [-0.375, -1.838], [-0.379, -1.896], [-1.794, -1.896], [-1.794, -1.755], [-1.417, -1.502], [-0.315, 0.083], [-0.32, 0.166], [-0.565, 0.487], [-1.334, 1.467], [-1.716, 1.746], [-1.8, 1.9], [-0.391, 1.9], [-0.391, 1.769], [-0.509, 1.753], [-0.795, 1.661], [-0.924, 1.332], [-0.796, 1.076], [-0.359, 0.501], [-0.183, 0.276], [-0.16, 0.3], [0.672, 1.498], [0.609, 1.718], [0.422, 1.769], [0.422, 1.897], [1.831, 1.897], [1.831, 1.759], [1.493, 1.544], [1.348, 1.358], [0.297, -0.151], [0.299, -0.255], [1.032, -1.255], [1.683, -1.77], [1.683, -1.843]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [71.711, 77.549], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.144, -0.03], [0, 0], [0, 0], [0, 0], [-0.059, 0.025], [0.065, 0.095], [0.278, 0.399], [0.012, 0.012], [0.057, -0.076], [0.14, -0.195], [0.031, -0.089], [-0.129, -0.065], [-0.096, -0.028], [-0.043, -0.005], [0, 0], [0, 0], [-0.107, 0.034], [-0.1, 0.125], [-0.256, 0.327], [-0.078, 0.109], [0.012, 0.018], [0.369, 0.527], [0.175, 0.011], [0, 0], [0, 0], [0, -0.022], [0, -0.024], [0.055, -0.021], [-0.074, -0.107], [-0.248, -0.358], [-0.02, -0.026], [-0.144, 0.204], [-0.074, 0.12], [-0.014, 0.048], [0.118, 0.041], [0.1, 0.023], [0, 0], [0, 0], [-0.001, -0.021], [0, -0.028], [0.171, -0.228], [0.245, -0.333], [-0.028, -0.041], [-0.35, -0.503], [-0.049, -0.062]], "o": [[0, 0], [0, 0], [0, 0], [0.063, -0.017], [0.106, -0.045], [-0.277, -0.4], [-0.004, -0.006], [-0.06, 0.075], [-0.147, 0.19], [-0.056, 0.077], [-0.048, 0.138], [0.089, 0.044], [0.036, 0.01], [0, 0], [0, 0], [-0.031, -0.106], [0.158, -0.05], [0.261, -0.323], [0.083, -0.106], [0.014, -0.02], [-0.366, -0.53], [-0.092, -0.131], [0, 0], [0, 0], [0.001, 0.014], [0.001, 0.023], [-0.063, 0.015], [-0.122, 0.045], [0.248, 0.358], [0.014, 0.02], [0.15, -0.209], [0.081, -0.116], [0.026, -0.043], [0.036, -0.12], [-0.091, -0.032], [0, 0], [0, 0], [0.001, 0.014], [0, 0.023], [-0.299, 0.07], [-0.246, 0.332], [-0.029, 0.038], [0.351, 0.503], [0.045, 0.064], [0.086, 0.108]], "v": [[1.831, 1.759], [1.831, 1.898], [0.421, 1.898], [0.421, 1.77], [0.609, 1.718], [0.672, 1.498], [-0.161, 0.3], [-0.183, 0.277], [-0.359, 0.502], [-0.796, 1.076], [-0.924, 1.332], [-0.795, 1.661], [-0.509, 1.754], [-0.391, 1.769], [-0.391, 1.9], [-1.8, 1.9], [-1.716, 1.746], [-1.335, 1.467], [-0.565, 0.487], [-0.32, 0.167], [-0.315, 0.084], [-1.417, -1.502], [-1.794, -1.755], [-1.794, -1.895], [-0.379, -1.895], [-0.376, -1.837], [-0.375, -1.768], [-0.554, -1.724], [-0.627, -1.485], [0.118, -0.411], [0.167, -0.345], [0.609, -0.96], [0.84, -1.317], [0.897, -1.457], [0.776, -1.706], [0.486, -1.775], [0.486, -1.9], [1.679, -1.9], [1.683, -1.843], [1.683, -1.769], [1.031, -1.255], [0.299, -0.255], [0.297, -0.151], [1.348, 1.359], [1.493, 1.545]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [71.711, 77.549], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.194, 0], [0.102, -0.021], [0.126, -0.466], [0, -0.326], [0, -0.039], [0, 0], [0, 0], [0, 0], [0, 0], [0.001, -0.932], [0.007, -0.076], [0.217, -0.036], [0.071, -0.009], [0, 0], [0, 0], [0, 0], [0.087, 0.01], [0.054, 0.229], [0.001, 0.07], [0, 0.938], [-0.002, 0.034], [0, 0], [0, 0], [0, 0], [-0.07, 0.439], [-0.331, 0], [-0.023, -0.002], [-0.067, -0.121], [-0.014, -0.23], [-0.211, -0.018], [-0.012, 0], [-0.041, 0.165], [0.236, 0.142]], "o": [[-0.101, 0], [-0.489, 0.103], [-0.086, 0.318], [-0.001, 0.03], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.932], [0, 0.076], [-0.021, 0.22], [-0.071, 0.012], [0, 0], [0, 0], [0, 0], [-0.092, -0.009], [-0.283, -0.034], [-0.016, -0.069], [-0.001, -0.937], [0, -0.026], [0, 0], [0, 0], [0, 0], [0.003, -0.453], [0.059, -0.367], [0.022, 0], [0.114, 0.007], [-0.247, 0.089], [0.012, 0.191], [0.012, 0.001], [0.18, 0], [0.059, -0.235], [-0.179, -0.107]], "v": [[0.491, -2.897], [0.187, -2.864], [-0.706, -1.971], [-0.801, -0.999], [-0.803, -0.901], [-1.404, -0.901], [-1.404, -0.756], [-0.803, -0.756], [-0.803, -0.656], [-0.803, 2.141], [-0.816, 2.369], [-1.158, 2.739], [-1.372, 2.765], [-1.372, 2.897], [0.661, 2.897], [0.661, 2.764], [0.395, 2.739], [-0.107, 2.349], [-0.13, 2.138], [-0.13, -0.675], [-0.126, -0.762], [0.672, -0.762], [0.672, -0.907], [-0.132, -0.907], [-0.078, -2.244], [0.506, -2.783], [0.574, -2.78], [0.866, -2.626], [0.553, -2.193], [0.926, -1.841], [0.962, -1.84], [1.344, -2.122], [1.053, -2.745]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [57.33, 76.549], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.247, 0.089], [0.114, 0.007], [0.062, -0.391], [0.003, -0.453], [0, 0], [0, 0], [0, 0], [0, -0.026], [-0.001, -0.937], [-0.016, -0.069], [-0.283, -0.034], [-0.092, -0.009], [0, 0], [0, 0], [0, 0], [-0.072, 0.012], [-0.021, 0.22], [0, 0.076], [0, 0.932], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.03], [-0.085, 0.318], [-0.489, 0.103], [-0.271, -0.163], [0.059, -0.235], [0.193, 0.016], [0.012, 0.192]], "o": [[-0.067, -0.121], [-0.373, -0.025], [-0.071, 0.439], [0, 0], [0, 0], [0, 0], [-0.001, 0.034], [0, 0.938], [0, 0.07], [0.054, 0.229], [0.088, 0.01], [0, 0], [0, 0], [0, 0], [0.07, -0.009], [0.217, -0.036], [0.007, -0.076], [0, -0.933], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.039], [0.001, -0.327], [0.126, -0.466], [0.3, -0.062], [0.236, 0.142], [-0.043, 0.177], [-0.211, -0.018], [-0.014, -0.23]], "v": [[0.865, -2.612], [0.573, -2.766], [-0.078, -2.229], [-0.131, -0.892], [0.672, -0.892], [0.672, -0.747], [-0.127, -0.747], [-0.131, -0.66], [-0.13, 2.152], [-0.107, 2.363], [0.394, 2.754], [0.662, 2.779], [0.662, 2.911], [-1.372, 2.911], [-1.372, 2.78], [-1.158, 2.754], [-0.815, 2.384], [-0.803, 2.156], [-0.803, -0.642], [-0.803, -0.741], [-1.403, -0.741], [-1.403, -0.887], [-0.802, -0.887], [-0.802, -0.984], [-0.707, -1.956], [0.186, -2.849], [1.053, -2.73], [1.344, -2.107], [0.927, -1.827], [0.553, -2.178]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [57.33, 76.535], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.074, 0], [0.017, 0.392], [-0.305, 0.184], [-0.145, 0.071], [-0.161, 0.182], [0, -0.022], [0.004, -0.428], [0.028, -0.041], [0.259, -0.074]], "o": [[-0.339, 0], [-0.015, -0.359], [0.138, -0.084], [0.198, -0.097], [0.003, 0.035], [0.001, 0.428], [0, 0.049], [-0.144, 0.213], [-0.082, 0.024]], "v": [[-0.455, 1.699], [-1.048, 1.068], [-0.611, 0.25], [-0.176, 0.033], [0.4, -0.31], [0.405, -0.231], [0.402, 1.053], [0.358, 1.199], [-0.221, 1.664]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.187, 0], [0.307, -0.171], [0.086, -0.111], [-0.1, -0.187], [-0.139, 0], [-0.025, 0.003], [-0.041, 0.152], [0.156, 0.109], [0.057, 0.029], [-0.099, 0.04], [-0.15, 0], [-0.06, -0.007], [-0.017, -0.37], [-0.008, -0.179], [0.164, -0.079], [0.134, -0.053], [0.261, -0.105], [0.133, -0.132], [-0.034, -0.293], [-0.163, -0.136], [-0.18, -0.014], [-0.056, 0], [-0.272, 0.417], [-0.025, 0.033], [-0.002, -0.033], [-0.21, -0.059], [-0.091, 0], [-0.205, 0.184], [0.027, 0.041], [0.045, -0.024], [0.05, -0.011], [0.022, 0], [0.024, 0.124], [0, 0.055], [0.004, 0.669], [0.021, 0.172], [0.381, 0.11]], "o": [[-0.33, 0], [-0.122, 0.069], [-0.13, 0.169], [0.075, 0.138], [0.024, 0], [0.187, -0.02], [0.051, -0.191], [-0.047, -0.032], [0.068, -0.086], [0.143, -0.057], [0.059, 0], [0.369, 0.048], [0.008, 0.179], [0.009, 0.188], [-0.129, 0.062], [-0.261, 0.103], [-0.173, 0.071], [-0.223, 0.221], [0.025, 0.205], [0.142, 0.117], [0.059, 0.005], [0.457, 0], [0.014, -0.021], [0.003, 0.054], [0.019, 0.25], [0.097, 0.027], [0.248, 0], [-0.025, -0.038], [-0.05, 0.034], [-0.047, 0.024], [-0.025, 0.005], [-0.11, 0], [-0.009, -0.054], [-0.001, -0.669], [-0.001, -0.174], [-0.047, -0.393], [-0.193, -0.056]], "v": [[-0.163, -1.995], [-1.12, -1.739], [-1.443, -1.458], [-1.467, -0.906], [-1.132, -0.721], [-1.057, -0.725], [-0.701, -0.998], [-0.874, -1.496], [-1.033, -1.58], [-0.776, -1.762], [-0.335, -1.844], [-0.155, -1.833], [0.398, -1.228], [0.407, -0.689], [0.17, -0.292], [-0.227, -0.12], [-1.012, 0.183], [-1.466, 0.496], [-1.697, 1.282], [-1.43, 1.803], [-0.938, 1.983], [-0.766, 1.99], [0.351, 1.413], [0.406, 1.34], [0.413, 1.46], [0.772, 1.955], [1.053, 1.995], [1.73, 1.713], [1.655, 1.596], [1.52, 1.686], [1.372, 1.743], [1.302, 1.751], [1.096, 1.56], [1.082, 1.394], [1.079, -0.613], [1.049, -1.135], [0.408, -1.911]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [68.081, 77.522], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 4, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.002, 0.035], [0.198, -0.097], [0.138, -0.084], [-0.015, -0.359], [-0.46, 0.131], [-0.145, 0.213], [0, 0.05], [0, 0.427]], "o": [[-0.162, 0.182], [-0.145, 0.071], [-0.305, 0.184], [0.021, 0.476], [0.259, -0.074], [0.027, -0.041], [0.004, -0.427], [0, -0.022]], "v": [[0.401, -0.306], [-0.177, 0.037], [-0.611, 0.254], [-1.048, 1.072], [-0.221, 1.668], [0.358, 1.203], [0.402, 1.056], [0.405, -0.227]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.05, 0.034], [-0.025, -0.038], [0.362, 0.101], [0.019, 0.25], [0.003, 0.055], [0.014, -0.021], [0.533, 0.043], [0.142, 0.118], [0.025, 0.205], [-0.223, 0.22], [-0.173, 0.07], [-0.261, 0.103], [-0.129, 0.062], [0.009, 0.188], [0.008, 0.179], [0.369, 0.048], [0.2, -0.079], [0.068, -0.085], [-0.047, -0.032], [0.051, -0.191], [0.187, -0.02], [0.089, 0.163], [-0.13, 0.169], [-0.122, 0.069], [-0.529, -0.152], [-0.047, -0.392], [-0.001, -0.175], [-0.001, -0.669], [-0.009, -0.055], [-0.149, 0.032], [-0.047, 0.024]], "o": [[0.027, 0.041], [-0.28, 0.252], [-0.21, -0.059], [-0.002, -0.033], [-0.025, 0.034], [-0.306, 0.468], [-0.18, -0.015], [-0.163, -0.135], [-0.034, -0.293], [0.133, -0.133], [0.261, -0.105], [0.134, -0.053], [0.164, -0.08], [-0.008, -0.18], [-0.017, -0.371], [-0.21, -0.027], [-0.099, 0.039], [0.057, 0.03], [0.156, 0.108], [-0.041, 0.151], [-0.17, 0.019], [-0.1, -0.186], [0.086, -0.111], [0.483, -0.269], [0.381, 0.111], [0.021, 0.173], [0.004, 0.669], [0, 0.055], [0.028, 0.149], [0.05, -0.011], [0.045, -0.024]], "v": [[1.655, 1.6], [1.73, 1.716], [0.771, 1.959], [0.413, 1.464], [0.405, 1.343], [0.35, 1.417], [-0.939, 1.987], [-1.43, 1.806], [-1.697, 1.285], [-1.466, 0.5], [-1.013, 0.187], [-0.227, -0.116], [0.169, -0.288], [0.406, -0.685], [0.397, -1.224], [-0.156, -1.829], [-0.776, -1.758], [-1.033, -1.577], [-0.875, -1.492], [-0.702, -0.994], [-1.057, -0.722], [-1.467, -0.903], [-1.444, -1.454], [-1.121, -1.735], [0.408, -1.908], [1.048, -1.132], [1.079, -0.609], [1.082, 1.398], [1.095, 1.564], [1.372, 1.747], [1.52, 1.69]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [68.081, 77.519], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 4, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.2, 0], [0.157, 0.196], [0.044, 0.267], [-0.11, 0.578], [-0.165, 0.198], [-0.218, 0], [-0.164, -0.229], [-0.037, -0.28], [-0.021, -0.247], [0.117, -0.368], [0.136, -0.142]], "o": [[-0.224, 0], [-0.173, -0.216], [-0.096, -0.578], [0.046, -0.248], [0.164, -0.198], [0.24, 0], [0.167, 0.236], [0.032, 0.245], [-0.013, 0.384], [-0.059, 0.186], [-0.156, 0.161]], "v": [[0.027, 1.872], [-0.577, 1.576], [-0.883, 0.841], [-0.87, -0.895], [-0.573, -1.576], [0.029, -1.871], [0.671, -1.524], [0.953, -0.74], [1.018, 0], [0.865, 1.133], [0.583, 1.631]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.255, 0], [0.117, -0.019], [0.204, -0.558], [-0.22, -0.667], [-0.453, -0.164], [-0.204, 0], [-0.102, 0.014], [-0.241, 0.524], [-0.018, 0.382], [0.041, 0.221], [0.503, 0.238]], "o": [[-0.115, 0], [-0.584, 0.097], [-0.241, 0.661], [0.15, 0.458], [0.196, 0.072], [0.101, 0], [0.566, -0.078], [0.161, -0.35], [-0.026, -0.223], [-0.101, -0.532], [-0.239, -0.112]], "v": [[0.053, -2.018], [-0.297, -1.988], [-1.508, -1.018], [-1.525, 0.986], [-0.6, 1.916], [0.001, 2.018], [0.307, 1.997], [1.533, 1.107], [1.749, 0.001], [1.669, -0.669], [0.796, -1.856]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.566, 77.549], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 4, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.013, 0.384], [0.032, 0.245], [0.167, 0.235], [0.346, -0.414], [0.047, -0.248], [-0.096, -0.578], [-0.173, -0.216], [-0.33, 0.343], [-0.058, 0.186]], "o": [[-0.021, -0.247], [-0.037, -0.28], [-0.312, -0.44], [-0.165, 0.198], [-0.11, 0.578], [0.044, 0.267], [0.297, 0.371], [0.137, -0.142], [0.117, -0.368]], "v": [[1.019, 0.005], [0.954, -0.735], [0.671, -1.518], [-0.572, -1.571], [-0.869, -0.89], [-0.882, 0.846], [-0.577, 1.581], [0.583, 1.636], [0.865, 1.138]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.026, -0.223], [0.161, -0.35], [0.565, -0.078], [0.294, 0.107], [0.15, 0.458], [-0.242, 0.662], [-0.584, 0.097], [-0.348, -0.165], [-0.101, -0.533]], "o": [[-0.018, 0.382], [-0.242, 0.524], [-0.308, 0.043], [-0.452, -0.165], [-0.22, -0.667], [0.204, -0.558], [0.376, -0.062], [0.503, 0.237], [0.041, 0.22]], "v": [[1.749, 0.006], [1.535, 1.112], [0.309, 2.002], [-0.6, 1.922], [-1.525, 0.991], [-1.507, -1.013], [-0.296, -1.983], [0.796, -1.85], [1.669, -0.663]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.566, 77.544], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 4, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.2, 0], [0.158, 0.196], [0.045, 0.266], [-0.109, 0.578], [-0.141, 0.192], [-0.234, 0], [-0.165, -0.227], [-0.037, -0.282], [-0.021, -0.249], [0.103, -0.354], [0.147, -0.153]], "o": [[-0.224, 0], [-0.174, -0.215], [-0.096, -0.579], [0.043, -0.23], [0.165, -0.225], [0.236, 0], [0.172, 0.236], [0.033, 0.248], [-0.013, 0.366], [-0.059, 0.201], [-0.157, 0.163]], "v": [[0.02, 1.87], [-0.585, 1.574], [-0.891, 0.84], [-0.878, -0.897], [-0.614, -1.536], [0.021, -1.873], [0.658, -1.533], [0.946, -0.744], [1.012, 0.004], [0.873, 1.087], [0.578, 1.627]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.317, 0], [0.071, -0.006], [0.198, -0.627], [-0.183, -0.613], [-0.501, -0.169], [-0.192, 0], [-0.104, 0.015], [-0.24, 0.518], [-0.018, 0.382], [0.041, 0.223], [0.418, 0.253]], "o": [[-0.07, 0], [-0.657, 0.055], [-0.194, 0.612], [0.15, 0.505], [0.186, 0.062], [0.104, 0], [0.56, -0.081], [0.162, -0.35], [-0.026, -0.226], [-0.087, -0.468], [-0.28, -0.17]], "v": [[0.033, -2.017], [-0.178, -2.007], [-1.549, -0.933], [-1.558, 0.913], [-0.57, 1.927], [-0.003, 2.017], [0.31, 1.994], [1.525, 1.11], [1.743, 0.004], [1.661, -0.673], [0.934, -1.78]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [54.242, 77.552], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 4, "cix": 2, "bm": 0, "ix": 14, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.013, 0.367], [0.032, 0.248], [0.172, 0.236], [0.331, -0.45], [0.044, -0.23], [-0.097, -0.579], [-0.174, -0.215], [-0.332, 0.343], [-0.059, 0.201]], "o": [[-0.021, -0.249], [-0.038, -0.282], [-0.329, -0.452], [-0.141, 0.192], [-0.11, 0.577], [0.044, 0.267], [0.3, 0.372], [0.147, -0.153], [0.103, -0.354]], "v": [[1.011, 0.005], [0.946, -0.743], [0.657, -1.532], [-0.614, -1.535], [-0.878, -0.895], [-0.89, 0.842], [-0.585, 1.575], [0.577, 1.63], [0.872, 1.088]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.026, -0.226], [0.162, -0.35], [0.56, -0.08], [0.287, 0.097], [0.151, 0.504], [-0.193, 0.612], [-0.656, 0.055], [-0.342, -0.208], [-0.087, -0.468]], "o": [[-0.018, 0.382], [-0.24, 0.518], [-0.296, 0.043], [-0.501, -0.169], [-0.182, -0.614], [0.199, -0.626], [0.394, -0.033], [0.418, 0.253], [0.041, 0.223]], "v": [[1.742, 0.005], [1.524, 1.111], [0.309, 1.995], [-0.57, 1.928], [-1.558, 0.915], [-1.549, -0.932], [-0.178, -2.006], [0.933, -1.779], [1.66, -0.671]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [54.242, 77.55], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 15", "np": 4, "cix": 2, "bm": 0, "ix": 15, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.058, 0.572], [0, 0], [0.046, -0.575], [0, 0], [-0.008, 0.044], [-0.069, 0.263], [-0.293, 0.075], [-0.201, 0], [-0.131, 0], [-0.011, -0.007], [0, 0], [0.001, -1.488], [0.008, -0.134], [0.31, -0.051], [0.128, -0.015], [0, 0], [0, 0], [0, 0], [0.042, 0.005], [0.112, 0.021], [0.033, 0.192], [0, 0.106], [0, 1.573], [-0.003, 0.042], [-0.293, -0.036], [-0.09, -0.364], [-0.034, -0.189], [-0.014, -0.111], [0, 0], [-0.022, 0], [-0.019, 0.002]], "o": [[0, 0], [0.057, 0.575], [0, 0], [0.006, -0.05], [0.05, -0.268], [0.075, -0.288], [0.198, -0.05], [0.131, 0], [0.004, 0], [0, 0], [0, 1.487], [0, 0.134], [-0.02, 0.316], [-0.126, 0.021], [0, 0], [0, 0], [0, 0], [-0.047, -0.004], [-0.113, -0.015], [-0.194, -0.037], [-0.017, -0.104], [-0.002, -1.573], [0, -0.022], [0.3, 0.016], [0.367, 0.045], [0.046, 0.186], [0.018, 0.106], [0, 0], [0.022, 0], [0.012, 0], [-0.038, -0.577]], "v": [[2.36, -2.921], [-2.36, -2.921], [-2.327, -1.199], [-2.138, -1.199], [-2.12, -1.339], [-1.965, -2.142], [-1.397, -2.686], [-0.798, -2.746], [-0.406, -2.742], [-0.387, -2.729], [-0.387, -2.636], [-0.388, 1.826], [-0.401, 2.228], [-0.851, 2.732], [-1.233, 2.778], [-1.233, 2.921], [1.234, 2.921], [1.234, 2.774], [1.102, 2.762], [0.763, 2.715], [0.423, 2.364], [0.393, 2.047], [0.391, -2.673], [0.396, -2.759], [1.286, -2.708], [1.986, -2.084], [2.097, -1.519], [2.139, -1.194], [2.217, -1.194], [2.283, -1.194], [2.326, -1.198]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [61.343, 76.527], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 16", "np": 2, "cix": 2, "bm": 0, "ix": 16, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.038, -0.577], [0.012, 0], [0.053, 0], [0.018, 0.106], [0.046, 0.186], [0.367, 0.045], [0.3, 0.016], [0, -0.022], [-0.002, -1.573], [-0.017, -0.104], [-0.194, -0.037], [-0.114, -0.015], [-0.047, -0.004], [0, 0], [0, 0], [0, 0], [-0.126, 0.021], [-0.02, 0.317], [0, 0.134], [0, 1.487], [0, 0], [0.004, 0], [0.327, -0.083], [0.075, -0.288], [0.05, -0.268], [0.006, -0.05], [0, 0], [0.057, 0.575]], "o": [[-0.058, 0.572], [-0.019, 0.002], [-0.046, 0], [-0.014, -0.111], [-0.034, -0.189], [-0.09, -0.364], [-0.293, -0.036], [-0.003, 0.042], [0, 1.573], [0, 0.106], [0.033, 0.192], [0.112, 0.021], [0.042, 0.005], [0, 0], [0, 0], [0, 0], [0.128, -0.015], [0.31, -0.051], [0.008, -0.134], [0.001, -1.488], [0, 0], [-0.011, -0.007], [-0.331, 0], [-0.293, 0.075], [-0.069, 0.263], [-0.008, 0.044], [0, 0], [0.046, -0.575], [0, 0]], "v": [[2.36, -2.921], [2.326, -1.198], [2.283, -1.194], [2.139, -1.194], [2.097, -1.519], [1.986, -2.084], [1.286, -2.708], [0.396, -2.759], [0.391, -2.673], [0.393, 2.047], [0.423, 2.364], [0.763, 2.715], [1.102, 2.762], [1.234, 2.774], [1.234, 2.921], [-1.233, 2.921], [-1.233, 2.778], [-0.851, 2.732], [-0.401, 2.228], [-0.388, 1.826], [-0.387, -2.636], [-0.387, -2.729], [-0.406, -2.742], [-1.397, -2.686], [-1.965, -2.142], [-2.12, -1.339], [-2.138, -1.199], [-2.327, -1.199], [-2.36, -2.921]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [61.343, 76.527], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 17", "np": 2, "cix": 2, "bm": 0, "ix": 17, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.053, 0], [0.073, -0.002], [0.086, -0.006], [0.18, -0.015], [0.021, -0.007], [0.026, -0.006], [0.004, 0], [0.015, 0], [0.003, 0], [0.014, 0], [0, 0], [0.039, 0], [0.021, -0.004], [0.309, -0.151], [-0.045, 0], [-0.042, 0.042], [-0.036, 0], [-0.036, 0.022], [-0.012, 0], [-0.051, 0], [-0.017, 0.001], [-0.246, 0], [0, 0], [-0.243, -0.008], [-0.175, -0.044], [-0.082, -0.011], [-0.021, 0], [-0.046, 0], [0.015, -0.006], [-0.155, -0.037], [-0.004, 0], [-0.013, 0], [-0.006, -0.002], [-0.076, -0.022], [-0.061, -0.07], [-0.009, -0.004], [-0.216, -0.09], [-0.035, -0.021], [-0.14, -0.084], [-0.059, -0.04], [-0.092, -0.069], [-0.039, -0.014], [-0.132, -0.023], [-0.153, -0.099], [-0.103, -0.058], [-0.081, -0.049], [-0.121, -0.089], [-0.037, -0.004], [-0.043, -0.015], [-0.087, 0], [-0.025, 0.01], [-0.012, -0.004], [-0.05, -0.012], [-0.037, -0.015], [-0.02, -0.005], [-0.092, -0.019], [-0.043, -0.011], [-0.038, -0.007], [-0.118, -0.026], [-0.121, -0.022], [-0.14, 0], [0, 0], [-0.106, 0.015], [-0.004, 0], [-0.015, -0.002], [-0.042, 0], [-0.004, 0], [-0.067, 0.007], [-0.164, 0.026], [-0.07, 0.029], [-0.016, 0], [-0.044, 0.02], [-0.004, 0], [0, 0], [-0.014, 0], [-0.097, 0.068], [-0.006, 0], [-0.003, 0], [-0.004, 0], [-0.072, 0.021], [-0.063, 0.022], [-0.135, 0.047], [-0.113, 0.03], [-0.003, 0.066], [0.016, 0], [0.009, -0.002], [0.059, -0.014], [0.255, -0.026], [0.204, 0], [0.01, 0], [0.024, 0], [0.137, -0.007], [0.061, -0.004], [0.003, 0], [0.022, 0.005], [0.028, 0.003], [0.031, 0], [0.058, 0], [0.04, 0.009], [0.228, 0], [0.026, 0], [0.001, 0], [0.023, 0.012], [0.006, 0.001], [0.081, 0.015], [0.055, 0.016], [0.08, 0.025], [0.038, 0.013], [0.083, 0.029], [0.033, 0.031], [0.101, 0.048], [0.199, 0.091], [0.118, 0.061], [0.068, 0.042], [0, 0], [0.076, 0.062], [0.097, 0.1], [0.045, 0.029], [0.052, 0.043], [0.109, 0.015], [0.006, 0.009], [0.128, 0.06], [0.223, 0.088], [0.043, 0.006], [0.019, 0.005], [0.135, 0.03], [0.019, 0.007], [0.087, 0], [0.029, -0.008], [0.003, 0], [0.019, 0.007], [0.028, 0.005], [0.105, 0.008], [0.26, 0.012]], "o": [[-0.073, 0], [-0.086, 0.001], [-0.179, 0.014], [-0.022, 0.002], [-0.025, 0.007], [-0.004, 0.001], [-0.015, 0], [-0.002, 0], [-0.014, 0.001], [0, 0], [-0.04, 0], [-0.022, 0], [-0.33, 0.063], [0.018, 0.032], [0.055, 0], [0.036, 0.012], [0.037, 0], [0.01, -0.006], [0.051, 0], [0.016, 0], [0.245, -0.011], [0, 0], [0.243, 0.001], [0.179, 0.006], [0.08, 0.019], [0.019, 0.002], [0.034, 0], [-0.033, 0.011], [0.158, 0.039], [0.005, 0.001], [0.014, 0], [0.005, 0], [0.074, 0.028], [0.078, 0.022], [0.006, 0.007], [0.216, 0.091], [0.039, 0.016], [0.14, 0.083], [0.061, 0.038], [0.096, 0.064], [0.035, 0.025], [0.121, 0.043], [0.113, 0.153], [0.1, 0.064], [0.083, 0.047], [0.125, 0.075], [0.026, 0.019], [0.059, 0.006], [0.083, 0.028], [0.025, 0], [0.01, 0.026], [0.049, 0.014], [0.038, 0.009], [0.019, 0.008], [0.09, 0.023], [0.044, 0.009], [0.039, 0.009], [0.119, 0.023], [0.119, 0.025], [0.136, 0.024], [0, 0], [0.105, -0.001], [0.005, -0.001], [0.015, 0], [0.042, 0.005], [0.004, 0], [0.067, -0.004], [0.166, -0.019], [0.064, -0.009], [0.025, 0.014], [0.018, 0], [0.004, -0.002], [0, 0], [0.014, 0.001], [0.115, 0], [0.004, -0.003], [0.003, 0], [0.004, 0], [0.074, -0.014], [0.064, -0.019], [0.136, -0.047], [0.111, -0.039], [0.072, -0.02], [-0.019, 0], [-0.01, 0], [-0.06, 0.014], [-0.248, 0.061], [-0.202, 0.02], [-0.011, 0], [-0.025, 0], [-0.137, 0], [-0.061, 0.003], [-0.002, 0.001], [-0.021, 0], [-0.028, -0.007], [-0.032, -0.005], [-0.058, 0], [-0.041, 0], [-0.223, -0.044], [-0.027, 0], [-0.001, 0.001], [-0.024, 0], [-0.007, -0.004], [-0.081, -0.018], [-0.057, -0.011], [-0.081, -0.022], [-0.039, -0.012], [-0.083, -0.031], [-0.051, -0.018], [-0.088, -0.083], [-0.197, -0.095], [-0.121, -0.056], [-0.07, -0.036], [0, 0], [-0.087, -0.044], [-0.104, -0.086], [-0.036, -0.038], [-0.058, -0.036], [-0.081, -0.066], [-0.012, -0.002], [-0.089, -0.125], [-0.217, -0.101], [-0.035, -0.014], [-0.01, -0.007], [-0.134, -0.031], [-0.019, -0.005], [-0.081, -0.03], [-0.027, 0], [-0.003, 0.001], [-0.019, 0], [-0.027, -0.009], [-0.104, -0.017], [-0.26, -0.018], [-0.054, -0.002]], "v": [[-4.244, -1.65], [-4.463, -1.647], [-4.721, -1.633], [-5.258, -1.59], [-5.323, -1.58], [-5.397, -1.551], [-5.41, -1.55], [-5.456, -1.554], [-5.463, -1.554], [-5.505, -1.539], [-5.515, -1.539], [-5.634, -1.543], [-5.699, -1.538], [-6.677, -1.297], [-6.574, -1.25], [-6.415, -1.314], [-6.307, -1.294], [-6.197, -1.324], [-6.161, -1.333], [-6.007, -1.331], [-5.957, -1.332], [-5.221, -1.367], [-5.215, -1.367], [-4.487, -1.329], [-3.956, -1.265], [-3.713, -1.221], [-3.654, -1.218], [-3.538, -1.22], [-3.598, -1.199], [-3.13, -1.085], [-3.117, -1.084], [-3.077, -1.087], [-3.06, -1.084], [-2.84, -0.996], [-2.612, -0.916], [-2.585, -0.903], [-1.939, -0.63], [-1.817, -0.597], [-1.401, -0.338], [-1.209, -0.237], [-0.93, -0.03], [-0.824, 0.042], [-0.511, 0.244], [-0.07, 0.567], [0.246, 0.731], [0.486, 0.885], [0.881, 1.075], [0.99, 1.091], [1.117, 1.176], [1.367, 1.254], [1.443, 1.24], [1.469, 1.305], [1.62, 1.333], [1.733, 1.372], [1.788, 1.407], [2.061, 1.47], [2.194, 1.489], [2.304, 1.53], [2.661, 1.592], [3.022, 1.62], [3.439, 1.65], [3.49, 1.65], [3.807, 1.606], [3.821, 1.605], [3.866, 1.61], [3.992, 1.626], [4.003, 1.626], [4.203, 1.599], [4.701, 1.542], [4.895, 1.464], [4.949, 1.486], [5.032, 1.457], [5.045, 1.452], [5.046, 1.452], [5.087, 1.454], [5.405, 1.354], [5.422, 1.35], [5.431, 1.35], [5.442, 1.35], [5.663, 1.308], [5.849, 1.23], [6.259, 1.097], [6.589, 0.971], [6.677, 0.848], [6.625, 0.847], [6.598, 0.849], [6.42, 0.896], [5.669, 1.051], [5.067, 1.121], [5.036, 1.121], [4.961, 1.12], [4.55, 1.135], [4.367, 1.156], [4.36, 1.157], [4.295, 1.144], [4.212, 1.121], [4.118, 1.115], [3.943, 1.12], [3.822, 1.109], [3.148, 1.016], [3.069, 1.017], [3.067, 1.018], [2.995, 0.989], [2.982, 0.961], [2.738, 0.911], [2.566, 0.883], [2.328, 0.802], [2.208, 0.779], [1.961, 0.68], [1.806, 0.642], [1.502, 0.498], [0.905, 0.226], [0.546, 0.052], [0.341, -0.071], [0.339, -0.07], [0.089, -0.222], [-0.254, -0.429], [-0.386, -0.521], [-0.56, -0.627], [-0.827, -0.777], [-0.86, -0.794], [-1.219, -1.024], [-1.884, -1.296], [-2.004, -1.314], [-2.048, -1.347], [-2.451, -1.435], [-2.507, -1.455], [-2.756, -1.518], [-2.84, -1.507], [-2.849, -1.506], [-2.909, -1.524], [-2.989, -1.557], [-3.302, -1.601], [-4.083, -1.647]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.234, 79.787], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 18", "np": 2, "cix": 2, "bm": 0, "ix": 18, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.071, -0.036], [-0.12, -0.056], [-0.197, -0.095], [-0.088, -0.083], [-0.05, -0.017], [-0.084, -0.031], [-0.038, -0.012], [-0.08, -0.022], [-0.057, -0.011], [-0.081, -0.018], [-0.008, -0.004], [-0.024, 0.002], [-0.248, -0.049], [-0.129, -0.018], [-0.029, -0.007], [-0.024, 0.002], [-0.062, 0.002], [-0.162, -0.004], [-0.211, 0.021], [-0.249, 0.061], [-0.06, 0.014], [-0.03, 0], [0.073, -0.02], [0.111, -0.039], [0.136, -0.047], [0.064, -0.018], [0.074, -0.013], [0.009, -0.007], [0.131, 0.008], [0.005, -0.002], [0.052, 0.031], [0.064, -0.009], [0.166, -0.018], [0.068, -0.004], [0.046, 0.006], [0.019, -0.003], [0.106, -0.001], [0.153, 0.028], [0.119, 0.025], [0.12, 0.023], [0.038, 0.009], [0.044, 0.009], [0.091, 0.023], [0.02, 0.009], [0.039, 0.009], [0.049, 0.015], [0.01, 0.026], [0.106, 0.035], [0.059, 0.005], [0.026, 0.019], [0.125, 0.075], [0.083, 0.047], [0.099, 0.064], [0.113, 0.154], [0.121, 0.043], [0.034, 0.025], [0.096, 0.064], [0.062, 0.038], [0.14, 0.083], [0.039, 0.017], [0.215, 0.091], [0.007, 0.007], [0.078, 0.022], [0.074, 0.028], [0.022, 0.005], [0.158, 0.039], [-0.033, 0.011], [0.051, 0.006], [0.08, 0.019], [0.18, 0.006], [0.243, 0], [0.248, -0.011], [0.068, 0], [0.011, -0.006], [0.074, 0.023], [0.039, 0.07], [-0.329, 0.063], [-0.065, 0.001], [-0.014, 0.001], [-0.021, 0.004], [-0.024, 0.007], [-0.022, 0.001], [-0.179, 0.014], [-0.086, 0.001], [-0.126, -0.006], [-0.26, -0.018], [-0.104, -0.017], [-0.027, -0.009], [-0.02, 0.005], [-0.108, -0.039], [-0.019, -0.005], [-0.133, -0.031], [-0.01, -0.007], [-0.036, -0.014], [-0.216, -0.101], [-0.088, -0.125], [-0.012, -0.001], [-0.081, -0.066], [-0.057, -0.036], [-0.037, -0.038], [-0.104, -0.086], [-0.088, -0.043]], "o": [[0.068, 0.042], [0.118, 0.061], [0.199, 0.091], [0.1, 0.048], [0.033, 0.031], [0.084, 0.03], [0.037, 0.013], [0.08, 0.025], [0.056, 0.016], [0.082, 0.015], [0.006, 0.002], [0.023, 0.012], [0.256, -0.013], [0.13, 0.026], [0.028, 0.004], [0.023, 0.006], [0.061, -0.004], [0.162, -0.008], [0.215, 0.005], [0.256, -0.026], [0.059, -0.014], [0.024, -0.005], [-0.002, 0.066], [-0.113, 0.03], [-0.135, 0.047], [-0.064, 0.022], [-0.072, 0.021], [-0.012, 0.003], [-0.107, 0.076], [-0.004, 0], [-0.084, 0.038], [-0.071, 0.029], [-0.165, 0.026], [-0.067, 0.008], [-0.045, 0.002], [-0.019, -0.002], [-0.106, 0.014], [-0.156, 0.001], [-0.12, -0.022], [-0.118, -0.026], [-0.037, -0.008], [-0.043, -0.011], [-0.092, -0.019], [-0.02, -0.005], [-0.037, -0.015], [-0.049, -0.012], [-0.012, -0.003], [-0.118, 0.044], [-0.044, -0.015], [-0.037, -0.004], [-0.12, -0.089], [-0.081, -0.049], [-0.104, -0.058], [-0.153, -0.099], [-0.133, -0.022], [-0.039, -0.014], [-0.093, -0.07], [-0.06, -0.04], [-0.139, -0.085], [-0.035, -0.021], [-0.215, -0.089], [-0.01, -0.004], [-0.061, -0.071], [-0.075, -0.022], [-0.02, -0.008], [-0.155, -0.037], [0.014, -0.006], [-0.073, 0], [-0.081, -0.011], [-0.175, -0.044], [-0.242, -0.008], [-0.248, -0.001], [-0.068, 0.004], [-0.012, 0], [-0.071, 0.042], [-0.076, 0.076], [0.309, -0.151], [0.063, -0.012], [0.014, 0], [0.022, -0.002], [0.025, -0.006], [0.021, -0.007], [0.179, -0.015], [0.085, -0.006], [0.127, -0.003], [0.26, 0.012], [0.105, 0.008], [0.028, 0.005], [0.022, 0.009], [0.12, -0.032], [0.019, 0.007], [0.134, 0.03], [0.02, 0.005], [0.043, 0.006], [0.223, 0.088], [0.128, 0.059], [0.007, 0.009], [0.109, 0.016], [0.051, 0.043], [0.046, 0.029], [0.096, 0.1], [0.076, 0.062], [0, 0]], "v": [[0.342, -0.07], [0.546, 0.053], [0.904, 0.227], [1.503, 0.499], [1.807, 0.643], [1.96, 0.68], [2.208, 0.78], [2.327, 0.803], [2.566, 0.884], [2.738, 0.912], [2.982, 0.962], [2.995, 0.99], [3.07, 1.018], [3.822, 1.11], [4.212, 1.121], [4.296, 1.145], [4.367, 1.157], [4.551, 1.136], [5.036, 1.122], [5.669, 1.052], [6.421, 0.897], [6.598, 0.85], [6.676, 0.849], [6.589, 0.972], [6.258, 1.098], [5.85, 1.231], [5.663, 1.308], [5.441, 1.35], [5.404, 1.355], [5.046, 1.453], [5.031, 1.458], [4.895, 1.465], [4.701, 1.543], [4.204, 1.599], [4.003, 1.627], [3.865, 1.61], [3.807, 1.607], [3.49, 1.651], [3.021, 1.62], [2.661, 1.593], [2.303, 1.531], [2.195, 1.49], [2.061, 1.471], [1.788, 1.408], [1.732, 1.373], [1.62, 1.334], [1.469, 1.305], [1.443, 1.241], [1.117, 1.177], [0.99, 1.092], [0.88, 1.076], [0.486, 0.886], [0.247, 0.732], [-0.07, 0.568], [-0.51, 0.244], [-0.824, 0.043], [-0.93, -0.029], [-1.208, -0.236], [-1.402, -0.337], [-1.818, -0.596], [-1.939, -0.63], [-2.585, -0.902], [-2.613, -0.915], [-2.84, -0.995], [-3.061, -1.083], [-3.13, -1.084], [-3.598, -1.198], [-3.538, -1.219], [-3.714, -1.22], [-3.956, -1.264], [-4.488, -1.328], [-5.215, -1.365], [-5.958, -1.331], [-6.161, -1.332], [-6.197, -1.323], [-6.415, -1.313], [-6.676, -1.296], [-5.699, -1.537], [-5.505, -1.538], [-5.462, -1.553], [-5.396, -1.55], [-5.324, -1.579], [-5.258, -1.589], [-4.721, -1.632], [-4.464, -1.646], [-4.083, -1.646], [-3.303, -1.6], [-2.99, -1.556], [-2.908, -1.523], [-2.84, -1.506], [-2.507, -1.454], [-2.451, -1.434], [-2.049, -1.346], [-2.003, -1.313], [-1.883, -1.295], [-1.219, -1.023], [-0.861, -0.793], [-0.827, -0.777], [-0.559, -0.626], [-0.387, -0.52], [-0.253, -0.428], [0.089, -0.221], [0.339, -0.07]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.234, 79.786], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 19", "np": 2, "cix": 2, "bm": 0, "ix": 19, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-0.098, -0.276], [-0.002, -0.008], [-0.57, -1.672], [-0.055, 0], [-0.034, 0.004], [-0.444, 1.421], [-0.478, -1.398], [-0.017, 0], [-0.03, 0.095], [0, 0], [-0.286, 0.893], [-0.341, 0.102], [-0.002, 0.015], [0, 0.037], [0, 0], [0, 0], [-0.092, -0.016], [0.02, -0.296], [0.057, -0.184], [0.336, -1.042], [0.017, -0.045], [0.006, 0.018], [0.391, 1.201], [0.015, 0.104], [-0.175, 0.036], [-0.113, 0.017], [0, 0], [0, 0], [0, 0], [-0.084, -0.179], [-0.017, -0.048], [-0.066, -0.204], [0.014, -0.044], [0.32, -1.001], [0.015, -0.041], [0.008, 0.024], [0.352, 1.068], [0.057, 0.235], [-0.174, 0.041], [-0.06, 0.011]], "o": [[0, 0], [0, 0], [0.329, 0.032], [0.003, 0.007], [0.571, 1.671], [0.031, 0.09], [0.019, 0], [0.437, -1.399], [0.486, 1.423], [0.025, 0.001], [0.07, 0], [0, 0], [0.283, -0.893], [0.1, -0.313], [0.014, -0.005], [0.004, -0.039], [0, 0], [0, 0], [0.099, 0.012], [0.293, 0.05], [-0.013, 0.192], [-0.329, 1.043], [-0.009, 0.031], [-0.013, -0.034], [-0.393, -1.201], [-0.033, -0.1], [-0.024, -0.178], [0.111, -0.022], [0, 0], [0, 0], [0, 0], [0.217, 0.03], [0.021, 0.046], [0.071, 0.202], [0.014, 0.045], [-0.317, 1.002], [-0.008, 0.025], [-0.014, -0.043], [-0.354, -1.067], [-0.076, -0.23], [-0.042, -0.175], [0.06, -0.014], [0, 0]], "v": [[-2.016, -2.917], [-3.79, -2.917], [-3.79, -2.77], [-3.221, -2.233], [-3.214, -2.211], [-1.504, 2.803], [-1.41, 2.917], [-1.331, 2.911], [-0.012, -1.309], [1.432, 2.914], [1.494, 2.915], [1.6, 2.803], [2.296, 0.598], [3.145, -2.083], [3.753, -2.758], [3.786, -2.8], [3.787, -2.917], [2.223, -2.917], [2.223, -2.774], [2.507, -2.738], [2.915, -2.243], [2.807, -1.671], [1.803, 1.454], [1.764, 1.561], [1.736, 1.492], [0.559, -2.111], [0.488, -2.421], [0.705, -2.725], [1.042, -2.775], [1.042, -2.917], [-0.81, -2.917], [-0.81, -2.77], [-0.395, -2.414], [-0.338, -2.273], [-0.129, -1.665], [-0.128, -1.52], [-1.085, 1.484], [-1.118, 1.575], [-1.148, 1.484], [-2.208, -1.718], [-2.411, -2.415], [-2.199, -2.74], [-2.016, -2.773]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.182, 76.583], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 20", "np": 2, "cix": 2, "bm": 0, "ix": 20, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.004, -0.039], [0.014, -0.004], [0.101, -0.313], [0.283, -0.893], [0, 0], [0.13, 0.006], [0.486, 1.423], [0.437, -1.399], [0.042, 0.122], [0.57, 1.671], [0.003, 0.007], [0.33, 0.032], [0, 0], [0, 0], [0, 0], [0.06, -0.014], [-0.042, -0.175], [-0.076, -0.23], [-0.354, -1.067], [-0.014, -0.043], [-0.008, 0.025], [-0.317, 1.002], [0.014, 0.045], [0.07, 0.202], [0.022, 0.046], [0.218, 0.03], [0, 0], [0, 0], [0, 0], [0.111, -0.022], [-0.024, -0.178], [-0.033, -0.1], [-0.393, -1.2], [-0.014, -0.034], [-0.01, 0.031], [-0.33, 1.043], [-0.013, 0.192], [0.293, 0.05], [0.099, 0.012], [0, 0]], "o": [[0, 0.037], [-0.002, 0.015], [-0.34, 0.103], [-0.285, 0.893], [0, 0], [-0.037, 0.117], [-0.478, -1.398], [-0.444, 1.421], [-0.131, 0.015], [-0.57, -1.672], [-0.002, -0.008], [-0.098, -0.276], [0, 0], [0, 0], [0, 0], [-0.06, 0.011], [-0.174, 0.041], [0.056, 0.235], [0.351, 1.068], [0.008, 0.024], [0.015, -0.041], [0.32, -1.001], [0.014, -0.044], [-0.066, -0.204], [-0.018, -0.048], [-0.084, -0.179], [0, 0], [0, 0], [0, 0], [-0.112, 0.017], [-0.174, 0.036], [0.014, 0.104], [0.391, 1.201], [0.006, 0.019], [0.017, -0.045], [0.336, -1.042], [0.057, -0.184], [0.02, -0.296], [-0.093, -0.016], [0, 0], [0, 0]], "v": [[3.788, -2.921], [3.787, -2.805], [3.753, -2.764], [3.145, -2.088], [2.296, 0.593], [1.6, 2.798], [1.433, 2.909], [-0.011, -1.314], [-1.33, 2.906], [-1.503, 2.799], [-3.213, -2.216], [-3.22, -2.238], [-3.791, -2.775], [-3.791, -2.921], [-2.016, -2.921], [-2.016, -2.778], [-2.198, -2.744], [-2.411, -2.42], [-2.208, -1.723], [-1.147, 1.479], [-1.117, 1.57], [-1.084, 1.48], [-0.127, -1.524], [-0.128, -1.67], [-0.337, -2.278], [-0.395, -2.419], [-0.81, -2.775], [-0.81, -2.921], [1.042, -2.921], [1.042, -2.78], [0.705, -2.73], [0.488, -2.425], [0.56, -2.116], [1.737, 1.486], [1.765, 1.556], [1.804, 1.449], [2.807, -1.676], [2.915, -2.248], [2.508, -2.743], [2.223, -2.779], [2.223, -2.921]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.182, 76.588], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 21", "np": 2, "cix": 2, "bm": 0, "ix": 21, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.027, 0], [0.028, -0.033], [0.022, -0.062], [0.047, -0.147], [-0.168, -0.232], [-0.21, -0.131], [-0.124, -0.088], [-0.017, -0.273], [0.044, -0.236], [0.302, -0.445], [0.296, -0.133], [0.127, 0], [0.12, 0.081], [0.07, 0.03], [0.037, 0.006], [0.009, 0], [0.031, -0.048], [-0.033, -0.047], [-0.056, -0.04], [-0.175, -0.071], [-0.208, 0], [-0.235, 0.157], [-0.12, 0.084], [-0.161, 0.088], [-0.075, 0], [-0.107, -0.083], [-0.063, -0.154], [-0.013, -0.297], [-0.009, -0.345], [-0.08, -0.101], [-0.048, 0], [-0.036, 0.059], [-0.012, 0.048], [0.051, 0.473], [0.061, 0.578], [-0.089, 0.466], [-0.039, 0.147], [-0.008, -0.001], [-0.021, -0.055], [-0.265, -0.288], [-0.131, -0.12], [-0.077, 0], [-0.025, 0.007], [-0.01, 0], [-0.022, -0.025], [-0.272, -0.287], [-0.601, -0.297], [-0.013, -0.012], [0.027, -0.003], [0.053, 0], [0, 0], [0.429, 0.205], [0.036, 0.038], [0.126, 0.264], [-0.192, 0.089], [0.004, 0.012], [0.022, 0.006], [0.009, 0], [0.019, -0.006], [-0.047, -0.196], [-0.056, -0.088], [0.02, -0.371], [0.035, -0.22], [0.101, -0.104], [0.063, -0.054], [0.15, -0.12], [-0.025, -0.077], [-0.06, 0], [-0.019, 0.007], [-0.101, 0.075], [-0.223, 0.179], [-0.094, -0.239], [-0.066, -0.124], [-0.032, 0], [0, 0], [0.034, 0.108], [0.047, 0.115], [0.079, 0.193], [-0.015, 0], [-0.008, -0.002], [-0.304, -0.077], [-0.155, -0.062], [0.035, -0.061], [0.038, -0.06], [0.085, -0.149], [-0.001, -0.066], [-0.04, -0.025], [-0.003, 0], [-0.02, 0.026], [-0.11, 0.145], [-0.123, 0.156], [-0.066, -0.089], [-0.154, -0.208], [-0.068, -0.072], [-0.02, 0], [-0.019, 0.017], [0.01, 0.027], [0.034, 0.052], [0.137, 0.185], [-0.021, 0.224], [-0.013, 0.135], [-0.128, 0.212], [-0.056, 0], [-0.091, -0.047], [-0.002, -0.001], [-0.134, 0], [-0.157, 0.145], [0.213, 0.146], [0.353, 0.247], [0.006, 0.024], [0.033, 0.125], [0.015, 0.037], [0.009, -0.001], [0.03, -0.132], [0.044, 0], [0.033, 0.026], [0.043, 0.046], [0.083, 0.086], [0.006, -0.004], [-0.041, -0.14], [0.079, -0.171], [0.15, -0.133], [0.043, 0], [0.031, 0.014], [0.413, 0.411], [0.339, 0.354], [-0.009, 0.022], [0.044, 0.06], [0.054, 0.06], [0.076, 0.118], [0.076, 0.35], [0.031, 0.107], [0.54, 0.108], [0.459, 0.076], [0.203, 0.117], [-0.097, 0.251], [-0.016, 0.092], [0.066, 0.029]], "o": [[-0.037, 0], [-0.043, 0.052], [-0.055, 0.144], [-0.086, 0.277], [0.147, 0.204], [0.129, 0.08], [0.218, 0.157], [0.014, 0.242], [-0.099, 0.525], [-0.179, 0.263], [-0.128, 0.057], [-0.121, 0], [-0.062, -0.042], [-0.034, -0.015], [-0.01, -0.002], [-0.054, 0], [-0.036, 0.054], [0.04, 0.058], [0.156, 0.112], [0.21, 0.086], [0.248, 0], [0.122, -0.082], [0.151, -0.105], [0.078, -0.042], [0.117, 0], [0.134, 0.105], [0.115, 0.278], [0.016, 0.344], [0.003, 0.132], [0.038, 0.049], [0.054, 0], [0.027, -0.042], [0.118, -0.461], [-0.061, -0.579], [-0.05, -0.471], [0.028, -0.149], [0.009, 0], [0.023, 0.055], [0.134, 0.363], [0.119, 0.13], [0.057, 0.053], [0.025, 0], [0.012, -0.003], [0.029, 0], [0.269, 0.29], [0.457, 0.483], [0.01, 0.005], [-0.035, 0.008], [-0.053, 0.007], [0, 0], [-0.482, 0], [-0.047, -0.022], [-0.199, -0.215], [-0.09, -0.191], [0.018, -0.009], [-0.007, -0.023], [-0.008, -0.003], [-0.02, 0], [-0.193, 0.059], [0.024, 0.101], [0.195, 0.308], [-0.012, 0.221], [-0.021, 0.135], [-0.058, 0.061], [-0.147, 0.123], [-0.052, 0.042], [0.016, 0.048], [0.019, 0], [0.119, -0.047], [0.226, -0.166], [0.149, 0.197], [0.053, 0.13], [0.014, 0.028], [0, 0], [0.112, -0.001], [-0.036, -0.118], [-0.076, -0.185], [0.027, 0], [0.01, 0], [0.306, 0.064], [0.16, 0.041], [0.065, 0.026], [-0.035, 0.061], [-0.091, 0.146], [-0.033, 0.059], [0.001, 0.043], [0.003, 0.001], [0.027, 0], [0.112, -0.144], [0.115, -0.151], [0.081, 0.091], [0.154, 0.208], [0.059, 0.078], [0.019, 0.019], [0.023, 0], [0.019, -0.017], [-0.02, -0.059], [-0.128, -0.192], [-0.139, -0.186], [0.012, -0.136], [0.024, -0.242], [0.072, -0.121], [0.041, 0], [0.002, 0.001], [0.116, 0.059], [0.222, 0], [0.191, -0.176], [-0.356, -0.243], [-0.02, -0.014], [-0.034, -0.124], [-0.01, -0.039], [-0.009, 0], [-0.03, 0.132], [-0.016, 0.068], [-0.026, 0], [-0.049, -0.039], [-0.083, -0.086], [-0.006, 0.004], [0.037, 0.141], [0.051, 0.177], [-0.082, 0.179], [-0.038, 0.035], [-0.024, 0], [-0.532, -0.242], [-0.347, -0.346], [-0.019, -0.02], [0.031, -0.074], [-0.048, -0.066], [-0.095, -0.106], [-0.19, -0.297], [-0.023, -0.108], [-0.153, -0.52], [-0.456, -0.091], [-0.229, -0.038], [-0.255, -0.147], [0.034, -0.086], [0.011, -0.063], [-0.027, -0.013]], "v": [[-3.984, -4.875], [-4.084, -4.828], [-4.193, -4.657], [-4.337, -4.218], [-4.204, -3.456], [-3.652, -2.975], [-3.269, -2.728], [-2.913, -2.087], [-2.965, -1.37], [-3.545, 0.094], [-4.234, 0.718], [-4.618, 0.812], [-4.98, 0.698], [-5.184, 0.597], [-5.293, 0.569], [-5.321, 0.566], [-5.447, 0.644], [-5.439, 0.8], [-5.301, 0.96], [-4.819, 1.273], [-4.189, 1.408], [-3.463, 1.182], [-3.107, 0.92], [-2.646, 0.615], [-2.416, 0.553], [-2.079, 0.683], [-1.793, 1.078], [-1.626, 1.946], [-1.597, 2.979], [-1.467, 3.328], [-1.333, 3.401], [-1.189, 3.312], [-1.131, 3.173], [-1.021, 1.772], [-1.217, 0.038], [-1.181, -1.37], [-1.066, -1.811], [-1.04, -1.809], [-0.969, -1.645], [-0.407, -0.646], [-0.034, -0.268], [0.173, -0.182], [0.248, -0.193], [0.281, -0.198], [0.35, -0.157], [1.158, 0.712], [2.716, 1.922], [2.746, 1.949], [2.658, 1.968], [2.498, 1.982], [2.492, 1.982], [1.135, 1.634], [1.008, 1.538], [0.516, 0.82], [0.656, 0.453], [0.697, 0.403], [0.645, 0.347], [0.618, 0.343], [0.557, 0.354], [0.287, 0.804], [0.416, 1.091], [0.738, 2.094], [0.646, 2.754], [0.494, 3.126], [0.322, 3.308], [-0.129, 3.668], [-0.209, 3.833], [-0.081, 3.915], [-0.023, 3.904], [0.32, 3.734], [0.983, 3.206], [1.328, 3.875], [1.51, 4.257], [1.604, 4.311], [1.604, 4.311], [1.731, 4.142], [1.591, 3.799], [1.36, 3.235], [1.421, 3.234], [1.447, 3.236], [2.367, 3.434], [2.832, 3.612], [2.876, 3.747], [2.77, 3.93], [2.495, 4.367], [2.442, 4.566], [2.528, 4.688], [2.538, 4.69], [2.637, 4.636], [2.962, 4.197], [3.318, 3.743], [3.544, 4.002], [3.995, 4.632], [4.197, 4.848], [4.255, 4.875], [4.32, 4.846], [4.339, 4.754], [4.255, 4.586], [3.863, 4.016], [3.701, 3.4], [3.748, 2.994], [3.951, 2.308], [4.111, 2.136], [4.298, 2.21], [4.304, 2.213], [4.688, 2.301], [5.291, 2.079], [5.253, 1.571], [4.191, 0.834], [4.147, 0.77], [4.051, 0.396], [4.007, 0.284], [3.979, 0.285], [3.889, 0.681], [3.798, 0.785], [3.708, 0.747], [3.576, 0.611], [3.328, 0.352], [3.309, 0.364], [3.421, 0.787], [3.387, 1.303], [3.069, 1.788], [2.952, 1.843], [2.869, 1.822], [1.447, 0.847], [0.426, -0.208], [0.397, -0.294], [0.378, -0.495], [0.22, -0.68], [-0.064, -0.995], [-0.488, -1.955], [-0.577, -2.276], [-1.598, -3.247], [-2.972, -3.488], [-3.626, -3.697], [-3.896, -4.434], [-3.827, -4.705], [-3.903, -4.855]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.86, 75.128], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 22", "np": 2, "cix": 2, "bm": 0, "ix": 22, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.149, 0.197], [0.226, -0.166], [0.119, -0.047], [0.02, 0.064], [-0.053, 0.042], [-0.147, 0.124], [-0.058, 0.06], [-0.021, 0.136], [-0.012, 0.221], [0.194, 0.307], [0.024, 0.1], [-0.192, 0.059], [-0.026, -0.008], [-0.007, -0.023], [0.019, -0.009], [-0.09, -0.191], [-0.199, -0.215], [-0.047, -0.022], [-0.485, 0.001], [-0.053, 0.006], [-0.035, 0.007], [0.01, 0.005], [0.457, 0.484], [0.269, 0.291], [0.05, -0.014], [0.077, 0.07], [0.12, 0.13], [0.135, 0.363], [0.023, 0.055], [0.008, 0.001], [0.029, -0.149], [-0.049, -0.47], [-0.061, -0.578], [0.117, -0.46], [0.027, -0.042], [0.082, 0.103], [0.004, 0.132], [0.015, 0.344], [0.114, 0.277], [0.134, 0.104], [0.197, -0.107], [0.151, -0.106], [0.121, -0.082], [0.461, 0.188], [0.156, 0.112], [0.04, 0.057], [-0.035, 0.054], [-0.069, -0.011], [-0.034, -0.016], [-0.062, -0.042], [-0.25, 0.112], [-0.179, 0.263], [-0.099, 0.525], [0.014, 0.243], [0.217, 0.156], [0.129, 0.081], [0.146, 0.203], [-0.087, 0.276], [-0.054, 0.144], [-0.043, 0.052], [-0.066, -0.029], [0.011, -0.063], [0.033, -0.086], [-0.255, -0.147], [-0.229, -0.038], [-0.456, -0.091], [-0.152, -0.521], [-0.024, -0.109], [-0.191, -0.297], [-0.095, -0.105], [-0.048, -0.065], [0.031, -0.073], [-0.02, -0.02], [-0.346, -0.345], [-0.532, -0.242], [-0.062, 0.055], [-0.083, 0.178], [0.051, 0.177], [0.036, 0.141], [-0.006, 0.004], [-0.083, -0.087], [-0.049, -0.04], [-0.025, 0.109], [-0.03, 0.132], [-0.008, 0], [-0.011, -0.038], [-0.034, -0.124], [-0.02, -0.013], [-0.356, -0.243], [0.191, -0.176], [0.306, 0.157], [0.002, 0.001], [0.125, -0.208], [0.024, -0.242], [0.012, -0.136], [-0.138, -0.186], [-0.128, -0.192], [-0.021, -0.059], [0.019, -0.018], [0.041, 0.043], [0.059, 0.079], [0.153, 0.208], [0.081, 0.092], [0.114, -0.15], [0.112, -0.143], [0.019, 0.012], [0.001, 0.043], [-0.033, 0.058], [-0.092, 0.146], [-0.035, 0.061], [0.065, 0.026], [0.16, 0.04], [0.307, 0.064], [0.041, 0], [-0.076, -0.186], [-0.036, -0.118], [0.111, -0.001], [0.015, 0.028], [0.052, 0.131]], "o": [[-0.224, 0.18], [-0.102, 0.074], [-0.08, 0.031], [-0.025, -0.076], [0.15, -0.12], [0.064, -0.053], [0.1, -0.104], [0.034, -0.219], [0.02, -0.371], [-0.056, -0.088], [-0.048, -0.196], [0.029, -0.009], [0.022, 0.007], [0.003, 0.011], [-0.191, 0.09], [0.126, 0.264], [0.036, 0.038], [0.432, 0.206], [0.053, -0.001], [0.027, -0.003], [-0.013, -0.013], [-0.602, -0.299], [-0.272, -0.287], [-0.029, -0.032], [-0.105, 0.029], [-0.13, -0.119], [-0.265, -0.289], [-0.021, -0.056], [-0.009, 0], [-0.039, 0.147], [-0.088, 0.467], [0.062, 0.579], [0.05, 0.474], [-0.012, 0.049], [-0.07, 0.112], [-0.08, -0.102], [-0.008, -0.344], [-0.014, -0.296], [-0.063, -0.155], [-0.176, -0.138], [-0.162, 0.087], [-0.12, 0.084], [-0.434, 0.291], [-0.174, -0.071], [-0.056, -0.04], [-0.034, -0.047], [0.037, -0.056], [0.037, 0.007], [0.069, 0.03], [0.245, 0.166], [0.297, -0.134], [0.301, -0.445], [0.044, -0.237], [-0.017, -0.273], [-0.124, -0.088], [-0.211, -0.131], [-0.168, -0.233], [0.047, -0.147], [0.023, -0.062], [0.048, -0.058], [0.066, 0.029], [-0.017, 0.092], [-0.098, 0.252], [0.203, 0.116], [0.459, 0.076], [0.539, 0.108], [0.032, 0.106], [0.075, 0.349], [0.075, 0.118], [0.054, 0.06], [0.044, 0.061], [-0.009, 0.022], [0.338, 0.354], [0.414, 0.411], [0.085, 0.039], [0.15, -0.134], [0.078, -0.171], [-0.041, -0.14], [0.006, -0.005], [0.083, 0.087], [0.043, 0.046], [0.088, 0.069], [0.031, -0.132], [0.009, 0], [0.016, 0.037], [0.033, 0.125], [0.006, 0.024], [0.353, 0.247], [0.213, 0.147], [-0.253, 0.233], [-0.003, -0.001], [-0.218, -0.112], [-0.128, 0.211], [-0.014, 0.136], [-0.02, 0.223], [0.138, 0.185], [0.034, 0.053], [0.009, 0.026], [-0.033, 0.03], [-0.068, -0.071], [-0.154, -0.207], [-0.066, -0.088], [-0.124, 0.157], [-0.11, 0.145], [-0.024, 0.03], [-0.041, -0.024], [-0.001, -0.067], [0.085, -0.15], [0.037, -0.06], [0.035, -0.061], [-0.155, -0.062], [-0.303, -0.078], [-0.021, -0.004], [0.079, 0.193], [0.047, 0.114], [0.033, 0.107], [-0.033, 0], [-0.066, -0.125], [-0.095, -0.238]], "v": [[0.984, 3.203], [0.32, 3.732], [-0.023, 3.901], [-0.208, 3.83], [-0.129, 3.666], [0.32, 3.305], [0.494, 3.124], [0.646, 2.751], [0.738, 2.091], [0.417, 1.089], [0.287, 0.802], [0.557, 0.351], [0.645, 0.344], [0.697, 0.401], [0.656, 0.45], [0.516, 0.818], [1.008, 1.535], [1.134, 1.631], [2.499, 1.98], [2.658, 1.966], [2.746, 1.947], [2.716, 1.92], [1.158, 0.709], [0.35, -0.16], [0.249, -0.195], [-0.035, -0.271], [-0.408, -0.648], [-0.971, -1.647], [-1.04, -1.812], [-1.066, -1.814], [-1.182, -1.373], [-1.219, 0.035], [-1.021, 1.769], [-1.131, 3.17], [-1.189, 3.309], [-1.467, 3.326], [-1.598, 2.976], [-1.626, 1.943], [-1.792, 1.076], [-2.079, 0.681], [-2.646, 0.613], [-3.107, 0.918], [-3.463, 1.179], [-4.82, 1.271], [-5.301, 0.957], [-5.439, 0.798], [-5.448, 0.641], [-5.292, 0.566], [-5.184, 0.595], [-4.98, 0.695], [-4.236, 0.716], [-3.544, 0.092], [-2.965, -1.372], [-2.913, -2.09], [-3.269, -2.73], [-3.652, -2.978], [-4.204, -3.458], [-4.337, -4.22], [-4.193, -4.66], [-4.083, -4.83], [-3.902, -4.858], [-3.827, -4.707], [-3.896, -4.437], [-3.626, -3.699], [-2.973, -3.49], [-1.598, -3.25], [-0.577, -2.278], [-0.488, -1.957], [-0.064, -0.998], [0.221, -0.683], [0.378, -0.498], [0.397, -0.297], [0.426, -0.211], [1.447, 0.844], [2.869, 1.819], [3.07, 1.786], [3.387, 1.301], [3.421, 0.785], [3.309, 0.362], [3.327, 0.349], [3.576, 0.609], [3.708, 0.745], [3.889, 0.678], [3.979, 0.282], [4.005, 0.281], [4.051, 0.393], [4.147, 0.767], [4.191, 0.831], [5.253, 1.568], [5.292, 2.076], [4.305, 2.21], [4.298, 2.207], [3.951, 2.306], [3.749, 2.991], [3.701, 3.398], [3.863, 4.013], [4.255, 4.583], [4.339, 4.752], [4.32, 4.844], [4.197, 4.845], [3.995, 4.629], [3.544, 3.999], [3.318, 3.74], [2.962, 4.194], [2.637, 4.633], [2.529, 4.685], [2.443, 4.564], [2.495, 4.365], [2.77, 3.927], [2.876, 3.744], [2.833, 3.61], [2.365, 3.432], [1.447, 3.233], [1.361, 3.233], [1.591, 3.797], [1.731, 4.14], [1.604, 4.308], [1.509, 4.255], [1.328, 3.872]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.861, 75.13], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 23", "np": 2, "cix": 2, "bm": 0, "ix": 23, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.104, 0], [0, 0], [0, -1.104], [0, 0], [1.104, 0], [0, 0], [0, 1.104], [0, 0]], "o": [[0, 0], [1.104, 0], [0, 0], [0, 1.104], [0, 0], [-1.104, 0], [0, 0], [0, -1.104]], "v": [[-24.184, -9.814], [24.184, -9.814], [26.184, -7.814], [26.184, 7.815], [24.184, 9.814], [-24.184, 9.814], [-26.184, 7.815], [-26.184, -7.814]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [51.55, 75.732], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 24", "np": 2, "cix": 2, "bm": 0, "ix": 24, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[14.499, 83.371], [-14.499, 83.371], [-14.499, -83.371], [14.499, -83.371]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.819999964097, 0.438999998803, 0.294000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [54.622, 83.621], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 25", "np": 2, "cix": 2, "bm": 0, "ix": 25, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[14.499, 83.371], [-14.499, 83.371], [-14.499, -83.371], [14.499, -83.371]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.736999990426, 0.395999983245, 0.26699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [163.367, 83.621], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 26", "np": 2, "cix": 2, "bm": 0, "ix": 26, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.523, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 5.522]], "v": [[-44.372, 83.371], [54.372, 83.371], [54.372, -83.371], [-54.372, -83.371], [-54.372, 73.371]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952999997606, 0.588000009574, 0.294000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [54.622, 83.621], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 27", "np": 2, "cix": 2, "bm": 0, "ix": 27, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.522, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 5.522]], "v": [[44.373, 83.371], [-54.373, 83.371], [-54.373, -83.371], [54.373, -83.371], [54.373, 73.371]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.819999964097, 0.438999998803, 0.294000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [163.367, 83.621], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 28", "np": 2, "cix": 2, "bm": 0, "ix": 28, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "board-left", "parent": 1, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [300, 400, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "w": 600, "h": 800, "ip": 8, "op": 228, "st": 8, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Shape Layer 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [5]}, {"t": 19, "s": [10]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [296, 516, 0], "ix": 2}, "a": {"a": 0, "k": [-4, 190, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 4, "s": [94, 94, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 11, "s": [85, 85, 100]}, {"t": 19, "s": [94, 94, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [214.647, 17.17], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-5.677, 181.415], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 4, "op": 224, "st": 4, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "board-back", "parent": 1, "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-10.5, 0, 0], "ix": 2}, "a": {"a": 0, "k": [300, 400, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "w": 600, "h": 800, "ip": 4, "op": 224, "st": 4, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 2, "ty": 3, "nm": "rebord-face/gift.ai: Path 1 [1.0]", "cl": "ai 0", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.135, "y": 0}, "t": 0, "s": [370.604, 341.4, 0], "to": [0, -23.494, 0], "ti": [35.778, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 1.728, "s": [305.822, 276.618, 0], "to": [-35.778, 0, 0], "ti": [0, -35.778, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4.579, "s": [241.041, 341.4, 0], "to": [0, 21.661, 0], "ti": [-22.295, -22.744, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.197, "y": 0}, "t": 13.333, "s": [267.084, 397.262, 0], "to": [0.291, 0.296, 0], "ti": [-0.352, -0.254, 0]}, {"t": 20, "s": [268, 394, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 3, "nm": "rebord-face/gift.ai: Path 1 [1.3]", "cl": "ai 3", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.135, "y": 0}, "t": 0, "s": [252.781, 340.5, 0], "to": [0, -23.494, 0], "ti": [35.778, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 1.728, "s": [188, 275.719, 0], "to": [-35.778, 0, 0], "ti": [0, -35.778, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4.579, "s": [123.219, 340.5, 0], "to": [0, 21.661, 0], "ti": [-22.295, -22.744, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.197, "y": 0}, "t": 13.333, "s": [149.261, 396.362, 0], "to": [0.291, 0.296, 0], "ti": [-0.352, -0.254, 0]}, {"t": 20, "s": [150.178, 393.1, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "rebord-face/gift.ai", "cl": "ai", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [223.799, 368.065, 0], "ix": 2}, "a": {"a": 0, "k": [72.799, 26.315, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.0]", "np": 3, "mn": "ADBE Layer Control", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.1]", "np": 3, "mn": "ADBE Layer Control", "ix": 2, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 4, "ix": 1}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.2]", "np": 3, "mn": "ADBE Layer Control", "ix": 3, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 4, "ix": 1}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.3]", "np": 3, "mn": "ADBE Layer Control", "ix": 4, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[117.335, 52.38], [145.349, 0.25], [36.278, 0.25], [0.25, 52.141]], "c": true}, "ix": 2, "x": "var $bm_rt;\nvar nullLayerNames = [\n        'rebord-face/gift.ai: Path 1 [1.0]',\n        'rebord-face/gift.ai: Path 1 [1.1]',\n        'rebord-face/gift.ai: Path 1 [1.2]',\n        'rebord-face/gift.ai: Path 1 [1.3]'\n    ];\nvar origPath = thisProperty;\nvar origPoints = origPath.points();\nvar origInTang = origPath.inTangents();\nvar origOutTang = origPath.outTangents();\nvar getNullLayers = [];\nfor (var i = 0, il = nullLayerNames.length; i < il; i++) {\n    try {\n        getNullLayers.push(effect(nullLayerNames[i])('ADBE Layer Control-0001'));\n    } catch (err) {\n        getNullLayers.push(null);\n    }\n}\nfor (var i = 0, il = getNullLayers.length; i < il; i++) {\n    if (getNullLayers[i] != null && getNullLayers[i].index != thisLayer.index) {\n        origPoints[i] = fromCompToSurface(getNullLayers[i].toComp(getNullLayers[i].anchorPoint));\n    }\n}\n$bm_rt = createPath(origPoints, origInTang, origOutTang, origPath.isClosed());"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.972999961703, 0.722000002394, 0.419999994016, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 220, "st": 0, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "reboard-cote/gift.ai: Path 1 [1.0]", "cl": "ai 0", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.376, "y": 0}, "t": 0, "s": [228.585, 343.439, 0], "to": [0, -32.231, 0], "ti": [-33.745, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3.275, "s": [289.685, 282.338, 0], "to": [33.745, 0, 0], "ti": [0, -33.745, 0]}, {"i": {"x": 0.045, "y": 1}, "o": {"x": 0.167, "y": 0.397}, "t": 5.434, "s": [350.785, 343.439, 0], "to": [0, 21.024, 0], "ti": [12.703, -14.391, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0, "y": 0}, "t": 13.333, "s": [325.017, 400.1, 0], "to": [-0.233, 0.729, 0], "ti": [0.725, -0.493, 0]}, {"t": 16, "s": [324, 394, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "reboard-cote/gift.ai: Path 1 [1.3]", "cl": "ai 3", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.376, "y": 0}, "t": 0, "s": [344.079, 342.312, 0], "to": [0, -32.231, 0], "ti": [-33.745, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3.275, "s": [405.179, 281.212, 0], "to": [33.745, 0, 0], "ti": [0, -33.745, 0]}, {"i": {"x": 0.045, "y": 1}, "o": {"x": 0.167, "y": 0.397}, "t": 5.434, "s": [466.279, 342.312, 0], "to": [0, 21.024, 0], "ti": [12.703, -14.391, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0, "y": 0}, "t": 13.333, "s": [440.511, 398.974, 0], "to": [-0.233, 0.729, 0], "ti": [0.725, -0.493, 0]}, {"t": 16, "s": [439.494, 394, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "reboard-cote/gift.ai", "cl": "ai", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [368.549, 368.315, 0], "ix": 2}, "a": {"a": 0, "k": [72.799, 26.315, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.0]", "np": 3, "mn": "ADBE Layer Control", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}]}, {"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.1]", "np": 3, "mn": "ADBE Layer Control", "ix": 2, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}, {"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.2]", "np": 3, "mn": "ADBE Layer Control", "ix": 3, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}, {"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.3]", "np": 3, "mn": "ADBE Layer Control", "ix": 4, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[28.264, 52.38], [0.25, 0.25], [109.321, 0.25], [145.349, 52.141]], "c": true}, "ix": 2, "x": "var $bm_rt;\nvar nullLayerNames = [\n        'reboard-cote/gift.ai: Path 1 [1.0]',\n        'reboard-cote/gift.ai: Path 1 [1.1]',\n        'reboard-cote/gift.ai: Path 1 [1.2]',\n        'reboard-cote/gift.ai: Path 1 [1.3]'\n    ];\nvar origPath = thisProperty;\nvar origPoints = origPath.points();\nvar origInTang = origPath.inTangents();\nvar origOutTang = origPath.outTangents();\nvar getNullLayers = [];\nfor (var i = 0, il = nullLayerNames.length; i < il; i++) {\n    try {\n        getNullLayers.push(effect(nullLayerNames[i])('ADBE Layer Control-0001'));\n    } catch (err) {\n        getNullLayers.push(null);\n    }\n}\nfor (var i = 0, il = getNullLayers.length; i < il; i++) {\n    if (getNullLayers[i] != null && getNullLayers[i].index != thisLayer.index) {\n        origPoints[i] = fromCompToSurface(getNullLayers[i].toComp(getNullLayers[i].anchorPoint));\n    }\n}\n$bm_rt = createPath(origPoints, origInTang, origOutTang, origPath.isClosed());"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952999997606, 0.588000009574, 0.294000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 220, "st": 0, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "reboard-cote/gift.ai: Path 1 [1.0]", "cl": "ai 0", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.376, "y": 0}, "t": 0, "s": [228.585, 343.439, 0], "to": [0, -32.231, 0], "ti": [-33.745, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3.275, "s": [289.685, 282.338, 0], "to": [33.745, 0, 0], "ti": [0, -33.745, 0]}, {"i": {"x": 0.045, "y": 1}, "o": {"x": 0.167, "y": 0.397}, "t": 5.434, "s": [350.785, 343.439, 0], "to": [0, 21.024, 0], "ti": [12.703, -14.391, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0, "y": 0}, "t": 13.333, "s": [325.017, 400.1, 0], "to": [-0.233, 0.729, 0], "ti": [0.725, -0.493, 0]}, {"t": 16, "s": [324, 394, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "reboard-cote/gift.ai: Path 1 [1.3]", "cl": "ai 3", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.376, "y": 0}, "t": 0, "s": [344.079, 342.312, 0], "to": [0, -32.231, 0], "ti": [-33.745, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3.275, "s": [405.179, 281.212, 0], "to": [33.745, 0, 0], "ti": [0, -33.745, 0]}, {"i": {"x": 0.045, "y": 1}, "o": {"x": 0.167, "y": 0.397}, "t": 5.434, "s": [466.279, 342.312, 0], "to": [0, 21.024, 0], "ti": [12.703, -14.391, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0, "y": 0}, "t": 13.333, "s": [440.511, 398.974, 0], "to": [-0.233, 0.729, 0], "ti": [0.725, -0.493, 0]}, {"t": 16, "s": [439.494, 394, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "reboard-cote/gift.ai", "cl": "ai", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [368.549, 368.315, 0], "ix": 2}, "a": {"a": 0, "k": [72.799, 26.315, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.0]", "np": 3, "mn": "ADBE Layer Control", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}]}, {"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.1]", "np": 3, "mn": "ADBE Layer Control", "ix": 2, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}, {"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.2]", "np": 3, "mn": "ADBE Layer Control", "ix": 3, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}, {"ty": 5, "nm": "reboard-cote/gift.ai: Path 1 [1.3]", "np": 3, "mn": "ADBE Layer Control", "ix": 4, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 5, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [0.992156863213, 0.784313738346, 0.529411792755, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[28.264, 52.38], [0.25, 0.25], [109.321, 0.25], [145.349, 52.141]], "c": true}, "ix": 2, "x": "var $bm_rt;\nvar nullLayerNames = [\n        'reboard-cote/gift.ai: Path 1 [1.0]',\n        'reboard-cote/gift.ai: Path 1 [1.1]',\n        'reboard-cote/gift.ai: Path 1 [1.2]',\n        'reboard-cote/gift.ai: Path 1 [1.3]'\n    ];\nvar origPath = thisProperty;\nvar origPoints = origPath.points();\nvar origInTang = origPath.inTangents();\nvar origOutTang = origPath.outTangents();\nvar getNullLayers = [];\nfor (var i = 0, il = nullLayerNames.length; i < il; i++) {\n    try {\n        getNullLayers.push(effect(nullLayerNames[i])('ADBE Layer Control-0001'));\n    } catch (err) {\n        getNullLayers.push(null);\n    }\n}\nfor (var i = 0, il = getNullLayers.length; i < il; i++) {\n    if (getNullLayers[i] != null && getNullLayers[i].index != thisLayer.index) {\n        origPoints[i] = fromCompToSurface(getNullLayers[i].toComp(getNullLayers[i].anchorPoint));\n    }\n}\n$bm_rt = createPath(origPoints, origInTang, origOutTang, origPath.isClosed());"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952999997606, 0.588000009574, 0.294000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 220, "st": 0, "bm": 0}]}, {"id": "comp_4", "layers": [{"ddd": 0, "ind": 2, "ty": 3, "nm": "rebord-face/gift.ai: Path 1 [1.0]", "cl": "ai 0", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.135, "y": 0}, "t": 0, "s": [370.604, 341.4, 0], "to": [0, -23.494, 0], "ti": [35.778, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 1.728, "s": [305.822, 276.618, 0], "to": [-35.778, 0, 0], "ti": [0, -35.778, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4.579, "s": [241.041, 341.4, 0], "to": [0, 21.661, 0], "ti": [-22.295, -22.744, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.197, "y": 0}, "t": 13.333, "s": [267.084, 397.262, 0], "to": [0.291, 0.296, 0], "ti": [-0.352, -0.254, 0]}, {"t": 20, "s": [268, 394, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 3, "nm": "rebord-face/gift.ai: Path 1 [1.3]", "cl": "ai 3", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.135, "y": 0}, "t": 0, "s": [252.781, 340.5, 0], "to": [0, -23.494, 0], "ti": [35.778, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 1.728, "s": [188, 275.719, 0], "to": [-35.778, 0, 0], "ti": [0, -35.778, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4.579, "s": [123.219, 340.5, 0], "to": [0, 21.661, 0], "ti": [-22.295, -22.744, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.197, "y": 0}, "t": 13.333, "s": [149.261, 396.362, 0], "to": [0.291, 0.296, 0], "ti": [-0.352, -0.254, 0]}, {"t": 20, "s": [150.178, 393.1, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "rebord-face/gift.ai", "cl": "ai", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [223.799, 368.065, 0], "ix": 2}, "a": {"a": 0, "k": [72.799, 26.315, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [0.993257403374, 0.78356564045, 0.531280219555, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.0]", "np": 3, "mn": "ADBE Layer Control", "ix": 2, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.1]", "np": 3, "mn": "ADBE Layer Control", "ix": 3, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 4, "ix": 1}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.2]", "np": 3, "mn": "ADBE Layer Control", "ix": 4, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 4, "ix": 1}}]}, {"ty": 5, "nm": "rebord-face/gift.ai: Path 1 [1.3]", "np": 3, "mn": "ADBE Layer Control", "ix": 5, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[117.335, 52.38], [145.349, 0.25], [36.278, 0.25], [0.25, 52.141]], "c": true}, "ix": 2, "x": "var $bm_rt;\nvar nullLayerNames = [\n        'rebord-face/gift.ai: Path 1 [1.0]',\n        'rebord-face/gift.ai: Path 1 [1.1]',\n        'rebord-face/gift.ai: Path 1 [1.2]',\n        'rebord-face/gift.ai: Path 1 [1.3]'\n    ];\nvar origPath = thisProperty;\nvar origPoints = origPath.points();\nvar origInTang = origPath.inTangents();\nvar origOutTang = origPath.outTangents();\nvar getNullLayers = [];\nfor (var i = 0, il = nullLayerNames.length; i < il; i++) {\n    try {\n        getNullLayers.push(effect(nullLayerNames[i])('ADBE Layer Control-0001'));\n    } catch (err) {\n        getNullLayers.push(null);\n    }\n}\nfor (var i = 0, il = getNullLayers.length; i < il; i++) {\n    if (getNullLayers[i] != null && getNullLayers[i].index != thisLayer.index) {\n        origPoints[i] = fromCompToSurface(getNullLayers[i].toComp(getNullLayers[i].anchorPoint));\n    }\n}\n$bm_rt = createPath(origPoints, origInTang, origOutTang, origPath.isClosed());"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.972999961703, 0.722000002394, 0.419999994016, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 220, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "open", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [300, 488, 0], "ix": 2}, "a": {"a": 0, "k": [300, 400, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 600, "h": 800, "ip": 0, "op": 220, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "flash 10", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 67, "s": [0]}, {"t": 91, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [293.061, 321.797, 0], "ix": 2}, "a": {"a": 0, "k": [15.723, 15.724, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 67, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 77, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 87, "s": [100, 100, 100]}, {"t": 92, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "ip": 67, "op": 92, "st": 39, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "flash 9", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [0]}, {"t": 125, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [358.561, 321.297, 0], "ix": 2}, "a": {"a": 0, "k": [15.723, 15.724, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 101, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 111, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 117, "s": [68, 68, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 121, "s": [100, 100, 100]}, {"t": 126, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "ip": 101, "op": 101.242424242424, "st": 73, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "flash 8", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [0]}, {"t": 102, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [343.561, 361.797, 0], "ix": 2}, "a": {"a": 0, "k": [15.723, 15.724, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 78, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 88, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 97, "s": [85, 85, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 98, "s": [100, 100, 100]}, {"t": 103, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "ip": 78, "op": 96, "st": 50, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "flash 7", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 72, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 56, "s": [0]}, {"t": 80, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [356.061, 355.797, 0], "ix": 2}, "a": {"a": 0, "k": [15.723, 15.724, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 56, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 66, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 76, "s": [100, 100, 100]}, {"t": 81, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "ip": 56, "op": 81, "st": 28, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "flash 11", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [0]}, {"t": 43, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [365.061, 304.797, 0], "ix": 2}, "a": {"a": 0, "k": [15.723, 15.724, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 19, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 29, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 39, "s": [100, 100, 100]}, {"t": 44, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "ip": 19, "op": 44, "st": -9, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 2, "nm": "flash 6", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 58, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33, "s": [0]}, {"t": 57, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [244.561, 311.797, 0], "ix": 2}, "a": {"a": 0, "k": [15.723, 15.724, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 33, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 43, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 45, "s": [86, 86, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 53, "s": [100, 100, 100]}, {"t": 58, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "ip": 33, "op": 58, "st": 5, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 2, "nm": "flash 5", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [0]}, {"t": 33, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [261.061, 399.297, 0], "ix": 2}, "a": {"a": 0, "k": [15.723, 15.724, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": -98, "s": [1, 1, 100]}, {"i": {"x": [0.145, 0.145, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [-0.175, -0.175, 0]}, "t": 9, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 19, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [1, 1, 0.333], "y": [0, 0, 0]}, "t": 29, "s": [100, 100, 100]}, {"t": 34, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "ip": 9, "op": 34, "st": -19, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "coin 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [21]}, {"t": 128, "s": [360]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.373}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [249.617, 486.945, 0], "to": [-0.088, -0.702, 0], "ti": [0.63, 5.283, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.096}, "t": 9, "s": [249.091, 482.735, 0], "to": [-0.63, -5.283, 0], "ti": [1.143, 9.684, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.158}, "t": 10, "s": [245.84, 455.248, 0], "to": [-1.143, -9.684, 0], "ti": [0.897, 7.627, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.252}, "t": 11, "s": [242.234, 424.631, 0], "to": [-0.897, -7.627, 0], "ti": [0.464, 3.987, 0]}, {"i": {"x": 0.833, "y": 0.865}, "o": {"x": 0.167, "y": 0.227}, "t": 12, "s": [240.46, 409.488, 0], "to": [-0.464, -3.987, 0], "ti": [0.271, 2.372, 0]}, {"i": {"x": 0.833, "y": 0.865}, "o": {"x": 0.167, "y": 0.218}, "t": 13, "s": [239.447, 400.708, 0], "to": [-0.271, -2.372, 0], "ti": [0.163, 1.476, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.217}, "t": 14, "s": [238.834, 395.254, 0], "to": [-0.163, -1.476, 0], "ti": [0.097, 0.895, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.227}, "t": 15, "s": [238.469, 391.85, 0], "to": [-0.097, -0.895, 0], "ti": [0.06, 0.474, 0]}, {"i": {"x": 0.833, "y": 0.901}, "o": {"x": 0.167, "y": 0.27}, "t": 16, "s": [238.25, 389.882, 0], "to": [-0.06, -0.474, 0], "ti": [0.035, 0.151, 0]}, {"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.516}, "t": 17, "s": [238.111, 389.008, 0], "to": [-0.035, -0.151, 0], "ti": [0.021, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.13}, "t": 18, "s": [238.038, 388.975, 0], "to": [-0.021, 0.044, 0], "ti": [0.02, -0.092, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.179}, "t": 19, "s": [237.984, 389.273, 0], "to": [-0.02, 0.092, 0], "ti": [0.022, -0.076, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.187}, "t": 20, "s": [237.918, 389.527, 0], "to": [-0.022, 0.076, 0], "ti": [0.021, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.201}, "t": 21, "s": [237.851, 389.728, 0], "to": [-0.021, 0.056, 0], "ti": [0.017, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.885}, "o": {"x": 0.167, "y": 0.238}, "t": 22, "s": [237.792, 389.866, 0], "to": [-0.017, 0.034, 0], "ti": [0.011, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.685}, "o": {"x": 0.167, "y": 0.303}, "t": 23, "s": [237.749, 389.934, 0], "to": [-0.011, 0.01, 0], "ti": [0.002, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.752}, "o": {"x": 0.167, "y": 0.114}, "t": 24, "s": [237.728, 389.927, 0], "to": [-0.002, -0.015, 0], "ti": [-0.008, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.791}, "o": {"x": 0.167, "y": 0.126}, "t": 25, "s": [237.737, 389.842, 0], "to": [0.008, -0.041, 0], "ti": [-0.02, 0.067, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.139}, "t": 26, "s": [237.778, 389.679, 0], "to": [0.02, -0.067, 0], "ti": [-0.033, 0.093, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.146}, "t": 27, "s": [237.856, 389.438, 0], "to": [0.033, -0.093, 0], "ti": [-0.046, 0.116, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.151}, "t": 28, "s": [237.974, 389.123, 0], "to": [0.046, -0.116, 0], "ti": [-0.06, 0.138, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.154}, "t": 29, "s": [238.133, 388.74, 0], "to": [0.06, -0.138, 0], "ti": [-0.073, 0.157, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 30, "s": [238.333, 388.294, 0], "to": [0.073, -0.157, 0], "ti": [-0.086, 0.173, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 31, "s": [238.573, 387.797, 0], "to": [0.086, -0.173, 0], "ti": [-0.099, 0.184, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 32, "s": [238.852, 387.259, 0], "to": [0.099, -0.184, 0], "ti": [-0.11, 0.19, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 33, "s": [239.166, 386.694, 0], "to": [0.11, -0.19, 0], "ti": [-0.12, 0.191, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 34, "s": [239.511, 386.118, 0], "to": [0.12, -0.191, 0], "ti": [-0.127, 0.186, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.168}, "t": 35, "s": [239.883, 385.548, 0], "to": [0.127, -0.186, 0], "ti": [-0.133, 0.173, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.171}, "t": 36, "s": [240.275, 385.004, 0], "to": [0.133, -0.173, 0], "ti": [-0.135, 0.154, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.174}, "t": 37, "s": [240.679, 384.507, 0], "to": [0.135, -0.154, 0], "ti": [-0.135, 0.125, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.178}, "t": 38, "s": [241.087, 384.082, 0], "to": [0.135, -0.125, 0], "ti": [-0.131, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.175}, "t": 39, "s": [241.49, 383.754, 0], "to": [0.131, -0.099, 0], "ti": [-0.124, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.17}, "t": 40, "s": [241.876, 383.487, 0], "to": [0.124, -0.091, 0], "ti": [-0.114, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.173}, "t": 41, "s": [242.236, 383.211, 0], "to": [0.114, -0.091, 0], "ti": [-0.101, 0.088, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.176}, "t": 42, "s": [242.56, 382.938, 0], "to": [0.101, -0.088, 0], "ti": [-0.085, 0.082, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.18}, "t": 43, "s": [242.84, 382.68, 0], "to": [0.085, -0.082, 0], "ti": [-0.068, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.185}, "t": 44, "s": [243.071, 382.445, 0], "to": [0.068, -0.073, 0], "ti": [-0.049, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.193}, "t": 45, "s": [243.248, 382.24, 0], "to": [0.049, -0.062, 0], "ti": [-0.03, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.207}, "t": 46, "s": [243.366, 382.073, 0], "to": [0.03, -0.049, 0], "ti": [-0.009, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.227}, "t": 47, "s": [243.425, 381.949, 0], "to": [0.009, -0.034, 0], "ti": [0.011, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.774}, "o": {"x": 0.167, "y": 0.176}, "t": 48, "s": [243.423, 381.871, 0], "to": [-0.011, -0.018, 0], "ti": [0.03, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.788}, "o": {"x": 0.167, "y": 0.132}, "t": 49, "s": [243.36, 381.841, 0], "to": [-0.03, -0.001, 0], "ti": [0.049, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.137}, "t": 50, "s": [243.24, 381.862, 0], "to": [-0.049, 0.015, 0], "ti": [0.067, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.145}, "t": 51, "s": [243.065, 381.932, 0], "to": [-0.067, 0.031, 0], "ti": [0.082, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.151}, "t": 52, "s": [242.84, 382.051, 0], "to": [-0.082, 0.047, 0], "ti": [0.096, -0.061, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.155}, "t": 53, "s": [242.571, 382.215, 0], "to": [-0.096, 0.061, 0], "ti": [0.106, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.158}, "t": 54, "s": [242.266, 382.42, 0], "to": [-0.106, 0.074, 0], "ti": [0.114, -0.085, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 55, "s": [241.933, 382.66, 0], "to": [-0.114, 0.085, 0], "ti": [0.117, -0.093, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 56, "s": [241.584, 382.93, 0], "to": [-0.117, 0.093, 0], "ti": [0.117, -0.098, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.166}, "t": 57, "s": [241.229, 383.219, 0], "to": [-0.117, 0.098, 0], "ti": [0.112, -0.1, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.17}, "t": 58, "s": [240.882, 383.52, 0], "to": [-0.112, 0.1, 0], "ti": [0.102, -0.098, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.174}, "t": 59, "s": [240.557, 383.82, 0], "to": [-0.102, 0.098, 0], "ti": [0.086, -0.092, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.18}, "t": 60, "s": [240.27, 384.109, 0], "to": [-0.086, 0.092, 0], "ti": [0.065, -0.081, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.19}, "t": 61, "s": [240.039, 384.371, 0], "to": [-0.065, 0.081, 0], "ti": [0.037, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.211}, "t": 62, "s": [239.88, 384.593, 0], "to": [-0.037, 0.064, 0], "ti": [0.015, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.199}, "t": 63, "s": [239.816, 384.757, 0], "to": [-0.015, 0.048, 0], "ti": [0.016, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.159}, "t": 64, "s": [239.791, 384.881, 0], "to": [-0.016, 0.041, 0], "ti": [0.031, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 65, "s": [239.717, 385, 0], "to": [-0.031, 0.038, 0], "ti": [0.043, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 66, "s": [239.603, 385.108, 0], "to": [-0.043, 0.033, 0], "ti": [0.053, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 67, "s": [239.456, 385.198, 0], "to": [-0.053, 0.027, 0], "ti": [0.061, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 68, "s": [239.284, 385.267, 0], "to": [-0.061, 0.019, 0], "ti": [0.066, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 69, "s": [239.093, 385.311, 0], "to": [-0.066, 0.01, 0], "ti": [0.069, 0, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 70, "s": [238.889, 385.326, 0], "to": [-0.069, 0, 0], "ti": [0.07, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 71, "s": [238.679, 385.312, 0], "to": [-0.07, -0.01, 0], "ti": [0.07, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.165}, "t": 72, "s": [238.467, 385.265, 0], "to": [-0.07, -0.021, 0], "ti": [0.068, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 73, "s": [238.258, 385.187, 0], "to": [-0.068, -0.032, 0], "ti": [0.065, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 74, "s": [238.058, 385.076, 0], "to": [-0.065, -0.042, 0], "ti": [0.06, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 75, "s": [237.87, 384.935, 0], "to": [-0.06, -0.052, 0], "ti": [0.054, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 76, "s": [237.698, 384.765, 0], "to": [-0.054, -0.061, 0], "ti": [0.047, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 77, "s": [237.545, 384.568, 0], "to": [-0.047, -0.069, 0], "ti": [0.04, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 78, "s": [237.413, 384.348, 0], "to": [-0.04, -0.076, 0], "ti": [0.032, 0.082, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 79, "s": [237.305, 384.11, 0], "to": [-0.032, -0.082, 0], "ti": [0.023, 0.085, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [237.223, 383.858, 0], "to": [-0.023, -0.085, 0], "ti": [0.014, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 81, "s": [237.169, 383.599, 0], "to": [-0.014, -0.087, 0], "ti": [0.004, 0.086, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.169}, "t": 82, "s": [237.142, 383.338, 0], "to": [-0.004, -0.086, 0], "ti": [-0.005, 0.082, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.171}, "t": 83, "s": [237.144, 383.085, 0], "to": [0.005, -0.082, 0], "ti": [-0.015, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.173}, "t": 84, "s": [237.174, 382.846, 0], "to": [0.015, -0.076, 0], "ti": [-0.024, 0.066, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 85, "s": [237.233, 382.631, 0], "to": [0.024, -0.066, 0], "ti": [-0.033, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.178}, "t": 86, "s": [237.319, 382.449, 0], "to": [0.033, -0.053, 0], "ti": [-0.038, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.202}, "t": 87, "s": [237.431, 382.312, 0], "to": [0.038, -0.03, 0], "ti": [-0.033, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.775}, "o": {"x": 0.167, "y": 0.16}, "t": 88, "s": [237.544, 382.271, 0], "to": [0.033, 0.01, 0], "ti": [-0.025, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.132}, "t": 89, "s": [237.631, 382.371, 0], "to": [0.025, 0.053, 0], "ti": [-0.019, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.142}, "t": 90, "s": [237.696, 382.59, 0], "to": [0.019, 0.09, 0], "ti": [-0.014, -0.121, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.15}, "t": 91, "s": [237.744, 382.912, 0], "to": [0.014, 0.121, 0], "ti": [-0.01, -0.146, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.155}, "t": 92, "s": [237.779, 383.316, 0], "to": [0.01, 0.146, 0], "ti": [-0.008, -0.165, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 93, "s": [237.806, 383.787, 0], "to": [0.008, 0.165, 0], "ti": [-0.007, -0.179, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.162}, "t": 94, "s": [237.829, 384.308, 0], "to": [0.007, 0.179, 0], "ti": [-0.004, -0.092, 0]}, {"t": 95, "s": [237.85, 384.863, 0]}], "ix": 2}, "a": {"a": 0, "k": [15.701, 16.078, 0], "ix": 1}, "s": {"a": 0, "k": [110, 110, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.221, 3.203], [-3.203, -6.221], [6.22, -3.203], [3.203, 6.221]], "o": [[6.221, -3.203], [3.204, 6.221], [-6.222, 3.203], [-3.203, -6.221]], "v": [[-5.8, -11.264], [11.264, -5.8], [5.801, 11.264], [-11.264, 5.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.179, -0.093], [0.162, -0.128], [0.062, -0.067], [0.133, -0.541], [0.245, -1.019], [-0.931, -1.297], [-1.009, 0.266], [-1.715, 0.883], [0, 0], [0, 0], [-0.432, 0.339], [0.16, 0.802], [1.372, 0.439], [0.971, 0.392], [0.57, -0.083], [0.083, -0.026]], "o": [[-0.181, 0.092], [-0.069, 0.052], [-0.399, 0.415], [-0.238, 1.015], [-0.439, 1.373], [0.564, 0.603], [0.558, -0.156], [0, 0], [0, 0], [1.635, -0.796], [0.803, -0.667], [-0.511, -1.521], [-0.965, -0.396], [-0.511, -0.21], [-0.09, 0.012], [-0.187, 0.061]], "v": [[-0.368, -1.561], [-0.897, -1.245], [-1.094, -1.066], [-1.833, 0.47], [-2.512, 3.365], [-2.618, 7.61], [-0.181, 8.385], [3.493, 6.108], [3.618, 6.045], [3.876, 5.915], [7.605, 4.376], [8.389, 1.942], [4.876, -0.448], [2.126, -1.577], [0.445, -1.867], [0.185, -1.811]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0.955, -0.492], [-0.491, -0.955], [-0.954, 0.491], [0.491, 0.954]], "o": [[-0.955, 0.491], [0.492, 0.954], [0.955, -0.492], [-0.492, -0.956]], "v": [[-6.801, -1.311], [-7.641, 1.308], [-5.021, 2.146], [-4.182, -0.472]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0.955, -0.492], [-0.492, -0.954], [-0.955, 0.492], [0.492, 0.955]], "o": [[-0.954, 0.492], [0.491, 0.956], [0.954, -0.491], [-0.491, -0.954]], "v": [[3.57, -6.652], [2.733, -4.034], [5.352, -3.195], [6.19, -5.814]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0.954, -0.491], [-0.492, -0.954], [-0.955, 0.492], [0.491, 0.955]], "o": [[-0.955, 0.492], [0.491, 0.955], [0.954, -0.492], [-0.492, -0.955]], "v": [[-5.113, -5.68], [-5.952, -3.061], [-3.333, -2.222], [-2.494, -4.841]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0.956, -0.491], [-0.492, -0.955], [-0.955, 0.491], [0.492, 0.954]], "o": [[-0.954, 0.491], [0.492, 0.954], [0.954, -0.492], [-0.491, -0.955]], "v": [[-0.965, -7.816], [-1.803, -5.197], [0.816, -4.358], [1.654, -6.977]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.995000023935, 0.779999976065, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.334, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.995000023935, 0.779999976065, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.302, 15.302], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.536, 2.851], [-2.851, -5.536], [5.536, -2.85], [2.85, 5.536]], "o": [[5.536, -2.851], [2.85, 5.536], [-5.536, 2.851], [-2.851, -5.537]], "v": [[-5.161, -10.024], [10.024, -5.161], [5.162, 10.024], [-10.024, 5.162]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.262, 15.225], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.221, 3.203], [-3.203, -6.221], [6.221, -3.204], [3.203, 6.222]], "o": [[6.221, -3.204], [3.203, 6.221], [-6.221, 3.203], [-3.203, -6.221]], "v": [[-5.8, -11.264], [11.264, -5.8], [5.8, 11.265], [-11.264, 5.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.922000002394, 0.713999968884, 0.156999999402, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.1, 16.852], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 8, "op": 96, "st": 8, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "coin 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [0]}, {"t": 128, "s": [360]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.411}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [290.115, 501.59, 0], "to": [-0.052, -1.825, 0], "ti": [0.219, 12.908, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.097}, "t": 9, "s": [289.805, 490.64, 0], "to": [-0.219, -12.908, 0], "ti": [0.349, 23.423, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.158}, "t": 10, "s": [288.801, 424.141, 0], "to": [-0.349, -23.423, 0], "ti": [0.287, 18.546, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.249}, "t": 11, "s": [287.708, 350.104, 0], "to": [-0.287, -18.546, 0], "ti": [0.175, 9.889, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.224}, "t": 12, "s": [287.079, 312.866, 0], "to": [-0.175, -9.889, 0], "ti": [0.12, 6.049, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.213}, "t": 13, "s": [286.657, 290.768, 0], "to": [-0.12, -6.049, 0], "ti": [0.084, 3.91, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.211}, "t": 14, "s": [286.358, 276.572, 0], "to": [-0.084, -3.91, 0], "ti": [0.056, 2.52, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.215}, "t": 15, "s": [286.152, 267.306, 0], "to": [-0.056, -2.52, 0], "ti": [0.031, 1.53, 0]}, {"i": {"x": 0.833, "y": 0.883}, "o": {"x": 0.167, "y": 0.23}, "t": 16, "s": [286.024, 261.453, 0], "to": [-0.031, -1.53, 0], "ti": [0.008, 0.78, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.287}, "t": 17, "s": [285.967, 258.129, 0], "to": [-0.008, -0.78, 0], "ti": [-0.013, 0.316, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.293}, "t": 18, "s": [285.978, 256.771, 0], "to": [0.013, -0.316, 0], "ti": [-0.029, 0.184, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 19, "s": [286.043, 256.235, 0], "to": [0.029, -0.184, 0], "ti": [-0.045, 0.193, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 20, "s": [286.154, 255.667, 0], "to": [0.045, -0.193, 0], "ti": [-0.06, 0.196, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 21, "s": [286.311, 255.08, 0], "to": [0.06, -0.196, 0], "ti": [-0.074, 0.194, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [286.512, 254.49, 0], "to": [0.074, -0.194, 0], "ti": [-0.088, 0.185, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.169}, "t": 23, "s": [286.755, 253.916, 0], "to": [0.088, -0.185, 0], "ti": [-0.1, 0.17, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.171}, "t": 24, "s": [287.038, 253.377, 0], "to": [0.1, -0.17, 0], "ti": [-0.112, 0.146, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 25, "s": [287.358, 252.897, 0], "to": [0.112, -0.146, 0], "ti": [-0.121, 0.115, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.176}, "t": 26, "s": [287.708, 252.499, 0], "to": [0.121, -0.115, 0], "ti": [-0.124, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 27, "s": [288.083, 252.207, 0], "to": [0.124, -0.087, 0], "ti": [-0.119, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 28, "s": [288.452, 251.976, 0], "to": [0.119, -0.07, 0], "ti": [-0.109, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.179}, "t": 29, "s": [288.796, 251.785, 0], "to": [0.109, -0.056, 0], "ti": [-0.097, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.182}, "t": 30, "s": [289.107, 251.639, 0], "to": [0.097, -0.04, 0], "ti": [-0.084, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.187}, "t": 31, "s": [289.379, 251.543, 0], "to": [0.084, -0.023, 0], "ti": [-0.069, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.189}, "t": 32, "s": [289.608, 251.5, 0], "to": [0.069, -0.005, 0], "ti": [-0.052, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.185}, "t": 33, "s": [289.79, 251.513, 0], "to": [0.052, 0.014, 0], "ti": [-0.036, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [289.922, 251.581, 0], "to": [0.036, 0.032, 0], "ti": [-0.018, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.152}, "t": 35, "s": [290.003, 251.707, 0], "to": [0.018, 0.051, 0], "ti": [-0.002, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.149}, "t": 36, "s": [290.033, 251.887, 0], "to": [0.002, 0.069, 0], "ti": [0.015, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.15}, "t": 37, "s": [290.012, 252.12, 0], "to": [-0.015, 0.086, 0], "ti": [0.031, -0.102, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.153}, "t": 38, "s": [289.943, 252.402, 0], "to": [-0.031, 0.102, 0], "ti": [0.045, -0.116, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 39, "s": [289.829, 252.729, 0], "to": [-0.045, 0.116, 0], "ti": [0.057, -0.127, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.158}, "t": 40, "s": [289.674, 253.096, 0], "to": [-0.057, 0.127, 0], "ti": [0.068, -0.137, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 41, "s": [289.484, 253.494, 0], "to": [-0.068, 0.137, 0], "ti": [0.076, -0.144, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 42, "s": [289.266, 253.917, 0], "to": [-0.076, 0.144, 0], "ti": [0.082, -0.147, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.165}, "t": 43, "s": [289.027, 254.355, 0], "to": [-0.082, 0.147, 0], "ti": [0.084, -0.147, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [288.777, 254.798, 0], "to": [-0.084, 0.147, 0], "ti": [0.082, -0.143, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.17}, "t": 45, "s": [288.526, 255.235, 0], "to": [-0.082, 0.143, 0], "ti": [0.077, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.174}, "t": 46, "s": [288.285, 255.653, 0], "to": [-0.077, 0.134, 0], "ti": [0.067, -0.121, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.179}, "t": 47, "s": [288.066, 256.039, 0], "to": [-0.067, 0.121, 0], "ti": [0.052, -0.103, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.188}, "t": 48, "s": [287.884, 256.378, 0], "to": [-0.052, 0.103, 0], "ti": [0.032, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.207}, "t": 49, "s": [287.753, 256.654, 0], "to": [-0.032, 0.079, 0], "ti": [0.008, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.25}, "t": 50, "s": [287.69, 256.851, 0], "to": [-0.008, 0.049, 0], "ti": [-0.003, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.257}, "t": 51, "s": [287.708, 256.951, 0], "to": [0.003, 0.025, 0], "ti": [0.008, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.766}, "o": {"x": 0.167, "y": 0.155}, "t": 52, "s": [287.711, 256.999, 0], "to": [-0.008, 0.011, 0], "ti": [0.026, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.793}, "o": {"x": 0.167, "y": 0.129}, "t": 53, "s": [287.658, 257.018, 0], "to": [-0.026, 0.001, 0], "ti": [0.042, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.14}, "t": 54, "s": [287.555, 257.007, 0], "to": [-0.042, -0.009, 0], "ti": [0.057, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.147}, "t": 55, "s": [287.405, 256.966, 0], "to": [-0.057, -0.019, 0], "ti": [0.069, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.152}, "t": 56, "s": [287.215, 256.896, 0], "to": [-0.069, -0.029, 0], "ti": [0.08, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 57, "s": [286.99, 256.795, 0], "to": [-0.08, -0.038, 0], "ti": [0.089, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 58, "s": [286.735, 256.667, 0], "to": [-0.089, -0.047, 0], "ti": [0.096, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 59, "s": [286.455, 256.511, 0], "to": [-0.096, -0.056, 0], "ti": [0.102, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 60, "s": [286.156, 256.33, 0], "to": [-0.102, -0.064, 0], "ti": [0.106, 0.071, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 61, "s": [285.843, 256.126, 0], "to": [-0.106, -0.071, 0], "ti": [0.108, 0.078, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 62, "s": [285.52, 255.902, 0], "to": [-0.108, -0.078, 0], "ti": [0.108, 0.083, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 63, "s": [285.194, 255.66, 0], "to": [-0.108, -0.083, 0], "ti": [0.107, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 64, "s": [284.87, 255.405, 0], "to": [-0.107, -0.087, 0], "ti": [0.104, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 65, "s": [284.552, 255.139, 0], "to": [-0.104, -0.09, 0], "ti": [0.099, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 66, "s": [284.246, 254.868, 0], "to": [-0.099, -0.091, 0], "ti": [0.093, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 67, "s": [283.957, 254.595, 0], "to": [-0.093, -0.09, 0], "ti": [0.084, 0.088, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 68, "s": [283.69, 254.326, 0], "to": [-0.084, -0.088, 0], "ti": [0.074, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 69, "s": [283.451, 254.067, 0], "to": [-0.074, -0.084, 0], "ti": [0.063, 0.078, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.179}, "t": 70, "s": [283.244, 253.823, 0], "to": [-0.063, -0.078, 0], "ti": [0.049, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.184}, "t": 71, "s": [283.075, 253.601, 0], "to": [-0.049, -0.069, 0], "ti": [0.034, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.192}, "t": 72, "s": [282.948, 253.407, 0], "to": [-0.034, -0.059, 0], "ti": [0.017, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.865}, "o": {"x": 0.167, "y": 0.207}, "t": 73, "s": [282.87, 253.248, 0], "to": [-0.017, -0.046, 0], "ti": [-0.001, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.219}, "t": 74, "s": [282.845, 253.132, 0], "to": [0.001, -0.03, 0], "ti": [-0.013, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.719}, "o": {"x": 0.167, "y": 0.175}, "t": 75, "s": [282.876, 253.07, 0], "to": [0.013, -0.004, 0], "ti": [-0.015, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.786}, "o": {"x": 0.167, "y": 0.118}, "t": 76, "s": [282.923, 253.11, 0], "to": [0.015, 0.032, 0], "ti": [-0.016, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.137}, "t": 77, "s": [282.969, 253.261, 0], "to": [0.016, 0.065, 0], "ti": [-0.018, -0.093, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.147}, "t": 78, "s": [283.02, 253.502, 0], "to": [0.018, 0.093, 0], "ti": [-0.022, -0.115, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.154}, "t": 79, "s": [283.08, 253.817, 0], "to": [0.022, 0.115, 0], "ti": [-0.027, -0.131, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.158}, "t": 80, "s": [283.153, 254.19, 0], "to": [0.027, 0.131, 0], "ti": [-0.033, -0.143, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 81, "s": [283.241, 254.605, 0], "to": [0.033, 0.143, 0], "ti": [-0.039, -0.15, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 82, "s": [283.348, 255.047, 0], "to": [0.039, 0.15, 0], "ti": [-0.046, -0.153, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.165}, "t": 83, "s": [283.475, 255.504, 0], "to": [0.046, 0.153, 0], "ti": [-0.053, -0.151, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [283.623, 255.963, 0], "to": [0.053, 0.151, 0], "ti": [-0.061, -0.146, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 85, "s": [283.794, 256.412, 0], "to": [0.061, 0.146, 0], "ti": [-0.068, -0.138, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 86, "s": [283.988, 256.842, 0], "to": [0.068, 0.138, 0], "ti": [-0.075, -0.127, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 87, "s": [284.204, 257.242, 0], "to": [0.075, 0.127, 0], "ti": [-0.082, -0.113, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 88, "s": [284.44, 257.605, 0], "to": [0.082, 0.113, 0], "ti": [-0.088, -0.097, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 89, "s": [284.696, 257.923, 0], "to": [0.088, 0.097, 0], "ti": [-0.094, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.173}, "t": 90, "s": [284.97, 258.189, 0], "to": [0.094, 0.079, 0], "ti": [-0.098, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.172}, "t": 91, "s": [285.258, 258.399, 0], "to": [0.098, 0.06, 0], "ti": [-0.101, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.171}, "t": 92, "s": [285.556, 258.548, 0], "to": [0.101, 0.039, 0], "ti": [-0.102, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.169}, "t": 93, "s": [285.862, 258.632, 0], "to": [0.102, 0.017, 0], "ti": [-0.102, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [286.17, 258.649, 0], "to": [0.102, -0.006, 0], "ti": [-0.051, 0.009, 0]}, {"t": 95, "s": [286.476, 258.598, 0]}], "ix": 2}, "a": {"a": 0, "k": [15.701, 16.078, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.221, 3.203], [-3.203, -6.221], [6.22, -3.203], [3.203, 6.221]], "o": [[6.221, -3.203], [3.204, 6.221], [-6.222, 3.203], [-3.203, -6.221]], "v": [[-5.8, -11.264], [11.264, -5.8], [5.801, 11.264], [-11.264, 5.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.179, -0.093], [0.162, -0.128], [0.062, -0.067], [0.133, -0.541], [0.245, -1.019], [-0.931, -1.297], [-1.009, 0.266], [-1.715, 0.883], [0, 0], [0, 0], [-0.432, 0.339], [0.16, 0.802], [1.372, 0.439], [0.971, 0.392], [0.57, -0.083], [0.083, -0.026]], "o": [[-0.181, 0.092], [-0.069, 0.052], [-0.399, 0.415], [-0.238, 1.015], [-0.439, 1.373], [0.564, 0.603], [0.558, -0.156], [0, 0], [0, 0], [1.635, -0.796], [0.803, -0.667], [-0.511, -1.521], [-0.965, -0.396], [-0.511, -0.21], [-0.09, 0.012], [-0.187, 0.061]], "v": [[-0.368, -1.561], [-0.897, -1.245], [-1.094, -1.066], [-1.833, 0.47], [-2.512, 3.365], [-2.618, 7.61], [-0.181, 8.385], [3.493, 6.108], [3.618, 6.045], [3.876, 5.915], [7.605, 4.376], [8.389, 1.942], [4.876, -0.448], [2.126, -1.577], [0.445, -1.867], [0.185, -1.811]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0.955, -0.492], [-0.491, -0.955], [-0.954, 0.491], [0.491, 0.954]], "o": [[-0.955, 0.491], [0.492, 0.954], [0.955, -0.492], [-0.492, -0.956]], "v": [[-6.801, -1.311], [-7.641, 1.308], [-5.021, 2.146], [-4.182, -0.472]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0.955, -0.492], [-0.492, -0.954], [-0.955, 0.492], [0.492, 0.955]], "o": [[-0.954, 0.492], [0.491, 0.956], [0.954, -0.491], [-0.491, -0.954]], "v": [[3.57, -6.652], [2.733, -4.034], [5.352, -3.195], [6.19, -5.814]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0.954, -0.491], [-0.492, -0.954], [-0.955, 0.492], [0.491, 0.955]], "o": [[-0.955, 0.492], [0.491, 0.955], [0.954, -0.492], [-0.492, -0.955]], "v": [[-5.113, -5.68], [-5.952, -3.061], [-3.333, -2.222], [-2.494, -4.841]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0.956, -0.491], [-0.492, -0.955], [-0.955, 0.491], [0.492, 0.954]], "o": [[-0.954, 0.491], [0.492, 0.954], [0.954, -0.492], [-0.491, -0.955]], "v": [[-0.965, -7.816], [-1.803, -5.197], [0.816, -4.358], [1.654, -6.977]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.995000023935, 0.779999976065, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.334, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.995000023935, 0.779999976065, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.302, 15.302], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.536, 2.851], [-2.851, -5.536], [5.536, -2.85], [2.85, 5.536]], "o": [[5.536, -2.851], [2.85, 5.536], [-5.536, 2.851], [-2.851, -5.537]], "v": [[-5.161, -10.024], [10.024, -5.161], [5.162, 10.024], [-10.024, 5.162]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.262, 15.225], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.221, 3.203], [-3.203, -6.221], [6.221, -3.204], [3.203, 6.222]], "o": [[6.221, -3.204], [3.203, 6.221], [-6.221, 3.203], [-3.203, -6.221]], "v": [[-5.8, -11.264], [11.264, -5.8], [5.8, 11.265], [-11.264, 5.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.922000002394, 0.713999968884, 0.156999999402, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.1, 16.852], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 8, "op": 96, "st": 8, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "coin 3", "sr": 1, "ks": {"o": {"a": 0, "k": 75, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [203]}, {"t": 130, "s": [360]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.442}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [341.325, 484.041, 0], "to": [0.011, -1.44, 0], "ti": [-0.033, 9.646, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.098}, "t": 11, "s": [341.389, 475.401, 0], "to": [0.033, -9.646, 0], "ti": [-0.055, 17.314, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.158}, "t": 12, "s": [341.52, 426.165, 0], "to": [0.055, -17.314, 0], "ti": [-0.078, 13.701, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.249}, "t": 13, "s": [341.72, 371.515, 0], "to": [0.078, -13.701, 0], "ti": [-0.099, 7.312, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.224}, "t": 14, "s": [341.988, 343.961, 0], "to": [0.099, -7.312, 0], "ti": [-0.107, 4.455, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.214}, "t": 15, "s": [342.314, 327.644, 0], "to": [0.107, -4.455, 0], "ti": [-0.102, 2.855, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.213}, "t": 16, "s": [342.632, 317.229, 0], "to": [0.102, -2.855, 0], "ti": [-0.092, 1.805, 0]}, {"i": {"x": 0.833, "y": 0.873}, "o": {"x": 0.167, "y": 0.219}, "t": 17, "s": [342.924, 310.517, 0], "to": [0.092, -1.805, 0], "ti": [-0.081, 1.048, 0]}, {"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.167, "y": 0.241}, "t": 18, "s": [343.185, 306.401, 0], "to": [0.081, -1.048, 0], "ti": [-0.069, 0.47, 0]}, {"i": {"x": 0.833, "y": 0.893}, "o": {"x": 0.167, "y": 0.351}, "t": 19, "s": [343.411, 304.231, 0], "to": [0.069, -0.47, 0], "ti": [-0.055, 0.104, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.383}, "t": 20, "s": [343.598, 303.583, 0], "to": [0.055, -0.104, 0], "ti": [-0.041, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.214}, "t": 21, "s": [343.743, 303.61, 0], "to": [0.041, 0.015, 0], "ti": [-0.026, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.172}, "t": 22, "s": [343.844, 303.673, 0], "to": [0.026, 0.027, 0], "ti": [-0.012, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.157}, "t": 23, "s": [343.9, 303.771, 0], "to": [0.012, 0.038, 0], "ti": [0.003, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.152}, "t": 24, "s": [343.913, 303.899, 0], "to": [-0.003, 0.047, 0], "ti": [0.017, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.152}, "t": 25, "s": [343.882, 304.054, 0], "to": [-0.017, 0.055, 0], "ti": [0.03, -0.062, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 26, "s": [343.811, 304.23, 0], "to": [-0.03, 0.062, 0], "ti": [0.042, -0.067, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.157}, "t": 27, "s": [343.702, 304.425, 0], "to": [-0.042, 0.067, 0], "ti": [0.052, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.16}, "t": 28, "s": [343.56, 304.632, 0], "to": [-0.052, 0.07, 0], "ti": [0.06, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.162}, "t": 29, "s": [343.389, 304.846, 0], "to": [-0.06, 0.072, 0], "ti": [0.067, -0.071, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.165}, "t": 30, "s": [343.197, 305.062, 0], "to": [-0.067, 0.071, 0], "ti": [0.07, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.168}, "t": 31, "s": [342.99, 305.272, 0], "to": [-0.07, 0.068, 0], "ti": [0.071, -0.063, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.171}, "t": 32, "s": [342.776, 305.47, 0], "to": [-0.071, 0.063, 0], "ti": [0.068, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.176}, "t": 33, "s": [342.565, 305.649, 0], "to": [-0.068, 0.055, 0], "ti": [0.062, -0.045, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.182}, "t": 34, "s": [342.367, 305.801, 0], "to": [-0.062, 0.045, 0], "ti": [0.052, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.195}, "t": 35, "s": [342.193, 305.918, 0], "to": [-0.052, 0.032, 0], "ti": [0.038, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.225}, "t": 36, "s": [342.054, 305.992, 0], "to": [-0.038, 0.016, 0], "ti": [0.019, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.743}, "o": {"x": 0.167, "y": 0.226}, "t": 37, "s": [341.965, 306.013, 0], "to": [-0.019, -0.003, 0], "ti": [-0.002, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.123}, "t": 38, "s": [341.939, 305.973, 0], "to": [0.002, -0.024, 0], "ti": [-0.009, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.148}, "t": 39, "s": [341.976, 305.867, 0], "to": [0.009, -0.041, 0], "ti": [-0.001, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.156}, "t": 40, "s": [341.993, 305.724, 0], "to": [0.001, -0.051, 0], "ti": [0.008, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 41, "s": [341.982, 305.559, 0], "to": [-0.008, -0.057, 0], "ti": [0.017, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.162}, "t": 42, "s": [341.943, 305.38, 0], "to": [-0.017, -0.061, 0], "ti": [0.024, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 43, "s": [341.881, 305.196, 0], "to": [-0.024, -0.061, 0], "ti": [0.031, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 44, "s": [341.797, 305.013, 0], "to": [-0.031, -0.06, 0], "ti": [0.037, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 45, "s": [341.695, 304.838, 0], "to": [-0.037, -0.056, 0], "ti": [0.042, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 46, "s": [341.577, 304.677, 0], "to": [-0.042, -0.051, 0], "ti": [0.045, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 47, "s": [341.446, 304.534, 0], "to": [-0.045, -0.044, 0], "ti": [0.048, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 48, "s": [341.304, 304.414, 0], "to": [-0.048, -0.036, 0], "ti": [0.05, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 49, "s": [341.155, 304.318, 0], "to": [-0.05, -0.027, 0], "ti": [0.051, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.171}, "t": 50, "s": [341.002, 304.251, 0], "to": [-0.051, -0.018, 0], "ti": [0.052, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.17}, "t": 51, "s": [340.846, 304.212, 0], "to": [-0.052, -0.008, 0], "ti": [0.051, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.168}, "t": 52, "s": [340.692, 304.204, 0], "to": [-0.051, 0.002, 0], "ti": [0.049, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [340.541, 304.225, 0], "to": [-0.049, 0.012, 0], "ti": [0.046, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.165}, "t": 54, "s": [340.398, 304.274, 0], "to": [-0.046, 0.021, 0], "ti": [0.043, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 55, "s": [340.263, 304.351, 0], "to": [-0.043, 0.029, 0], "ti": [0.038, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 56, "s": [340.142, 304.451, 0], "to": [-0.038, 0.037, 0], "ti": [0.032, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 57, "s": [340.035, 304.572, 0], "to": [-0.032, 0.043, 0], "ti": [0.026, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [339.947, 304.71, 0], "to": [-0.026, 0.048, 0], "ti": [0.018, -0.05, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.168}, "t": 59, "s": [339.88, 304.858, 0], "to": [-0.018, 0.05, 0], "ti": [0.01, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 60, "s": [339.838, 305.012, 0], "to": [-0.01, 0.051, 0], "ti": [0, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.172}, "t": 61, "s": [339.822, 305.165, 0], "to": [0, 0.049, 0], "ti": [-0.009, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [339.836, 305.309, 0], "to": [0.009, 0.047, 0], "ti": [-0.008, -0.054, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.147}, "t": 63, "s": [339.874, 305.447, 0], "to": [0.008, 0.054, 0], "ti": [0, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.151}, "t": 64, "s": [339.887, 305.635, 0], "to": [0, 0.07, 0], "ti": [0.008, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.157}, "t": 65, "s": [339.873, 305.868, 0], "to": [-0.008, 0.083, 0], "ti": [0.013, -0.091, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.161}, "t": 66, "s": [339.84, 306.132, 0], "to": [-0.013, 0.091, 0], "ti": [0.016, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.165}, "t": 67, "s": [339.795, 306.413, 0], "to": [-0.016, 0.094, 0], "ti": [0.017, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.168}, "t": 68, "s": [339.744, 306.699, 0], "to": [-0.017, 0.094, 0], "ti": [0.017, -0.091, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.172}, "t": 69, "s": [339.692, 306.979, 0], "to": [-0.017, 0.091, 0], "ti": [0.015, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 70, "s": [339.643, 307.244, 0], "to": [-0.015, 0.084, 0], "ti": [0.011, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.179}, "t": 71, "s": [339.603, 307.486, 0], "to": [-0.011, 0.075, 0], "ti": [0.007, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.185}, "t": 72, "s": [339.576, 307.697, 0], "to": [-0.007, 0.064, 0], "ti": [0.001, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.192}, "t": 73, "s": [339.563, 307.872, 0], "to": [-0.001, 0.051, 0], "ti": [-0.005, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.203}, "t": 74, "s": [339.568, 308.006, 0], "to": [0.005, 0.037, 0], "ti": [-0.012, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.203}, "t": 75, "s": [339.593, 308.096, 0], "to": [0.012, 0.022, 0], "ti": [-0.019, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.793}, "o": {"x": 0.167, "y": 0.161}, "t": 76, "s": [339.64, 308.139, 0], "to": [0.019, 0.007, 0], "ti": [-0.027, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.139}, "t": 77, "s": [339.708, 308.136, 0], "to": [0.027, -0.009, 0], "ti": [-0.034, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.142}, "t": 78, "s": [339.799, 308.087, 0], "to": [0.034, -0.024, 0], "ti": [-0.041, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.148}, "t": 79, "s": [339.912, 307.993, 0], "to": [0.041, -0.038, 0], "ti": [-0.048, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.152}, "t": 80, "s": [340.047, 307.858, 0], "to": [0.048, -0.051, 0], "ti": [-0.054, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 81, "s": [340.201, 307.686, 0], "to": [0.054, -0.063, 0], "ti": [-0.06, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 82, "s": [340.374, 307.482, 0], "to": [0.06, -0.072, 0], "ti": [-0.065, 0.079, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 83, "s": [340.561, 307.253, 0], "to": [0.065, -0.079, 0], "ti": [-0.068, 0.083, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 84, "s": [340.761, 307.008, 0], "to": [0.068, -0.083, 0], "ti": [-0.07, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [340.97, 306.755, 0], "to": [0.07, -0.084, 0], "ti": [-0.072, 0.08, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.169}, "t": 86, "s": [341.182, 306.505, 0], "to": [0.072, -0.08, 0], "ti": [-0.078, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 87, "s": [341.4, 306.272, 0], "to": [0.078, -0.072, 0], "ti": [-0.088, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 88, "s": [341.651, 306.07, 0], "to": [0.088, -0.062, 0], "ti": [-0.095, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [341.927, 305.902, 0], "to": [0.095, -0.051, 0], "ti": [-0.098, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 90, "s": [342.22, 305.767, 0], "to": [0.098, -0.04, 0], "ti": [-0.099, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 91, "s": [342.518, 305.664, 0], "to": [0.099, -0.029, 0], "ti": [-0.097, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 92, "s": [342.813, 305.592, 0], "to": [0.097, -0.019, 0], "ti": [-0.092, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 93, "s": [343.098, 305.551, 0], "to": [0.092, -0.009, 0], "ti": [-0.085, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.175}, "t": 94, "s": [343.365, 305.538, 0], "to": [0.085, 0, 0], "ti": [-0.041, -0.002, 0]}, {"t": 95, "s": [343.608, 305.552, 0]}], "ix": 2}, "a": {"a": 0, "k": [15.701, 16.078, 0], "ix": 1}, "s": {"a": 0, "k": [140, 140, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.221, 3.203], [-3.203, -6.221], [6.22, -3.203], [3.203, 6.221]], "o": [[6.221, -3.203], [3.204, 6.221], [-6.222, 3.203], [-3.203, -6.221]], "v": [[-5.8, -11.264], [11.264, -5.8], [5.801, 11.264], [-11.264, 5.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.179, -0.093], [0.162, -0.128], [0.062, -0.067], [0.133, -0.541], [0.245, -1.019], [-0.931, -1.297], [-1.009, 0.266], [-1.715, 0.883], [0, 0], [0, 0], [-0.432, 0.339], [0.16, 0.802], [1.372, 0.439], [0.971, 0.392], [0.57, -0.083], [0.083, -0.026]], "o": [[-0.181, 0.092], [-0.069, 0.052], [-0.399, 0.415], [-0.238, 1.015], [-0.439, 1.373], [0.564, 0.603], [0.558, -0.156], [0, 0], [0, 0], [1.635, -0.796], [0.803, -0.667], [-0.511, -1.521], [-0.965, -0.396], [-0.511, -0.21], [-0.09, 0.012], [-0.187, 0.061]], "v": [[-0.368, -1.561], [-0.897, -1.245], [-1.094, -1.066], [-1.833, 0.47], [-2.512, 3.365], [-2.618, 7.61], [-0.181, 8.385], [3.493, 6.108], [3.618, 6.045], [3.876, 5.915], [7.605, 4.376], [8.389, 1.942], [4.876, -0.448], [2.126, -1.577], [0.445, -1.867], [0.185, -1.811]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0.955, -0.492], [-0.491, -0.955], [-0.954, 0.491], [0.491, 0.954]], "o": [[-0.955, 0.491], [0.492, 0.954], [0.955, -0.492], [-0.492, -0.956]], "v": [[-6.801, -1.311], [-7.641, 1.308], [-5.021, 2.146], [-4.182, -0.472]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0.955, -0.492], [-0.492, -0.954], [-0.955, 0.492], [0.492, 0.955]], "o": [[-0.954, 0.492], [0.491, 0.956], [0.954, -0.491], [-0.491, -0.954]], "v": [[3.57, -6.652], [2.733, -4.034], [5.352, -3.195], [6.19, -5.814]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0.954, -0.491], [-0.492, -0.954], [-0.955, 0.492], [0.491, 0.955]], "o": [[-0.955, 0.492], [0.491, 0.955], [0.954, -0.492], [-0.492, -0.955]], "v": [[-5.113, -5.68], [-5.952, -3.061], [-3.333, -2.222], [-2.494, -4.841]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0.956, -0.491], [-0.492, -0.955], [-0.955, 0.491], [0.492, 0.954]], "o": [[-0.954, 0.491], [0.492, 0.954], [0.954, -0.492], [-0.491, -0.955]], "v": [[-0.965, -7.816], [-1.803, -5.197], [0.816, -4.358], [1.654, -6.977]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.995000023935, 0.779999976065, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.334, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.995000023935, 0.779999976065, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.302, 15.302], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.536, 2.851], [-2.851, -5.536], [5.536, -2.85], [2.85, 5.536]], "o": [[5.536, -2.851], [2.85, 5.536], [-5.536, 2.851], [-2.851, -5.537]], "v": [[-5.161, -10.024], [10.024, -5.161], [5.162, 10.024], [-10.024, 5.162]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.262, 15.225], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.221, 3.203], [-3.203, -6.221], [6.221, -3.204], [3.203, 6.222]], "o": [[6.221, -3.204], [3.203, 6.221], [-6.221, 3.203], [-3.203, -6.221]], "v": [[-5.8, -11.264], [11.264, -5.8], [5.8, 11.265], [-11.264, 5.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.922000002394, 0.713999968884, 0.156999999402, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.1, 16.852], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 10, "op": 96, "st": 10, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "coin 6", "sr": 1, "ks": {"o": {"a": 0, "k": 60, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [-194]}, {"t": 131, "s": [178]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.417}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [356.911, 533.026, 0], "to": [-0.006, -1.212, 0], "ti": [0.014, 8.474, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.097}, "t": 12, "s": [356.877, 525.752, 0], "to": [-0.014, -8.474, 0], "ti": [0.019, 15.338, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.158}, "t": 13, "s": [356.828, 482.181, 0], "to": [-0.019, -15.338, 0], "ti": [0.023, 12.132, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.249}, "t": 14, "s": [356.765, 433.722, 0], "to": [-0.023, -12.132, 0], "ti": [0.026, 6.452, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.224}, "t": 15, "s": [356.691, 409.39, 0], "to": [-0.026, -6.452, 0], "ti": [0.029, 3.925, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.214}, "t": 16, "s": [356.607, 395.007, 0], "to": [-0.029, -3.925, 0], "ti": [0.031, 2.514, 0]}, {"i": {"x": 0.833, "y": 0.865}, "o": {"x": 0.167, "y": 0.213}, "t": 17, "s": [356.517, 385.839, 0], "to": [-0.031, -2.514, 0], "ti": [0.032, 1.594, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.218}, "t": 18, "s": [356.422, 379.926, 0], "to": [-0.032, -1.594, 0], "ti": [0.032, 0.937, 0]}, {"i": {"x": 0.833, "y": 0.889}, "o": {"x": 0.167, "y": 0.238}, "t": 19, "s": [356.326, 376.274, 0], "to": [-0.032, -0.937, 0], "ti": [0.03, 0.437, 0]}, {"i": {"x": 0.833, "y": 0.898}, "o": {"x": 0.167, "y": 0.332}, "t": 20, "s": [356.232, 374.304, 0], "to": [-0.03, -0.437, 0], "ti": [0.028, 0.128, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.455}, "t": 21, "s": [356.144, 373.65, 0], "to": [-0.028, -0.128, 0], "ti": [0.024, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.163}, "t": 22, "s": [356.066, 373.533, 0], "to": [-0.024, -0.043, 0], "ti": [0.018, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.158}, "t": 23, "s": [356.002, 373.393, 0], "to": [-0.018, -0.051, 0], "ti": [0.011, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.157}, "t": 24, "s": [355.957, 373.226, 0], "to": [-0.011, -0.06, 0], "ti": [0.002, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.156}, "t": 25, "s": [355.935, 373.031, 0], "to": [-0.002, -0.07, 0], "ti": [-0.006, 0.08, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.156}, "t": 26, "s": [355.942, 372.806, 0], "to": [0.006, -0.08, 0], "ti": [-0.009, 0.089, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.161}, "t": 27, "s": [355.971, 372.55, 0], "to": [0.009, -0.089, 0], "ti": [-0.009, 0.093, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.166}, "t": 28, "s": [355.999, 372.274, 0], "to": [0.009, -0.093, 0], "ti": [-0.01, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.17}, "t": 29, "s": [356.027, 371.995, 0], "to": [0.01, -0.091, 0], "ti": [-0.01, 0.085, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.175}, "t": 30, "s": [356.056, 371.726, 0], "to": [0.01, -0.085, 0], "ti": [-0.012, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.18}, "t": 31, "s": [356.09, 371.482, 0], "to": [0.012, -0.076, 0], "ti": [-0.013, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.187}, "t": 32, "s": [356.127, 371.273, 0], "to": [0.013, -0.062, 0], "ti": [-0.015, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.199}, "t": 33, "s": [356.17, 371.109, 0], "to": [0.015, -0.046, 0], "ti": [-0.017, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.213}, "t": 34, "s": [356.219, 370.997, 0], "to": [0.017, -0.028, 0], "ti": [-0.02, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.788}, "o": {"x": 0.167, "y": 0.183}, "t": 35, "s": [356.275, 370.943, 0], "to": [0.02, -0.008, 0], "ti": [-0.022, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.787}, "o": {"x": 0.167, "y": 0.137}, "t": 36, "s": [356.338, 370.951, 0], "to": [0.022, 0.013, 0], "ti": [-0.025, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.802}, "o": {"x": 0.167, "y": 0.137}, "t": 37, "s": [356.408, 371.023, 0], "to": [0.025, 0.035, 0], "ti": [-0.027, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.144}, "t": 38, "s": [356.485, 371.161, 0], "to": [0.027, 0.057, 0], "ti": [-0.029, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.149}, "t": 39, "s": [356.57, 371.362, 0], "to": [0.029, 0.077, 0], "ti": [-0.032, -0.097, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.153}, "t": 40, "s": [356.662, 371.625, 0], "to": [0.032, 0.097, 0], "ti": [-0.033, -0.114, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 41, "s": [356.759, 371.943, 0], "to": [0.033, 0.114, 0], "ti": [-0.035, -0.13, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 42, "s": [356.862, 372.311, 0], "to": [0.035, 0.13, 0], "ti": [-0.036, -0.142, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 43, "s": [356.969, 372.721, 0], "to": [0.036, 0.142, 0], "ti": [-0.037, -0.15, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 44, "s": [357.08, 373.161, 0], "to": [0.037, 0.15, 0], "ti": [-0.037, -0.154, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 45, "s": [357.192, 373.622, 0], "to": [0.037, 0.154, 0], "ti": [-0.037, -0.154, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.168}, "t": 46, "s": [357.305, 374.088, 0], "to": [0.037, 0.154, 0], "ti": [-0.036, -0.148, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.172}, "t": 47, "s": [357.416, 374.545, 0], "to": [0.036, 0.148, 0], "ti": [-0.035, -0.136, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.176}, "t": 48, "s": [357.523, 374.975, 0], "to": [0.035, 0.136, 0], "ti": [-0.033, -0.117, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.183}, "t": 49, "s": [357.625, 375.36, 0], "to": [0.033, 0.117, 0], "ti": [-0.028, -0.099, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.179}, "t": 50, "s": [357.718, 375.679, 0], "to": [0.028, 0.099, 0], "ti": [-0.02, -0.098, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.16}, "t": 51, "s": [357.794, 375.957, 0], "to": [0.02, 0.098, 0], "ti": [-0.011, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.162}, "t": 52, "s": [357.84, 376.268, 0], "to": [0.011, 0.107, 0], "ti": [-0.004, -0.111, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.165}, "t": 53, "s": [357.862, 376.599, 0], "to": [0.004, 0.111, 0], "ti": [0.002, -0.111, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.168}, "t": 54, "s": [357.862, 376.936, 0], "to": [-0.002, 0.111, 0], "ti": [0.007, -0.108, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.171}, "t": 55, "s": [357.847, 377.267, 0], "to": [-0.007, 0.108, 0], "ti": [0.01, -0.1, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.174}, "t": 56, "s": [357.821, 377.581, 0], "to": [-0.01, 0.1, 0], "ti": [0.012, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.178}, "t": 57, "s": [357.786, 377.869, 0], "to": [-0.012, 0.09, 0], "ti": [0.013, -0.078, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.182}, "t": 58, "s": [357.748, 378.122, 0], "to": [-0.013, 0.078, 0], "ti": [0.013, -0.063, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.189}, "t": 59, "s": [357.708, 378.335, 0], "to": [-0.013, 0.063, 0], "ti": [0.011, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.201}, "t": 60, "s": [357.672, 378.502, 0], "to": [-0.011, 0.047, 0], "ti": [0.009, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.228}, "t": 61, "s": [357.64, 378.619, 0], "to": [-0.009, 0.03, 0], "ti": [0.006, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.77}, "o": {"x": 0.167, "y": 0.342}, "t": 62, "s": [357.615, 378.684, 0], "to": [-0.006, 0.013, 0], "ti": [0.003, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.728}, "o": {"x": 0.167, "y": 0.131}, "t": 63, "s": [357.601, 378.697, 0], "to": [-0.003, -0.004, 0], "ti": [-0.001, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.789}, "o": {"x": 0.167, "y": 0.12}, "t": 64, "s": [357.598, 378.658, 0], "to": [0.001, -0.021, 0], "ti": [-0.006, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.138}, "t": 65, "s": [357.609, 378.57, 0], "to": [0.006, -0.037, 0], "ti": [-0.011, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.147}, "t": 66, "s": [357.634, 378.436, 0], "to": [0.011, -0.051, 0], "ti": [-0.016, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.152}, "t": 67, "s": [357.675, 378.261, 0], "to": [0.016, -0.064, 0], "ti": [-0.022, 0.074, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.157}, "t": 68, "s": [357.733, 378.052, 0], "to": [0.022, -0.074, 0], "ti": [-0.028, 0.081, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.16}, "t": 69, "s": [357.808, 377.818, 0], "to": [0.028, -0.081, 0], "ti": [-0.034, 0.085, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.163}, "t": 70, "s": [357.9, 377.567, 0], "to": [0.034, -0.085, 0], "ti": [-0.039, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.166}, "t": 71, "s": [358.009, 377.31, 0], "to": [0.039, -0.084, 0], "ti": [-0.045, 0.08, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.17}, "t": 72, "s": [358.136, 377.061, 0], "to": [0.045, -0.08, 0], "ti": [-0.05, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.173}, "t": 73, "s": [358.279, 376.833, 0], "to": [0.05, -0.07, 0], "ti": [-0.057, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.173}, "t": 74, "s": [358.437, 376.64, 0], "to": [0.057, -0.055, 0], "ti": [-0.069, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.163}, "t": 75, "s": [358.622, 376.5, 0], "to": [0.069, -0.037, 0], "ti": [-0.081, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.161}, "t": 76, "s": [358.85, 376.416, 0], "to": [0.081, -0.019, 0], "ti": [-0.09, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 77, "s": [359.109, 376.384, 0], "to": [0.09, -0.002, 0], "ti": [-0.095, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 78, "s": [359.39, 376.401, 0], "to": [0.095, 0.013, 0], "ti": [-0.097, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 79, "s": [359.681, 376.463, 0], "to": [0.097, 0.028, 0], "ti": [-0.096, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 80, "s": [359.973, 376.568, 0], "to": [0.096, 0.041, 0], "ti": [-0.093, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 81, "s": [360.258, 376.709, 0], "to": [0.093, 0.053, 0], "ti": [-0.087, -0.063, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [360.529, 376.884, 0], "to": [0.087, 0.063, 0], "ti": [-0.079, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [360.78, 377.087, 0], "to": [0.079, 0.072, 0], "ti": [-0.07, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 84, "s": [361.005, 377.315, 0], "to": [0.07, 0.079, 0], "ti": [-0.06, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 85, "s": [361.2, 377.561, 0], "to": [0.06, 0.084, 0], "ti": [-0.048, -0.088, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 86, "s": [361.363, 377.821, 0], "to": [0.048, 0.088, 0], "ti": [-0.036, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 87, "s": [361.49, 378.089, 0], "to": [0.036, 0.09, 0], "ti": [-0.024, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 88, "s": [361.581, 378.361, 0], "to": [0.024, 0.09, 0], "ti": [-0.012, -0.088, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 89, "s": [361.635, 378.629, 0], "to": [0.012, 0.088, 0], "ti": [0, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 90, "s": [361.652, 378.889, 0], "to": [0, 0.084, 0], "ti": [0.011, -0.078, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 91, "s": [361.636, 379.133, 0], "to": [-0.011, 0.078, 0], "ti": [0.021, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 92, "s": [361.588, 379.357, 0], "to": [-0.021, 0.07, 0], "ti": [0.029, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 93, "s": [361.511, 379.552, 0], "to": [-0.029, 0.059, 0], "ti": [0.036, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.178}, "t": 94, "s": [361.412, 379.713, 0], "to": [-0.036, 0.047, 0], "ti": [0.02, -0.02, 0]}, {"t": 95, "s": [361.295, 379.831, 0]}], "ix": 2}, "a": {"a": 0, "k": [15.701, 16.078, 0], "ix": 1}, "s": {"a": 0, "k": [150, 150, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.221, 3.203], [-3.203, -6.221], [6.22, -3.203], [3.203, 6.221]], "o": [[6.221, -3.203], [3.204, 6.221], [-6.222, 3.203], [-3.203, -6.221]], "v": [[-5.8, -11.264], [11.264, -5.8], [5.801, 11.264], [-11.264, 5.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.179, -0.093], [0.162, -0.128], [0.062, -0.067], [0.133, -0.541], [0.245, -1.019], [-0.931, -1.297], [-1.009, 0.266], [-1.715, 0.883], [0, 0], [0, 0], [-0.432, 0.339], [0.16, 0.802], [1.372, 0.439], [0.971, 0.392], [0.57, -0.083], [0.083, -0.026]], "o": [[-0.181, 0.092], [-0.069, 0.052], [-0.399, 0.415], [-0.238, 1.015], [-0.439, 1.373], [0.564, 0.603], [0.558, -0.156], [0, 0], [0, 0], [1.635, -0.796], [0.803, -0.667], [-0.511, -1.521], [-0.965, -0.396], [-0.511, -0.21], [-0.09, 0.012], [-0.187, 0.061]], "v": [[-0.368, -1.561], [-0.897, -1.245], [-1.094, -1.066], [-1.833, 0.47], [-2.512, 3.365], [-2.618, 7.61], [-0.181, 8.385], [3.493, 6.108], [3.618, 6.045], [3.876, 5.915], [7.605, 4.376], [8.389, 1.942], [4.876, -0.448], [2.126, -1.577], [0.445, -1.867], [0.185, -1.811]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0.955, -0.492], [-0.491, -0.955], [-0.954, 0.491], [0.491, 0.954]], "o": [[-0.955, 0.491], [0.492, 0.954], [0.955, -0.492], [-0.492, -0.956]], "v": [[-6.801, -1.311], [-7.641, 1.308], [-5.021, 2.146], [-4.182, -0.472]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0.955, -0.492], [-0.492, -0.954], [-0.955, 0.492], [0.492, 0.955]], "o": [[-0.954, 0.492], [0.491, 0.956], [0.954, -0.491], [-0.491, -0.954]], "v": [[3.57, -6.652], [2.733, -4.034], [5.352, -3.195], [6.19, -5.814]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0.954, -0.491], [-0.492, -0.954], [-0.955, 0.492], [0.491, 0.955]], "o": [[-0.955, 0.492], [0.491, 0.955], [0.954, -0.492], [-0.492, -0.955]], "v": [[-5.113, -5.68], [-5.952, -3.061], [-3.333, -2.222], [-2.494, -4.841]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0.956, -0.491], [-0.492, -0.955], [-0.955, 0.491], [0.492, 0.954]], "o": [[-0.954, 0.491], [0.492, 0.954], [0.954, -0.492], [-0.491, -0.955]], "v": [[-0.965, -7.816], [-1.803, -5.197], [0.816, -4.358], [1.654, -6.977]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.995000023935, 0.779999976065, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.334, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.995000023935, 0.779999976065, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.302, 15.302], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.536, 2.851], [-2.851, -5.536], [5.536, -2.85], [2.85, 5.536]], "o": [[5.536, -2.851], [2.85, 5.536], [-5.536, 2.851], [-2.851, -5.537]], "v": [[-5.161, -10.024], [10.024, -5.161], [5.162, 10.024], [-10.024, 5.162]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.262, 15.225], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.221, 3.203], [-3.203, -6.221], [6.221, -3.204], [3.203, 6.222]], "o": [[6.221, -3.204], [3.203, 6.221], [-6.221, 3.203], [-3.203, -6.221]], "v": [[-5.8, -11.264], [11.264, -5.8], [5.8, 11.265], [-11.264, 5.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.922000002394, 0.713999968884, 0.156999999402, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.1, 16.852], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 11, "op": 96, "st": 11, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "coin 2", "sr": 1, "ks": {"o": {"a": 0, "k": 60, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [87]}, {"t": 131, "s": [360]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.423}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [221.309, 455.863, 0], "to": [0.004, -1.23, 0], "ti": [-0.007, 8.518, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.097}, "t": 12, "s": [221.33, 448.482, 0], "to": [0.007, -8.518, 0], "ti": [-0.006, 15.398, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.158}, "t": 13, "s": [221.35, 404.756, 0], "to": [0.006, -15.398, 0], "ti": [-0.005, 12.203, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.248}, "t": 14, "s": [221.364, 356.097, 0], "to": [0.005, -12.203, 0], "ti": [-0.007, 6.531, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.223}, "t": 15, "s": [221.379, 331.538, 0], "to": [0.007, -6.531, 0], "ti": [-0.01, 4.003, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.213}, "t": 16, "s": [221.405, 316.912, 0], "to": [0.01, -4.003, 0], "ti": [-0.015, 2.585, 0]}, {"i": {"x": 0.833, "y": 0.865}, "o": {"x": 0.167, "y": 0.211}, "t": 17, "s": [221.442, 307.518, 0], "to": [0.015, -2.585, 0], "ti": [-0.02, 1.656, 0]}, {"i": {"x": 0.833, "y": 0.871}, "o": {"x": 0.167, "y": 0.217}, "t": 18, "s": [221.493, 301.4, 0], "to": [0.02, -1.656, 0], "ti": [-0.025, 0.984, 0]}, {"i": {"x": 0.833, "y": 0.888}, "o": {"x": 0.167, "y": 0.236}, "t": 19, "s": [221.56, 297.582, 0], "to": [0.025, -0.984, 0], "ti": [-0.031, 0.465, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.326}, "t": 20, "s": [221.643, 295.499, 0], "to": [0.031, -0.465, 0], "ti": [-0.037, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.425}, "t": 21, "s": [221.744, 294.792, 0], "to": [0.037, -0.134, 0], "ti": [-0.043, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.184}, "t": 22, "s": [221.862, 294.694, 0], "to": [0.043, -0.024, 0], "ti": [-0.048, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.161}, "t": 23, "s": [221.999, 294.648, 0], "to": [0.048, -0.006, 0], "ti": [-0.054, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.153}, "t": 24, "s": [222.153, 294.659, 0], "to": [0.054, 0.013, 0], "ti": [-0.059, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.152}, "t": 25, "s": [222.323, 294.727, 0], "to": [0.059, 0.033, 0], "ti": [-0.064, -0.052, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.153}, "t": 26, "s": [222.509, 294.854, 0], "to": [0.064, 0.052, 0], "ti": [-0.069, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.154}, "t": 27, "s": [222.709, 295.037, 0], "to": [0.069, 0.07, 0], "ti": [-0.072, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 28, "s": [222.921, 295.274, 0], "to": [0.072, 0.087, 0], "ti": [-0.075, -0.102, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 29, "s": [223.142, 295.559, 0], "to": [0.075, 0.102, 0], "ti": [-0.077, -0.115, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 30, "s": [223.37, 295.887, 0], "to": [0.077, 0.115, 0], "ti": [-0.077, -0.125, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 31, "s": [223.602, 296.25, 0], "to": [0.077, 0.125, 0], "ti": [-0.077, -0.132, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.165}, "t": 32, "s": [223.834, 296.638, 0], "to": [0.077, 0.132, 0], "ti": [-0.075, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [224.063, 297.041, 0], "to": [0.075, 0.134, 0], "ti": [-0.071, -0.133, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.17}, "t": 34, "s": [224.283, 297.445, 0], "to": [0.071, 0.133, 0], "ti": [-0.066, -0.126, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.173}, "t": 35, "s": [224.49, 297.837, 0], "to": [0.066, 0.126, 0], "ti": [-0.059, -0.114, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.179}, "t": 36, "s": [224.679, 298.201, 0], "to": [0.059, 0.114, 0], "ti": [-0.05, -0.096, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.187}, "t": 37, "s": [224.845, 298.52, 0], "to": [0.05, 0.096, 0], "ti": [-0.042, -0.085, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.169}, "t": 38, "s": [224.982, 298.775, 0], "to": [0.042, 0.085, 0], "ti": [-0.038, -0.091, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.158}, "t": 39, "s": [225.1, 299.029, 0], "to": [0.038, 0.091, 0], "ti": [-0.035, -0.102, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.162}, "t": 40, "s": [225.208, 299.322, 0], "to": [0.035, 0.102, 0], "ti": [-0.032, -0.108, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.164}, "t": 41, "s": [225.307, 299.64, 0], "to": [0.032, 0.108, 0], "ti": [-0.03, -0.11, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [225.399, 299.969, 0], "to": [0.03, 0.11, 0], "ti": [-0.027, -0.108, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.169}, "t": 43, "s": [225.484, 300.298, 0], "to": [0.027, 0.108, 0], "ti": [-0.026, -0.103, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 44, "s": [225.564, 300.618, 0], "to": [0.026, 0.103, 0], "ti": [-0.024, -0.096, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.174}, "t": 45, "s": [225.639, 300.919, 0], "to": [0.024, 0.096, 0], "ti": [-0.023, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.177}, "t": 46, "s": [225.71, 301.193, 0], "to": [0.023, 0.086, 0], "ti": [-0.022, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.181}, "t": 47, "s": [225.778, 301.435, 0], "to": [0.022, 0.074, 0], "ti": [-0.022, -0.061, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.186}, "t": 48, "s": [225.844, 301.638, 0], "to": [0.022, 0.061, 0], "ti": [-0.022, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.192}, "t": 49, "s": [225.908, 301.8, 0], "to": [0.022, 0.047, 0], "ti": [-0.022, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.198}, "t": 50, "s": [225.973, 301.918, 0], "to": [0.022, 0.032, 0], "ti": [-0.022, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.195}, "t": 51, "s": [226.038, 301.99, 0], "to": [0.022, 0.016, 0], "ti": [-0.023, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [226.104, 302.017, 0], "to": [0.023, 0.001, 0], "ti": [-0.023, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.147}, "t": 53, "s": [226.173, 301.999, 0], "to": [0.023, -0.013, 0], "ti": [-0.025, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.146}, "t": 54, "s": [226.245, 301.939, 0], "to": [0.025, -0.026, 0], "ti": [-0.026, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.15}, "t": 55, "s": [226.32, 301.841, 0], "to": [0.026, -0.038, 0], "ti": [-0.028, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.155}, "t": 56, "s": [226.401, 301.71, 0], "to": [0.028, -0.048, 0], "ti": [-0.03, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.158}, "t": 57, "s": [226.486, 301.552, 0], "to": [0.03, -0.056, 0], "ti": [-0.032, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.162}, "t": 58, "s": [226.578, 301.375, 0], "to": [0.032, -0.061, 0], "ti": [-0.034, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.165}, "t": 59, "s": [226.677, 301.187, 0], "to": [0.034, -0.063, 0], "ti": [-0.037, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.169}, "t": 60, "s": [226.783, 300.999, 0], "to": [0.037, -0.061, 0], "ti": [-0.04, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 61, "s": [226.897, 300.823, 0], "to": [0.04, -0.055, 0], "ti": [-0.047, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.171}, "t": 62, "s": [227.021, 300.67, 0], "to": [0.047, -0.042, 0], "ti": [-0.058, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.162}, "t": 63, "s": [227.177, 300.568, 0], "to": [0.058, -0.024, 0], "ti": [-0.069, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.158}, "t": 64, "s": [227.371, 300.527, 0], "to": [0.069, -0.004, 0], "ti": [-0.076, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 65, "s": [227.59, 300.542, 0], "to": [0.076, 0.014, 0], "ti": [-0.08, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 66, "s": [227.826, 300.611, 0], "to": [0.08, 0.031, 0], "ti": [-0.08, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 67, "s": [228.068, 300.729, 0], "to": [0.08, 0.047, 0], "ti": [-0.078, -0.061, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 68, "s": [228.307, 300.891, 0], "to": [0.078, 0.061, 0], "ti": [-0.074, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 69, "s": [228.537, 301.093, 0], "to": [0.074, 0.073, 0], "ti": [-0.067, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 70, "s": [228.75, 301.33, 0], "to": [0.067, 0.084, 0], "ti": [-0.059, -0.093, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 71, "s": [228.941, 301.596, 0], "to": [0.059, 0.093, 0], "ti": [-0.05, -0.1, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 72, "s": [229.105, 301.887, 0], "to": [0.05, 0.1, 0], "ti": [-0.039, -0.106, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 73, "s": [229.239, 302.198, 0], "to": [0.039, 0.106, 0], "ti": [-0.028, -0.109, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [229.339, 302.521, 0], "to": [0.028, 0.109, 0], "ti": [-0.016, -0.11, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 75, "s": [229.405, 302.852, 0], "to": [0.016, 0.11, 0], "ti": [-0.004, -0.11, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 76, "s": [229.435, 303.184, 0], "to": [0.004, 0.11, 0], "ti": [0.008, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 77, "s": [229.43, 303.51, 0], "to": [-0.008, 0.107, 0], "ti": [0.019, -0.102, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 78, "s": [229.39, 303.825, 0], "to": [-0.019, 0.102, 0], "ti": [0.029, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 79, "s": [229.318, 304.121, 0], "to": [-0.029, 0.094, 0], "ti": [0.038, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 80, "s": [229.216, 304.391, 0], "to": [-0.038, 0.084, 0], "ti": [0.046, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 81, "s": [229.09, 304.627, 0], "to": [-0.046, 0.072, 0], "ti": [0.051, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 82, "s": [228.943, 304.823, 0], "to": [-0.051, 0.057, 0], "ti": [0.055, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.178}, "t": 83, "s": [228.783, 304.971, 0], "to": [-0.055, 0.04, 0], "ti": [0.056, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.177}, "t": 84, "s": [228.615, 305.062, 0], "to": [-0.056, 0.02, 0], "ti": [0.054, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.169}, "t": 85, "s": [228.448, 305.089, 0], "to": [-0.054, -0.003, 0], "ti": [0.055, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 86, "s": [228.29, 305.044, 0], "to": [-0.055, -0.02, 0], "ti": [0.062, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.157}, "t": 87, "s": [228.117, 304.968, 0], "to": [-0.062, -0.025, 0], "ti": [0.069, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.162}, "t": 88, "s": [227.918, 304.895, 0], "to": [-0.069, -0.024, 0], "ti": [0.072, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.165}, "t": 89, "s": [227.704, 304.823, 0], "to": [-0.072, -0.024, 0], "ti": [0.072, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.168}, "t": 90, "s": [227.485, 304.752, 0], "to": [-0.072, -0.024, 0], "ti": [0.069, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.171}, "t": 91, "s": [227.271, 304.68, 0], "to": [-0.069, -0.024, 0], "ti": [0.064, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.174}, "t": 92, "s": [227.07, 304.608, 0], "to": [-0.064, -0.024, 0], "ti": [0.056, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.178}, "t": 93, "s": [226.888, 304.534, 0], "to": [-0.056, -0.025, 0], "ti": [0.047, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.181}, "t": 94, "s": [226.733, 304.459, 0], "to": [-0.047, -0.025, 0], "ti": [0.021, 0.013, 0]}, {"t": 95, "s": [226.607, 304.383, 0]}], "ix": 2}, "a": {"a": 0, "k": [15.701, 16.078, 0], "ix": 1}, "s": {"a": 0, "k": [150, 150, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.221, 3.203], [-3.203, -6.221], [6.22, -3.203], [3.203, 6.221]], "o": [[6.221, -3.203], [3.204, 6.221], [-6.222, 3.203], [-3.203, -6.221]], "v": [[-5.8, -11.264], [11.264, -5.8], [5.801, 11.264], [-11.264, 5.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.179, -0.093], [0.162, -0.128], [0.062, -0.067], [0.133, -0.541], [0.245, -1.019], [-0.931, -1.297], [-1.009, 0.266], [-1.715, 0.883], [0, 0], [0, 0], [-0.432, 0.339], [0.16, 0.802], [1.372, 0.439], [0.971, 0.392], [0.57, -0.083], [0.083, -0.026]], "o": [[-0.181, 0.092], [-0.069, 0.052], [-0.399, 0.415], [-0.238, 1.015], [-0.439, 1.373], [0.564, 0.603], [0.558, -0.156], [0, 0], [0, 0], [1.635, -0.796], [0.803, -0.667], [-0.511, -1.521], [-0.965, -0.396], [-0.511, -0.21], [-0.09, 0.012], [-0.187, 0.061]], "v": [[-0.368, -1.561], [-0.897, -1.245], [-1.094, -1.066], [-1.833, 0.47], [-2.512, 3.365], [-2.618, 7.61], [-0.181, 8.385], [3.493, 6.108], [3.618, 6.045], [3.876, 5.915], [7.605, 4.376], [8.389, 1.942], [4.876, -0.448], [2.126, -1.577], [0.445, -1.867], [0.185, -1.811]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0.955, -0.492], [-0.491, -0.955], [-0.954, 0.491], [0.491, 0.954]], "o": [[-0.955, 0.491], [0.492, 0.954], [0.955, -0.492], [-0.492, -0.956]], "v": [[-6.801, -1.311], [-7.641, 1.308], [-5.021, 2.146], [-4.182, -0.472]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0.955, -0.492], [-0.492, -0.954], [-0.955, 0.492], [0.492, 0.955]], "o": [[-0.954, 0.492], [0.491, 0.956], [0.954, -0.491], [-0.491, -0.954]], "v": [[3.57, -6.652], [2.733, -4.034], [5.352, -3.195], [6.19, -5.814]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0.954, -0.491], [-0.492, -0.954], [-0.955, 0.492], [0.491, 0.955]], "o": [[-0.955, 0.492], [0.491, 0.955], [0.954, -0.492], [-0.492, -0.955]], "v": [[-5.113, -5.68], [-5.952, -3.061], [-3.333, -2.222], [-2.494, -4.841]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0.956, -0.491], [-0.492, -0.955], [-0.955, 0.491], [0.492, 0.954]], "o": [[-0.954, 0.491], [0.492, 0.954], [0.954, -0.492], [-0.491, -0.955]], "v": [[-0.965, -7.816], [-1.803, -5.197], [0.816, -4.358], [1.654, -6.977]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.995000023935, 0.779999976065, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.334, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.995000023935, 0.779999976065, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.302, 15.302], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.536, 2.851], [-2.851, -5.536], [5.536, -2.85], [2.85, 5.536]], "o": [[5.536, -2.851], [2.85, 5.536], [-5.536, 2.851], [-2.851, -5.537]], "v": [[-5.161, -10.024], [10.024, -5.161], [5.162, 10.024], [-10.024, 5.162]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.262, 15.225], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.221, 3.203], [-3.203, -6.221], [6.221, -3.204], [3.203, 6.222]], "o": [[6.221, -3.204], [3.203, 6.221], [-6.221, 3.203], [-3.203, -6.221]], "v": [[-5.8, -11.264], [11.264, -5.8], [5.8, 11.265], [-11.264, 5.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.922000002394, 0.713999968884, 0.156999999402, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.1, 16.852], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 11, "op": 96, "st": 11, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "coin", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 7, "s": [-29]}, {"t": 127, "s": [360]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.423}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [292.258, 475.425, 0], "to": [0.006, -1.056, 0], "ti": [0.06, 7.321, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.097}, "t": 8, "s": [292.296, 469.088, 0], "to": [-0.06, -7.321, 0], "ti": [0.14, 13.225, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.158}, "t": 9, "s": [291.897, 431.5, 0], "to": [-0.14, -13.225, 0], "ti": [0.094, 10.45, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.25}, "t": 10, "s": [291.454, 389.736, 0], "to": [-0.094, -10.45, 0], "ti": [0.017, 5.544, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.225}, "t": 11, "s": [291.333, 368.801, 0], "to": [-0.017, -5.544, 0], "ti": [-0.02, 3.353, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.215}, "t": 12, "s": [291.352, 356.47, 0], "to": [0.02, -3.353, 0], "ti": [-0.042, 2.122, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.215}, "t": 13, "s": [291.452, 348.683, 0], "to": [0.042, -2.122, 0], "ti": [-0.058, 1.316, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.223}, "t": 14, "s": [291.606, 343.74, 0], "to": [0.058, -1.316, 0], "ti": [-0.069, 0.735, 0]}, {"i": {"x": 0.833, "y": 0.894}, "o": {"x": 0.167, "y": 0.25}, "t": 15, "s": [291.799, 340.789, 0], "to": [0.069, -0.735, 0], "ti": [-0.078, 0.29, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.384}, "t": 16, "s": [292.021, 339.332, 0], "to": [0.078, -0.29, 0], "ti": [-0.083, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.182}, "t": 17, "s": [292.265, 339.05, 0], "to": [0.083, -0.01, 0], "ti": [-0.084, -0.076, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [292.517, 339.27, 0], "to": [0.084, 0.076, 0], "ti": [-0.083, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 19, "s": [292.769, 339.504, 0], "to": [0.083, 0.079, 0], "ti": [-0.08, -0.081, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.168}, "t": 20, "s": [293.014, 339.746, 0], "to": [0.08, 0.081, 0], "ti": [-0.076, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.171}, "t": 21, "s": [293.251, 339.987, 0], "to": [0.076, 0.079, 0], "ti": [-0.071, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.174}, "t": 22, "s": [293.473, 340.221, 0], "to": [0.071, 0.075, 0], "ti": [-0.064, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.178}, "t": 23, "s": [293.677, 340.438, 0], "to": [0.064, 0.068, 0], "ti": [-0.055, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.185}, "t": 24, "s": [293.856, 340.627, 0], "to": [0.055, 0.057, 0], "ti": [-0.045, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.196}, "t": 25, "s": [294.007, 340.777, 0], "to": [0.045, 0.043, 0], "ti": [-0.035, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.177}, "t": 26, "s": [294.125, 340.883, 0], "to": [0.035, 0.035, 0], "ti": [-0.028, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 27, "s": [294.219, 340.988, 0], "to": [0.028, 0.036, 0], "ti": [-0.023, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 28, "s": [294.294, 341.099, 0], "to": [0.023, 0.037, 0], "ti": [-0.018, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 29, "s": [294.354, 341.208, 0], "to": [0.018, 0.035, 0], "ti": [-0.013, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.179}, "t": 30, "s": [294.4, 341.311, 0], "to": [0.013, 0.033, 0], "ti": [-0.01, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.184}, "t": 31, "s": [294.433, 341.403, 0], "to": [0.01, 0.028, 0], "ti": [-0.007, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.194}, "t": 32, "s": [294.457, 341.481, 0], "to": [0.007, 0.023, 0], "ti": [-0.004, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.882}, "o": {"x": 0.167, "y": 0.213}, "t": 33, "s": [294.473, 341.54, 0], "to": [0.004, 0.016, 0], "ti": [-0.003, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.281}, "t": 34, "s": [294.483, 341.578, 0], "to": [0.003, 0.009, 0], "ti": [-0.002, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.655}, "o": {"x": 0.167, "y": 0.211}, "t": 35, "s": [294.489, 341.593, 0], "to": [0.002, 0.001, 0], "ti": [-0.001, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.11}, "t": 36, "s": [294.493, 341.584, 0], "to": [0.001, -0.007, 0], "ti": [-0.001, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.131}, "t": 37, "s": [294.496, 341.551, 0], "to": [0.001, -0.015, 0], "ti": [-0.002, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.142}, "t": 38, "s": [294.501, 341.492, 0], "to": [0.002, -0.023, 0], "ti": [-0.003, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.149}, "t": 39, "s": [294.508, 341.41, 0], "to": [0.003, -0.031, 0], "ti": [-0.005, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.153}, "t": 40, "s": [294.519, 341.305, 0], "to": [0.005, -0.038, 0], "ti": [-0.007, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 41, "s": [294.537, 341.18, 0], "to": [0.007, -0.045, 0], "ti": [-0.01, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 42, "s": [294.562, 341.037, 0], "to": [0.01, -0.05, 0], "ti": [-0.013, 0.054, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 43, "s": [294.595, 340.879, 0], "to": [0.013, -0.054, 0], "ti": [-0.016, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 44, "s": [294.639, 340.712, 0], "to": [0.016, -0.057, 0], "ti": [-0.02, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.165}, "t": 45, "s": [294.693, 340.539, 0], "to": [0.02, -0.058, 0], "ti": [-0.025, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [294.761, 340.367, 0], "to": [0.025, -0.056, 0], "ti": [-0.029, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 47, "s": [294.842, 340.201, 0], "to": [0.029, -0.053, 0], "ti": [-0.035, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 48, "s": [294.937, 340.048, 0], "to": [0.035, -0.047, 0], "ti": [-0.04, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.17}, "t": 49, "s": [295.049, 339.916, 0], "to": [0.04, -0.039, 0], "ti": [-0.048, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.165}, "t": 50, "s": [295.178, 339.814, 0], "to": [0.048, -0.026, 0], "ti": [-0.056, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.161}, "t": 51, "s": [295.336, 339.758, 0], "to": [0.056, -0.011, 0], "ti": [-0.062, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.16}, "t": 52, "s": [295.517, 339.75, 0], "to": [0.062, 0.005, 0], "ti": [-0.065, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 53, "s": [295.711, 339.788, 0], "to": [0.065, 0.02, 0], "ti": [-0.066, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 54, "s": [295.909, 339.868, 0], "to": [0.066, 0.034, 0], "ti": [-0.064, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 55, "s": [296.105, 339.989, 0], "to": [0.064, 0.046, 0], "ti": [-0.06, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 56, "s": [296.291, 340.147, 0], "to": [0.06, 0.058, 0], "ti": [-0.054, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 57, "s": [296.463, 340.339, 0], "to": [0.054, 0.069, 0], "ti": [-0.046, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.164}, "t": 58, "s": [296.613, 340.562, 0], "to": [0.046, 0.079, 0], "ti": [-0.038, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 59, "s": [296.74, 340.811, 0], "to": [0.038, 0.087, 0], "ti": [-0.028, -0.093, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 60, "s": [296.84, 341.082, 0], "to": [0.028, 0.093, 0], "ti": [-0.018, -0.099, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 61, "s": [296.909, 341.371, 0], "to": [0.018, 0.099, 0], "ti": [-0.008, -0.102, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 62, "s": [296.948, 341.674, 0], "to": [0.008, 0.102, 0], "ti": [0.003, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 63, "s": [296.956, 341.985, 0], "to": [-0.003, 0.104, 0], "ti": [0.013, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 64, "s": [296.932, 342.299, 0], "to": [-0.013, 0.104, 0], "ti": [0.023, -0.102, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [296.878, 342.61, 0], "to": [-0.023, 0.102, 0], "ti": [0.031, -0.098, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 66, "s": [296.796, 342.912, 0], "to": [-0.031, 0.098, 0], "ti": [0.039, -0.092, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 67, "s": [296.689, 343.2, 0], "to": [-0.039, 0.092, 0], "ti": [0.045, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 68, "s": [296.561, 343.467, 0], "to": [-0.045, 0.084, 0], "ti": [0.05, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.174}, "t": 69, "s": [296.417, 343.705, 0], "to": [-0.05, 0.074, 0], "ti": [0.052, -0.061, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.177}, "t": 70, "s": [296.262, 343.909, 0], "to": [-0.052, 0.061, 0], "ti": [0.053, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.182}, "t": 71, "s": [296.103, 344.071, 0], "to": [-0.053, 0.046, 0], "ti": [0.05, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.186}, "t": 72, "s": [295.947, 344.183, 0], "to": [-0.05, 0.028, 0], "ti": [0.046, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.183}, "t": 73, "s": [295.802, 344.237, 0], "to": [-0.046, 0.008, 0], "ti": [0.048, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 74, "s": [295.672, 344.233, 0], "to": [-0.048, -0.001, 0], "ti": [0.057, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.154}, "t": 75, "s": [295.516, 344.231, 0], "to": [-0.057, 0.001, 0], "ti": [0.064, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.159}, "t": 76, "s": [295.333, 344.241, 0], "to": [-0.064, 0.004, 0], "ti": [0.069, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.163}, "t": 77, "s": [295.131, 344.254, 0], "to": [-0.069, 0.004, 0], "ti": [0.07, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.166}, "t": 78, "s": [294.921, 344.266, 0], "to": [-0.07, 0.003, 0], "ti": [0.07, 0, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.169}, "t": 79, "s": [294.709, 344.271, 0], "to": [-0.07, 0, 0], "ti": [0.066, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 80, "s": [294.504, 344.264, 0], "to": [-0.066, -0.005, 0], "ti": [0.061, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 81, "s": [294.31, 344.241, 0], "to": [-0.061, -0.011, 0], "ti": [0.055, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.174}, "t": 82, "s": [294.135, 344.199, 0], "to": [-0.055, -0.018, 0], "ti": [0.047, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.173}, "t": 83, "s": [293.981, 344.136, 0], "to": [-0.047, -0.025, 0], "ti": [0.038, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.17}, "t": 84, "s": [293.854, 344.049, 0], "to": [-0.038, -0.033, 0], "ti": [0.028, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.165}, "t": 85, "s": [293.755, 343.937, 0], "to": [-0.028, -0.041, 0], "ti": [0.018, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.16}, "t": 86, "s": [293.686, 343.8, 0], "to": [-0.018, -0.05, 0], "ti": [0.007, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.158}, "t": 87, "s": [293.649, 343.639, 0], "to": [-0.007, -0.058, 0], "ti": [-0.003, 0.066, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.157}, "t": 88, "s": [293.644, 343.453, 0], "to": [0.003, -0.066, 0], "ti": [-0.013, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 89, "s": [293.67, 343.245, 0], "to": [0.013, -0.073, 0], "ti": [-0.023, 0.079, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 90, "s": [293.725, 343.017, 0], "to": [0.023, -0.079, 0], "ti": [-0.031, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.16}, "t": 91, "s": [293.807, 342.772, 0], "to": [0.031, -0.084, 0], "ti": [-0.038, 0.088, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.162}, "t": 92, "s": [293.913, 342.513, 0], "to": [0.038, -0.088, 0], "ti": [-0.044, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 93, "s": [294.038, 342.246, 0], "to": [0.044, -0.09, 0], "ti": [-0.048, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 94, "s": [294.177, 341.975, 0], "to": [0.048, -0.09, 0], "ti": [-0.025, 0.045, 0]}, {"t": 95, "s": [294.326, 341.705, 0]}], "ix": 2}, "a": {"a": 0, "k": [15.701, 16.078, 0], "ix": 1}, "s": {"a": 0, "k": [150, 150, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.221, 3.203], [-3.203, -6.221], [6.22, -3.203], [3.203, 6.221]], "o": [[6.221, -3.203], [3.204, 6.221], [-6.222, 3.203], [-3.203, -6.221]], "v": [[-5.8, -11.264], [11.264, -5.8], [5.801, 11.264], [-11.264, 5.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.179, -0.093], [0.162, -0.128], [0.062, -0.067], [0.133, -0.541], [0.245, -1.019], [-0.931, -1.297], [-1.009, 0.266], [-1.715, 0.883], [0, 0], [0, 0], [-0.432, 0.339], [0.16, 0.802], [1.372, 0.439], [0.971, 0.392], [0.57, -0.083], [0.083, -0.026]], "o": [[-0.181, 0.092], [-0.069, 0.052], [-0.399, 0.415], [-0.238, 1.015], [-0.439, 1.373], [0.564, 0.603], [0.558, -0.156], [0, 0], [0, 0], [1.635, -0.796], [0.803, -0.667], [-0.511, -1.521], [-0.965, -0.396], [-0.511, -0.21], [-0.09, 0.012], [-0.187, 0.061]], "v": [[-0.368, -1.561], [-0.897, -1.245], [-1.094, -1.066], [-1.833, 0.47], [-2.512, 3.365], [-2.618, 7.61], [-0.181, 8.385], [3.493, 6.108], [3.618, 6.045], [3.876, 5.915], [7.605, 4.376], [8.389, 1.942], [4.876, -0.448], [2.126, -1.577], [0.445, -1.867], [0.185, -1.811]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0.955, -0.492], [-0.491, -0.955], [-0.954, 0.491], [0.491, 0.954]], "o": [[-0.955, 0.491], [0.492, 0.954], [0.955, -0.492], [-0.492, -0.956]], "v": [[-6.801, -1.311], [-7.641, 1.308], [-5.021, 2.146], [-4.182, -0.472]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0.955, -0.492], [-0.492, -0.954], [-0.955, 0.492], [0.492, 0.955]], "o": [[-0.954, 0.492], [0.491, 0.956], [0.954, -0.491], [-0.491, -0.954]], "v": [[3.57, -6.652], [2.733, -4.034], [5.352, -3.195], [6.19, -5.814]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0.954, -0.491], [-0.492, -0.954], [-0.955, 0.492], [0.491, 0.955]], "o": [[-0.955, 0.492], [0.491, 0.955], [0.954, -0.492], [-0.492, -0.955]], "v": [[-5.113, -5.68], [-5.952, -3.061], [-3.333, -2.222], [-2.494, -4.841]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0.956, -0.491], [-0.492, -0.955], [-0.955, 0.491], [0.492, 0.954]], "o": [[-0.954, 0.491], [0.492, 0.954], [0.954, -0.492], [-0.491, -0.955]], "v": [[-0.965, -7.816], [-1.803, -5.197], [0.816, -4.358], [1.654, -6.977]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.995000023935, 0.779999976065, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.334, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.995000023935, 0.779999976065, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.302, 15.302], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.536, 2.851], [-2.851, -5.536], [5.536, -2.85], [2.85, 5.536]], "o": [[5.536, -2.851], [2.85, 5.536], [-5.536, 2.851], [-2.851, -5.537]], "v": [[-5.161, -10.024], [10.024, -5.161], [5.162, 10.024], [-10.024, 5.162]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.262, 15.225], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.221, 3.203], [-3.203, -6.221], [6.221, -3.204], [3.203, 6.222]], "o": [[6.221, -3.204], [3.203, 6.221], [-6.221, 3.203], [-3.203, -6.221]], "v": [[-5.8, -11.264], [11.264, -5.8], [5.8, 11.265], [-11.264, 5.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.922000002394, 0.713999968884, 0.156999999402, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.1, 16.852], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 7, "op": 96, "st": 7, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Light", "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [302, 397, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[53.5, 7], [-58, 9.5], [-113, 38], [105, 38]], "c": true}]}, {"t": 15, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[147, -178], [-166, -173], [-113, 38], [105, 38]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.072, 1, 1, 1, 0.536, 0.998, 0.89, 0.5, 1, 0.996, 0.78, 0, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-6.04, -91.303], "ix": 5}, "e": {"a": 0, "k": [-3.947, 31.021], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 5, "op": 101, "st": 5, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "ruban-left-anim", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [301.711, 406.899, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [-57, 57, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Turbulent Displace", "np": 16, "mn": "ADBE Turbulent Displace", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Displacement", "mn": "ADBE Turbulent Displace-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 0, "nm": "Amount", "mn": "ADBE Turbulent Displace-0002", "ix": 2, "v": {"a": 0, "k": 30, "ix": 2}}, {"ty": 0, "nm": "Size", "mn": "ADBE Turbulent Displace-0003", "ix": 3, "v": {"a": 0, "k": 100, "ix": 3}}, {"ty": 3, "nm": "Offset (Turbulence)", "mn": "ADBE Turbulent Displace-0004", "ix": 4, "v": {"a": 0, "k": [300, 400], "ix": 4}}, {"ty": 0, "nm": "Complexity", "mn": "ADBE Turbulent Displace-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 0, "nm": "Evolution", "mn": "ADBE Turbulent Displace-0006", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 6, "nm": "Evolution Options", "mn": "ADBE Turbulent Displace-0007", "ix": 7, "v": 0}, {"ty": 7, "nm": "Cycle Evolution", "mn": "ADBE Turbulent Displace-0008", "ix": 8, "v": {"a": 0, "k": 0, "ix": 8}}, {"ty": 0, "nm": "Cycle (in Revolutions)", "mn": "ADBE Turbulent Displace-0009", "ix": 9, "v": {"a": 0, "k": 1, "ix": 9}}, {"ty": 0, "nm": "Random Seed", "mn": "ADBE Turbulent Displace-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10}}, {"ty": 6, "nm": "Random Seed", "mn": "ADBE Turbulent Displace-0011", "ix": 11, "v": 0}, {"ty": 7, "nm": "<PERSON>nning", "mn": "ADBE Turbulent Displace-0012", "ix": 12, "v": {"a": 0, "k": 3, "ix": 12}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>er", "mn": "ADBE Turbulent Displace-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13}}, {"ty": 7, "nm": "Antialiasing for Best Quality", "mn": "ADBE Turbulent Displace-0014", "ix": 14, "v": {"a": 0, "k": 1, "ix": 14}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-3.509, 0]], "o": [[3.509, 0]], "v": [[197.738, 50.177]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.823529411765, 0.439215686275, 0.294117647059, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 30, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-114.035, -44.737], [29.5, 77.144], [-74.561, -44.737], [-140.489, -174.561]], "o": [[0, 0], [198.315, 77.8], [-69.437, -181.579], [74.561, 44.737], [20.301, 25.224]], "v": [[40.523, -33.313], [242.613, -142.805], [370.683, -232.279], [201.385, -113.858], [540.859, -273.507]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [0]}, {"t": 28.3076171875, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14.14, "s": [0]}, {"t": 30, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.823529411765, 0.439215686275, 0.294117647059, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 30, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 96, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "ruban-right-anim", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [301.711, 406.899, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [57, 57, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Turbulent Displace", "np": 16, "mn": "ADBE Turbulent Displace", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Displacement", "mn": "ADBE Turbulent Displace-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 0, "nm": "Amount", "mn": "ADBE Turbulent Displace-0002", "ix": 2, "v": {"a": 0, "k": 30, "ix": 2}}, {"ty": 0, "nm": "Size", "mn": "ADBE Turbulent Displace-0003", "ix": 3, "v": {"a": 0, "k": 100, "ix": 3}}, {"ty": 3, "nm": "Offset (Turbulence)", "mn": "ADBE Turbulent Displace-0004", "ix": 4, "v": {"a": 0, "k": [300, 400], "ix": 4}}, {"ty": 0, "nm": "Complexity", "mn": "ADBE Turbulent Displace-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 0, "nm": "Evolution", "mn": "ADBE Turbulent Displace-0006", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 6, "nm": "Evolution Options", "mn": "ADBE Turbulent Displace-0007", "ix": 7, "v": 0}, {"ty": 7, "nm": "Cycle Evolution", "mn": "ADBE Turbulent Displace-0008", "ix": 8, "v": {"a": 0, "k": 0, "ix": 8}}, {"ty": 0, "nm": "Cycle (in Revolutions)", "mn": "ADBE Turbulent Displace-0009", "ix": 9, "v": {"a": 0, "k": 1, "ix": 9}}, {"ty": 0, "nm": "Random Seed", "mn": "ADBE Turbulent Displace-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10}}, {"ty": 6, "nm": "Random Seed", "mn": "ADBE Turbulent Displace-0011", "ix": 11, "v": 0}, {"ty": 7, "nm": "<PERSON>nning", "mn": "ADBE Turbulent Displace-0012", "ix": 12, "v": {"a": 0, "k": 3, "ix": 12}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>er", "mn": "ADBE Turbulent Displace-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13}}, {"ty": 7, "nm": "Antialiasing for Best Quality", "mn": "ADBE Turbulent Displace-0014", "ix": 14, "v": {"a": 0, "k": 1, "ix": 14}}]}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-114.035, -44.737], [31.579, 76.316], [-74.561, -44.737], [-4.824, 32.018]], "o": [[0, 0], [198.315, 77.8], [-31.579, -76.316], [74.561, 44.737], [4.825, -32.018]], "v": [[40.523, -33.313], [169.806, -113.858], [187.35, -279.647], [111.911, -224.384], [550.508, -391.928]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 4, "s": [0]}, {"t": 23.384765625, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 9.86, "s": [0]}, {"t": 25, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.823529411765, 0.439215686275, 0.294117647059, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 30, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 2, "op": 96, "st": 2, "bm": 0}], "markers": []}