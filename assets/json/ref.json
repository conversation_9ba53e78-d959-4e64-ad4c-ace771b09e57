{"success": true, "token": null, "currentLang": "us", "treatments": [{"id": 1, "speciesId": 1, "acronym": "C", "valence": "Calicivirus  (Upper Respiratory)", "type": 0}, {"id": 2, "speciesId": 1, "acronym": "R", "valence": "Herpes Virus (rhinotracheitis)(Upper Respiratory)", "type": 0}, {"id": 3, "speciesId": 1, "acronym": "P", "valence": "Panleucopenia (Feline Distemper)", "type": 0}, {"id": 4, "speciesId": 1, "acronym": "L", "valence": "Leukemia (Leucosis)", "type": 0}, {"id": 5, "speciesId": 1, "acronym": "R", "valence": "Rabbies", "type": 0}, {"id": 6, "speciesId": 1, "acronym": "FeLV", "valence": "FeLV (Leucosis)", "type": 0}, {"id": 7, "speciesId": 2, "acronym": "D", "valence": "Distemper", "type": 0}, {"id": 8, "speciesId": 2, "acronym": "H", "valence": "Hepatitis", "type": 0}, {"id": 9, "speciesId": 2, "acronym": "P", "valence": "Parvovirosis", "type": 0}, {"id": 10, "speciesId": 2, "acronym": "Pi", "valence": "<PERSON><PERSON>(Parainfluenza)", "type": 0}, {"id": 11, "speciesId": 2, "acronym": "Bb", "valence": "<PERSON><PERSON>(Bordetella)", "type": 0}, {"id": 12, "speciesId": 2, "acronym": "L", "valence": "Leptospirosis", "type": 0}, {"id": 13, "speciesId": 2, "acronym": "R", "valence": "Rabbies", "type": 0}, {"id": 14, "speciesId": 2, "acronym": "Bab", "valence": "Babesiosis", "type": 0}, {"id": 15, "speciesId": 2, "acronym": "<PERSON><PERSON>", "valence": "<PERSON><PERSON>'s Disease", "type": 0}, {"id": 16, "speciesId": 1, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 17, "speciesId": 2, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 18, "speciesId": 1, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 19, "speciesId": 2, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 20, "speciesId": 1, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 21, "speciesId": 2, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 22, "speciesId": 27, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 23, "speciesId": 27, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 24, "speciesId": 27, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 25, "speciesId": 27, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 26, "speciesId": 29, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 27, "speciesId": 29, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 28, "speciesId": 29, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 29, "speciesId": 29, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 30, "speciesId": 37, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 31, "speciesId": 37, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 32, "speciesId": 37, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 33, "speciesId": 37, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 34, "speciesId": 38, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 35, "speciesId": 38, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 36, "speciesId": 38, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 37, "speciesId": 38, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 38, "speciesId": 39, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 39, "speciesId": 39, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 40, "speciesId": 39, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 41, "speciesId": 39, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 42, "speciesId": 40, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 43, "speciesId": 40, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 44, "speciesId": 40, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 45, "speciesId": 40, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 46, "speciesId": 41, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 47, "speciesId": 41, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 48, "speciesId": 41, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 49, "speciesId": 41, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 50, "speciesId": 42, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 51, "speciesId": 42, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 52, "speciesId": 42, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 53, "speciesId": 42, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 54, "speciesId": 43, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 55, "speciesId": 43, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 56, "speciesId": 43, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 57, "speciesId": 43, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 58, "speciesId": 46, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 59, "speciesId": 46, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 60, "speciesId": 46, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 61, "speciesId": 46, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 62, "speciesId": 47, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 63, "speciesId": 47, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 64, "speciesId": 47, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 65, "speciesId": 47, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 66, "speciesId": 48, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 67, "speciesId": 48, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 68, "speciesId": 48, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 69, "speciesId": 48, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 70, "speciesId": 49, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 71, "speciesId": 49, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 72, "speciesId": 49, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 73, "speciesId": 49, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 74, "speciesId": 50, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 75, "speciesId": 50, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 76, "speciesId": 50, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 77, "speciesId": 50, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 78, "speciesId": 51, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 79, "speciesId": 51, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 80, "speciesId": 51, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 81, "speciesId": 51, "acronym": "STERILISATION", "valence": "Spay", "type": 3}, {"id": 82, "speciesId": 52, "acronym": "", "valence": "Vaccine", "type": 0}, {"id": 83, "speciesId": 52, "acronym": "INTERNALS", "valence": "<PERSON><PERSON><PERSON>", "type": 1}, {"id": 84, "speciesId": 52, "acronym": "EXTERNALS", "valence": "Products", "type": 2}, {"id": 85, "speciesId": 52, "acronym": "STERILISATION", "valence": "Spay", "type": 3}], "petSmileLevels": [{"id": 1, "label": "Ivory"}, {"id": 2, "label": "Bronze"}, {"id": 3, "label": "Silver"}, {"id": 4, "label": "Gold"}, {"id": 5, "label": "Platinum"}], "speciess": [{"id": 1, "name": "Cat"}, {"id": 2, "name": "Dog"}, {"id": 27, "name": "Rabbit"}, {"id": 29, "name": "Snake"}, {"id": 37, "name": "<PERSON><PERSON><PERSON>"}, {"id": 38, "name": "Turtle"}, {"id": 39, "name": "Lizard"}, {"id": 40, "name": "<PERSON>"}, {"id": 41, "name": "Other"}, {"id": 42, "name": "Horse"}, {"id": 43, "name": "<PERSON><PERSON>"}, {"id": 46, "name": "Pony"}, {"id": 47, "name": "Fish"}, {"id": 48, "name": "Pig"}, {"id": 49, "name": "<PERSON><PERSON>"}, {"id": 50, "name": "Sheep"}, {"id": 51, "name": "Goa<PERSON>"}, {"id": 52, "name": "Cow"}], "breeds": [{"id": 2, "speciesId": 2, "name": "Labrador"}, {"id": 23, "speciesId": 1, "name": "Norwegian Forest"}, {"id": 35, "speciesId": 1, "name": "Abyssinian"}, {"id": 36, "speciesId": 1, "name": "American Bobtail"}, {"id": 37, "speciesId": 1, "name": "American Curl"}, {"id": 38, "speciesId": 1, "name": "American Shorthair"}, {"id": 40, "speciesId": 1, "name": "American Wirehair"}, {"id": 41, "speciesId": 1, "name": "Balinese"}, {"id": 42, "speciesId": 1, "name": "Bengal"}, {"id": 43, "speciesId": 1, "name": "<PERSON><PERSON><PERSON>"}, {"id": 44, "speciesId": 1, "name": "Bombay"}, {"id": 45, "speciesId": 1, "name": "British Shorthair"}, {"id": 46, "speciesId": 1, "name": "Burmese"}, {"id": 47, "speciesId": 1, "name": "Chartreux"}, {"id": 48, "speciesId": 1, "name": "Cornish Rex"}, {"id": 49, "speciesId": 1, "name": "<PERSON><PERSON><PERSON>"}, {"id": 50, "speciesId": 1, "name": "Devon Rex"}, {"id": 51, "speciesId": 1, "name": "Egyptian Mau"}, {"id": 52, "speciesId": 1, "name": "Exotic Shorthair"}, {"id": 53, "speciesId": 1, "name": "Havana Brown"}, {"id": 54, "speciesId": 1, "name": "Himalayan"}, {"id": 55, "speciesId": 1, "name": "Japanese Bobtail"}, {"id": 56, "speciesId": 1, "name": "Javanese"}, {"id": 57, "speciesId": 1, "name": "<PERSON><PERSON>"}, {"id": 58, "speciesId": 1, "name": "Maine Coon"}, {"id": 59, "speciesId": 1, "name": "Manx"}, {"id": 60, "speciesId": 1, "name": "<PERSON><PERSON><PERSON>"}, {"id": 61, "speciesId": 1, "name": "Nebelung"}, {"id": 62, "speciesId": 1, "name": "Ocicat"}, {"id": 63, "speciesId": 1, "name": "Oriental"}, {"id": 64, "speciesId": 1, "name": "Persian"}, {"id": 65, "speciesId": 1, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 66, "speciesId": 1, "name": "Russian Blue"}, {"id": 67, "speciesId": 1, "name": "Scottish Fold"}, {"id": 68, "speciesId": 1, "name": "Selkirk Rex"}, {"id": 69, "speciesId": 1, "name": "Siamese"}, {"id": 70, "speciesId": 1, "name": "Siberian"}, {"id": 72, "speciesId": 1, "name": "Singapura"}, {"id": 73, "speciesId": 1, "name": "Snowshoe"}, {"id": 74, "speciesId": 1, "name": "Somali"}, {"id": 75, "speciesId": 1, "name": "<PERSON><PERSON><PERSON>"}, {"id": 76, "speciesId": 1, "name": "Tonkinese"}, {"id": 77, "speciesId": 1, "name": "Turkish Angora"}, {"id": 78, "speciesId": 1, "name": "Turkish Van"}, {"id": 79, "speciesId": 27, "name": "American"}, {"id": 80, "speciesId": 27, "name": "American fuzzy lop"}, {"id": 81, "speciesId": 27, "name": "American Sable"}, {"id": 82, "speciesId": 27, "name": "English Angora"}, {"id": 83, "speciesId": 27, "name": "French Angora"}, {"id": 84, "speciesId": 27, "name": "Belgian Hare"}, {"id": 85, "speciesId": 27, "name": "<PERSON><PERSON><PERSON>"}, {"id": 86, "speciesId": 27, "name": "Britannia Petite"}, {"id": 87, "speciesId": 27, "name": "California"}, {"id": 88, "speciesId": 27, "name": "Champagne d'Argent"}, {"id": 89, "speciesId": 27, "name": "Checkered Giant"}, {"id": 90, "speciesId": 27, "name": "Checkered Giant"}, {"id": 91, "speciesId": 27, "name": "<PERSON><PERSON>"}, {"id": 92, "speciesId": 27, "name": "American Chinchilla"}, {"id": 93, "speciesId": 27, "name": "Giant Chinchilla"}, {"id": 94, "speciesId": 27, "name": "Cinnamon"}, {"id": 95, "speciesId": 27, "name": "Creme D'Argent"}, {"id": 96, "speciesId": 27, "name": "Dutch"}, {"id": 97, "speciesId": 27, "name": "English Spot"}, {"id": 98, "speciesId": 27, "name": "Flemish Giant"}, {"id": 99, "speciesId": 27, "name": "Florida White"}, {"id": 100, "speciesId": 27, "name": "Giant"}, {"id": 101, "speciesId": 27, "name": "Ha<PERSON><PERSON>"}, {"id": 102, "speciesId": 27, "name": "Havana"}, {"id": 103, "speciesId": 27, "name": "Himalayan"}, {"id": 104, "speciesId": 27, "name": "<PERSON><PERSON><PERSON>"}, {"id": 105, "speciesId": 27, "name": "Hotot"}, {"id": 106, "speciesId": 27, "name": "Jersey Wooly"}, {"id": 107, "speciesId": 27, "name": "Lionhead"}, {"id": 108, "speciesId": 27, "name": "Lilac"}, {"id": 109, "speciesId": 27, "name": "English lop"}, {"id": 110, "speciesId": 27, "name": "French Lop"}, {"id": 111, "speciesId": 27, "name": "Holland Lop"}, {"id": 112, "speciesId": 27, "name": "American fuzzy lop"}, {"id": 113, "speciesId": 27, "name": "<PERSON><PERSON>"}, {"id": 114, "speciesId": 27, "name": "Mini Lop"}, {"id": 115, "speciesId": 27, "name": "Mini Plush Lop"}, {"id": 116, "speciesId": 27, "name": "Netherland Dwarf"}, {"id": 117, "speciesId": 27, "name": "New Zealand"}, {"id": 118, "speciesId": 27, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 119, "speciesId": 27, "name": "Polish"}, {"id": 120, "speciesId": 27, "name": "Mini Plush Lop"}, {"id": 121, "speciesId": 27, "name": "<PERSON>"}, {"id": 122, "speciesId": 27, "name": "Mini Rex"}, {"id": 123, "speciesId": 27, "name": "<PERSON>er"}, {"id": 124, "speciesId": 27, "name": "Satin"}, {"id": 125, "speciesId": 27, "name": "Mini Satin"}, {"id": 126, "speciesId": 27, "name": "<PERSON><PERSON>"}, {"id": 127, "speciesId": 27, "name": "Silver Fox"}, {"id": 128, "speciesId": 27, "name": "Silver"}, {"id": 129, "speciesId": 27, "name": "Silver Marten"}, {"id": 130, "speciesId": 27, "name": "<PERSON>"}, {"id": 131, "speciesId": 27, "name": "<PERSON><PERSON>"}, {"id": 132, "speciesId": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 133, "speciesId": 2, "name": "Afghan Hound"}, {"id": 134, "speciesId": 2, "name": "Airedale Terrier"}, {"id": 135, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 136, "speciesId": 2, "name": "Alaskan Malamute"}, {"id": 137, "speciesId": 2, "name": "American Cocker Spaniel"}, {"id": 138, "speciesId": 2, "name": "American Eskimo (Miniature)"}, {"id": 139, "speciesId": 2, "name": "American Eskimo (Standard)"}, {"id": 140, "speciesId": 2, "name": "American Eskimo (Toy)"}, {"id": 141, "speciesId": 2, "name": "American Foxhound"}, {"id": 142, "speciesId": 2, "name": "American Staffordshire Terrier"}, {"id": 143, "speciesId": 2, "name": "American Water Spaniel"}, {"id": 144, "speciesId": 2, "name": "Anatolian Shepherd"}, {"id": 145, "speciesId": 2, "name": "Australian Cattle"}, {"id": 146, "speciesId": 2, "name": "Australian Shepherd"}, {"id": 147, "speciesId": 2, "name": "Australian Terrier"}, {"id": 148, "speciesId": 2, "name": "Basenji"}, {"id": 149, "speciesId": 2, "name": "Basset Hound"}, {"id": 150, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 151, "speciesId": 2, "name": "<PERSON><PERSON>"}, {"id": 152, "speciesId": 2, "name": "Bedlington Terrier"}, {"id": 153, "speciesId": 2, "name": "Belgian <PERSON><PERSON><PERSON>"}, {"id": 154, "speciesId": 2, "name": "Belgian Sheepdog"}, {"id": 155, "speciesId": 2, "name": "Belgian Tervueren"}, {"id": 156, "speciesId": 2, "name": "Bernese Mountain"}, {"id": 157, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 158, "speciesId": 2, "name": "Black and Tan Coonhound"}, {"id": 159, "speciesId": 2, "name": "Bloodhound"}, {"id": 160, "speciesId": 2, "name": "Border Collie"}, {"id": 161, "speciesId": 2, "name": "Border Terrier"}, {"id": 162, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 163, "speciesId": 2, "name": "Boston Terrier"}, {"id": 164, "speciesId": 2, "name": "Bouvier des Flandres"}, {"id": 165, "speciesId": 2, "name": "Boxer"}, {"id": 166, "speciesId": 2, "name": "<PERSON>ria<PERSON>"}, {"id": 167, "speciesId": 2, "name": "Brittany"}, {"id": 168, "speciesId": 2, "name": "Brussels Griffon"}, {"id": 169, "speciesId": 2, "name": "Bull Terrier"}, {"id": 170, "speciesId": 2, "name": "Bulldog"}, {"id": 171, "speciesId": 2, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 172, "speciesId": 2, "name": "Cairn Terrier"}, {"id": 173, "speciesId": 2, "name": "Canaan"}, {"id": 174, "speciesId": 2, "name": "Cardigan Welsh Corgi"}, {"id": 175, "speciesId": 2, "name": "Cavalier King <PERSON>"}, {"id": 176, "speciesId": 2, "name": "Chesapeake Bay Retriever"}, {"id": 177, "speciesId": 2, "name": "Chihuahua"}, {"id": 178, "speciesId": 2, "name": "Chinese Crested"}, {"id": 179, "speciesId": 2, "name": "Chinese Shar-Pei"}, {"id": 180, "speciesId": 2, "name": "<PERSON>"}, {"id": 181, "speciesId": 2, "name": "<PERSON><PERSON> Spaniel"}, {"id": 182, "speciesId": 2, "name": "<PERSON><PERSON>"}, {"id": 183, "speciesId": 2, "name": "Curly-Coated Retriever"}, {"id": 184, "speciesId": 2, "name": "<PERSON><PERSON><PERSON><PERSON> (Standard)"}, {"id": 185, "speciesId": 2, "name": "<PERSON><PERSON><PERSON> (Miniature)"}, {"id": 186, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 187, "speciesId": 2, "name": "Dan<PERSON> Terrier"}, {"id": 188, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 189, "speciesId": 2, "name": "English Cocker Spaniel"}, {"id": 190, "speciesId": 2, "name": "English Foxhound "}, {"id": 191, "speciesId": 2, "name": "English Setter"}, {"id": 192, "speciesId": 2, "name": "English Springer Spaniel"}, {"id": 193, "speciesId": 2, "name": "English Toy Spaniel"}, {"id": 194, "speciesId": 2, "name": "Field Spaniel"}, {"id": 195, "speciesId": 2, "name": "Finnish Spitz"}, {"id": 196, "speciesId": 2, "name": "Flat-Coated Retriever"}, {"id": 197, "speciesId": 2, "name": "French Bulldog"}, {"id": 198, "speciesId": 2, "name": "German Shepherd"}, {"id": 199, "speciesId": 2, "name": "German Short<PERSON><PERSON>"}, {"id": 200, "speciesId": 2, "name": "German <PERSON><PERSON><PERSON>"}, {"id": 201, "speciesId": 2, "name": "<PERSON>"}, {"id": 202, "speciesId": 2, "name": "Golden Retriever"}, {"id": 203, "speciesId": 2, "name": "<PERSON>"}, {"id": 204, "speciesId": 2, "name": "Great Dane"}, {"id": 205, "speciesId": 2, "name": "Great Pyrenees"}, {"id": 206, "speciesId": 2, "name": "Greater Swiss Mountain"}, {"id": 207, "speciesId": 2, "name": "Greyhound"}, {"id": 208, "speciesId": 2, "name": "Harrier"}, {"id": 209, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 210, "speciesId": 2, "name": "Ibizan <PERSON>"}, {"id": 211, "speciesId": 2, "name": "Irish Setter"}, {"id": 212, "speciesId": 2, "name": "Irish Terrier"}, {"id": 213, "speciesId": 2, "name": "Irish Water Spaniel"}, {"id": 214, "speciesId": 2, "name": "Irish Wolfhound"}, {"id": 215, "speciesId": 2, "name": "Italian Greyhound"}, {"id": 216, "speciesId": 2, "name": "<PERSON>"}, {"id": 217, "speciesId": 2, "name": "Japanese Chin"}, {"id": 218, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 219, "speciesId": 2, "name": "Kerry Blue Terrier"}, {"id": 220, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 221, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 222, "speciesId": 2, "name": "Labrador Retriever"}, {"id": 223, "speciesId": 2, "name": "Lakeland Terrier"}, {"id": 224, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 225, "speciesId": 2, "name": "<PERSON><PERSON>"}, {"id": 226, "speciesId": 2, "name": "Maltese"}, {"id": 227, "speciesId": 2, "name": "Manchester Terrier (Standard)"}, {"id": 228, "speciesId": 2, "name": "Manchester Terrier (Toy)"}, {"id": 229, "speciesId": 2, "name": "Mastiff"}, {"id": 230, "speciesId": 2, "name": "Miniature Bull Terrier"}, {"id": 231, "speciesId": 2, "name": "Miniature Pinscher"}, {"id": 232, "speciesId": 2, "name": "Miniature Schnauzer"}, {"id": 233, "speciesId": 2, "name": "Newfoundland"}, {"id": 234, "speciesId": 2, "name": "Norfolk Terrier"}, {"id": 235, "speciesId": 2, "name": "Norwegian Elkhound"}, {"id": 236, "speciesId": 2, "name": "Norwich Terrier"}, {"id": 237, "speciesId": 2, "name": "Old English Sheepdog"}, {"id": 238, "speciesId": 2, "name": "Otterhound"}, {"id": 239, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 240, "speciesId": 2, "name": "Pekingese"}, {"id": 241, "speciesId": 2, "name": "Pembroke Welsh Corgi"}, {"id": 242, "speciesId": 2, "name": "Petit Basset Griffon Vendeen"}, {"id": 243, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 244, "speciesId": 2, "name": "Pointer"}, {"id": 245, "speciesId": 2, "name": "Pomeranian"}, {"id": 246, "speciesId": 2, "name": "Poodle (Miniature)"}, {"id": 247, "speciesId": 2, "name": "<PERSON><PERSON><PERSON> (Standard)"}, {"id": 248, "speciesId": 2, "name": "<PERSON>odle (Toy)"}, {"id": 249, "speciesId": 2, "name": "Portuguese Water"}, {"id": 250, "speciesId": 2, "name": "<PERSON><PERSON>"}, {"id": 251, "speciesId": 2, "name": "<PERSON><PERSON>"}, {"id": 252, "speciesId": 2, "name": "Rhodesian Ridgeback"}, {"id": 253, "speciesId": 2, "name": "Rottweiler"}, {"id": 254, "speciesId": 2, "name": "<PERSON>"}, {"id": 255, "speciesId": 2, "name": "Saluki (or Gazelle Hound)"}, {"id": 256, "speciesId": 2, "name": "<PERSON>oyed"}, {"id": 257, "speciesId": 2, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 258, "speciesId": 2, "name": "Scottish Deerhound"}, {"id": 259, "speciesId": 2, "name": "Scottish Terrier"}, {"id": 260, "speciesId": 2, "name": "Sealyham Terrier"}, {"id": 261, "speciesId": 2, "name": "Shetland Sheepdog"}, {"id": 262, "speciesId": 2, "name": "Shiba Inu"}, {"id": 263, "speciesId": 2, "name": "<PERSON><PERSON>"}, {"id": 264, "speciesId": 2, "name": "Siberian Husky"}, {"id": 265, "speciesId": 2, "name": "Silky Terrier"}, {"id": 266, "speciesId": 2, "name": "Skye Terrier"}, {"id": 267, "speciesId": 2, "name": "Smooth Fox Terrier"}, {"id": 268, "speciesId": 2, "name": "Soft Coated Wheaten Terrier"}, {"id": 269, "speciesId": 2, "name": "Spinone Italiano"}, {"id": 270, "speciesId": 2, "name": "Staffordshire Bull Terrier"}, {"id": 271, "speciesId": 2, "name": "Standard Schnauzer"}, {"id": 272, "speciesId": 2, "name": "Sussex Spaniel"}, {"id": 273, "speciesId": 2, "name": "Tibetan Spaniel"}, {"id": 274, "speciesId": 2, "name": "Tibetan Terrier"}, {"id": 275, "speciesId": 2, "name": "Vizsla"}, {"id": 276, "speciesId": 2, "name": "Weimaraner"}, {"id": 277, "speciesId": 2, "name": "Welsh Springer Spaniel"}, {"id": 278, "speciesId": 2, "name": "Welsh Terrier"}, {"id": 279, "speciesId": 2, "name": "West Highland White Terrier"}, {"id": 280, "speciesId": 2, "name": "Whippet"}, {"id": 281, "speciesId": 2, "name": "Wire Fox Terrier"}, {"id": 282, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>ing Griffon"}, {"id": 283, "speciesId": 2, "name": "Yorkshire Terrier"}, {"id": 284, "speciesId": 2, "name": "Fox Terrier"}, {"id": 285, "speciesId": 1, "name": "European"}, {"id": 286, "speciesId": 29, "name": "Snake"}, {"id": 295, "speciesId": 37, "name": "<PERSON><PERSON><PERSON>"}, {"id": 296, "speciesId": 38, "name": "Turtle"}, {"id": 297, "speciesId": 39, "name": "Lizard"}, {"id": 298, "speciesId": 2, "name": "other"}, {"id": 299, "speciesId": 41, "name": "Other"}, {"id": 300, "speciesId": 2, "name": "<PERSON>"}, {"id": 301, "speciesId": 2, "name": "Spitz"}, {"id": 302, "speciesId": 1, "name": "European shorthair"}, {"id": 303, "speciesId": 2, "name": "Dogo Argentino"}, {"id": 304, "speciesId": 2, "name": "Berger Blanc Su<PERSON>"}, {"id": 305, "speciesId": 2, "name": "<PERSON><PERSON>"}, {"id": 307, "speciesId": 2, "name": "<PERSON>e corso"}, {"id": 309, "speciesId": 2, "name": "Great Pyrenees"}, {"id": 310, "speciesId": 2, "name": "Karelian Bear"}, {"id": 311, "speciesId": 2, "name": "Wolfdog"}, {"id": 312, "speciesId": 2, "name": "Mexican Hairless"}, {"id": 313, "speciesId": 2, "name": "Dogue de Bordeaux"}, {"id": 314, "speciesId": 2, "name": "Belgian Griffon"}, {"id": 315, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 317, "speciesId": 2, "name": "Nova Scotia Duck Tolling Retriever"}, {"id": 318, "speciesId": 2, "name": "Porcelaine"}, {"id": 320, "speciesId": 2, "name": "Whippet"}, {"id": 321, "speciesId": 2, "name": "Bloodhound"}, {"id": 322, "speciesId": 2, "name": "Bouvier des Ardennes"}, {"id": 323, "speciesId": 2, "name": "Basset B<PERSON>u de Gascogne"}, {"id": 324, "speciesId": 2, "name": "Dutch Shepherd"}, {"id": 325, "speciesId": 2, "name": "<PERSON><PERSON>"}, {"id": 327, "speciesId": 2, "name": "Portuguese Pointer"}, {"id": 328, "speciesId": 2, "name": "Curs<PERSON>u"}, {"id": 329, "speciesId": 2, "name": "Neapolitan Mastiff"}, {"id": 330, "speciesId": 1, "name": "Other"}, {"id": 331, "speciesId": 2, "name": "Other"}, {"id": 332, "speciesId": 27, "name": "Other"}, {"id": 334, "speciesId": 1, "name": "British Longhair"}, {"id": 335, "speciesId": 1, "name": "<PERSON><PERSON><PERSON>"}, {"id": 336, "speciesId": 1, "name": "<PERSON><PERSON><PERSON>"}, {"id": 337, "speciesId": 1, "name": "<PERSON><PERSON><PERSON>"}, {"id": 338, "speciesId": 1, "name": "German Rex"}, {"id": 339, "speciesId": 1, "name": "Donskoy"}, {"id": 340, "speciesId": 1, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 341, "speciesId": 2, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 342, "speciesId": 1, "name": "LaPerm"}, {"id": 343, "speciesId": 1, "name": "<PERSON><PERSON><PERSON>"}, {"id": 344, "speciesId": 1, "name": "Pixie-bob"}, {"id": 345, "speciesId": 1, "name": "Ragamuffin"}, {"id": 346, "speciesId": 1, "name": "Safari"}, {"id": 347, "speciesId": 1, "name": "Savannah"}, {"id": 348, "speciesId": 1, "name": "Sokoke"}, {"id": 349, "speciesId": 1, "name": "Thai"}, {"id": 350, "speciesId": 1, "name": "York chocolate"}, {"id": 351, "speciesId": 2, "name": "<PERSON><PERSON>"}, {"id": 352, "speciesId": 2, "name": "Pyrenean Shepherd"}, {"id": 356, "speciesId": 43, "name": "<PERSON><PERSON>"}, {"id": 357, "speciesId": 42, "name": "Horse"}, {"id": 358, "speciesId": 46, "name": "Pony"}, {"id": 359, "speciesId": 47, "name": "Fish"}, {"id": 360, "speciesId": 48, "name": "Pig"}, {"id": 361, "speciesId": 42, "name": "Abstang"}, {"id": 362, "speciesId": 42, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 363, "speciesId": 42, "name": "Ethiopian horses"}, {"id": 364, "speciesId": 42, "name": "Giara horse"}, {"id": 365, "speciesId": 42, "name": "Giara horse"}, {"id": 366, "speciesId": 42, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 367, "speciesId": 42, "name": "The horse of Ainos"}, {"id": 368, "speciesId": 1, "name": "<PERSON><PERSON>"}, {"id": 369, "speciesId": 42, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 370, "speciesId": 42, "name": "Albanian horse"}, {"id": 372, "speciesId": 42, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 373, "speciesId": 42, "name": "Altai"}, {"id": 374, "speciesId": 42, "name": "Alter Real"}, {"id": 375, "speciesId": 42, "name": "American Cream Draught"}, {"id": 376, "speciesId": 49, "name": "Agouti"}, {"id": 377, "speciesId": 49, "name": "Vole"}, {"id": 378, "speciesId": 49, "name": "Capybara"}, {"id": 379, "speciesId": 49, "name": "Beaver"}, {"id": 380, "speciesId": 49, "name": "Prairie dog"}, {"id": 381, "speciesId": 49, "name": "<PERSON><PERSON><PERSON>"}, {"id": 382, "speciesId": 49, "name": "Guinea Pig"}, {"id": 383, "speciesId": 49, "name": "Guinea Pig"}, {"id": 384, "speciesId": 49, "name": "Tree squirrel"}, {"id": 385, "speciesId": 49, "name": "Red squirrel"}, {"id": 386, "speciesId": 49, "name": "Gopher"}, {"id": 387, "speciesId": 49, "name": "Gopher"}, {"id": 388, "speciesId": 49, "name": "<PERSON><PERSON><PERSON>"}, {"id": 389, "speciesId": 49, "name": "<PERSON><PERSON><PERSON>"}, {"id": 390, "speciesId": 49, "name": "<PERSON><PERSON>"}, {"id": 391, "speciesId": 49, "name": "Garden Dormouse"}, {"id": 392, "speciesId": 49, "name": "<PERSON><PERSON>"}, {"id": 393, "speciesId": 49, "name": "<PERSON><PERSON><PERSON>"}, {"id": 394, "speciesId": 49, "name": "Mara"}, {"id": 395, "speciesId": 49, "name": "<PERSON><PERSON>"}, {"id": 396, "speciesId": 49, "name": "Harvest Mouse"}, {"id": 397, "speciesId": 49, "name": "Hazel dormouse"}, {"id": 398, "speciesId": 49, "name": "Octodon"}, {"id": 399, "speciesId": 49, "name": "Northern Flying Squirrel"}, {"id": 400, "speciesId": 49, "name": "Po<PERSON><PERSON><PERSON>"}, {"id": 401, "speciesId": 49, "name": "Co<PERSON><PERSON>"}, {"id": 402, "speciesId": 49, "name": "Rat"}, {"id": 403, "speciesId": 49, "name": "Muskrat"}, {"id": 404, "speciesId": 49, "name": "Indian Giant Squirrel"}, {"id": 405, "speciesId": 49, "name": "Indian giant squirrel"}, {"id": 406, "speciesId": 49, "name": "Mouse"}, {"id": 407, "speciesId": 49, "name": "<PERSON><PERSON>"}, {"id": 408, "speciesId": 49, "name": "Malabar giant squirrel"}, {"id": 409, "speciesId": 39, "name": "Dragon Lizard (other)"}, {"id": 410, "speciesId": 39, "name": "Central bearded dragon"}, {"id": 411, "speciesId": 39, "name": "Knight anole"}, {"id": 412, "speciesId": 39, "name": "Carolina anole"}, {"id": 413, "speciesId": 39, "name": "Plumed basilisk"}, {"id": 414, "speciesId": 39, "name": "Cha<PERSON><PERSON> (other)"}, {"id": 415, "speciesId": 39, "name": "Veiled chameleon"}, {"id": 416, "speciesId": 39, "name": "Panther chameleon"}, {"id": 417, "speciesId": 39, "name": "Australian water dragon"}, {"id": 418, "speciesId": 39, "name": "Gecko (other)"}, {"id": 419, "speciesId": 39, "name": "Crested gecko"}, {"id": 420, "speciesId": 39, "name": "Crested gecko"}, {"id": 421, "speciesId": 39, "name": "Crested gecko"}, {"id": 422, "speciesId": 39, "name": "African fat-tailed gecko"}, {"id": 423, "speciesId": 39, "name": "Barking gecko"}, {"id": 424, "speciesId": 39, "name": "Peacock day gecko"}, {"id": 425, "speciesId": 39, "name": "Gargoyle gecko"}, {"id": 426, "speciesId": 39, "name": "Common leopard gecko"}, {"id": 427, "speciesId": 39, "name": "<PERSON>'s dwarf gecko"}, {"id": 428, "speciesId": 39, "name": "Tokay gecko"}, {"id": 429, "speciesId": 39, "name": "Tokay gecko"}, {"id": 430, "speciesId": 39, "name": "Long-nosed leopard lizard"}, {"id": 431, "speciesId": 39, "name": "Iguanidae (other)"}, {"id": 432, "speciesId": 39, "name": "Filled-necked lizard"}, {"id": 433, "speciesId": 39, "name": "Sudan plated lizard﻿"}, {"id": 434, "speciesId": 39, "name": "<PERSON><PERSON><PERSON><PERSON> stumpffi"}, {"id": 435, "speciesId": 39, "name": "Rank<PERSON>'s dragon"}, {"id": 436, "speciesId": 39, "name": "Skink"}, {"id": 437, "speciesId": 39, "name": "Blue-tongued skink"}, {"id": 438, "speciesId": 39, "name": "<PERSON><PERSON><PERSON>"}, {"id": 439, "speciesId": 39, "name": "Red-eyed crocodile skinks"}, {"id": 440, "speciesId": 39, "name": "Argentine giant tegu"}, {"id": 441, "speciesId": 39, "name": "Argentine giant tegu"}, {"id": 442, "speciesId": 39, "name": "Argentine black and white tegu"}, {"id": 443, "speciesId": 39, "name": "Monitor lizard (other)"}, {"id": 444, "speciesId": 39, "name": "Spiny-tailed monitor"}, {"id": 445, "speciesId": 39, "name": "Savannah monitor"}, {"id": 446, "speciesId": 39, "name": "Frog-eyed Gecko"}, {"id": 447, "speciesId": 39, "name": "Red-eyed crocodile skink"}, {"id": 448, "speciesId": 39, "name": "<PERSON><PERSON> olivacea"}, {"id": 449, "speciesId": 39, "name": "Green grass lizard"}, {"id": 450, "speciesId": 51, "name": "Goa<PERSON>"}, {"id": 451, "speciesId": 50, "name": "Sheep"}, {"id": 452, "speciesId": 2, "name": "Grand Bleu de Gascogne"}, {"id": 453, "speciesId": 2, "name": "Grand Bleu de Gascogne"}, {"id": 454, "speciesId": 46, "name": "Pottok"}, {"id": 455, "speciesId": 2, "name": "Anglo-Français de Petite Vénerie"}, {"id": 456, "speciesId": 52, "name": "Cow"}, {"id": 457, "speciesId": 42, "name": "American Saddlebred"}, {"id": 458, "speciesId": 42, "name": "American Warmblood"}, {"id": 459, "speciesId": 42, "name": "Anadolu Pony"}, {"id": 460, "speciesId": 42, "name": "Anadolu Pony"}, {"id": 461, "speciesId": 42, "name": "Anadolu Pony"}, {"id": 462, "speciesId": 42, "name": "Anadolu Pony"}, {"id": 463, "speciesId": 42, "name": "<PERSON><PERSON>"}, {"id": 464, "speciesId": 42, "name": "Cruzado"}, {"id": 466, "speciesId": 42, "name": "Andravida horse"}, {"id": 467, "speciesId": 42, "name": "Eleia Horse"}, {"id": 468, "speciesId": 42, "name": "Anglo-Kabarde"}, {"id": 469, "speciesId": 42, "name": "Sardinian"}, {"id": 470, "speciesId": 42, "name": "Sardinian Anglo-Arab"}, {"id": 472, "speciesId": 42, "name": "other"}, {"id": 473, "speciesId": 42, "name": "Appaloosa"}, {"id": 474, "speciesId": 42, "name": "Ardennais, Ardennes"}, {"id": 475, "speciesId": 42, "name": "Swedish Ardennes"}, {"id": 477, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 478, "speciesId": 42, "name": "Asturcón"}, {"id": 479, "speciesId": 42, "name": "Australian stock horse"}, {"id": 480, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 481, "speciesId": 42, "name": "Azteca horse"}, {"id": 482, "speciesId": 42, "name": "Bashkir horse"}, {"id": 483, "speciesId": 42, "name": "Bagual"}, {"id": 484, "speciesId": 1, "name": "Guangxi"}, {"id": 485, "speciesId": 42, "name": "Guangxi"}, {"id": 486, "speciesId": 42, "name": "Baixadeiro"}, {"id": 487, "speciesId": 42, "name": "Bali Pony"}, {"id": 488, "speciesId": 42, "name": "Baluchi horse"}, {"id": 489, "speciesId": 42, "name": "Ban’ei horse"}, {"id": 490, "speciesId": 42, "name": "Barb horse"}, {"id": 491, "speciesId": 42, "name": "Abaco Barb"}, {"id": 492, "speciesId": 42, "name": "Bar<PERSON>gia<PERSON>"}, {"id": 493, "speciesId": 42, "name": "Horse of Vercors de Barraquand"}, {"id": 494, "speciesId": 42, "name": "Basuto pony"}, {"id": 495, "speciesId": 42, "name": "Batak pony"}, {"id": 496, "speciesId": 42, "name": "Schleswig Coldblood"}, {"id": 497, "speciesId": 42, "name": "Bavarian Warmblood"}, {"id": 498, "speciesId": 42, "name": "Bhirum Pony"}, {"id": 499, "speciesId": 42, "name": "Bhirum Pony"}, {"id": 500, "speciesId": 42, "name": "Horse of Voronej"}, {"id": 501, "speciesId": 42, "name": "Black Forest Horse"}, {"id": 502, "speciesId": 42, "name": "Blazer horse"}, {"id": 503, "speciesId": 42, "name": "Budyonny horse"}, {"id": 504, "speciesId": 42, "name": "Boulonnais horse"}, {"id": 505, "speciesId": 42, "name": "Breton horse"}, {"id": 506, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 507, "speciesId": 42, "name": "<PERSON><PERSON><PERSON> horse"}, {"id": 509, "speciesId": 42, "name": "Burguete horse"}, {"id": 510, "speciesId": 42, "name": "Belgian Warmblood"}, {"id": 511, "speciesId": 42, "name": "Calabrese horse"}, {"id": 512, "speciesId": 42, "name": "Camargue horse"}, {"id": 513, "speciesId": 42, "name": "<PERSON><PERSON><PERSON> White Horse"}, {"id": 514, "speciesId": 42, "name": "<PERSON><PERSON>"}, {"id": 515, "speciesId": 42, "name": "Campolina"}, {"id": 516, "speciesId": 42, "name": "Canadian horse"}, {"id": 517, "speciesId": 42, "name": "Canik horse"}, {"id": 518, "speciesId": 42, "name": "Carolina Marsh Tacky"}, {"id": 519, "speciesId": 42, "name": "Andalusian horse"}, {"id": 520, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 521, "speciesId": 42, "name": "Cerbat Mustang"}, {"id": 522, "speciesId": 42, "name": "Tchernomor"}, {"id": 523, "speciesId": 42, "name": "Chilean horse"}, {"id": 524, "speciesId": 42, "name": "Auvergne horse"}, {"id": 525, "speciesId": 42, "name": "Catria horse"}, {"id": 526, "speciesId": 42, "name": "<PERSON><PERSON>"}, {"id": 527, "speciesId": 42, "name": "Foutanké"}, {"id": 528, "speciesId": 42, "name": "Flemish Horse"}, {"id": 529, "speciesId": 42, "name": "Israeli Local Horse"}, {"id": 530, "speciesId": 42, "name": "Florida Cracker Horse"}, {"id": 531, "speciesId": 42, "name": "Chincoteague Pony"}, {"id": 532, "speciesId": 42, "name": "As<PERSON><PERSON>gue horse"}, {"id": 533, "speciesId": 42, "name": "Choctaw horse"}, {"id": 534, "speciesId": 42, "name": "Chumbivilcano"}, {"id": 535, "speciesId": 42, "name": "Clydesdale horse"}, {"id": 536, "speciesId": 42, "name": "Gypsy Cob"}, {"id": 537, "speciesId": 42, "name": "<PERSON>"}, {"id": 538, "speciesId": 42, "name": "Connemara pony"}, {"id": 539, "speciesId": 42, "name": "Corsican horse"}, {"id": 540, "speciesId": 42, "name": "Peruvian Paso"}, {"id": 541, "speciesId": 42, "name": "<PERSON><PERSON>"}, {"id": 542, "speciesId": 42, "name": "Çukurova"}, {"id": 543, "speciesId": 42, "name": "Curly Horse"}, {"id": 544, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 545, "speciesId": 42, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 546, "speciesId": 42, "name": "Azerbaijan horse"}, {"id": 547, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 548, "speciesId": 42, "name": "Dølehest"}, {"id": 549, "speciesId": 42, "name": "Russian Don"}, {"id": 550, "speciesId": 42, "name": "Dongola horse"}, {"id": 551, "speciesId": 42, "name": "Einsiedeln"}, {"id": 552, "speciesId": 42, "name": "Estonian horse"}, {"id": 553, "speciesId": 42, "name": "Falabella"}, {"id": 554, "speciesId": 42, "name": "Finnhorse"}, {"id": 555, "speciesId": 42, "name": "Fjord horse"}, {"id": 556, "speciesId": 42, "name": "Foutanké"}, {"id": 557, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 558, "speciesId": 42, "name": "Frederiksborg horse"}, {"id": 559, "speciesId": 42, "name": "Friesian horse"}, {"id": 560, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 561, "speciesId": 42, "name": "Gelderland horse"}, {"id": 562, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 563, "speciesId": 42, "name": "Guanzhong"}, {"id": 564, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 565, "speciesId": 42, "name": "Haflo-Arabier"}, {"id": 566, "speciesId": 42, "name": "Hanoverian horse"}, {"id": 567, "speciesId": 42, "name": "<PERSON><PERSON>e horse"}, {"id": 568, "speciesId": 42, "name": "<PERSON><PERSON>"}, {"id": 569, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 570, "speciesId": 42, "name": "<PERSON>er"}, {"id": 571, "speciesId": 42, "name": "Irish Sport Horse"}, {"id": 572, "speciesId": 42, "name": "Icelandic horse"}, {"id": 573, "speciesId": 42, "name": "Jianchang"}, {"id": 574, "speciesId": 42, "name": "Jutland horse"}, {"id": 575, "speciesId": 42, "name": "Kabarda horse"}, {"id": 576, "speciesId": 42, "name": "Kaimanawa horse"}, {"id": 577, "speciesId": 42, "name": "Karabair"}, {"id": 578, "speciesId": 42, "name": "Karabakh horse"}, {"id": 579, "speciesId": 42, "name": "Kazakh horse"}, {"id": 580, "speciesId": 42, "name": "Kyrgyz Horse"}, {"id": 581, "speciesId": 42, "name": "Kladruber"}, {"id": 582, "speciesId": 42, "name": "Dutch Warmblood"}, {"id": 583, "speciesId": 42, "name": "Lipizzan"}, {"id": 584, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 585, "speciesId": 42, "name": "<PERSON><PERSON><PERSON><PERSON> horse"}, {"id": 586, "speciesId": 42, "name": "<PERSON>or<PERSON><PERSON>ín horse"}, {"id": 587, "speciesId": 42, "name": "Poitevin horse, Mulassier"}, {"id": 588, "speciesId": 42, "name": "Mustang"}, {"id": 589, "speciesId": 42, "name": "New Forest pony"}, {"id": 590, "speciesId": 42, "name": "<PERSON><PERSON> horse"}, {"id": 591, "speciesId": 42, "name": "Noriker"}, {"id": 592, "speciesId": 42, "name": "American Paint Horse"}, {"id": 593, "speciesId": 42, "name": "Paso Fino"}, {"id": 594, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 595, "speciesId": 42, "name": "Australian Pony"}, {"id": 596, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 597, "speciesId": 42, "name": "Bosnian Mountain Horse"}, {"id": 598, "speciesId": 42, "name": " Brazilian pony"}, {"id": 599, "speciesId": 42, "name": "Arenberg-Nordkirchener"}, {"id": 600, "speciesId": 42, "name": "Azores pony"}, {"id": 601, "speciesId": 42, "name": "Pony of the Americas"}, {"id": 602, "speciesId": 42, "name": "<PERSON><PERSON><PERSON> Horse"}, {"id": 603, "speciesId": 42, "name": "Pure Spanish Horse, PRE"}, {"id": 604, "speciesId": 42, "name": "Thoroughbred"}, {"id": 605, "speciesId": 42, "name": "Arabian horse"}, {"id": 606, "speciesId": 42, "name": "American Quarter Horse"}, {"id": 607, "speciesId": 42, "name": "Belgian Sport Horse"}, {"id": 608, "speciesId": 42, "name": " Argentinian saddle"}, {"id": 609, "speciesId": 42, "name": "<PERSON><PERSON>"}, {"id": 610, "speciesId": 42, "name": "Shetland pony"}, {"id": 611, "speciesId": 42, "name": "Shire horse"}, {"id": 612, "speciesId": 42, "name": "Skyros Pony"}, {"id": 613, "speciesId": 42, "name": "<PERSON> Walker"}, {"id": 614, "speciesId": 42, "name": "French Trotter"}, {"id": 615, "speciesId": 42, "name": "Vlaamperd"}, {"id": 616, "speciesId": 42, "name": "Welsh Pony and Cob"}, {"id": 617, "speciesId": 42, "name": "<PERSON><PERSON>"}, {"id": 618, "speciesId": 42, "name": "<PERSON><PERSON><PERSON>"}, {"id": 619, "speciesId": 40, "name": "poultry"}, {"id": 620, "speciesId": 40, "name": "prey"}, {"id": 621, "speciesId": 40, "name": "hen"}, {"id": 622, "speciesId": 40, "name": "duck"}, {"id": 623, "speciesId": 40, "name": "goose"}, {"id": 624, "speciesId": 40, "name": "pheasant"}, {"id": 625, "speciesId": 40, "name": "canary"}, {"id": 626, "speciesId": 40, "name": "pigeon"}, {"id": 627, "speciesId": 40, "name": "parrot"}, {"id": 628, "speciesId": 40, "name": "budgie"}, {"id": 629, "speciesId": 40, "name": "crow"}, {"id": 630, "speciesId": 1, "name": "Domestic shorthair"}, {"id": 631, "speciesId": 40, "name": "Other"}, {"id": 632, "speciesId": 2, "name": "Eurasier"}, {"id": 633, "speciesId": 2, "name": "Hokkaïdo"}, {"id": 635, "speciesId": 2, "name": "Parson Russell terrier"}, {"id": 636, "speciesId": 2, "name": "Kooikerhondje"}, {"id": 637, "speciesId": 2, "name": "Cockapoo"}, {"id": 638, "speciesId": 2, "name": "Cava<PERSON>o"}, {"id": 640, "speciesId": 2, "name": "Labradoodle"}, {"id": 641, "speciesId": 2, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 642, "speciesId": 2, "name": "Goldendoodle"}, {"id": 643, "speciesId": 2, "name": "Cavachon"}, {"id": 644, "speciesId": 2, "name": "<PERSON>uggle"}, {"id": 645, "speciesId": 2, "name": "Jackapoo"}, {"id": 646, "speciesId": 2, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 647, "speciesId": 2, "name": "Maltipoo"}, {"id": 648, "speciesId": 2, "name": "Shorkie"}, {"id": 649, "speciesId": 2, "name": "American Bully"}, {"id": 650, "speciesId": 2, "name": "Bluetick Coonhound"}, {"id": 651, "speciesId": 2, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 652, "speciesId": 2, "name": "Coonhound"}, {"id": 653, "speciesId": 2, "name": "English Bulldog"}, {"id": 654, "speciesId": 2, "name": "<PERSON>"}, {"id": 655, "speciesId": 2, "name": "Miniature Australian Shepherd"}, {"id": 656, "speciesId": 2, "name": "Miniature Poodle"}, {"id": 657, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 658, "speciesId": 2, "name": "Pyranean Ma<PERSON>iff"}, {"id": 659, "speciesId": 2, "name": "Rat Terrier"}, {"id": 660, "speciesId": 2, "name": "Treeing Walker Coonhound"}, {"id": 661, "speciesId": 2, "name": "Yorkipoo"}, {"id": 662, "speciesId": 2, "name": "Borsky"}, {"id": 663, "speciesId": 1, "name": "Domestic long-haired"}, {"id": 664, "speciesId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 665, "speciesId": 2, "name": "<PERSON> <PERSON><PERSON> Mixed Breed"}, {"id": 667, "speciesId": 2, "name": "Black Mouth Cur"}], "genders": [{"id": 1, "name": "Male"}, {"id": 2, "name": "Female"}, {"id": 3, "name": "Neutered Male"}, {"id": 4, "name": "Spayed Female"}], "petNoteTypes": [{"id": 1, "label": "Health"}, {"id": 2, "label": "Weight"}, {"id": 3, "label": "Food"}, {"id": 4, "label": "Walk"}, {"id": 5, "label": "Social"}, {"id": 6, "label": "Other"}, {"id": 7, "label": "Game"}, {"id": 8, "label": "Veterinarian message"}], "serviceCategory": [{"id": 1, "label": "<PERSON><PERSON><PERSON>"}, {"id": 2, "label": "Boarding"}, {"id": 20, "label": "Breeder"}, {"id": 25, "label": "<PERSON> Trainer"}, {"id": 26, "label": "Other"}], "serviceTypes": [{"id": 1, "label": "Veterinary Clinic"}, {"id": 2, "label": "Service"}, {"id": 3, "label": "Internal Service"}], "insuranceTypes": [{"id": 2, "label": "Insurance"}, {"id": 1, "label": "Wellness"}], "reminderTypes": [{"id": 1, "label": "Medication"}, {"id": 2, "label": "Other"}, {"id": 9, "label": "Appointment"}, {"id": 8, "label": "Dental cleaning"}, {"id": 7, "label": "Flea/Tick treatment"}, {"id": 6, "label": "<PERSON><PERSON><PERSON>"}, {"id": 5, "label": "Post-op check-up"}, {"id": 4, "label": "Shampoo & Derm Rx"}, {"id": 3, "label": "Vaccination"}], "reminderRecurrences": [], "activitySnoozes": [], "orderStatus": [{"id": 1, "label": "Request received"}, {"id": 2, "label": "Processing"}, {"id": 3, "label": "Available for pick-up"}, {"id": 4, "label": "Order retrieved"}], "unitLengths": [{"id": 1, "name": "Meters", "shortName": "m", "toMeters": 1}, {"id": 2, "name": "Feet", "shortName": "ft", "toMeters": 0.3048}], "unitWeights": [{"id": 1, "name": "Kilograms", "shortName": "kg", "toKilos": 1}, {"id": 2, "name": "Pounds", "shortName": "lb", "toKilos": 0.453592}], "countries": [{"id": 1, "shortLangLabel": "FR", "longLangLabel": "Français", "name": "FRANCE", "dateFormat": "d/m/Y", "timeFormat": "H:i", "datePlaceholder": "JJ/MM/AAAA", "isCurrent": false, "showLof": true, "showEuPassport": true}, {"id": 2, "shortLangLabel": "US", "longLangLabel": "American English", "name": "U.S.A.", "dateFormat": "m/d/Y", "timeFormat": "h:i A", "datePlaceholder": "MM/DD/YY", "isCurrent": true, "showLof": false, "showEuPassport": false}, {"id": 4, "shortLangLabel": "GB", "longLangLabel": "English", "name": "U.K.", "dateFormat": "d/m/Y", "timeFormat": "h:i A", "datePlaceholder": "JJ/MM/AAAA", "isCurrent": false, "showLof": false, "showEuPassport": false}, {"id": 5, "shortLangLabel": "LU", "longLangLabel": "Français LU", "name": "LUXEMBOURG", "dateFormat": "d/m/Y", "timeFormat": "H:i", "datePlaceholder": "JJ/MM/AAAA", "isCurrent": false, "showLof": false, "showEuPassport": true}, {"id": 6, "shortLangLabel": "BE", "longLangLabel": "Français BE", "name": "BELGIQUE", "dateFormat": "d/m/Y", "timeFormat": "H:i", "datePlaceholder": "JJ/MM/AAAA", "isCurrent": false, "showLof": false, "showEuPassport": true}, {"id": 7, "shortLangLabel": "CH", "longLangLabel": "Français CH", "name": "SUISSE", "dateFormat": "d/m/Y", "timeFormat": "H:i", "datePlaceholder": "JJ/MM/AAAA", "isCurrent": false, "showLof": false, "showEuPassport": false}, {"id": 8, "shortLangLabel": "CA", "longLangLabel": "Français CA", "name": "CANADA", "dateFormat": "d/m/Y", "timeFormat": "H:i", "datePlaceholder": "JJ/MM/AAAA", "isCurrent": false, "showLof": false, "showEuPassport": false}, {"id": 9, "shortLangLabel": "IE", "longLangLabel": "Irish English", "name": "IRELAND", "dateFormat": "d/m/Y", "timeFormat": "H:i", "datePlaceholder": "JJ/MM/AAAA", "isCurrent": false, "showLof": false, "showEuPassport": false}], "guiTexts": {"APPLICATION_MOBILE_APT_CAPTAINVET_CHOICE": "Take an appointment with Captain<PERSON><PERSON>", "APPLICATION_MOBILE_APT_CUSTOM_APT": "Use the clinic appointment scheduling website", "APPLICATION_MOBILE_APT_OFFICIAL_APT": "Make a request in myBuddy pet app®", "APPLICATION_MOBILE_AVATAR_CROP_PAGE_TITLE": "Crop your avatar", "APPLICATION_MOBILE_BUTTON_ACCEPT_AND_CONTINUE": "Accept and continue", "APPLICATION_MOBILE_BUTTON_FUNPIX_DELETE_ALL_SPRITES": "Delete all", "APPLICATION_MOBILE_BUTTON_FUNPIX_DELETE_SPRITE": "Delete this item", "APPLICATION_MOBILE_BUTTON_LABEL_ACTIVITY_LOSS_ADD": "I lost my pet", "APPLICATION_MOBILE_BUTTON_LABEL_ACTIVITY_NOTE_ADD": "Create a note", "APPLICATION_MOBILE_BUTTON_LABEL_ACTIVITY_REMINDER_ADD": "Add reminder", "APPLICATION_MOBILE_BUTTON_LABEL_ACTIVITY_SEE_ALL_REMINDERS": "See all reminders", "APPLICATION_MOBILE_BUTTON_LABEL_APPLE_LOGIN": "Continue with Apple", "APPLICATION_MOBILE_BUTTON_LABEL_CONTINUE_WITH_APPLE": "Continue with Apple", "APPLICATION_MOBILE_BUTTON_LABEL_BUY": "Buy", "APPLICATION_MOBILE_BUTTON_LABEL_CANCEL": "Cancel", "APPLICATION_MOBILE_BUTTON_LABEL_CLOSE": "Close", "APPLICATION_MOBILE_BUTTON_LABEL_CONFIRM": "Confirm", "APPLICATION_MOBILE_TITLE_WORKOUT_MOOD_SENTENCE": "Select your activity", "APPLICATION_MOBILE_TITLE_WORKOUT_MOOD": "Select your activity", "APPLICATION_MOBILE_BUTTON_LABEL_CONSENT_ERROR": "Your consent couldn't be sent correctly. Please contact directly your clinic.", "APPLICATION_MOBILE_BUTTON_LABEL_CONSENT_SENT": "Your consent has been sent correctly to your clinic. If you do not receive your code, please call your clinic directly.", "APPLICATION_MOBILE_BUTTON_LABEL_COUPON_CODE": "Coupon code ?", "APPLICATION_MOBILE_BUTTON_LABEL_DELETE": "Delete", "APPLICATION_MOBILE_BUTTON_LABEL_DONE": "Done", "APPLICATION_MOBILE_BUTTON_LABEL_EDIT": "Edit", "APPLICATION_MOBILE_BUTTON_LABEL_JOIN": "Join", "APPLICATION_MOBILE_BUTTON_LABEL_EDUCATION_FREE": "<p><span class=\"btnList-label\">Home care</span><span class=\"btnList-desc\">Learn how to protect and care for your pet everyday.</span></p>", "APPLICATION_MOBILE_BUTTON_LABEL_EDUCATION_FREE_LIGHT": "Learn how to protect and care for your pet everyday.", "APPLICATION_MOBILE_BUTTON_LABEL_EDUCATION_FULL": "<p><span class=\"btnList-label\">My Pet Care</span><span class=\"btnList-desc\">All the key information you need to care for your pet</span></p>", "APPLICATION_MOBILE_BUTTON_LABEL_EDUCATION_MY_PURCHASES": "<p><span class=\"btnList-label inline-block\">My purchases</span> <span class=\"btnList-desc\">Retrieve your premium pet care content here</span></p>", "APPLICATION_MOBILE_BUTTON_LABEL_EDUCATION_PAID": "<p><span class=\"btnList-label inline-block\">\"Breeds\" What you need to know!</span>&nbsp;<span class=\"btnList-desc\">Search our premium library for breed specific content. Our guides contain information for the care of your animals and the known risks associated with their breed.</span></p>", "APPLICATION_MOBILE_BUTTON_LABEL_EDUCATION_PAID_LIGHT": "Search our premium library for breed specific content. Our guides contain information for the care of your animals and the known risks associated with their breed.", "APPLICATION_MOBILE_BUTTON_LABEL_EMAIL_LOGIN": "Register with your email address", "APPLICATION_MOBILE_BUTTON_LABEL_FACEBOOK_LOGIN": "Sign in with Facebook", "APPLICATION_MOBILE_BUTTON_LABEL_GIVE_CONSENT": "GIVE YOUR<br>CONSENT", "APPLICATION_MOBILE_BUTTON_LABEL_GOOGLE_LOGIN": "Continue with Google", "APPLICATION_MOBILE_BUTTON_LABEL_CONTINUE_WITH_GOOGLE": "Continue with Google", "APPLICATION_MOBILE_BUTTON_LABEL_CONTINUE_WITH_FACEBOOK": "Continue with Facebook", "APPLICATION_MOBILE_BUTTON_LABEL_HEALTHBOOK2_CONSULTATION": "Consultations", "APPLICATION_MOBILE_BUTTON_LABEL_HEALTHBOOK2_PRESCRIPTION": "Prescriptions", "APPLICATION_MOBILE_BUTTON_LABEL_HEALTHBOOK2_PREVENTION": "Prevention", "APPLICATION_MOBILE_BUTTON_LABEL_INSURANCE_ADD": "Add insurance details", "APPLICATION_MOBILE_LOGIN_PAGE_DESCRIPTION": "Support your favorite animal charity just by walking your dog", "APPLICATION_MOBILE_BUTTON_LABEL_LOGIN": "Log in", "APPLICATION_MOBILE_BUTTON_LABEL_NB_PHOTO_NOTE": "(up to 5)", "APPLICATION_MOBILE_BUTTON_LABEL_NO": "No", "APPLICATION_MOBILE_BUTTON_LABEL_NOT_REGISTERED": "Not registered ? Sign up", "APPLICATION_MOBILE_BUTTON_LABEL_NOT_REGISTERED_SIGNUP": "Create Account", "APPLICATION_MOBILE_BUTTON_LABEL_SIGNUP": "Sign Up", "APPLICATION_MOBILE_BUTTON_LABEL_CREATE_ACCOUNT": "Sign Up", "APPLICATION_MOBILE_BUTTON_LABEL_OK": "OK", "APPLICATION_MOBILE_BUTTON_LABEL_PASSWORD_REQUEST": "Forgot your password?", "APPLICATION_MOBILE_BUTTON_LABEL_FORGOT_PASSWORD": "Forgot Password?", "APPLICATION_MOBILE_BUTTON_LABEL_PET_ADD": "Add a Dog", "APPLICATION_MOBILE_BUTTON_LABEL_DOG_ADD": "Add a Dog", "APPLICATION_MOBILE_BUTTON_LABEL_PET_ADD_FIRST_PET": "Add your first pet here", "APPLICATION_MOBILE_BUTTON_LABEL_PET_ADD_FROM_VET": "Automatically imported from data in your veterinary clinic", "APPLICATION_MOBILE_BUTTON_LABEL_PET_ADD_MANUALLY": "Or add pet details (manually)", "APPLICATION_MOBILE_BUTTON_LABEL_PHOTO_ADD": "Add a picture", "APPLICATION_MOBILE_BUTTON_LABEL_PICTURE_PROFILE_CROP": "Recenter the profile picture", "APPLICATION_MOBILE_BUTTON_LABEL_PICTURE_REMOVE_PICTURE": "Remove picture", "APPLICATION_MOBILE_BUTTON_LABEL_PICTURE_SELECT_FROM_DEVICE_GALLERY": "Select from Gallery", "APPLICATION_MOBILE_BUTTON_LABEL_PICTURE_SELECT_FROM_PET_GALERY": "Select from pet photo album", "APPLICATION_MOBILE_BUTTON_LABEL_PICTURE_SELECT_FROM_PET_GALLERY": "Select from pet photo album", "APPLICATION_MOBILE_BUTTON_LABEL_PICTURE_TAKE_PICTURE": "Take Picture", "APPLICATION_MOBILE_BUTTON_LABEL_RECOMMEND": "Recommend", "APPLICATION_MOBILE_BUTTON_LABEL_REMINDER": "Add a reminder", "APPLICATION_MOBILE_BUTTON_LABEL_RESET": "Reset", "APPLICATION_MOBILE_BUTTON_LABEL_SAVE": "Save", "APPLICATION_MOBILE_BUTTON_LABEL_SEARCH_FOR_A_BREED": "Search for a breed", "APPLICATION_MOBILE_BUTTON_LABEL_SEARCH_FOR_A_STATE": "Search for a state", "APPLICATION_MOBILE_BUTTON_LABEL_SEE": "Show", "APPLICATION_MOBILE_BUTTON_LABEL_SEND": "Send", "APPLICATION_MOBILE_BUTTON_LABEL_RESEND": "Resend", "APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_BY_CODE": "Use Clinic code\r\n(Complete access)", "APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_CLINIC": "Add a clinic", "APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_FIRST_CLINIC": "Add a clinic here", "APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_MANUALLY": "Or add Clinic details (manually)\r\n(Limited access)", "APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_SERVICE": "Add a service", "APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_CONNECT_TO": "Connect to ", "APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ORDER": "Order a product", "APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_REQUEST_APPOINTMENT": "Request an appointment", "APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT": "Submit", "APPLICATION_MOBILE_BUTTON_LABEL_USER_ALREADY_REGISTERED": "Have an account already? Log in", "APPLICATION_MOBILE_BUTTON_LABEL_HAVE_AN_ACCOUNT": "Have an account already? Log in", "APPLICATION_MOBILE_BUTTON_LABEL_USER_DETACH_FB_ACCOUNT": "Detach my Facebook account", "APPLICATION_MOBILE_BUTTON_LABEL_USER_EDIT_CONTACT_INFORMATIONS": "Edit my profile", "APPLICATION_MOBILE_BUTTON_LABEL_USER_EDIT_PASSWORD": "Change password", "APPLICATION_MOBILE_BUTTON_LABEL_USER_FINISH_FB_REGISTRATION": "Finish your registration", "APPLICATION_MOBILE_BUTTON_LABEL_USER_LOGOUT": "Logout", "APPLICATION_MOBILE_BUTTON_LABEL_USER_REGISTER": "Create my account", "APPLICATION_MOBILE_BUTTON_LABEL_WEIGHT_ADD": "Add a weight", "APPLICATION_MOBILE_BUTTON_LABEL_WELCOME": "Welcome to myBuddy pet app®", "APPLICATION_MOBILE_BUTTON_LABEL_WELCOME_SHORT": "Welcome to", "APPLICATION_MOBILE_BUTTON_LABEL_WELLNESS_ADD": "Add health plan details", "APPLICATION_MOBILE_BUTTON_LABEL_YES": "Yes", "APPLICATION_MOBILE_BUTTON_PET_DECEASED": "My pet is deceased", "APPLICATION_MOBILE_BUTTON_PET_DECEASED_ADJ": "deceased", "APPLICATION_MOBILE_BUTTON_RECONCILIATION_PET_LINK": "Link this pet", "APPLICATION_MOBILE_CAPTAINVET_16": "Fill in the following information", "APPLICATION_MOBILE_CAPTAINVET_23": "You must choose an animal", "APPLICATION_MOBILE_CAPTAINVET_24": "Choose an animal", "APPLICATION_MOBILE_CAPTAINVET_25": "You must choose an appointment reason", "APPLICATION_MOBILE_CAPTAINVET_27": "Choose a pattern", "APPLICATION_MOBILE_COMMUNITY_DELETE": "Delete", "APPLICATION_MOBILE_COMMUNITY_DELETE_PACK": "Delete Group", "APPLICATION_MOBILE_COMMUNITY_EDIT": "Edit", "APPLICATION_MOBILE_COMMUNITY_INVITE_BODY": "Enter the email address to which you would like to send an invitation.", "APPLICATION_MOBILE_COMMUNITY_SEND_INVITE": "Send Invite", "APPLICATION_MOBILE_COMMUNITY_INVITATION_SENT": "Email invitation sent successfully !", "APPLICATION_MOBILE_COMMUNITY_INVITATION_USER_ALREADY_IN_PACK": "User is already a member of the group.", "APPLICATION_MOBILE_COMMUNITY_INVITATION_USER_ADDED_TO_PACK": "User already on WoofTrax. Added to the group successfully.", "APPLICATION_MOBILE_COMMUNITY_INVITATION_USER_ALREADY_SENT": "User is already invited to the group.", "APPLICATION_MOBILE_COMMUNITY_SENT_INVITATIONS": "Sent Invitations", "APPLICATION_MOBILE_COMMUNITY_INVITE": "Invite", "APPLICATION_MOBILE_COMMUNITY_CREATE_NEW_GROUP": "Create a new Group", "APPLICATION_MOBILE_COMMUNITY_LEAVE": "Leave Group", "APPLICATION_MOBILE_COMMUNITY_LEAVE_PACK": "Leave Group", "APPLICATION_MOBILE_COMMUNITY_PACK_LEAVE": "Leave", "APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE": "Invite", "APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_CONTACTS": "Contacts", "APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_EMAIL": "Email", "APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_SHARE": "Share", "APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_WOOFTRAX_CONTACTS": "On WoofTrax", "APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_NOT_WOOFTRAX_CONTACTS": "Not on WoofTrax", "APPLICATION_MOBILE_COMMUNITY_SEARCH_EMAIL_NAME": "Search by Email or Name", "APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_DESC": "Add your contacts directly to your group", "APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_DESC_2": "Invite your contacts on WoofTrax", "APPLICATION_MOBILE_COMMUNITY_INVITATION_COPY": "Copy Link", "APPLICATION_MOBILE_COMMUNITY_INVITATION_COPY_DESC": "You can use this public link to invite your friends to this group", "APPLICATION_MOBILE_COMMUNITY_INVITATION_COPY_BUTTON": "Copy", "APPLICATION_MOBILE_COMMUNITY_INVITATION_LINK_COPIED": "Link copied", "APPLICATION_MOBILE_COMMUNITY_INVITATION_SHARE_LINK": "Share Link", "APPLICATION_MOBILE_COMMUNITY_INVITATION_SHARE_DESC": "Send Invitation via SMS, email or through another app", "APPLICATION_MOBILE_COMMUNITY_INVITATION_SHARE_QR": "Share QR Code", "APPLICATION_MOBILE_COMMUNITY_INVITATION_SHARE_QR_DESC": "Anyone with the WoofTrax app can join your group with this QR code", "APPLICATION_MOBILE_COMMUNITY_INVITATION_SHARE": "Share", "APPLICATION_MOBILE_COMMUNITY_NO_CONTACT": "No contacts", "APPLICATION_MOBILE_COMMUNITY_MEMBER_ADDED_TOAST": "Member Added Successfully !", "APPLICATION_MOBILE_BUTTON_LABEL_ADDED": "Added", "APPLICATION_MOBILE_BUTTON_LABEL_ADD": "Add", "APPLICATION_MOBILE_BUTTON_LABEL_INVITE": "Invite", "APPLICATION_MOBILE_BUTTON_LABEL_INVITE_SENT": "<PERSON><PERSON><PERSON>", "APPLICATION_MOBILE_BUTTON_LABEL_SET_WEEKLY_GOALS": "Set Weekly Goals", "APPLICATION_MOBILE_DRAWER_COPYRIGHT": "myBuddy pet app®\r\n© 2021 Medi-Productions INC.\r\nAll rights reserved.", "APPLICATION_MOBILE_DRAWER_COPYRIGHT_MYBUDDY": "myBuddy pet app®\r\n© 2021 Medi-Productions INC.\r\nAll rights reserved.", "APPLICATION_MOBILE_DRAWER_COPYRIGHT_WOOFTRAX": "Wooftrax myBuddy\r\n© 2021 Medi-Productions INC.\r\nAll rights reserved.", "APPLICATION_MOBILE_DRAWER_COPYRIGHT_WOOFTRAX_INC": "© 2022 WoofTrax Inc.\r\nwww.wooftrax.com", "APPLICATION_MOBILE_ERROR_ANNOUNCEMENT_NO_MATCHING_RECORD": "No news matches this request", "APPLICATION_MOBILE_ERROR_APPOINTMENT_NO_MATCHING_RECORD": "No appointment matches your requet", "APPLICATION_MOBILE_ERROR_EMAIL_ALREADY_USED": "Email address already in use !", "APPLICATION_MOBILE_ERROR_HOSPITALIZATION_NO_MATCHING_RECORD": "No hospitalization note matches this request", "APPLICATION_MOBILE_ERROR_ORDER_NO_MATCHING_RECORD": "No order matches this request", "APPLICATION_MOBILE_ERROR_REMINDER_NO_MATCHING_RECORD": "No reminders matches this request", "APPLICATION_MOBILE_FIELD_LABEL_ACTIVATION_PERIOD": "Activation period", "APPLICATION_MOBILE_FIELD_LABEL_ADD_APPOINTMENT_NOTE": "Add an existing note", "APPLICATION_MOBILE_FIELD_LABEL_ADDRESS": "Address", "APPLICATION_MOBILE_FIELD_LABEL_AUTHORIZE": "Authorize", "APPLICATION_MOBILE_FIELD_LABEL_BIRTHDATE": "Birthdate", "APPLICATION_MOBILE_FIELD_LABEL_BIRTHDATE_2": "Birthday", "APPLICATION_MOBILE_FIELD_LABEL_ADOPTION_DATE": "Adoption date or date dog joined family", "APPLICATION_MOBILE_FIELD_LABEL_ADOPTED_FROM_CHARITY": "I adopted this dog from an animal charity", "APPLICATION_MOBILE_FIELD_LABEL_ADOPTED_CHARITY": "Select animal charity adopted from", "APPLICATION_MOBILE_FIELD_LABEL_WHERE_DID_YOU_GET_DOG": "Where did you get this dog from?", "APPLICATION_MOBILE_FIELD_LABEL_AGE": "Age", "APPLICATION_MOBILE_FIELD_LABEL_BREED": "Breed", "APPLICATION_MOBILE_FIELD_LABEL_PRIMARY_BREED": "Primary breed", "APPLICATION_MOBILE_FIELD_LABEL_SECONDARY_BREED": "Secondary breed", "APPLICATION_MOBILE_FIELD_LABEL_CATEGORY": "Category", "APPLICATION_MOBILE_FIELD_LABEL_CHIP_NUMBER": "MicroChip ID", "APPLICATION_MOBILE_FIELD_LABEL_CITY": "City, State", "APPLICATION_MOBILE_FIELD_LABEL_NAME_CITY": "City", "APPLICATION_MOBILE_FIELD_LABEL_STATE": "State", "APPLICATION_MOBILE_FIELD_LABEL_COLLAR": "<PERSON><PERSON>", "APPLICATION_MOBILE_FIELD_LABEL_COLOR": "Color", "APPLICATION_MOBILE_FIELD_LABEL_COMPANY": "Company", "APPLICATION_MOBILE_FIELD_LABEL_CONTRACT_END_DATE": "Expiration date", "APPLICATION_MOBILE_FIELD_LABEL_CONTRACT_NUMBER": "Contract number", "APPLICATION_MOBILE_FIELD_LABEL_CONTRACT_START_DATE": "Starting date", "APPLICATION_MOBILE_FIELD_LABEL_COUNTRY": "Country", "APPLICATION_MOBILE_FIELD_LABEL_CROSSED": "Mixed breed", "APPLICATION_MOBILE_FIELD_LABEL_PURE_BREED": "Pure breed", "APPLICATION_MOBILE_FIELD_LABEL_DATE": "Date", "APPLICATION_MOBILE_FIELD_LABEL_DEATHDATE": "Date of death", "APPLICATION_MOBILE_FIELD_LABEL_DESCRIPTION": "Description", "APPLICATION_MOBILE_FIELD_LABEL_DISTANCE": "Distance", "APPLICATION_MOBILE_FIELD_LABEL_MILES": "<PERSON>", "APPLICATION_MOBILE_FIELD_LABEL_DURATION": "Duration", "APPLICATION_MOBILE_FIELD_LABEL_SEARCH_EMAIL_ADDRESS": "Search Email address", "APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS": "Email Address", "APPLICATION_MOBILE_FIELD_LABEL_EMAIL_WEBSITE": "Website address", "APPLICATION_MOBILE_FIELD_LABEL_EMERGENCY_PHONE_NUMBER": "Emergency phone number", "APPLICATION_MOBILE_FIELD_LABEL_EU_PASSPORT": "European passport number", "APPLICATION_MOBILE_FIELD_LABEL_FAX": "Fax", "APPLICATION_MOBILE_FIELD_LABEL_FOOD": "Food", "APPLICATION_MOBILE_FIELD_LABEL_FROM": "From", "APPLICATION_MOBILE_FIELD_LABEL_HOUR": "Hour", "APPLICATION_MOBILE_FIELD_LABEL_JOB": "Position", "APPLICATION_MOBILE_FIELD_LABEL_LOF": "LOF number", "APPLICATION_MOBILE_FIELD_LABEL_MESSAGE": "Order details", "APPLICATION_MOBILE_FIELD_LABEL_MSG_DESC": "Information about your pet and where you lost it", "APPLICATION_MOBILE_FIELD_LABEL_NOTE": "Note", "APPLICATION_MOBILE_FIELD_LABEL_NOTE_PLACEHOLDER": "Type your note here", "APPLICATION_MOBILE_FIELD_LABEL_NOTES": "Notes", "APPLICATION_MOBILE_FIELD_LABEL_NUMBER": "Number", "APPLICATION_MOBILE_FIELD_LABEL_OTHER_CHOICE": "Other", "APPLICATION_MOBILE_FIELD_LABEL_OTHER_EMAIL_ADDRESS": "Other email address", "APPLICATION_MOBILE_FIELD_LABEL_OTHER_PHONE_NUMBER": "Other phone number", "APPLICATION_MOBILE_FIELD_LABEL_PERSON1_TO_CONTACT": "1st contact", "APPLICATION_MOBILE_FIELD_LABEL_PERSON2_TO_CONTACT": "2nd contact", "APPLICATION_MOBILE_FIELD_LABEL_PERSON3_TO_CONTACT": "3rd contact", "APPLICATION_MOBILE_FIELD_LABEL_PERSONS_TO_CONTACT": "Emergency contact information", "APPLICATION_MOBILE_FIELD_LABEL_PET_GENDER": "Gender", "APPLICATION_MOBILE_FIELD_LABEL_PET_GENDER_FEMALE": "Female", "APPLICATION_MOBILE_FIELD_LABEL_PET_GENDER_MALE": "Male", "APPLICATION_MOBILE_FIELD_LABEL_PET_NAME": "Name", "APPLICATION_MOBILE_FIELD_HINT_PET_NAME": "Your dog’s name", "APPLICATION_MOBILE_FIELD_LABEL_PHONE_NUMBER": "Phone number", "APPLICATION_MOBILE_FIELD_LABEL_PHONE_NUMBER_FIRST": "Main phone number", "APPLICATION_MOBILE_FIELD_LABEL_PHONE_NUMBER_SECOND": "Second phone number", "APPLICATION_MOBILE_FIELD_LABEL_PRECISION": "Additional Details", "APPLICATION_MOBILE_FIELD_LABEL_PRODUCT_GIVEN": "Product delivered", "APPLICATION_MOBILE_FIELD_LABEL_RECURRENCE_END": "Until", "APPLICATION_MOBILE_FIELD_LABEL_RECURRENCE_END_TYPE": "During", "APPLICATION_MOBILE_FIELD_LABEL_RECURRENCE_LAST": "last reminder", "APPLICATION_MOBILE_FIELD_LABEL_RECURRENCE_REMAINING": "remaining", "APPLICATION_MOBILE_FIELD_LABEL_RECURRENCE_TYPE": "Every", "APPLICATION_MOBILE_FIELD_LABEL_RECURRENT": "Recurring ?", "APPLICATION_MOBILE_FIELD_LABEL_SEND_TO": "<p>Send to my vet</p>\r\n<p>(not guaranteed response)</p>", "APPLICATION_MOBILE_FIELD_LABEL_SEND_WHICH_CLINICS": "Select at least one recipient", "APPLICATION_MOBILE_FIELD_LABEL_SERVICE_CODE": "Your veterinarian code (optional)", "APPLICATION_MOBILE_FIELD_LABEL_SERVICE_NAME": "Name", "APPLICATION_MOBILE_FIELD_LABEL_SERVICE_SHORT_CODE": "Code", "APPLICATION_MOBILE_FIELD_LABEL_STERILIZED": "Neutered", "APPLICATION_MOBILE_FIELD_LABEL_TITLE": "Title", "APPLICATION_MOBILE_FIELD_LABEL_TO": "To", "APPLICATION_MOBILE_FIELD_LABEL_TYPE": "Type", "APPLICATION_MOBILE_FIELD_LABEL_UNIT_DISTANCE": "Distance", "APPLICATION_MOBILE_FIELD_LABEL_UNIT_WEIGHT": "Weight", "APPLICATION_MOBILE_FIELD_LABEL_UNITS_DISTANCE": "Length", "APPLICATION_MOBILE_FIELD_LABEL_UNITS_WEIGHT": "Weight", "APPLICATION_MOBILE_FIELD_LABEL_USER_FIRSTNAME": "First Name", "APPLICATION_MOBILE_FIELD_LABEL_USER_LAST_NAME": "Last name", "APPLICATION_MOBILE_FIELD_LABEL_USER_LAST_name": "Last Name", "APPLICATION_MOBILE_FIELD_LABEL_USER_LOGIN": "Email", "APPLICATION_MOBILE_FIELD_LABEL_USER_EMAIL": "Email", "APPLICATION_MOBILE_FIELD_LABEL_USER_PASSWORD": "Password", "APPLICATION_MOBILE_FIELD_LABEL_USER_CONFIRM_PASSWORD": "Confirm Password", "APPLICATION_MOBILE_FIELD_LABEL_USER_NEW_PASSWORD": "New Password", "APPLICATION_MOBILE_FIELD_LABEL_USER_CONFIRM_NEW_PASSWORD": "Confirm New Password", "APPLICATION_MOBILE_FIELD_LABEL_USER_PIMS_ID": "Unique identifier at your veterinary clinic", "APPLICATION_MOBILE_FIELD_LABEL_WEIGHT": "Weight", "APPLICATION_MOBILE_FIELD_LABEL_WHEN_LOST_PET": "When was your pet lost?", "APPLICATION_MOBILE_FIELD_LABEL_WHICH_CAUSE": "Reason for Appointment", "APPLICATION_MOBILE_FIELD_LABEL_WHICH_CLINIC": "With", "APPLICATION_MOBILE_FIELD_LABEL_WHICH_PET": "For", "APPLICATION_MOBILE_FIELD_LABEL_WHICH_VET": "Contact", "APPLICATION_MOBILE_FIELD_LABEL_ZIPCODE": "Zip code", "APPLICATION_MOBILE_FIELD_OPTION_CREATE_PET": "Import this pet", "APPLICATION_MOBILE_FIELD_OPTION_IGNORE_PET": "Ignore this animal", "APPLICATION_MOBILE_FIELD_OPTION_PET_ALREADY_CREATED": "Pet already created in myBuddy pet app®", "APPLICATION_MOBILE_FIELD_REMINDER_DELAY_001": "At event time", "APPLICATION_MOBILE_FIELD_REMINDER_DELAY_002": "5 min before", "APPLICATION_MOBILE_FIELD_REMINDER_DELAY_003": "15 min before", "APPLICATION_MOBILE_FIELD_REMINDER_DELAY_004": "1 hour before", "APPLICATION_MOBILE_FIELD_REMINDER_DELAY_005": "1 day before", "APPLICATION_MOBILE_FIELD_TEXT_001_RECURRENCE_TYPE": "days", "APPLICATION_MOBILE_FIELD_TEXT_002_RECURRENCE_TYPE": "weeks", "APPLICATION_MOBILE_FIELD_TEXT_003_RECURRENCE_TYPE": "months", "APPLICATION_MOBILE_FIELD_TEXT_004_RECURRENCE_TYPE": "years", "APPLICATION_MOBILE_FIELD_VIEW_ALL_PETS": "All my pets", "APPLICATION_MOBILE_FORM_GENERAL_VALIDATION_ERROR": "Form is not valid!  Please review and correct.", "APPLICATION_MOBILE_HAPPY_BIRTHDAY": "Happy Birthday", "APPLICATION_MOBILE_HB_ERROR_NO_PET": "You must choose at least one pet.", "APPLICATION_MOBILE_HB_ERROR_NO_TYPE": "You must choose a type.", "APPLICATION_MOBILE_HB_ERROR_NO_VALENCE": "You must choose at least one valence.", "APPLICATION_MOBILE_HB_ERROR_RELEASE_DATE_AFTER_NEXT": "Next reminder date can't be before the release date.", "APPLICATION_MOBILE_HB_ERROR_RELEASE_DATE_NOT_POSSIBLE": "Release date can't be in the future.", "APPLICATION_MOBILE_HOME_ANNOUNCEMENT_EMPTY": "No announcements", "APPLICATION_MOBILE_HOME_MESSAGE_EMPTY": "No discussion", "APPLICATION_MOBILE_HOME_MESSAGE_IMPOSSIBLE": "You are not connected to any clinic that accepts messages", "APPLICATION_MOBILE_HOME_NOTIFICATION_EMPTY": "No notification", "APPLICATION_MOBILE_ICON_APT_TITLE": "Appointment", "APPLICATION_MOBILE_ICON_CALL_TITLE": "Call", "APPLICATION_MOBILE_ICON_EMAIL_TITLE": "E-mail", "APPLICATION_MOBILE_ICON_EMERGENCY_TITLE": "Emergency", "APPLICATION_MOBILE_ICON_MESSAGE_TITLE": "Message", "APPLICATION_MOBILE_ICON_ORDER_TITLE": "Order", "APPLICATION_MOBILE_LABEL_ACCEPT_CGU": "I accept <u>the terms & conditions</u>", "APPLICATION_MOBILE_LABEL_ALERT_BUY_WITH_CODE": "Buy with a coupon code", "APPLICATION_MOBILE_LABEL_ALLPET_PET_LIFE": "My pets' life", "APPLICATION_MOBILE_LABEL_ANOMALY_REPORTED": "Anomalies reported during the review", "APPLICATION_MOBILE_LABEL_APPLICATION_REBOOT": "Restarting", "APPLICATION_MOBILE_LABEL_AVATAR_REACH_LEVEL_COUNT": "Reach Level %l", "APPLICATION_MOBILE_LABEL_BUDDY_CREDITS_HISTORICAL_ADD": "Purchase of {0} Buddy Credits", "APPLICATION_MOBILE_LABEL_BUDDY_CREDITS_HISTORICAL_SPEND": "Expenditure of {0} Buddy Credits", "APPLICATION_MOBILE_LABEL_BUDDYCREDITS": "Buddy Credits™", "APPLICATION_MOBILE_LABEL_BUY_BUDDYCREDITS": "Buy Buddy Credits™", "APPLICATION_MOBILE_LABEL_CAUSE": "Reason for Appointment", "APPLICATION_MOBILE_LABEL_CONFIRM_PURCHASE": "Confirm your in-app purchase", "APPLICATION_MOBILE_LABEL_CONFIRM_PURCHASE_QUESTION": "Do you want to buy this content for {0} Buddy Credits ?", "APPLICATION_MOBILE_LABEL_CONFIRM_PURCHASE_QUESTION_NOT_ENOUGHT": "You don't have enough Buddy Credits ™ to purchase this content. Buy a new Buddy Credits ™ group or use a promo code.", "APPLICATION_MOBILE_LABEL_CONFIRMATION": "Confirmation", "APPLICATION_MOBILE_LABEL_DATE_TODAY": "Today", "APPLICATION_MOBILE_LABEL_DATE_TODAY_AT": "Today at", "APPLICATION_MOBILE_LABEL_DATE_YESTERDAY": "Yesterday", "APPLICATION_MOBILE_LABEL_DATE_YESTERDAY_AT": "Yesterday at", "APPLICATION_MOBILE_LABEL_DAY_FRIDAY": "Friday", "APPLICATION_MOBILE_LABEL_DAY_MONDAY": "Monday", "APPLICATION_MOBILE_LABEL_DAY_SATURDAY": "Saturday", "APPLICATION_MOBILE_LABEL_DAY_SUNDAY": "Saturday", "APPLICATION_MOBILE_LABEL_DAY_SUNDAY_FIX": "Sunday", "APPLICATION_MOBILE_LABEL_DAY_THURSDAY": "Thursday", "APPLICATION_MOBILE_LABEL_DAY_TUESDAY": "Tuesday", "APPLICATION_MOBILE_LABEL_DAY_WEDNESDAY": "Wednesday", "APPLICATION_MOBILE_LABEL_DIAGNOSTIC": "Diagnostic", "APPLICATION_MOBILE_LABEL_DISPLAY": "Display", "APPLICATION_MOBILE_LABEL_EMERGENCY": "Emergency", "APPLICATION_MOBILE_LABEL_EMERGENCY_HOLIDAYS": "Holidays", "APPLICATION_MOBILE_LABEL_EMERGENCY_NIGHTS": "Nights", "APPLICATION_MOBILE_LABEL_EMERGENCY_SERVICE": "Emergency", "APPLICATION_MOBILE_LABEL_EMERGENCY_STAFF": "Availability during emergencies", "APPLICATION_MOBILE_LABEL_EMERGENCY_SUNDAYS": "Sundays", "APPLICATION_MOBILE_LABEL_ENTER_CODE_TO_BUY": "Please enter your coupon code :", "APPLICATION_MOBILE_LABEL_ERROR": "Error", "APPLICATION_MOBILE_LABEL_ERROR_CLINIC_CODE": "Invalid clinic code ", "APPLICATION_MOBILE_LABEL_EXTERNAL_PEST_CONTROL": "External Antiparasitics", "APPLICATION_MOBILE_LABEL_FIELDS_REQUIRED": "* required field", "APPLICATION_MOBILE_LABEL_FILTER_ALL": "All", "APPLICATION_MOBILE_LABEL_FILTER_SEE_ALL": "See All", "APPLICATION_MOBILE_LABEL_FILTER_ONLY_VET": "Only information from my vet", "APPLICATION_MOBILE_LABEL_GENERAL_CONDITION": "Condition", "APPLICATION_MOBILE_LABEL_GEOLOCATION": "Geolocation", "APPLICATION_MOBILE_LABEL_GOOGLE_RANK": "Rank us on Google Maps", "APPLICATION_MOBILE_LABEL_HEALTH_VISIT": "Health visit", "APPLICATION_MOBILE_LABEL_HEALTHBOOK_ANTI_EXTERNAL": "External parasites", "APPLICATION_MOBILE_LABEL_HEALTHBOOK_ANTI_INTERNAL": "Internal parasites", "APPLICATION_MOBILE_LABEL_HEALTHBOOK_NOT_REALISED": "not included", "APPLICATION_MOBILE_LABEL_HEALTHBOOK_STERILISATION": "Neutering", "APPLICATION_MOBILE_LABEL_HEALTHBOOK_VACCINES": "Vaccines", "APPLICATION_MOBILE_LABEL_HEALTHBOOK2_CONSULTATION": "Consultations", "APPLICATION_MOBILE_LABEL_HEALTHBOOK2_PRESCRIPTION": "Prescriptions", "APPLICATION_MOBILE_LABEL_HEALTHBOOK2_PREVENTION": "Prevention", "APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_ADD": "add a vaccine/treatment", "APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_LOT_NUMBER": "lot number", "APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_NAME": "product name", "APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_NEXT_DATE": "next administration date", "APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_RELEASE_DATE": "release date", "APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_VALENCE": "valence", "APPLICATION_MOBILE_LABEL_HOSPITALIZATION": "Hospitalization", "APPLICATION_MOBILE_LABEL_HOSPITALIZATION_NEWS_ANIMAL": "news from your hospitalized pet", "APPLICATION_MOBILE_LABEL_IN_PROCESS": "In process", "APPLICATION_MOBILE_LABEL_INFORMATION": "Information", "APPLICATION_MOBILE_LABEL_INFORMATIONS": "Information", "APPLICATION_MOBILE_LABEL_INSURANCES": "Insurance", "APPLICATION_MOBILE_LABEL_INTERNAL_PEST_CONTROL": "Anti-Parasites Internal (API)", "APPLICATION_MOBILE_LABEL_JPG_ONLY": "The application accepts only .jpg and .jpeg", "APPLICATION_MOBILE_LABEL_LANGUAGE": "Language", "APPLICATION_MOBILE_LABEL_LANGUAGE_SYSTEM": "Use the phone language", "APPLICATION_MOBILE_LABEL_LOADING": "Loading", "APPLICATION_MOBILE_LABEL_MARK_AS": "<PERSON> as", "APPLICATION_MOBILE_LABEL_MARK_AS_DONE": "Mark as done", "APPLICATION_MOBILE_LABEL_MARK_AS_DONE_ALL": "Mark all as done", "APPLICATION_MOBILE_LABEL_MARK_AS_DONE_TODAY": "Mark as done until today", "APPLICATION_MOBILE_LABEL_MEDICAL_TREATMENT": "Medical treatment", "APPLICATION_MOBILE_LABEL_MSG_ALERT_MANUAL_RECONCILIATION_KO": "Your personal information could not be found at your veterinary clinic.", "APPLICATION_MOBILE_LABEL_MSG_ALERT_MANUAL_RECONCILIATION_LATER": "This procedure will always be available on the page of your veterinary clinic.", "APPLICATION_MOBILE_LABEL_MSG_ALERT_MANUAL_RECONCILIATION_OK": "Reconciliation has been successfully completed!", "APPLICATION_MOBILE_LABEL_MSG_ALERT_MANUAL_RECONCILIATION_OK_NO_PETS": "Congratulations, the link between your myBuddy pet app® account and your veterinary clinic has been successfully completed!", "APPLICATION_MOBILE_LABEL_MSG_ALERT_RECONCILIATION_AUTO_OK": "A successful data synchronization has been performed with your veterinary !", "APPLICATION_MOBILE_LABEL_MY_PETS": "My pets", "APPLICATION_MOBILE_LABEL_MY_DOGS": "Dogs", "APPLICATION_MOBILE_LABEL_MY_REMINDERS": "My reminders", "APPLICATION_MOBILE_LABEL_MY_SERVICES": "My services", "APPLICATION_MOBILE_LABEL_MY_VETS": "My vet", "APPLICATION_MOBILE_LABEL_NEXT": "Next", "APPLICATION_MOBILE_LABEL_NO_CLINIC_SENTENCE": "you haven't added a clinic yet", "APPLICATION_MOBILE_LABEL_NO_PET_SENTENCE": "you haven't added a pet yet", "APPLICATION_MOBILE_LABEL_NO_DOG_SENTENCE": "you haven't added a dog yet", "APPLICATION_MOBILE_LABEL_ADD_YOUR_DOG": "+ Add your dog", "APPLICATION_MOBILE_LABEL_NOTES": "Notes", "APPLICATION_MOBILE_LABEL_NOTIFICATION": "Notification", "APPLICATION_MOBILE_LABEL_NOTIFICATION_NEW_REMINDER": "myBuddy pet app® has recorded a reminder %1(%3) for %2.\r\nYou will receive a notification before the date of the prescribed deadline.", "APPLICATION_MOBILE_LABEL_NOTIFICATIONS": "Notifications", "APPLICATION_MOBILE_LABEL_OPEN_24": "24/7", "APPLICATION_MOBILE_LABEL_OR": "Or", "APPLICATION_MOBILE_LABEL_ORDER_PRODUCT": "Ordered product :", "APPLICATION_MOBILE_LABEL_ORDER_SENT_DATE": "Order date", "APPLICATION_MOBILE_LABEL_PARTNERS": "Partners", "APPLICATION_MOBILE_LABEL_PET_GALLERY": "Image gallery", "APPLICATION_MOBILE_LABEL_PET_IMAGE_GALLERY": "Image Gallery", "APPLICATION_MOBILE_LABEL_PET_HEALTH_BOOK": "Health book", "APPLICATION_MOBILE_LABEL_PET_PET_LIFE": "My pet's life", "APPLICATION_MOBILE_LABEL_PETSMILES": "PetSmiles™", "APPLICATION_MOBILE_LABEL_PREV": "Previous", "APPLICATION_MOBILE_LABEL_PRODUCT_ORDER_PRESCRIPTION": "When ordering prescription medicines you must have previously obtained a prescription from your veterinarian. If you do not have a prescription, please get in touch with your clinic prior to ordering.", "APPLICATION_MOBILE_LABEL_PURCHARSE_FOR_ONLY": "Access all information below for only", "APPLICATION_MOBILE_LABEL_RECONCILIATION_IMPORT": "Import of ", "APPLICATION_MOBILE_LABEL_RECONCILIATION_INTRO_PETS_MULTI": "{0} animals are referenced in your veterinary clinic.\r\nPlease validate the information collected for each animal:", "APPLICATION_MOBILE_LABEL_RECONCILIATION_INTRO_PETS_UNIQUE": "One animal is referenced in your veterinary clinic.\r\nPlease validate the information collected for this animal:", "APPLICATION_MOBILE_LABEL_RECONCILIATION_NO_PET": "No pet", "APPLICATION_MOBILE_LABEL_RECONCILIATION_NO_PIMSPET": "No pet data imported from your clinic", "APPLICATION_MOBILE_LABEL_RECONCILIATION_SELECT_CHOICE": "Select a choice", "APPLICATION_MOBILE_LABEL_RECONCILIATION_SELECT_INFOS": "Select the information to be updated in the application:", "APPLICATION_MOBILE_LABEL_RECONCILIATION_WHICH_PET": "Which animal does the information below match?", "APPLICATION_MOBILE_LABEL_REFUSE_CGU": "Refuse", "APPLICATION_MOBILE_LABEL_REMINDER": "Reminder", "APPLICATION_MOBILE_LABEL_REMINDER_ALREADY_DONE": "Reminder already marked as done", "APPLICATION_MOBILE_LABEL_REMINDER_DELAY": "Notify me", "APPLICATION_MOBILE_LABEL_REMINDERS": "Reminders", "APPLICATION_MOBILE_LABEL_RESPONSE": "Clinic message:", "APPLICATION_MOBILE_LABEL_SEARCH": "Search", "APPLICATION_MOBILE_LABEL_SERVICE": "Services", "APPLICATION_MOBILE_LABEL_SERVICE_CLINIC": "Clinic", "APPLICATION_MOBILE_LABEL_SERVICE_CLOSED": "Closed", "APPLICATION_MOBILE_LABEL_SERVICE_OPENING_HOURS": "Appointment Hours", "APPLICATION_MOBILE_LABEL_SERVICE_PRESENCE_HOURS": "Presence", "APPLICATION_MOBILE_LABEL_SERVICE_STAFF": "Staff", "APPLICATION_MOBILE_LABEL_SERVICE_STAFF_ABSENT": "Unavailable", "APPLICATION_MOBILE_LABEL_SERVICE_WALK_IN_HOURS": "Walk-in hours", "APPLICATION_MOBILE_LABEL_SETTINGS": "Settings", "APPLICATION_MOBILE_LABEL_SETTINGS_HOME_PET_SERVICE": "I want to highlight my animals on the home page instead of my clinic.", "APPLICATION_MOBILE_LABEL_SNOOZE": "Repeat ?", "APPLICATION_MOBILE_LABEL_SOURCE": "From", "APPLICATION_MOBILE_LABEL_SPECIES": "Species", "APPLICATION_MOBILE_LABEL_START_DOING": "Start %1!", "APPLICATION_MOBILE_LABEL_SUGGESTIONS": "Recommendations", "APPLICATION_MOBILE_LABEL_SURGERY_TREATMENT": "Surgical treatment", "APPLICATION_MOBILE_LABEL_TAB_BUDDYCREDITS_BUY": "Purchase", "APPLICATION_MOBILE_LABEL_TAB_BUDDYCREDITS_HISTORY": "History", "APPLICATION_MOBILE_LABEL_TAB_BUDDYCREDITS_HISTORY_FILTER": "Filters", "APPLICATION_MOBILE_LABEL_TAB_BUDDYCREDITS_HISTORY_FILTER_CLEAR_ALL": "Clear All", "APPLICATION_MOBILE_LABEL_WORKOUT_SHARE": "Share", "APPLICATION_MOBILE_LABEL_WORKOUT_SHARE_GRAPHIC": "Share Graphic", "APPLICATION_MOBILE_LABEL_WORKOUT_SHARE_MAP": "Share Map", "APPLICATION_MOBILE_LABEL_TARGET": "Recipient", "APPLICATION_MOBILE_LABEL_TEST_PERFORMED": "Reviews", "APPLICATION_MOBILE_LABEL_UNITS": "Units", "APPLICATION_MOBILE_LABEL_VACCINATION": "Immunizations", "APPLICATION_MOBILE_LABEL_VERSION": "Version", "APPLICATION_MOBILE_LABEL_VETS": "Vets", "APPLICATION_MOBILE_LABEL_WALK": "Walk", "APPLICATION_MOBILE_LABEL_WEIGHT": "Weight", "APPLICATION_MOBILE_LABEL_WEIGHT_DATE": "Date and time of weighing", "APPLICATION_MOBILE_LABEL_WEIGHT_NO_WEIGHT": "No weight", "APPLICATION_MOBILE_LABEL_WEIGHT_ADD_WEIGHT": "Add Weight", "APPLICATION_MOBILE_LABEL_WELLNESS": "Health plan", "APPLICATION_MOBILE_MESSAGE_ACTIVITY_LOSS_REMINDER_FLYER_URL": "To recap, the flyer is available at : <a href=\"#\" onclick=\"window.open('{0}', '_system', 'location=no');\" >{1}</a><br/> Have you found your pet?", "APPLICATION_MOBILE_MESSAGE_APPLICATION_MUST_BE_ON_DEVICE": "You must be on a phone to perform this action", "APPLICATION_MOBILE_MESSAGE_APPLICATION_REBOOT": "<p>The application must restart in order to apply this setting.<br />Do you want to continue ?</p>", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_CALENDAR_ADD_CONFIRMATION": "Appointment successfully added to your smartphone calendar", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_CALENDAR_EVENT_ALREADY_IN_CALENDAR": "This event is already set in your device calendar", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_CAUSE": "Please select a cause.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_END_TIME": "Please choose an end time", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_1_DATE": "Please select a date for the first request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_1_DATE_AFTER_NOW": "Please select a later date for the first request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_1_END_DATE_BEFORE_START_DATE": "Please select a start date for the first request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_1_START_HOUR": "Please select a start hour for the first request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_2_DATE": "Please select a date for the second request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_2_DATE_AFTER_NOW": "Please select a later date for the second request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_2_END_DATE_BEFORE_START_DAT1": "End date occurs before start date for the first request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_2_END_DATE_BEFORE_START_DATE": "End date occurs before start date for the second request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_2_START_HOUR": "Please select a start hour for the second request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_3_DATE": "Please select a date for the third request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_3_END_DATE_BEFORE_START_DAT1": "End date occurs before start date for the third request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_3_END_DATE_BEFORE_START_DATE": "End date occurs before start date for the third request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_REQUEST_3_START_HOUR": "Please select a start date for the third request.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_SERVICE": "Please select a clinic.", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_LABEL_REQUEST": "Choice", "APPLICATION_MOBILE_MESSAGE_APPOINTMENT_LABEL_REQUEST_SLOT": "Time slot", "APPLICATION_MOBILE_MESSAGE_BUDDYCREDITS_NO_BUDDYCREDITS": "Use Buddy Credits to purchase premium content.", "APPLICATION_MOBILE_MESSAGE_CLINIC_SUCCESSFULLY_ADDED": "Clinic successfully added", "APPLICATION_MOBILE_MESSAGE_CONFIRM_APPLICATION_EXIT": "Do you want to close the application ?", "APPLICATION_MOBILE_MESSAGE_CONFIRMATION": "Are you sure you want to continue ? ", "APPLICATION_MOBILE_MESSAGE_CONGRATS_PACK_PURCHASED": "You're just purchased the item '{0}'", "APPLICATION_MOBILE_MESSAGE_CONNECTING": "Sign In", "APPLICATION_MOBILE_MESSAGE_SIGN_IN": "Sign In", "APPLICATION_MOBILE_MESSAGE_ERROR_ACTIVITY_LOSS_ALREADY_MADE": "Loss statement already made", "APPLICATION_MOBILE_MESSAGE_ERROR_ADD_BUDDY_CREDIT_TRY_NEXT_START": "Buddy Credits couldn't be add to your account. A new attempt will be made the next time you restart the application.", "APPLICATION_MOBILE_MESSAGE_ERROR_APPOINTMENT_CALENDAR_ADD": "An error occured when adding the appointment to your device calendar", "APPLICATION_MOBILE_MESSAGE_ERROR_FACEBOOK_NO_EMAIL": "Unable to use the functionality of Facebook Connect (your email is not accessible)", "APPLICATION_MOBILE_MESSAGE_ERROR_LENGTH_DESCRIPTION": "The description must be at least {0} characters.", "APPLICATION_MOBILE_MESSAGE_ERROR_LENGTH_TITLE": "The title must be at least {0} characters.", "APPLICATION_MOBILE_MESSAGE_ERROR_MISSING_FIELD": "Missing field", "APPLICATION_MOBILE_MESSAGE_ERROR_NETWORK": "Network access error", "APPLICATION_MOBILE_MESSAGE_ERROR_NETWORK_CONNECTION": "Insufficient network connection", "APPLICATION_MOBILE_MESSAGE_ERROR_NO_NETWORK": "No network available", "APPLICATION_MOBILE_MESSAGE_ERROR_NO_SERVER_RESPONSE": "No server response", "APPLICATION_MOBILE_MESSAGE_ERROR_NO_USER": "No user exists for this email address", "APPLICATION_MOBILE_MESSAGE_ERROR_NOT_LOGIN": "Your session has expired", "APPLICATION_MOBILE_MESSAGE_ERROR_NOT_RIGHT_CLINIC": "Unknown clinic", "APPLICATION_MOBILE_MESSAGE_ERROR_NOT_RIGHT_PET": "Request rejected. This animal doesn't exist.", "APPLICATION_MOBILE_MESSAGE_ERROR_PET": "A pet must be selected", "APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_COMPANY": "Please select a company.", "APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_CONTRACT_NUMBER": "Please enter a contract number.", "APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_DATE": "Please select a date.", "APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_DATE_AND_HOUR": "Please select a date and an hour. ", "APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_DESCRIPTION": "Please enter a description.", "APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_HOUR": "Please select an hour.", "APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_PET": "Please select a pet.", "APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_REMINDER_DELAY": "Please select when the application should notify you", "APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_TITLE": "Please enter a title.", "APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_TYPE": "Please select a type.", "APPLICATION_MOBILE_MESSAGE_ERROR_RECONCILIATION_SAVE_PETS": "An error occurred while saving information about your pets.", "APPLICATION_MOBILE_MESSAGE_ERROR_REGISTER_EMAIL_USED": "Email already used", "APPLICATION_MOBILE_MESSAGE_ERROR_SERVER": "Server error", "APPLICATION_MOBILE_MESSAGE_ERROR_SERVER_ACCESS": "Network access error", "APPLICATION_MOBILE_MESSAGE_ERROR_SERVER_RESPONSE": "Invalid server response", "APPLICATION_MOBILE_MESSAGE_ERROR_TRANSACTION": "Transaction error", "APPLICATION_MOBILE_MESSAGE_ERROR_VALID_EMAIL_REQUIRED": "Valid email address required", "APPLICATION_MOBILE_MESSAGE_ERROR_VALIDATION_RECONCILIATION": "An error occurred while retrieving data about your pets.", "APPLICATION_MOBILE_MESSAGE_ERROR_WRONG_LOGIN": "Wrong username or password.  If you previously logged in via Facebook, please use that login", "APPLICATION_MOBILE_MESSAGE_ERROR_PASSWORD_NOT_MATCHED": "Email or password is incorrect.", "APPLICATION_MOBILE_MESSAGE_ERROR_USER_NOT_FOUND": "User not found.", "APPLICATION_MOBILE_MESSAGE_ERROR_EMAIL_NOT_VERIFIED": "Email not verified.", "APPLICATION_MOBILE_MESSAGE_FUNPIX_DELETE_ALL": "Are you sure you want to delete all items ? ", "APPLICATION_MOBILE_MESSAGE_FUNPIX_EDITOR": "FunPix editor", "APPLICATION_MOBILE_MESSAGE_FUNPIX_PHOTO_SAVED": "Picture successfully saved", "APPLICATION_MOBILE_MESSAGE_FUNPIX_SAVE_PHOTO": "You are about to save this picture in your pet gallery", "APPLICATION_MOBILE_MESSAGE_INSURANCE_ALREADY_IN_CALENDAR": "This element is already in your calendar", "APPLICATION_MOBILE_MESSAGE_INSURANCE_ERROR_END_BEFORE_START": "Starting date must be before ending date", "APPLICATION_MOBILE_MESSAGE_INSURANCE_ERROR_PRESENCE_END_DATE": "Please select an end date for the contract.", "APPLICATION_MOBILE_MESSAGE_INSURANCE_ERROR_PRESENCE_START_DATE": "Please select a start date for the contract.", "APPLICATION_MOBILE_MESSAGE_INVALID_DATE": "Invalid date", "APPLICATION_MOBILE_MESSAGE_INVITATION_PET_PHOTO_PROFILE": "Do you want to add a picture of your pet ?", "APPLICATION_MOBILE_MESSAGE_LABEL_ACTIVITY_LOSS_CREATED": "Loss statement made", "APPLICATION_MOBILE_MESSAGE_LANGUAGE_UPDATE": "Application language update", "APPLICATION_MOBILE_MESSAGE_LOST_ALERT_ERROR_NO_CONTACTS": "Please enter at least one contact", "APPLICATION_MOBILE_MESSAGE_LOST_ALERT_ERROR_PRESENCE_MESSAGE": "Please provide some information about your pet and where you lost it.", "APPLICATION_MOBILE_MESSAGE_MUST_PURCHASE_ITEM": "You must purchase this item.", "APPLICATION_MOBILE_MESSAGE_MYSQL_TRANSACTION_ERROR": "An error occured during the execution of this command", "APPLICATION_MOBILE_MESSAGE_NETWORK_REQUIRED": "You're offline! Check your internet connection.", "APPLICATION_MOBILE_MESSAGE_NEW_PASSWORD_SEND": "A provisional password has been sent", "APPLICATION_MOBILE_MESSAGE_NOT_AVAILABLE_YET": "This feature is not available yet.", "APPLICATION_MOBILE_MESSAGE_NOTE_ERROR_BEFORE_NOW": "Must be an anterior date", "APPLICATION_MOBILE_MESSAGE_NOTE_ERROR_PRESENCE_GAME_DURATION": "Please enter the game duration", "APPLICATION_MOBILE_MESSAGE_NOTE_SENT": "Your note has been sent !", "APPLICATION_MOBILE_MESSAGE_ORDER_CONFIRMATION": "Order successfully sent.", "APPLICATION_MOBILE_MESSAGE_ORDER_FORM_IMAGE_REQUIRED": "Please add an image to your order.", "APPLICATION_MOBILE_MESSAGE_ORDER_FORM_MESSAGE_REQUIRED": "Please leave a message with your order.", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_BIRTHDATE_BEFORE_NOW": "The birthday can't be in the future", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_ADOPTION_DATE_BEFORE_NOW": "The date can't be in the future", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_ADOPTION_DATE_BEFORE_BIRTH": "The date can't be older than birthday", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_DATE_BEFORE_NOW": "The date can't be in the future", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_BREED": "Please enter a breed for your dog.", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_BIRTH_DATE": "Please enter a birthdate or age for your dog.", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_ADOPTION_DATE": "Please select a date.", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_ADOPTED_CHARITY": "Please select an animal charity.", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_FOOD_NAME": "Please enter the name of the food given", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_FOOD_WEIGHT": "Please enter the amount of food given", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_GENDER": "Please choose the gender of your dog.", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_NAME": "Please enter a name for your dog.", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_SPECIES": "Please enter a species for your dog.", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_WALK_DISTANCE": "Please enter the distance covered during the walk", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_WALK_DURATION": "Please enter a walk duration", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_WEIGHT": "Please enter a weight", "APPLICATION_MOBILE_MESSAGE_PET_PROFILE_CHANGED": "The pet profile picture has been changed successfully", "APPLICATION_MOBILE_MESSAGE_PET_REQUIRED": "You need at least one animal registred to perform this request", "APPLICATION_MOBILE_MESSAGE_PETCARE_SEARCH_ERROR_PRESENCE_BREED": "Please select a breed", "APPLICATION_MOBILE_MESSAGE_PETSMILES_GAIN": "Bravo ! You just earned PetSmiles™.", "APPLICATION_MOBILE_MESSAGE_PETSMILES_NEW_LEVEL": "Congratulations ! Your new level is", "APPLICATION_MOBILE_MESSAGE_PETSMILES_NO_PETSMILES": "You can earn some PetSmiles™ by using the app regularly. Find more information below.", "APPLICATION_MOBILE_MESSAGE_PLEASE_WAIT": "Please wait", "APPLICATION_MOBILE_MESSAGE_REMINDER_END_ERROR_AFTER_NOW": "End recurrence must be in the future", "APPLICATION_MOBILE_MESSAGE_REMINDER_ERROR_AFTER_NOW": "The reminder must be in the future", "APPLICATION_MOBILE_MESSAGE_REMINDER_ERROR_RECURRENCE_VALUE_UNDEFINED": "Recurrence value must be defined", "APPLICATION_MOBILE_MESSAGE_REQUEST_APPOINTMENT_OK": "Your appointment request has been successfully submitted", "APPLICATION_MOBILE_MESSAGE_RESQUEST_APPOINTMENT_OK": "Your appointment request has been sent successfully", "APPLICATION_MOBILE_MESSAGE_SERVICE_CLINIC_UNKNOWN": "Unknown clinic", "APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_ADDRESS": "Please enter an address.", "APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_CATEGORY": "Please select a category.", "APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_CITY": "Please enter a city.", "APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_COUNTRY": "Please select a country.", "APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_DESCRIPTION": "Please enter a description", "APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_MAIL": "Please enter a valid email.", "APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_NAME": "Please enter a name.", "APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_PHONE": "Please enter a valid phone number.", "APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_ZIPCODE": "Please enter a valid zipcode.", "APPLICATION_MOBILE_MESSAGE_SERVICE_NOTE_PLACEHOLDER": "Enter your note here", "APPLICATION_MOBILE_MESSAGE_SERVICE_SUCCESSFULLY_ADDED": "Service/Clinic successfully added", "APPLICATION_MOBILE_MESSAGE_SUCESS_ACTIVITY_LOSS_CREATED": "A flyer has been created and is available at: <a href=\"#\" onclick=\"window.open('{0}','_system','location=no');\"> {1} </a> Do you want to share it now?", "APPLICATION_MOBILE_MESSAGE_TITLE_CONGRATS_PACK_PURCHASED": "Congratulations !", "APPLICATION_MOBILE_MESSAGE_TMP01": "Only for demonstration purpose", "APPLICATION_MOBILE_MESSAGE_UPDATE_USER_OK": "Update successful", "APPLICATION_MOBILE_MESSAGE_UPDATING": "Updating", "APPLICATION_MOBILE_MESSAGE_UPDATING_FAILED": "Error during application update", "APPLICATION_MOBILE_MESSAGE_UPDATING_USER": "Updating user information", "APPLICATION_MOBILE_MESSAGE_USER_CONFIRM_LOGOUT": "Are you sure you want to log out ?", "APPLICATION_MOBILE_MESSAGE_USER_EMAIL_ADDRESS_CONFIRM_CHANGE": "Do you really want to change this email address<br>\"{0}\"<br>by this one :<br>\"{1}\" ?", "APPLICATION_MOBILE_MESSAGE_USER_FORM_CGU_ERROR": "Please accept the terms and conditions", "APPLICATION_MOBILE_MESSAGE_USER_FORM_CLINIC_CODE_ERROR": "The clinic code contains some invalid characters.", "APPLICATION_MOBILE_MESSAGE_USER_FORM_EMAIL_ADDRESS_ERROR": "Your email address contains some invalid characters", "APPLICATION_MOBILE_MESSAGE_USER_FORM_EMAIL_ADDRESS_REQUIRED": "Your email address is required", "APPLICATION_MOBILE_MESSAGE_USER_FORM_EMAIL_REQUIRED": "Your email address is required", "APPLICATION_MOBILE_MESSAGE_USER_FORM_EMAIL_WRONG_FORMAT": "Some invalid characters are present in your email address", "APPLICATION_MOBILE_MESSAGE_USER_FORM_FIRSTNAME_ERROR": "Your first name contains some invalid characters.", "APPLICATION_MOBILE_MESSAGE_USER_FORM_FIRSTNAME_REQUIRED": "Your first name is required", "APPLICATION_MOBILE_MESSAGE_USER_FORM_LASTNAME_ERROR": "Your last name contains some invalid characters.", "APPLICATION_MOBILE_MESSAGE_USER_FORM_LASTNAME_REQUIRED": "Your last name is required", "APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_CHANGE_ERROR": "An error occured during the password update.", "APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MIN_LENGTH": "Your password must be at least 6 characters long", "APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MATCH": "Password does not match.", "APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_REQUEST": "Please enter your new password", "APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_REQUIRED": "Your password is required", "APPLICATION_MOBILE_MESSAGE_USER_FORM_PHONE_NUMBER_ERROR": "Your phone number contains some invalid characters.", "APPLICATION_MOBILE_MESSAGE_USER_FORM_PHONE_NUMBER_REQUIRED": "Phone number required", "APPLICATION_MOBILE_MESSAGE_USER_PASSWORD_LOST_REQUEST": "Please enter the email address associated with your account :", "APPLICATION_MOBILE_MESSAGE_USER_PASSWORD_NEW_REQUEST": "In order to complete your password reset, please enter your new password :", "APPLICATION_MOBILE_MESSAGE_VALIDATION_RECONCILIATION_OK": "The data collected from your vet has been imported successfully into your myBuddy pet app® account! The app must restart.", "APPLICATION_MOBILE_MESSAGE_VALIDATION_RECONCILIATION_WAIT_FOR_DATA": "The data of your pets will be imported in a few minutes", "APPLICATION_MOBILE_MESSAGING_ADD_DISCUSSION": "Add a discussion", "APPLICATION_MOBILE_MESSAGING_SERVICE_DOESNT_ACCEPT_MESSAGE": "This clinic no longer accepts messages\r\n", "APPLICATION_MOBILE_MSG_ALERT_AFTER_ASYNC_RECONCILIATION_WITH_RESTART": "Congratulations, all your data have been imported into myBuddy pet app®! A restart is necessary for this data to be visible in the application.", "APPLICATION_MOBILE_MSG_ALERT_AFTER_ASYNC_RECONCILIATION_WITHOUT_RESTART": "Congratulations, all your data have been imported into myBuddy pet app®!", "APPLICATION_MOBILE_MSG_ALERT_MULTIPLE_REC_ERROR_NO_SELECTION": "Please select an account.", "APPLICATION_MOBILE_NOTIFICATION_APPOINTMENT": "Your appointment is [STATUS].", "APPLICATION_MOBILE_NOTIFICATION_APPOINTMENT_CONFIRMED": "Your appointment request has been confirmed", "APPLICATION_MOBILE_OR": "OR", "APPLICATION_MOBILE_REGISTER_TITLE": "Create Your Account", "APPLICATION_MOBILE_REGISTER_TITLE_2": "Create Account", "APPLICATION_MOBILE_SECTION_READ_ADVICE_TITLE": "ADVICE & CARE", "APPLICATION_MOBILE_SECTION_READ_NEWS_TITLE": "NEWS", "APPLICATION_MOBILE_SECTION_READ_TITLE": "Read", "APPLICATION_MOBILE_SETTING_READ_TITLE": "Read Articles", "APPLICATION_MOBILE_SENTENCE_LABEL_PET_GENDER": "Select your dog’s gender", "APPLICATION_MOBILE_SENTENCE_LABEL_PET_NAME": "What's your dog name?", "APPLICATION_MOBILE_SENTENCE_LABEL_DOG_NAME": "Your dog’s name", "APPLICATION_MOBILE_SENTENCE_LABEL_PET_SPECIES": "What's its species?", "APPLICATION_MOBILE_SENTENCE_LABEL_SPECIES": "Select breed", "APPLICATION_MOBILE_SENTENCE_LABEL_BREED": "Select your dog’s breed", "APPLICATION_MOBILE_SENTENCE_LABEL_BIRTH_DATE": "Select your dog's birthday", "APPLICATION_MOBILE_SENTENCE_LABEL_ADOPTION_DATE": "Select the date", "APPLICATION_MOBILE_SENTENCE_LABEL_ADOPTED_CHARITY": "Select animal charity", "APPLICATION_MOBILE_SENTENCE_LABEL_UNKNOWN_BIRTHDAY": "I don't know my dog's birthday", "APPLICATION_MOBILE_SENTENCE_LABEL_ADOPTED_FROM_SELECTED_CHARITY": "Adopted from my selected animal charity", "APPLICATION_MOBILE_SENTENCE_LABEL_RAINBOW": "Passed Over the Rainbow Bridge?", "APPLICATION_MOBILE_SENTENCE_LABEL_SECONDARY_BREED": "Select secondary breed", "APPLICATION_MOBILE_STORE_CUSTOM_STORE": "Use the clinic webstore", "APPLICATION_MOBILE_STORE_OFFICIAL_STORE": "Make a request in myBuddy pet app®", "APPLICATION_MOBILE_SUBTITLE_BADGE_NEWS_READ_PROFESSOR": "Read 100 news articles", "APPLICATION_MOBILE_SUBTITLE_BADGE_NEWS_READ_PROFESSOR_LOCKED": "You must obtain the Scholar badge to unlock this one", "APPLICATION_MOBILE_SUBTITLE_BADGE_NEWS_READ_SCHOLAR": "Read 25 news articles", "APPLICATION_MOBILE_SUBTITLE_BADGE_NEWS_READ_SCHOLAR_LOCKED": "Read 25 news articles", "APPLICATION_MOBILE_SUBTITLE_BADGE_PET_CREATE_FAMILY": "10 Pets on app", "APPLICATION_MOBILE_SUBTITLE_BADGE_PET_CREATE_FAMILY_LOCKED": "You must obtain the You & Me badge to unlock this one", "APPLICATION_MOBILE_SUBTITLE_BADGE_PET_CREATE_YOU_AND_ME": "First Pet on app", "APPLICATION_MOBILE_SUBTITLE_BADGE_PET_CREATE_YOU_AND_ME_LOCKED": "First Pet on app", "APPLICATION_MOBILE_SUBTITLE_BADGE_SERVICE_CREATE_VETERINARY": "Create or connect to a Clinic", "APPLICATION_MOBILE_SUBTITLE_BADGE_SERVICE_CREATE_VETERINARY_LOCKED": "Create or connect to a Clinic", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_COPPER": "Take 10 walks", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_COPPER_LOCKED": "You must obtain the Good Start badge to unlock this one", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_DIAMOND": "Take 250 walks", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_DIAMOND_LOCKED": "You must obtain the Platinium Hiker badge to unlock this one", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_GOLD": "Take 50 walks", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_GOLD_LOCKED": "You must obtain the Silver Hiker badge to unlock this one", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_GOOD_START": "Take your first ride", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_GOOD_START_LOCKED": "Take your first ride", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_PLATINIUM": "Take 100 walks", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_PLATINIUM_LOCKED": "You must obtain the Gold Hiker badge to unlock this one", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_SILVER": "Take 25 walks", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_SILVER_LOCKED": "You must obtain the Copper Hiker badge to unlock this one", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_STELLAR": "Take 500 walks", "APPLICATION_MOBILE_SUBTITLE_BADGE_WORKOUT_CREATE_STELLAR_LOCKED": "You must obtain the Diamond Hiker badge to unlock this one", "APPLICATION_MOBILE_TEXT_ABOUT_LEGAL_NOTICE": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut consequat id orci ut viverra. Integer eget neque at nulla semper ultricies in ut sapien. In est elit, cursus et tempus ut, pulvinar fringilla nulla. Vestibulum suscipit mauris non leo vulputate, quis scelerisque tortor sollicitudin. Pellentesque lectus tortor, dictum sit amet quam a, mollis varius arcu. In finibus dictum dolor ut molestie. In aliquam eros tellus, et eleifend velit tincidunt ac. Maecenas a scelerisque eros.&lt;br&gt;&lt;br&gt;Nulla arcu felis, rutrum eu tellus at, sollicitudin venenatis lacus. Mauris vel lectus elementum, facilisis urna id, consequat lacus. Sed hendrerit sed enim id pharetra. Sed faucibus et diam sed malesuada. Curabitur vitae risus non enim interdum semper ut at neque. Donec auctor odio et mauris accumsan semper. Suspendisse sodales at magna vitae imperdiet. Quisque lacinia enim arcu, suscipit ultricies nisi commodo et. Aliquam erat volutpat. Donec at iaculis magna, ultrices mattis purus.</p>", "APPLICATION_MOBILE_TEXT_ABOUT_LEGALS": "<p><span style=\"font-size: x-large;\"><span class=\"roboto-bold-font\">LEGAL NOTICE</span></span></p>\r\n<p><span style=\"font-size: large;\"><span class=\"roboto-bold-font\">GENERAL TERMS AND CONDITIONS OF SALE AND SERVICE PROVISION</span></span><br />Definitions:</p>\r\n<p>&bull; Customer: means any non-merchant physical person aged at least 18 years old and making orders for products for private use and having an account on the MEDI-PRODUCTIONS website or application.</p>\r\n<p>&bull; Order: any commitment by a Customer to purchase Products and/or Services.</p>\r\n<p>&bull; Product(s): means the Products marketed by MEDI-PRODUCTIONS on the myBuddy pet app&reg;&trade; Websites and or the myBuddy pet app&reg;&trade; Mobile Application.</p>\r\n<p>&bull; Service(s): means the Services offered by MEDI-PRODUCTIONS on the myBuddy pet app&reg;&trade; Websites and or the myBuddy pet app&reg;&trade; Mobile Application.</p>\r\n<p>&bull; User name: means the e-mail address required to identify a Customer on the myBuddy pet app&reg;&trade; Websites and on the myBuddy pet app&reg;&trade; Mobile Application in order to access the Customer&rsquo;s account.</p>\r\n<p>&bull; Password: means the succession of characters that a Customer must keep secret and which, along with his/her User name, allows the Customer to access his/her account.</p>\r\n<p>&bull; myBuddy pet app&reg;&trade; Websites: means the whole website and the website adapted to the mobile device (such as smartphones and tablets) formats of MEDI-PRODUCTIONS.</p>\r\n<p>&bull; myBuddy pet app&reg;&trade; Mobile Application: means the MEDI-PRODUCTIONS program, which is downloadable and executable from a mobile device operating system (such as a smartphone or tablet).</p>\r\n<p>&bull; Intellectual Property Rights: means all intellectual property rights (including, but not limited to, trademarks, designs and models, patents, copyrights) with respect to the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application.</p>\r\n<p><span class=\"roboto-bold-font\">Article 1. Purpose of the general terms and conditions of sale and service provision</span></p>\r\n<p>These general terms and conditions of sale and service provision govern the contractual relationship between MEDI-PRODUCTIONS and its Customers for any purchase of Products or Services made on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application.</p>\r\n<p>By using the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application to place an Order, Customer accepts all of the following terms and conditions, and agrees to comply fully with all such terms and conditions.</p>\r\n<p><span class=\"roboto-bold-font\">Article 2. Changes to the general terms and conditions of sale and service provision</span></p>\r\n<p>MEDI-PRODUCTIONS reserves the right to modify or amend these general terms and conditions of sale and service provision at any time. Any such modification or amendment will be effective for all Customers immediately upon publication and shall apply to all Orders submitted after publication.</p>\r\n<p>If Customer does not accept the amended terms, Customer shall close Customer&rsquo;s account and cease using the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application, without any refund or consideration due from MEDI-PRODUCTIONS to Customer.</p>\r\n<p><span class=\"roboto-bold-font\">Article 3. Products</span></p>\r\n<p>The Products covered by these general terms and conditions of sale and service provision are data sheets made and marketed by MEDI-PRODUCTIONS and which are in the form of digital files accessible, after purchase, on the Customer's account.</p>\r\n<p>The Customer indicates the essential characteristics of the Products and their prices on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application before purchase. All sales of Products are final.</p>\r\n<p><span class=\"roboto-bold-font\">Article 4. Service</span></p>\r\n<p>The Service covered by these general terms and conditions of sale and service provision is the provision by MEDI-PRODUCTIONS of a helpline via an \"SOS myBuddy pet app&reg;&trade;\" call center, which allows Customers to make emergency calls 24/7 to on-call veterinarians.</p>\r\n<p>The Service offered by MEDI-PRODUCTIONS consists of creating a direct link with this helpline via a tab made available to the Customer, allowing him/her to initiate an emergency call. The advice given during these telephone conversations is provided by third parties and not by MEDI-PRODUCTIONS itself. MEDI-PRODUCTIONS makes no representation that any service provided hereunder will be available at any given time and the user agrees to indemnify and hold MEDI-PRODUCTIONS harmless in the event of any liability accruing as a result of the inability to be able to access any service or resulting from any assistance provided by any third party provider of information herein.</p>\r\n<p>Essential characteristics of the Service and its price are indicated on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application before purchase by the Customer.</p>\r\n<p><span class=\"roboto-bold-font\">Article. 5. Order process and validation</span></p>\r\n<p>To place an Order on the myBuddy pet app&reg;&trade; Websites and/or on the myBuddy pet app&reg;&trade; Mobile Application:</p>\r\n<p>‐ The Customer must go to the myBuddy pet app&reg;&trade; Websites and/or the myBuddy pet app&reg;&trade; Mobile Application previously downloaded from the Apple Store or Google Play and accessible through the following links:</p>\r\n<p>https://itunes.apple.com/gb/app/my-buddy-pet-app/id1083419601 and<br />https://play.google.com/store/apps/details?id=com.mediproductions.mybuddy</p>\r\n<p>‐ The Customer must log in by creating an account through a form to be completed with the following details: the Customer's last name, first name, email address, mobile and/or home phone number, marital status, information about the Customer's pet, the Customer's User name and Password; or, if the account is already created, the Customer must enter his/her User name and Password in the spaces provided for this purpose;</p>\r\n<p>‐ Prior to placing any Order, the Customer has the option to purchase virtual currency called \"myBuddy pet app&reg; Credits\" in any amount he/she wishes through the Apple Store or Google Play platform, accessible via a link provided on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application. In this case, the \"myBuddy pet app&reg; Credits\" virtual currency purchased by the Customer will appear in the Customer's \"myBuddy pet app&reg; Credits\" account;</p>\r\n<p>‐ The Customer must go to the \"Order\" tab and select the Product(s) and/or the Service(s) desired, and add them to the shopping cart;</p>\r\n<p>‐ Once the Product(s) and/or Service(s) are selected, the Customer must go to the Order summary where he/she will be able to modify it with a simple click;</p>\r\n<p>‐ Once the shopping cart is validated, the Customer is directed to the Apple Store or Google Play platform to purchase \"myBuddy pet app&reg; Credits\" virtual currency in the event that the Customer does not yet have such virtual currency in its \"myBuddy pet app&reg; Credits\" account. This virtual currency must be purchased by the Customer through the Apple Store or Google Play, namely using a credit card depending on the amount necessary to complete the purchase of the Product(s) and/or Service(s) desired on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application;</p>\r\n<p>‐ Once the virtual currency has been purchased, or if the Customer's \"myBuddy pet app&reg; Credits\" account has sufficient funds, the Customer can complete the Order of the Product(s) and/or Service(s) selected:</p>\r\n<p> By making the payment for the Product(s) and/or Service(s) selected using the \"myBuddy pet app&reg; Credits\" virtual currency;<br /> By accepting these general terms and conditions of sale and service provision by checking a box for this purpose, located next to a hyperlink which redirects to said general terms and conditions of sale and service provision;</p>\r\n", "APPLICATION_MOBILE_TEXT_ABOUT_LEGALS_MYBUDDY": "<p><span style=\"font-size: x-large;\"><span class=\"roboto-bold-font\">LEGAL NOTICE</span></span></p>\r\n<p><span style=\"font-size: large;\"><span class=\"roboto-bold-font\">GENERAL TERMS AND CONDITIONS OF SALE AND SERVICE PROVISION</span></span><br />Definitions:</p>\r\n<p>&bull; Customer: means any non-merchant physical person aged at least 18 years old and making orders for products for private use and having an account on the MEDI-PRODUCTIONS website or application.</p>\r\n<p>&bull; Order: any commitment by a Customer to purchase Products and/or Services.</p>\r\n<p>&bull; Product(s): means the Products marketed by MEDI-PRODUCTIONS on the myBuddy pet app&reg;&trade; Websites and or the myBuddy pet app&reg;&trade; Mobile Application.</p>\r\n<p>&bull; Service(s): means the Services offered by MEDI-PRODUCTIONS on the myBuddy pet app&reg;&trade; Websites and or the myBuddy pet app&reg;&trade; Mobile Application.</p>\r\n<p>&bull; User name: means the e-mail address required to identify a Customer on the myBuddy pet app&reg;&trade; Websites and on the myBuddy pet app&reg;&trade; Mobile Application in order to access the Customer&rsquo;s account.</p>\r\n<p>&bull; Password: means the succession of characters that a Customer must keep secret and which, along with his/her User name, allows the Customer to access his/her account.</p>\r\n<p>&bull; myBuddy pet app&reg;&trade; Websites: means the whole website and the website adapted to the mobile device (such as smartphones and tablets) formats of MEDI-PRODUCTIONS.</p>\r\n<p>&bull; myBuddy pet app&reg;&trade; Mobile Application: means the MEDI-PRODUCTIONS program, which is downloadable and executable from a mobile device operating system (such as a smartphone or tablet).</p>\r\n<p>&bull; Intellectual Property Rights: means all intellectual property rights (including, but not limited to, trademarks, designs and models, patents, copyrights) with respect to the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application.</p>\r\n<p><span class=\"roboto-bold-font\">Article 1. Purpose of the general terms and conditions of sale and service provision</span></p>\r\n<p>These general terms and conditions of sale and service provision govern the contractual relationship between MEDI-PRODUCTIONS and its Customers for any purchase of Products or Services made on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application.</p>\r\n<p>By using the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application to place an Order, Customer accepts all of the following terms and conditions, and agrees to comply fully with all such terms and conditions.</p>\r\n<p><span class=\"roboto-bold-font\">Article 2. Changes to the general terms and conditions of sale and service provision</span></p>\r\n<p>MEDI-PRODUCTIONS reserves the right to modify or amend these general terms and conditions of sale and service provision at any time. Any such modification or amendment will be effective for all Customers immediately upon publication and shall apply to all Orders submitted after publication.</p>\r\n<p>If Customer does not accept the amended terms, Customer shall close Customer&rsquo;s account and cease using the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application, without any refund or consideration due from MEDI-PRODUCTIONS to Customer.</p>\r\n<p><span class=\"roboto-bold-font\">Article 3. Products</span></p>\r\n<p>The Products covered by these general terms and conditions of sale and service provision are data sheets made and marketed by MEDI-PRODUCTIONS and which are in the form of digital files accessible, after purchase, on the Customer's account.</p>\r\n<p>The Customer indicates the essential characteristics of the Products and their prices on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application before purchase. All sales of Products are final.</p>\r\n<p><span class=\"roboto-bold-font\">Article 4. Service</span></p>\r\n<p>The Service covered by these general terms and conditions of sale and service provision is the provision by MEDI-PRODUCTIONS of a helpline via an \"SOS myBuddy pet app&reg;&trade;\" call center, which allows Customers to make emergency calls 24/7 to on-call veterinarians.</p>\r\n<p>The Service offered by MEDI-PRODUCTIONS consists of creating a direct link with this helpline via a tab made available to the Customer, allowing him/her to initiate an emergency call. The advice given during these telephone conversations is provided by third parties and not by MEDI-PRODUCTIONS itself. MEDI-PRODUCTIONS makes no representation that any service provided hereunder will be available at any given time and the user agrees to indemnify and hold MEDI-PRODUCTIONS harmless in the event of any liability accruing as a result of the inability to be able to access any service or resulting from any assistance provided by any third party provider of information herein.</p>\r\n<p>Essential characteristics of the Service and its price are indicated on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application before purchase by the Customer.</p>\r\n<p><span class=\"roboto-bold-font\">Article. 5. Order process and validation</span></p>\r\n<p>To place an Order on the myBuddy pet app&reg;&trade; Websites and/or on the myBuddy pet app&reg;&trade; Mobile Application:</p>\r\n<p>‐ The Customer must go to the myBuddy pet app&reg;&trade; Websites and/or the myBuddy pet app&reg;&trade; Mobile Application previously downloaded from the Apple Store or Google Play and accessible through the following links:</p>\r\n<p>https://itunes.apple.com/gb/app/my-buddy-pet-app/id1083419601 and<br />https://play.google.com/store/apps/details?id=com.mediproductions.mybuddy</p>\r\n<p>‐ The Customer must log in by creating an account through a form to be completed with the following details: the Customer's last name, first name, email address, mobile and/or home phone number, marital status, information about the Customer's pet, the Customer's User name and Password; or, if the account is already created, the Customer must enter his/her User name and Password in the spaces provided for this purpose;</p>\r\n<p>‐ Prior to placing any Order, the Customer has the option to purchase virtual currency called \"myBuddy pet app&reg; Credits\" in any amount he/she wishes through the Apple Store or Google Play platform, accessible via a link provided on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application. In this case, the \"myBuddy pet app&reg; Credits\" virtual currency purchased by the Customer will appear in the Customer's \"myBuddy pet app&reg; Credits\" account;</p>\r\n<p>‐ The Customer must go to the \"Order\" tab and select the Product(s) and/or the Service(s) desired, and add them to the shopping cart;</p>\r\n<p>‐ Once the Product(s) and/or Service(s) are selected, the Customer must go to the Order summary where he/she will be able to modify it with a simple click;</p>\r\n<p>‐ Once the shopping cart is validated, the Customer is directed to the Apple Store or Google Play platform to purchase \"myBuddy pet app&reg; Credits\" virtual currency in the event that the Customer does not yet have such virtual currency in its \"myBuddy pet app&reg; Credits\" account. This virtual currency must be purchased by the Customer through the Apple Store or Google Play, namely using a credit card depending on the amount necessary to complete the purchase of the Product(s) and/or Service(s) desired on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application;</p>\r\n<p>‐ Once the virtual currency has been purchased, or if the Customer's \"myBuddy pet app&reg; Credits\" account has sufficient funds, the Customer can complete the Order of the Product(s) and/or Service(s) selected:</p>\r\n<p> By making the payment for the Product(s) and/or Service(s) selected using the \"myBuddy pet app&reg; Credits\" virtual currency;<br /> By accepting these general terms and conditions of sale and service provision by checking a box for this purpose, located next to a hyperlink which redirects to said general terms and conditions of sale and service provision;</p>\"", "APPLICATION_MOBILE_TEXT_ABOUT_LEGALS_WOOFTRAX": "<p><span style=\"font-size: x-large;\"><span class=\"roboto-bold-font\">LEGAL NOTICE</span></span></p>\r\n<p><span style=\"font-size: large;\"><span class=\"roboto-bold-font\">GENERAL TERMS AND CONDITIONS OF SALE AND SERVICE PROVISION</span></span><br />Definitions:</p>\r\n<p>&bull; Customer: means any non-merchant physical person aged at least 18 years old and making orders for products for private use and having an account on the MEDI-PRODUCTIONS website or application.</p>\r\n<p>&bull; Order: any commitment by a Customer to purchase Products and/or Services.</p>\r\n<p>&bull; Product(s): means the Products marketed by MEDI-PRODUCTIONS on the myBuddy pet app&reg;&trade; Websites and or the myBuddy pet app&reg;&trade; Mobile Application.</p>\r\n<p>&bull; Service(s): means the Services offered by MEDI-PRODUCTIONS on the myBuddy pet app&reg;&trade; Websites and or the myBuddy pet app&reg;&trade; Mobile Application.</p>\r\n<p>&bull; User name: means the e-mail address required to identify a Customer on the myBuddy pet app&reg;&trade; Websites and on the myBuddy pet app&reg;&trade; Mobile Application in order to access the Customer&rsquo;s account.</p>\r\n<p>&bull; Password: means the succession of characters that a Customer must keep secret and which, along with his/her User name, allows the Customer to access his/her account.</p>\r\n<p>&bull; myBuddy pet app&reg;&trade; Websites: means the whole website and the website adapted to the mobile device (such as smartphones and tablets) formats of MEDI-PRODUCTIONS.</p>\r\n<p>&bull; myBuddy pet app&reg;&trade; Mobile Application: means the MEDI-PRODUCTIONS program, which is downloadable and executable from a mobile device operating system (such as a smartphone or tablet).</p>\r\n<p>&bull; Intellectual Property Rights: means all intellectual property rights (including, but not limited to, trademarks, designs and models, patents, copyrights) with respect to the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application.</p>\r\n<p><span class=\"roboto-bold-font\">Article 1. Purpose of the general terms and conditions of sale and service provision</span></p>\r\n<p>These general terms and conditions of sale and service provision govern the contractual relationship between MEDI-PRODUCTIONS and its Customers for any purchase of Products or Services made on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application.</p>\r\n<p>By using the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application to place an Order, Customer accepts all of the following terms and conditions, and agrees to comply fully with all such terms and conditions.</p>\r\n<p><span class=\"roboto-bold-font\">Article 2. Changes to the general terms and conditions of sale and service provision</span></p>\r\n<p>MEDI-PRODUCTIONS reserves the right to modify or amend these general terms and conditions of sale and service provision at any time. Any such modification or amendment will be effective for all Customers immediately upon publication and shall apply to all Orders submitted after publication.</p>\r\n<p>If Customer does not accept the amended terms, Customer shall close Customer&rsquo;s account and cease using the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application, without any refund or consideration due from MEDI-PRODUCTIONS to Customer.</p>\r\n<p><span class=\"roboto-bold-font\">Article 3. Products</span></p>\r\n<p>The Products covered by these general terms and conditions of sale and service provision are data sheets made and marketed by MEDI-PRODUCTIONS and which are in the form of digital files accessible, after purchase, on the Customer's account.</p>\r\n<p>The Customer indicates the essential characteristics of the Products and their prices on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application before purchase. All sales of Products are final.</p>\r\n<p><span class=\"roboto-bold-font\">Article 4. Service</span></p>\r\n<p>The Service covered by these general terms and conditions of sale and service provision is the provision by MEDI-PRODUCTIONS of a helpline via an \"SOS myBuddy pet app&reg;&trade;\" call center, which allows Customers to make emergency calls 24/7 to on-call veterinarians.</p>\r\n<p>The Service offered by MEDI-PRODUCTIONS consists of creating a direct link with this helpline via a tab made available to the Customer, allowing him/her to initiate an emergency call. The advice given during these telephone conversations is provided by third parties and not by MEDI-PRODUCTIONS itself. MEDI-PRODUCTIONS makes no representation that any service provided hereunder will be available at any given time and the user agrees to indemnify and hold MEDI-PRODUCTIONS harmless in the event of any liability accruing as a result of the inability to be able to access any service or resulting from any assistance provided by any third party provider of information herein.</p>\r\n<p>Essential characteristics of the Service and its price are indicated on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application before purchase by the Customer.</p>\r\n<p><span class=\"roboto-bold-font\">Article. 5. Order process and validation</span></p>\r\n<p>To place an Order on the myBuddy pet app&reg;&trade; Websites and/or on the myBuddy pet app&reg;&trade; Mobile Application:</p>\r\n<p>‐ The Customer must go to the myBuddy pet app&reg;&trade; Websites and/or the myBuddy pet app&reg;&trade; Mobile Application previously downloaded from the Apple Store or Google Play and accessible through the following links:</p>\r\n<p>https://itunes.apple.com/gb/app/my-buddy-pet-app/id1083419601 and<br />https://play.google.com/store/apps/details?id=com.mediproductions.mybuddy</p>\r\n<p>‐ The Customer must log in by creating an account through a form to be completed with the following details: the Customer's last name, first name, email address, mobile and/or home phone number, marital status, information about the Customer's pet, the Customer's User name and Password; or, if the account is already created, the Customer must enter his/her User name and Password in the spaces provided for this purpose;</p>\r\n<p>‐ Prior to placing any Order, the Customer has the option to purchase virtual currency called \"myBuddy pet app&reg; Credits\" in any amount he/she wishes through the Apple Store or Google Play platform, accessible via a link provided on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application. In this case, the \"myBuddy pet app&reg; Credits\" virtual currency purchased by the Customer will appear in the Customer's \"myBuddy pet app&reg; Credits\" account;</p>\r\n<p>‐ The Customer must go to the \"Order\" tab and select the Product(s) and/or the Service(s) desired, and add them to the shopping cart;</p>\r\n<p>‐ Once the Product(s) and/or Service(s) are selected, the Customer must go to the Order summary where he/she will be able to modify it with a simple click;</p>\r\n<p>‐ Once the shopping cart is validated, the Customer is directed to the Apple Store or Google Play platform to purchase \"myBuddy pet app&reg; Credits\" virtual currency in the event that the Customer does not yet have such virtual currency in its \"myBuddy pet app&reg; Credits\" account. This virtual currency must be purchased by the Customer through the Apple Store or Google Play, namely using a credit card depending on the amount necessary to complete the purchase of the Product(s) and/or Service(s) desired on the myBuddy pet app&reg;&trade; Websites and the myBuddy pet app&reg;&trade; Mobile Application;</p>\r\n<p>‐ Once the virtual currency has been purchased, or if the Customer's \"myBuddy pet app&reg; Credits\" account has sufficient funds, the Customer can complete the Order of the Product(s) and/or Service(s) selected:</p>\r\n<p> By making the payment for the Product(s) and/or Service(s) selected using the \"myBuddy pet app&reg; Credits\" virtual currency;<br /> By accepting these general terms and conditions of sale and service provision by checking a box for this purpose, located next to a hyperlink which redirects to said general terms and conditions of sale and service provision;</p>", "APPLICATION_MOBILE_TEXT_ACTIVITY_NO_APPOINTMENT": "No active appointments or requests", "APPLICATION_MOBILE_TEXT_ACTIVITY_NO_INSURANCE": "No insurance details", "APPLICATION_MOBILE_TEXT_ACTIVITY_NO_PET_PICTURES": "No picture added yet", "APPLICATION_MOBILE_TEXT_ACTIVITY_NO_REMINDER": "No active reminders", "APPLICATION_MOBILE_TEXT_ACTIVITY_NO_WELLNESS": "No health plan details", "APPLICATION_MOBILE_TEXT_ANNOUNCEMENT_NO_ANNOUNCEMENT": "No news available", "APPLICATION_MOBILE_TEXT_APPOINTMENT_ACCEPTED": "Your appointment is confirmed", "APPLICATION_MOBILE_TEXT_APPOINTMENT_CANCELED": "Your appointment is canceled", "APPLICATION_MOBILE_TEXT_APPOINTMENT_REFUSED": "Unavailable, please contact the practice to arrange an appointment.", "APPLICATION_MOBILE_TEXT_APPOINTMENT_REQUESTED": "Your appointment request is being processed", "APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_ACCEPTED": "Accepted", "APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_CANCELED": "Canceled", "APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_DONE": "Done", "APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_REFUSED": "Rejected", "APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_REQUESTED": "Requested", "APPLICATION_MOBILE_TEXT_BUDDY_CREDITS_BOUGHT_PACk": "Item \"{0}\" purchased", "APPLICATION_MOBILE_TEXT_BUDDYCREDITS": "<p><span style=\"font-size: large;\"><span class=\"roboto-bold-font\">Buddy Credits&trade;</span> represent monetary points needed to obtain certain services and illustrated factsheets available in the app. Users of <span class=\"roboto-bold-font\">myBuddy pet app®&copy;</span> may purchase or receive <span class=\"roboto-bold-font\">Buddy Credits&trade;</span> and exchange them for content available in&nbsp;<span class=\"roboto-bold-font\">my Pet Care. This can be either</span>&nbsp;on home care or breed specific content.&nbsp;</span></p>", "APPLICATION_MOBILE_TEXT_BUDDYCREDITS_SEE_MORE": "Learn more about Buddy Credits™", "APPLICATION_MOBILE_TEXT_BUDDYCREDITS_SUMMARY": "You currently have <span class=\"roboto-bold-font\">{0}</span> Buddy Credits™.", "APPLICATION_MOBILE_TEXT_CHOICE_VET_INDIFFERENT": "First available", "APPLICATION_MOBILE_TEXT_CLINIC_ADD_MANUALLY": "Add clinic by its code", "APPLICATION_MOBILE_TEXT_CLINIC_ENTER_ID": "Enter a clinic identifier", "APPLICATION_MOBILE_TEXT_CLINIC_UNLINK": "Unlink", "APPLICATION_MOBILE_TEXT_ELEMENT_IN_CALENDAR": "This element is already in your device calendar", "APPLICATION_MOBILE_TEXT_EMPTY_DATA": "No data found", "APPLICATION_MOBILE_TEXT_FUNPIX_ACCESSORIES_SPRITE_CATEGORY": "Accessories", "APPLICATION_MOBILE_TEXT_FUNPIX_EYES_SPRITE_CATEGORY": "Eyes", "APPLICATION_MOBILE_TEXT_FUNPIX_HAIR_SPRITE_CATEGORY": "Hair", "APPLICATION_MOBILE_TEXT_FUNPIX_MOUTHS_SPRITE_CATEGORY": "Mouths", "APPLICATION_MOBILE_TEXT_NO_PETCARE_IN_YOUR_LANGUAGE": "No available group in your language", "APPLICATION_MOBILE_TEXT_NOTIFICATION_NO_NOTIFICATION": "No active notifications", "APPLICATION_MOBILE_TEXT_PET_FUN_PICTURES": "FunPix", "APPLICATION_MOBILE_TEXT_PET_NO_PET": "Add your first pet below", "APPLICATION_MOBILE_TEXT_PET_NO_WEIGHT": "-", "APPLICATION_MOBILE_TEXT_PET_PICTURES": "Photo album", "APPLICATION_MOBILE_TEXT_PETSLIFE_INCOMING": "Upcoming", "APPLICATION_MOBILE_TEXT_PETSLIFE_NO_INCOMING": "No coming events", "APPLICATION_MOBILE_TEXT_PETSLIFE_NO_PASSED": "No passed elements", "APPLICATION_MOBILE_TEXT_PETSLIFE_PASSED": "Past", "APPLICATION_MOBILE_TEXT_PETSMILES_SEE_MORE": "Learn more about PetSmiles™", "APPLICATION_MOBILE_TEXT_PETSMILES_SUMMARY": "You currently have <span class=\"roboto-bold-font\">{0}</span> PetSmiles™.<br>Your level is <span class=\"roboto-bold-font\">{1}</span>.", "APPLICATION_MOBILE_TEXT_POINTS": "<p><span class=\"roboto-bold-font\">PetSmiles&trade;</span> are monetary valued \"points\" that <span class=\"roboto-bold-font\">myBuddy pet app&reg;</span> users will receive as they use the App. The more users interact with&nbsp;<span class=\"roboto-bold-font\">myBuddy pet app&reg;</span>, the more <span class=\"roboto-bold-font\">PetSmiles&trade;</span>&nbsp;they can earn.<br /><br />As <span class=\"roboto-bold-font\">PetSmiles&trade;</span> are accumulated, loyalty levels can be achieved: Bronze, Silver, Gold, and Platinum. Earning progressive &nbsp;loyalty level will open new access and benefits to <span class=\"roboto-bold-font\">myBuddy pet app&reg;</span> users proposed by <span class=\"roboto-bold-font\">myBuddy pet app&reg;</span> and its partners.<br /><br /><span class=\"roboto-bold-font\"><span>In the future, it will also be possible for users to redeem their&nbsp;</span>PetSmiles&trade;</span> for products, toys, services, samplesincluding services from participating partners and veterinary practices;<br /><br /><br /></p>", "APPLICATION_MOBILE_TEXT_SERVICE_ADDBYCODE_ERROR": "Adding clinic by code error", "APPLICATION_MOBILE_TEXT_SERVICE_CLINIC_CODE_ALREADY_EXIST": "Clinic already added.", "APPLICATION_MOBILE_TEXT_SERVICE_NO_CLINIC": "Add your first clinic below", "APPLICATION_MOBILE_TEXT_SERVICE_NO_NOTIFICATION": "No notification", "APPLICATION_MOBILE_TEXT_SERVICE_NO_ORDER": "No active orders", "APPLICATION_MOBILE_TEXT_SERVICE_NO_SERVICE": "Add your first service below", "APPLICATION_MOBILE_TEXT_SETTINGS_AUTHORIZATION": "I authorize my vet to send me notifications about my pets.", "APPLICATION_MOBILE_TITLE_ABOUT": "About", "APPLICATION_MOBILE_TITLE_ABOUT_LEGALS": "Legals", "APPLICATION_MOBILE_TITLE_ALERT_AFTER_ASYNC_RECONCILIATION": "Information", "APPLICATION_MOBILE_TITLE_ANNOUNCEMENT": "News", "APPLICATION_MOBILE_TITLE_ANNOUNCEMENTS": "News", "APPLICATION_MOBILE_TITLE_APPOINTMENT": "Appointment", "APPLICATION_MOBILE_TITLE_APPOINTMENT_REQUEST": "Appointment requests", "APPLICATION_MOBILE_TITLE_BADGE_NEWS_READ_PROFESSOR": "Professor", "APPLICATION_MOBILE_TITLE_BADGE_NEWS_READ_SCHOLAR": "Scholar", "APPLICATION_MOBILE_TITLE_BADGE_PET_CREATE_FAMILY": "Family", "APPLICATION_MOBILE_TITLE_BADGE_PET_CREATE_YOU_AND_ME": "You & Me", "APPLICATION_MOBILE_TITLE_BADGE_SERVICE_CREATE_VETERINARY": "Veterinary", "APPLICATION_MOBILE_TITLE_BADGE_WORKOUT_CREATE_COPPER": "Copper Hiker", "APPLICATION_MOBILE_TITLE_BADGE_WORKOUT_CREATE_DIAMOND": "Diamond Hiker", "APPLICATION_MOBILE_TITLE_BADGE_WORKOUT_CREATE_GOLD": "Gold Hiker", "APPLICATION_MOBILE_TITLE_BADGE_WORKOUT_CREATE_GOOD_START": "Good Start", "APPLICATION_MOBILE_TITLE_BADGE_WORKOUT_CREATE_PLATINIUM": "Platinium Hiker", "APPLICATION_MOBILE_TITLE_BADGE_WORKOUT_CREATE_SILVER": "Silver Hiker", "APPLICATION_MOBILE_TITLE_BADGE_WORKOUT_CREATE_STELLAR": "<PERSON><PERSON>", "APPLICATION_MOBILE_TITLE_BUDDYCREDITS": "What are Buddy Credits™ ?", "APPLICATION_MOBILE_TITLE_CHOOSE_PICTURE": "Choose a picture", "APPLICATION_MOBILE_TITLE_EDUCATION": "My Pet Care", "APPLICATION_MOBILE_TITLE_EDUCATION_FREE": "Home care", "APPLICATION_MOBILE_TITLE_EDUCATION_FULL": "<p><span class=\"xbig-text\">All the key information you need to care for your pet</span></p>", "APPLICATION_MOBILE_TITLE_EDUCATION_PAID": "Breed specific information", "APPLICATION_MOBILE_TITLE_HEALTHBOOK2": "Healthbook", "APPLICATION_MOBILE_TITLE_HOSPITALIZATION": "Hospitalization", "APPLICATION_MOBILE_TITLE_LABEL_GIVE_CONSENT": "<span class='bold'>You don't have a personnal id ?</span> to get one, you have to give your consent to your vet to share your pets'data with the application myBuddy pet app®.", "APPLICATION_MOBILE_TITLE_LEGALS": "Terms of Service", "APPLICATION_MOBILE_TITLE_MISC": "Other", "APPLICATION_MOBILE_TITLE_MY_ACCOUNT": "My account", "APPLICATION_MOBILE_TITLE_MY_APPOINTMENTS": "My appointments", "APPLICATION_MOBILE_TITLE_MY_ORDERS": "My orders", "APPLICATION_MOBILE_TITLE_NOTE": "Health Note", "APPLICATION_MOBILE_TITLE_ORDER": "Order", "APPLICATION_MOBILE_TITLE_OVERLAY_FUNPIX": "Select an item", "APPLICATION_MOBILE_TITLE_PET_PROFILE_DETAILS": "Details", "APPLICATION_MOBILE_TITLE_PETCARE_MY_PURCHASES": "My purchases", "APPLICATION_MOBILE_TITLE_PETCARE_SUGGESTIONS": "Recommended", "APPLICATION_MOBILE_TITLE_POINTS": "What are PetSmiles™ ?", "APPLICATION_MOBILE_TITLE_PROFILE_PICTURE": "Profile picture", "APPLICATION_MOBILE_TITLE_RECONCILIATION_PET_LINK": "Reconciliation of my pet", "APPLICATION_MOBILE_TITLE_RECONCILIATION_PIMSPET_LINK": "Pet import", "APPLICATION_MOBILE_TITLE_RECONCILIATION_STATE": "Account reconciliation", "APPLICATION_MOBILE_TITLE_REMINDER": "Reminder", "APPLICATION_MOBILE_TITLE_USER_MANUAL_RECONCILIATION": "Reconcile with vet record", "APPLICATION_MOBILE_TITLE_USER_PASSWORD_EDIT": "Change your password", "APPLICATION_MOBILE_TITLE_USER_PASSWORD_RECOVER": "Password lost ?", "APPLICATION_MOBILE_TITLE_WT_AVATAR_1": "Welcome Avatar", "APPLICATION_MOBILE_TITLE_WT_AVATAR_13": "Sung<PERSON><PERSON>", "APPLICATION_MOBILE_TITLE_WT_AVATAR_4": "Smile Avatar", "APPLICATION_MOBILE_TITLE_WT_AVATAR_5": "Rambo Avatar", "APPLICATION_MOBILE_TITLE_WT_AVATAR_8": "King <PERSON><PERSON>", "APPLICATION_MOBILE_TREATMENT-ACRONYM-1": "C", "APPLICATION_MOBILE_TREATMENT-ACRONYM-10": "Pi", "APPLICATION_MOBILE_TREATMENT-ACRONYM-11": "Bb", "APPLICATION_MOBILE_TREATMENT-ACRONYM-12": "L", "APPLICATION_MOBILE_TREATMENT-ACRONYM-13": "R", "APPLICATION_MOBILE_TREATMENT-ACRONYM-14": "Bab", "APPLICATION_MOBILE_TREATMENT-ACRONYM-15": "<PERSON><PERSON>", "APPLICATION_MOBILE_TREATMENT-ACRONYM-16": "INTERNALS", "APPLICATION_MOBILE_TREATMENT-ACRONYM-17": "INTERNALS", "APPLICATION_MOBILE_TREATMENT-ACRONYM-18": "EXTERNALS", "APPLICATION_MOBILE_TREATMENT-ACRONYM-19": "EXTERNALS", "APPLICATION_MOBILE_TREATMENT-ACRONYM-2": "R", "APPLICATION_MOBILE_TREATMENT-ACRONYM-20": "STERILISATION", "APPLICATION_MOBILE_TREATMENT-ACRONYM-21": "STERILISATION", "APPLICATION_MOBILE_TREATMENT-ACRONYM-3": "P", "APPLICATION_MOBILE_TREATMENT-ACRONYM-4": "L", "APPLICATION_MOBILE_TREATMENT-ACRONYM-5": "R", "APPLICATION_MOBILE_TREATMENT-ACRONYM-6": "FeLV", "APPLICATION_MOBILE_TREATMENT-ACRONYM-7": "D", "APPLICATION_MOBILE_TREATMENT-ACRONYM-8": "H", "APPLICATION_MOBILE_TREATMENT-ACRONYM-9": "P", "APPLICATION_MOBILE_TREATMENT-ACRONYM-DEFAULT0": "", "APPLICATION_MOBILE_TREATMENT-ACRONYM-DEFAULT1": "INTERNALS", "APPLICATION_MOBILE_TREATMENT-ACRONYM-DEFAULT2": "EXTERNALS", "APPLICATION_MOBILE_TREATMENT-ACRONYM-DEFAULT3": "STERILISATION", "APPLICATION_MOBILE_TREATMENT-VALENCE-1": "Calicivirus  (Upper Respiratory)", "APPLICATION_MOBILE_TREATMENT-VALENCE-10": "<PERSON><PERSON>(Parainfluenza)", "APPLICATION_MOBILE_TREATMENT-VALENCE-11": "<PERSON><PERSON>(Bordetella)", "APPLICATION_MOBILE_TREATMENT-VALENCE-12": "Leptospirosis", "APPLICATION_MOBILE_TREATMENT-VALENCE-13": "Rabbies", "APPLICATION_MOBILE_TREATMENT-VALENCE-14": "Babesiosis", "APPLICATION_MOBILE_TREATMENT-VALENCE-15": "<PERSON><PERSON>'s Disease", "APPLICATION_MOBILE_TREATMENT-VALENCE-16": "<PERSON><PERSON><PERSON>", "APPLICATION_MOBILE_TREATMENT-VALENCE-17": "<PERSON><PERSON><PERSON>", "APPLICATION_MOBILE_TREATMENT-VALENCE-18": "Products", "APPLICATION_MOBILE_TREATMENT-VALENCE-19": "Products", "APPLICATION_MOBILE_TREATMENT-VALENCE-2": "Herpes Virus (rhinotracheitis)(Upper Respiratory)", "APPLICATION_MOBILE_TREATMENT-VALENCE-20": "Spay", "APPLICATION_MOBILE_TREATMENT-VALENCE-21": "Spay", "APPLICATION_MOBILE_TREATMENT-VALENCE-3": "Panleucopenia (Feline Distemper)", "APPLICATION_MOBILE_TREATMENT-VALENCE-4": "Leukemia (Leucosis)", "APPLICATION_MOBILE_TREATMENT-VALENCE-5": "Rabbies", "APPLICATION_MOBILE_TREATMENT-VALENCE-6": "FeLV (Leucosis)", "APPLICATION_MOBILE_TREATMENT-VALENCE-7": "Distemper", "APPLICATION_MOBILE_TREATMENT-VALENCE-8": "Hepatitis", "APPLICATION_MOBILE_TREATMENT-VALENCE-9": "Parvovirosis", "APPLICATION_MOBILE_TREATMENT-VALENCE-DEFAULT0": "Vaccine", "APPLICATION_MOBILE_TREATMENT-VALENCE-DEFAULT1": "<PERSON><PERSON><PERSON>", "APPLICATION_MOBILE_TREATMENT-VALENCE-DEFAULT2": "Products", "APPLICATION_MOBILE_TREATMENT-VALENCE-DEFAULT3": "Spay", "APPLICATION_MOBILE_TREATMENTS_STATE_DONE": "Done", "APPLICATION_MOBILE_TREATMENTS_STATE_TODO_NOW": "to do now", "APPLICATION_MOBILE_TREATMENTS_STATE_TODO_NULL": "to do", "APPLICATION_MOBILE_TREATMENTS_STATE_UP_TO_DATE": "up to date", "APPLICATION_MOBILE_TXT_WHY_MANUAL_RECONCILIATION": "An attempt to retrieve data from your clinic was unsuccessful. Please enter the phone number and / or referenced e-mail at your veterinary clinic or ask your veterinary clinic to provide you with your unique identifier that you can enter here.", "APPLICATION_MOBILE_TXT_WHY_MULTIPLE_RECONCILIATION": "Several user accounts were found with the information provided. Please select the one that corresponds to you:", "APPLICATION_MOBILE_WEIGHT_CHART_NO_DATA": "No data available", "APPLICATION_MOBILE_WORD_IN_DURATION": "In ", "APPLICATION_MOBILE_NEW_WELCOME_TITLE": "Welcome", "APPLICATION_MOBILE_NEW_WELCOME_HEY": "Hey", "APPLICATION_MOBILE_NEW_WELCOME_SAVE_PASSWORD": "Save Password", "APPLICATION_MOBILE_NEW_WELCOME_HEADER": "We welcome you to the latest WoofTrax application.", "APPLICATION_MOBILE_NEW_WELCOME_BODY": "Begin your experience by setting a secure password for your existing WoofTrax account.", "APPLICATION_MOBILE_NEW_WELCOME_PASWORD_SAVED": "Password Saved", "APPLICATION_MOBILE_PASSWORD_SAVED_SUCCESS_BODY": "Your password has been saved successfully.", "APPLICATION_MOBILE_CONTINUE": "Continue", "WOOFTRAX_ALERT_CONTACT_PERMISSION_DENIED_TITLE": "To view your contacts on this list, please allow permission for contacts.", "WOOFTRAX_ALERT_PERMISSION_DENIED_OPEN_SETTINGS": "Change app settings", "WOOFTRAX_ALERT_PERMISSION_DENIED_IGNORE": "Ignore", "APPLICATION_MOBILE_ON_BOARDING_01_TITLE": "Support your favorite local animal charity ", "APPLICATION_MOBILE_ON_BOARDING_02_TITLE": "Create your own virtual walking group ", "APPLICATION_MOBILE_ON_BOARDING_03_TITLE": "Set walking goals ", "APPLICATION_MOBILE_ON_BOARDING_01_SUB_TITLE": "just by walking your dog.", "APPLICATION_MOBILE_ON_BOARDING_02_SUB_TITLE": "and invite friends and family.", "APPLICATION_MOBILE_ON_BOARDING_03_SUB_TITLE": "for\nbetter health and fitness for both you and your dog.", "WOOFTRAX_ADD_A_DOG": "Add a Dog", "WOOFTRAX_WALK_WITH_DOG_DESCRIPTION": "Let’s add your first walking companion.", "WOOFTRAX_WALK_WITH_LEXI": "Walk with <PERSON>", "WOOFTRAX_WALK_WITH_LEXI_DESCRIPTION": "Don't have a dog? You can choose to walk with <PERSON> to help animal charities everywhere.", "APPLICATION_MOBILE_SHELTER_CHOOSE_YOUR": "Choose an Animal Charity to Walk For", "WOOFTRAX_SEARCH_SHELTER_LABEL": "Search by Name, State, City, or Zip Code", "WOOFTRAX__BUTTON_INTERNET_RETRY_LABEL": "Retry", "APPLICATION_MOBILE_TITLE_CHALLENGES": "Challenges", "APPLICATION_MOBILE_TITLE_CHALLENGE": "Challenge", "APPLICATION_MOBILE_TITLE_CHALLENGE_RULES": "Challenge Rules", "APPLICATION_MOBILE_TITLE_CHALLENGE_TAB_AVAILABLE": "Available", "APPLICATION_MOBILE_TITLE_CHALLENGE_TAB_JOINED": "Joined", "APPLICATION_MOBILE_TEXT_NO_CHALLENGE": "No Challenge Found", "APPLICATION_MOBILE_TEXT_NO_CHALLENGE_AVAILABLE": "No Challenges Available", "APPLICATION_MOBILE_TEXT_NO_CHALLENGE_JOINED": "No Challenges Joined", "APPLICATION_MOBILE_TEXT_NO_CHALLENGE_JOINED_SUBTITLE": "Touch the Available tab above to see available challenges.", "APPLICATION_MOBILE_TEXT_NO_CHALLENGE_AVAILABLE_SUBTITLE": "You have joined all the available challenges.", "APPLICATION_MOBILE_MESSAGE_CHALLENGE_JOIN": "Challenge Joined Successfully!", "APPLICATION_MOBILE_MESSAGE_CHALLENGE_LEAVE": "Challenge Left", "APPLICATION_MOBILE_TITLE_RESET_PASSWORD": "Reset Password", "APPLICATION_MOBILE_TITLE_FORGOT_PASSWORD": "Forgot Password", "APPLICATION_MOBILE_TITLE_EMAIL_VERIFIED": "<PERSON><PERSON>", "APPLICATION_MOBILE_TITLE_PASSWORD_SAVED": "Password Updated", "APPLICATION_MOBILE_TITLE_LINK_EXPIRED": "Link Expired", "APPLICATION_MOBILE_TITLE_PENDING_VERIFICATION": "Pending Verification", "APPLICATION_MOBILE_LABEL_SAVE_PASSWORD": "Save Password", "APPLICATION_MOBILE_TEXT_RESET_EMAIL_SENT": "We have sent you a password reset link.\n\nPlease check your email.", "APPLICATION_MOBILE_TEXT_EMAIL_VERIFIED": "Your email is verified\nsuccessfully.", "APPLICATION_MOBILE_TEXT_RESET_VERIFY_EXPIRED": "Your password reset link expired.\nPlease generate a new link to\nreset your password.", "APPLICATION_MOBILE_TEXT_EMAIL_VERIFY_EXPIRED": "Your email link expired.\nPlease generate a new link to\nverify your email.", "APPLICATION_MOBILE_TEXT_PASSWORD_SAVED": "Your password has been updated\nsuccessfully.", "APPLICATION_MOBILE_TEXT_RESET_LIMIT_ERROR": "You have exceeded the maximum number of password reset requests.", "APPLICATION_MOBILE_TEXT_VERIFICATION_LIMIT_ERROR": "You have exceeded maximum number of email verification requests.", "APPLICATION_MOBILE_TEXT_PENDING_VERIFICATION_TEXT1": "We have sent you a verification email at ", "APPLICATION_MOBILE_TEXT_PENDING_VERIFICATION_TEXT2": "Please visit the link provided in your email to verify your account.\n\nIf you are unable to find the email in your inbox, you may need to check your spam folder.", "APPLICATION_MOBILE_TEXT_PENDING_VERIFICATION_TEXT3": "Please verify your email to activate your account.\n\nWe have already sent you a verification email at ", "APPLICATION_MOBILE_TEXT_PENDING_VERIFICATION_TEXT4": "Please visit the link provided in your email to verify the change.\n\nIf you are unable to find the email in your inbox, you may need to check your spam folder or tap on the back button to update your email again.", "APPLICATION_MOBILE_CHALLENGE_RULES": "Read Rules", "APPLICATION_MOBILE_LEAVE_CHALLENGE": "Leave Challenge", "APPLICATION_MOBILE_LABEL_WORKOUT_DATE_SELECTION_ALL_TIME": "All Time", "APPLICATION_MOBILE_LABEL_WORKOUT_DATE_SELECTION_PAST_WEEK": "Past Week", "APPLICATION_MOBILE_LABEL_WORKOUT_DATE_SELECTION_PAST_MONTH": "Past Month", "APPLICATION_MOBILE_LABEL_WORKOUT_DATE_SELECTION_PAST_SIX_MONTHS": "Past 6 Months", "APPLICATION_MOBILE_LABEL_WORKOUT_DATE_SELECTION_CUSTOM": "Custom Range", "APPLICATION_MOBILE_LABEL_WORKOUT_FILTER_APPLY": "Apply", "APPLICATION_MOBILE_LABEL_PET_LEXI": "Lexi", "APPLICATION_MOBILE_LABEL_PROFILE": "Profile", "APPLICATION_MOBILE_PROFILE_ITEM_ACTIVITY_HISTORY": "Activity History", "APPLICATION_MOBILE_PROFILE_ITEM_MANAGE_DOGS": "Manage Dogs", "APPLICATION_MOBILE_PROFILE_ITEM_DAILY_GOALS": "Set Daily Goals", "APPLICATION_MOBILE_PROFILE_ITEM_SELECT_CHARITY": "Animal Charity", "APPLICATION_MOBILE_PROFILE_ITEM_SELECT_SETTINGS": "Settings", "APPLICATION_MOBILE_PROFILE_ITEM_SELECT_DELETE_ACCOUNT": "Delete Account", "APPLICATION_MOBILE_PROFILE_ITEM_EDIT_PROFILE": "Edit Profile", "APPLICATION_MOBILE_PROFILE_ITEM_CHANGE_PASSWORD": "Change Password", "APPLICATION_MOBILE_PROFILE_ITEM_CHANGE_EMAIL": "Change Email", "APPLICATION_MOBILE_PROFILE_ITEM_PRIVACY_POLICY": "Privacy Policy", "APPLICATION_MOBILE_PROFILE_ITEM_INVITE": "Invite", "APPLICATION_MOBILE_PROFILE_TITLE_DAILY_GOALS": "Daily Goals", "APPLICATION_MOBILE_PROFILE_TITLE_WEEKLY_GOALS": "Set Weekly Goals", "APPLICATION_MOBILE_PROFILE_DESCRIPTION_WEEKLY_GOALS": "Keep your pup and yourself fit with weekly walking goals!", "APPLICATION_MOBILE_PROFILE_SAVE_GOALS": "Save Goals", "APPLICATION_MOBILE_PROFILE_GOALS_SET_LATER": "Set Later", "APPLICATION_MOBILE_PROFILE_WALK_COUNTS": "Walks Count", "APPLICATION_MOBILE_PROFILE_WALKS": "Walks", "APPLICATION_MOBILE_LABEL_MILES": "<PERSON>", "APPLICATION_MOBILE_SHORT_LABEL_MILES": "mi", "APPLICATION_MOBILE_TITLE_CHANGE_PASSWORD": "Change Password", "APPLICATION_MOBILE_TITLE_SAVE_PASSWORD": "Save Password", "APPLICATION_MOBILE_CHANGE_PASSWORD_SUCCESS": "Password Updated!", "APPLICATION_MOBILE_PROFILE_UPDATED": "Profile Updated Successfully !", "APPLICATION_MOBILE_BUTTON_LABEL_SAVE_PROFILE": "Save Profile", "APPLICATION_MOBILE_HOME_STATS_WALKS_FINISHED": "Walks", "APPLICATION_MOBILE_HOME_STATS_MILES_WALKED": "<PERSON>", "APPLICATION_MOBILE_HOME_STATS_CHARITY_POINTS": "Charity Points", "APPLICATION_MOBILE_TITLE_CHARITY_POINTS_RULES": "Charity Points Rules", "APPLICATION_MOBILE_RULES_EARNING_CHARITY_POINTS": "Earning Charity Points", "APPLICATION_MOBILE_RULES_DAILY_LIMIT": "Daily Limit", "APPLICATION_MOBILE_RULES_DAILY_LIMIT_PARAGRAPH": "You can earn a maximum of 30 points per day.", "APPLICATION_MOBILE_TITLE_CHARITY_POINTS": "Charity Points", "APPLICATION_MOBILE_TEXT_NO_CHARITY_POINTS_FOUND": "No Charity Points Found", "APPLICATION_MOBILE_TEXT_NO_CHARITY_POINTS_FOUND_DESC": "You have not yet earned any charity points", "APPLICATION_MOBILE_TEXT_NO_WALKS_FOUND_DESC": "You have not completed any walks yet.", "APPLICATION_MOBILE_BUTTON_TEXT_FIRST_WALK": "Start Your First Walk !", "APPLICATION_MOBILE_TEXT_DELETE_ACCOUNT": "If you would like to delete your account and its associated data, please send an <NAME_EMAIL>.\n\nAfter we have received your email, we will remove your account and all data within 30 business days and email you to confirm this is complete.", "APPLICATION_MOBILE_TEXT_WALK_PAUSE_BY_USER": "Your walk has been paused.", "APPLICATION_MOBILE_TEXT_WALK_PAUSE_BY_SPEED_LIMIT": "Walk auto-paused as speed exceeded 30 mph.", "APPLICATION_MOBILE_TEXT_LOGOUT_TITLE_WHEN_UNSYNC_WALKS_AVAILABLE": "You have walks to upload", "APPLICATION_MOBILE_TEXT_LOGOUT_DESC_WHEN_UNSYNC_WALKS_AVAILABLE": "Walks finished in offline mode will be lost if you log out. Are you sure you want to logout?", "APPLICATION_MOBILE_TEXT_LOGOUT_UPLOAD_BUTTON": "Upload Walks Now", "APPLICATION_MOBILE_TEXT_FB_EMAIL_ERROR_MESSAGE": "To register you on WoofTrax, we need access to your Facebook email address. Kindly allow access and then try again.", "APPLICATION_MOBILE_TEXT_SET_WEEKLY_GOALS": "Keep your pup and yourself fit with weekly walking goals!", "APPLICATION_MOBILE_FIELD_LABEL_CURRENT_EMAIL_ADDRESS": "Current Email Address", "APPLICATION_MOBILE_FIELD_LABEL_NEW_EMAIL_ADDRESS": "New Email Address", "APPLICATION_MOBILE_FIELD_LABEL_YOUR_PASSWORD": "Your Password", "APPLICATION_MOBILE_BUTTON_TEXT_SAVE_CHANGES": "Save Changes", "APPLICATION_MOBILE_NOTIFICATION_PERMISSION_TITLE": "Stay connected to help you earn more", "APPLICATION_MOBILE_NOTIFICATION_PERMISSION_SUB_TITLE": "Get notified when:", "APPLICATION_MOBILE_NOTIFICATION_PERMISSION_POINT1": "View your active walk distance and time on your lock screen", "APPLICATION_MOBILE_NOTIFICATION_PERMISSION_POINT2": "A new walking challenge is available", "APPLICATION_MOBILE_NOTIFICATION_PERMISSION_POINT3": "You have a winning walk in a challenge", "APPLICATION_MOBILE_NOTIFICATION_PERMISSION_POINT4": "Your charity gets a donation", "APPLICATION_MOBILE_LABEL_DATE_SELECTION_SELECT_MONTH": "Select Month", "APPLICATION_MOBILE_LABEL_LEADERBOARD_MILES_TOOLTIP": "Includes only walks 1/4 mile and greater. To view all walks of any distance, see History screen", "APPLICATION_MOBILE_LABEL_EMPTY_GROUP_LINE1": "Create a Group", "APPLICATION_MOBILE_LABEL_EMPTY_GROUP_LINE2": "Invite friends, family, and colleagues to walk\nwith you to earn more for animal charities.", "APPLICATION_MOBILE_LABEL_EMPTY_GROUP_LINE3": "Walking together is more fun!", "APPLICATION_CONTENT_INFORMATION_ADOPTION_DATE": "Please enter the date your dog was adopted or otherwise joined your family to the best of your recollection.", "APPLICATION_MOBILE_PET_SHELTER_SELECT": "Select an animal charity", "APPLICATION_MOBILE_PET_SHELTER_ENTER": "Enter an animal charity", "APPLICATION_MOBILE_PET_BIRTHDATE_AGE_TITLE": "Age of ", "APPLICATION_MOBILE_PET_BIRTHDATE_AGE_TAB": "Age", "APPLICATION_MOBILE_PET_BIRTHDATE_DATE_TAB": "Birthdate", "APPLICATION_MOBILE_ERROR_NAME_EMPTY": "Name is required", "APPLICATION_MOBILE_ERROR_CITY_EMPTY": "City is required", "APPLICATION_MOBILE_ERROR_STATE_EMPTY": "State is required", "APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_STATES": "There are no states available to select", "APPLICATION_MOBILE_MESSAGE_PET_ADOPTED_FROM": "I adopted this pet from", "APPLICATION_MOBILE_PET_ADOPTION_TITLE": "Adoption Date"}, "appointmentRequestCauses": [{"id": 1, "label": "Other"}, {"id": 2, "label": "Check-up"}, {"id": 3, "label": "Vaccination"}, {"id": 4, "label": "Annual visit"}, {"id": 5, "label": "Dental "}, {"id": 6, "label": "Follow-up"}, {"id": 7, "label": "Injury"}, {"id": 8, "label": "Medical problem"}, {"id": 9, "label": "Post-op assessment"}, {"id": 10, "label": "Preventive surgery"}, {"id": 11, "label": "Wellness plan"}], "rewards": [{"id": 9, "code": "NEWS_READ_SCHOLAR", "taskCode": "NEWS_READ_0", "count": 25, "type": 0, "reachPoints": 1250, "rewardImageId": null, "rewardImageUrl": null}, {"id": 10, "code": "NEWS_READ_PROFESSOR", "taskCode": "NEWS_READ_0", "count": 100, "type": 0, "reachPoints": 5000, "rewardImageId": null, "rewardImageUrl": null}, {"id": 6, "code": "PET_CREATE_YOU_AND_ME", "taskCode": "PET_CREATE_10", "count": 1, "type": 0, "reachPoints": 50, "rewardImageId": null, "rewardImageUrl": null}, {"id": 7, "code": "PET_CREATE_FAMILY", "taskCode": "PET_CREATE_10", "count": 10, "type": 0, "reachPoints": 500, "rewardImageId": null, "rewardImageUrl": null}, {"id": 8, "code": "SERVICE_CREATE_VETERINARY", "taskCode": "SERVICE_CREATE_1", "count": 1, "type": 0, "reachPoints": 150, "rewardImageId": null, "rewardImageUrl": null}, {"id": 11, "code": "WORKOUT_CREATE_GOOD_START", "taskCode": "WORKOUT_CREATE_0", "count": 1, "type": 0, "reachPoints": 100, "rewardImageId": null, "rewardImageUrl": null}, {"id": 12, "code": "WORKOUT_CREATE_COPPER", "taskCode": "WORKOUT_CREATE_0", "count": 10, "type": 0, "reachPoints": 1000, "rewardImageId": null, "rewardImageUrl": null}, {"id": 13, "code": "WORKOUT_CREATE_SILVER", "taskCode": "WORKOUT_CREATE_0", "count": 25, "type": 0, "reachPoints": 2500, "rewardImageId": null, "rewardImageUrl": null}, {"id": 14, "code": "WORKOUT_CREATE_GOLD", "taskCode": "WORKOUT_CREATE_0", "count": 50, "type": 0, "reachPoints": 5000, "rewardImageId": null, "rewardImageUrl": null}, {"id": 15, "code": "WORKOUT_CREATE_PLATINIUM", "taskCode": "WORKOUT_CREATE_0", "count": 100, "type": 0, "reachPoints": 10000, "rewardImageId": null, "rewardImageUrl": null}, {"id": 16, "code": "WORKOUT_CREATE_DIAMOND", "taskCode": "WORKOUT_CREATE_0", "count": 250, "type": 0, "reachPoints": 25000, "rewardImageId": null, "rewardImageUrl": null}, {"id": 17, "code": "WORKOUT_CREATE_STELLAR", "taskCode": "WORKOUT_CREATE_0", "count": 500, "type": 0, "reachPoints": 50000, "rewardImageId": null, "rewardImageUrl": null}, {"id": 5, "code": "wt-avatar-1", "taskCode": "LEVEL_UP", "count": 1, "type": 1, "reachPoints": 0, "rewardImageId": null, "rewardImageUrl": null}, {"id": 3, "code": "wt-avatar-4", "taskCode": "LEVEL_UP", "count": 6, "type": 1, "reachPoints": 0, "rewardImageId": null, "rewardImageUrl": null}, {"id": 1, "code": "wt-avatar-13", "taskCode": "LEVEL_UP", "count": 18, "type": 1, "reachPoints": 0, "rewardImageId": null, "rewardImageUrl": null}, {"id": 2, "code": "wt-avatar-5", "taskCode": "LEVEL_UP", "count": 78, "type": 1, "reachPoints": 0, "rewardImageId": null, "rewardImageUrl": null}, {"id": 4, "code": "wt-avatar-8", "taskCode": "LEVEL_UP", "count": 460, "type": 1, "reachPoints": 0, "rewardImageId": null, "rewardImageUrl": null}]}