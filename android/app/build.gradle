def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new FileNotFoundException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {

    compileSdkVersion 33

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
        //Looks like upgrading gradle build tools broke some lints.
        //checkReleaseBuilds param on false due to error:
        //Execution failed for task ':app:lintVitalRelease'
        checkReleaseBuilds false 
    }

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 33
        multiDexEnabled true
    }
    signingConfigs {
        releaseMyBuddy {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
        releaseWooftrax {
            keyAlias keystoreProperties['WFkeyAlias']
            keyPassword keystoreProperties['WFkeyPassword']
            storeFile keystoreProperties['WFstoreFile'] ? file(keystoreProperties['WFstoreFile']) : null
            storePassword keystoreProperties['WFstorePassword']
        }
    }

    buildTypes {
        debug{
            minifyEnabled false
            shrinkResources false
            useProguard true

            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            useProguard true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.releaseWooftrax
        }
    }
    flavorDimensions "app"

    productFlavors {
        mybuddy {
            dimension "app"
            applicationId "com.mediproductions.mybuddy"
            versionCode(flutterVersionCode.toInteger() * 10)
            versionName flutterVersionName
            signingConfig signingConfigs.releaseMyBuddy
        }

        wooftrax {
            dimension "app"
            applicationId "com.actionxl.wooftrax.wfad"
            versionCode(flutterVersionCode.toInteger())
//            versionCode 19
            versionName flutterVersionName
            signingConfig signingConfigs.releaseWooftrax
        }
    }
//    splits {
//
//        // Configures multiple APKs based on ABI.
//        abi {
//
//            // Enables building multiple APKs per ABI.
//            enable true
//
//            // By default all ABIs are included, so use reset() and include to specify that we only
//            // want APKs for x86 and x86_64.
//
//            // Resets the list of ABIs that Gradle should create APKs for to none.
//            reset()
//
//            // Specifies a list of ABIs that Gradle should create APKs for.
//            include  "x86_64", "armeabi-v7a", "arm64-v8a"
//
//            // Specifies that we do not want to also generate a universal APK that includes all ABIs.
//            universalApk false
//        }
//    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.android.support:multidex:2.0.1'
    implementation 'com.google.firebase:firebase-messaging:22.0.0'
    implementation 'com.google.firebase:firebase-common:20.0.0'
    implementation 'com.google.firebase:firebase-iid:21.1.0'
    implementation 'com.pushwoosh:pushwoosh-firebase:6.3.0'
    implementation 'com.google.firebase:firebase-crashlytics:17.2.2'
    implementation 'com.google.ads.mediation:facebook:6.13.7.0'
    implementation(platform("org.jetbrains.kotlin:kotlin-bom:1.8.0"))
    implementation "androidx.core:core-splashscreen:1.0.1"
}
apply plugin: 'com.google.gms.google-services'  // Google Play services Gradle plugin
apply plugin: 'com.google.firebase.crashlytics'

