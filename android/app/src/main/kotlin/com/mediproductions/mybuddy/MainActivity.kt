package com.mediproductions.mybuddy

import android.content.Context
import android.os.BatteryManager
import android.os.PowerManager
import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {

    private val CHANNEL = "com.mediproductions/mybuddy"

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(
            flutterEngine.dartExecutor.binaryMessenger,
            CHANNEL
        ).setMethodCallHandler { call, result ->
            if (call.method == "getPowerMode") {
                val powerManager: PowerManager =
                    applicationContext.getSystemService(Context.POWER_SERVICE) as PowerManager
                val powerSaveMode: Boolean = powerManager.isPowerSaveMode
                result.success(powerSaveMode)
            } else if (call.method == "getBatteryLevel") {
                val batteryManager: BatteryManager =
                    applicationContext.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
                val batteryLevel =
                    batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
                result.success(batteryLevel)
            } else {
                result.notImplemented()
            }
        }
    }

}
