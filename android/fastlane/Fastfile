# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    gradle(task: "clean assembleRelease")
    crashlytics
  
    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleRelease")
    upload_to_play_store
  end

  desc "upload android apk to app center"
  lane :upload_to_appcenter do
      appcenter_upload(
          api_token: "baf3cd1622d9686d6606d4dbe91f5eb530a510d5",
          owner_name: "akhatri-1", # owner name to be shown in appcenter for the app
          owner_type: "user", # Default is user - set to organization for appcenter organizations
          app_name: "WoofTrax-Android", # app name  is the name of your app registered with android
          file: "../build/app/outputs/flutter-apk/app-wooftrax-release.apk", # path of the apk that was built for upload
          notify_testers: false # Set to false if you don't want to notify testers of your new release (default: `false`)
      )
  end
end
