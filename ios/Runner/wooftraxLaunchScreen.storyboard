<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="Ydg-fD-yQy"/>
                        <viewControllerLayoutGuide type="bottom" id="xbc-2k-c8Z"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" image="wooftrax" translatesAutoresizingMaskIntoConstraints="NO" id="1Yr-4W-71o">
                                <rect key="frame" x="30" y="284.5" width="315" height="98"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="1Yr-4W-71o" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" constant="30" id="49n-mZ-aa1"/>
                            <constraint firstItem="1Yr-4W-71o" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="ULX-4D-j33"/>
                            <constraint firstItem="1Yr-4W-71o" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="VOB-ti-bPR"/>
                            <constraint firstAttribute="trailing" secondItem="1Yr-4W-71o" secondAttribute="trailing" constant="30" id="Wvc-az-AWg"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="84.799999999999997" y="337.33133433283359"/>
        </scene>
    </scenes>
    <resources>
        <image name="wooftrax" width="370.5" height="97.5"/>
    </resources>
</document>
