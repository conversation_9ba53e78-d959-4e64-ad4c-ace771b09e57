<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <true/>
    </dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>WoofTrax</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>WoofTrax</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
			    <string>fb776595422472276</string>
				<string>com.googleusercontent.apps.348729777085-q6lvc1jhdm15384ovk0qe33tvs8meed7</string>
			</array>
		</dict>
	</array>
	<key>GADApplicationIdentifier</key>
    <string>ca-app-pub-9068079999050700~8130496275</string>
    <key>SKAdNetworkItems</key>
      <array>
        <dict>
          <key>SKAdNetworkIdentifier</key>
          <string>cstr6suwn9.skadnetwork</string>
        </dict>
        <dict>
            <key>SKAdNetworkIdentifier</key>
            <string>v9wttpbfk9.skadnetwork</string>
        </dict>
        <dict>
            <key>SKAdNetworkIdentifier</key>
            <string>n38lu8286q.skadnetwork</string>
        </dict>
      </array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
    <key>FacebookAppID</key>
    <string>776595422472276</string>
    <key>FacebookClientToken</key>
    <string>********************************</string>
    <key>FacebookDisplayName</key>
    <string>$(BUNDLE_NAME)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.medical</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		 <string>fbapi</string>
         <string>fb-messenger-share-api</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MinimumOSVersion</key>
	<string>12.0</string>
	<key>NSContactsUsageDescription</key>
	<string>this app needs access to your contacts if you want to invite them in a community</string>
	<key>NSCalendarsUsageDescription</key>
	<string>This app uses your calendar</string>
	<key>NSCameraUsageDescription</key>
	<string>WoofTrax needs access to your camera so you can add a picture of you or your dog(s).</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Your precise location is needed to record your activity.</string>
    <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Your precise location is needed to record your activity.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Your precise location is needed to record your activity.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>This app requires photo library access to function properly.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>WoofTrax needs access to your photos so you can add a picture of you or your dog(s).
    </string>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>location</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>$(ASSET_PREFIX)LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
    <key>NSSupportsLiveActivities</key>
    <true/>
</dict>
</plist>
