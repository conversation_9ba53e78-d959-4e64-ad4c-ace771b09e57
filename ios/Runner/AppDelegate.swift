import UIKit
import ActivityKit
import Flutter
import SwiftUI
import GoogleMaps
import FBAudienceNetwork
import awesome_notifications
import shared_preferences_ios
import CioMessagingPushFCM
import CioTracking
import FirebaseMessaging
import FirebaseCore

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
    private var activity: Any? = nil
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        
        GMSServices.provideAPIKey("AIzaSyC_iSB9po5uXZO5Qq667HszEvkXfGnR69s")
        // Set the flag as true
        FBAdSettings.setAdvertiserTrackingEnabled(false)
        
        // This function registers the desired plugins to be used within a notification background action
        SwiftAwesomeNotificationsPlugin.setPluginRegistrantCallback { registry in
            SwiftAwesomeNotificationsPlugin.register(
                with: registry.registrar(forPlugin: "io.flutter.plugins.awesomenotifications.AwesomeNotificationsPlugin")!)
            FLTSharedPreferencesPlugin.register(
                with: registry.registrar(forPlugin: "io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin")!)
        }
        
        
        let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
        let batteryChannel = FlutterMethodChannel(name: "com.mediproductions/mybuddy",
                                                  binaryMessenger: controller.binaryMessenger)
        
        batteryChannel.setMethodCallHandler({
            (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
            // This method is invoked on the UI thread.
            // Handle battery messages.
            
            if call.method == "getPowerMode" {
                result(ProcessInfo.processInfo.isLowPowerModeEnabled)
            }
            else if call.method == "getBatteryLevel" {
                let device = UIDevice.current
                device.isBatteryMonitoringEnabled = true
                if device.batteryState == .unknown {
                    result(-1)
                } else {
                    result(Int(device.batteryLevel * 100))
                }
            }
            else {
                result(FlutterMethodNotImplemented)
                return
            }
        })
        
        
        let lockScreenWidget = FlutterMethodChannel(name: "com.mediproductions/wooftrax/walkLockScreenWidget",
                                                    binaryMessenger: controller.binaryMessenger)
        
        if #available(iOS 16.1, *) {
            
            lockScreenWidget.setMethodCallHandler({
                (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
                
                if call.method == "show_walk_widget" {
                    let attribute = WalkAttributes(name: "Walk Lock Screen Stats")
                    let state = WalkAttributes.Status(duration: "00:00", speed: "0.0", distance: "0.0", isPaused: false)
                    
                    self.activity = try? Activity<WalkAttributes>.request(attributes: attribute, contentState: state, pushType: nil)
                    
                    result(true)
                }
                else if call.method == "dismiss_walk_widget" {
                    
                    if self.activity == nil && !Activity<WalkAttributes>.activities.isEmpty{
                        self.activity = Activity<WalkAttributes>.activities[0]
                    }

                    let state = WalkAttributes.Status(duration: "00:00", speed: "0.0", distance: "0.0", isPaused: false)
                    var act : Activity<WalkAttributes>? = self.activity as! Activity<WalkAttributes>?
                    Task {
                        await act?.end(using: state, dismissalPolicy: .immediate)
                    }
                    result(true)
                }
                else if call.method == "dismiss_all_widgets" {
                    
                    let state = WalkAttributes.Status(duration: "00:00", speed: "0.0", distance: "0.0", isPaused: false)
                    
                    Task {
                        for activity in Activity<WalkAttributes>.activities{
                            await activity.end(using: state, dismissalPolicy: .immediate)
                            
                        }
                    }
                    result(true)
                }
                else if call.method == "update_walk_stats" {
                    
                    if self.activity == nil {
                        if Activity<WalkAttributes>.activities.isEmpty{
                            let attribute = WalkAttributes(name: "Walk Lock Screen Stats")
                            let state = WalkAttributes.Status(duration: "00:00", speed: "0.0", distance: "0.0", isPaused: false)
                            
                            self.activity = try? Activity<WalkAttributes>.request(attributes: attribute, contentState: state, pushType: nil)
                        }
                        else{
                            self.activity = Activity<WalkAttributes>.activities[0]
                        }
                    }
                    
                    let data = call.arguments as! [String]
                    
                    let distance = data[0] as! String
                    let duration = data[1] as! String
                    let speed = data[2] as! String
                    let isPaused = (data[3] as! String) == "1"
                    
                    let state = WalkAttributes.Status(duration: duration, speed: speed, distance: distance, isPaused: isPaused)
                    var act : Activity<WalkAttributes>? = self.activity as! Activity<WalkAttributes>?
                    Task {
                        await act?.update(using: state)
                    }
                    result(true)
                }
                else {
                    result(FlutterMethodNotImplemented)
                    return
                }
                
            })
            
        }
        
        
        GeneratedPluginRegistrant.register(with: self)

        Messaging.messaging().delegate = self

        CustomerIO.initialize(siteId: "********************", apiKey: "be86f55fa1e40976f29a", region: .US) { config in
            config.autoTrackDeviceAttributes = true
            config.logLevel = .debug
        }
        // Initialize Customer.io push features after you initialize the SDK:
         MessagingPushFCM.initialize { config in
             // Automatically register push device tokens to the Customer.io SDK
             config.autoFetchDeviceToken = true
             // When your app is in the foreground and a push is delivered, show the push
             config.showPushAppInForeground = true
         }

        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    func application(application: UIApplication,
                     didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        Messaging.messaging().setAPNSToken(deviceToken, type: .unknown);
    }

    override func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        MessagingPush.shared.application(application, didFailToRegisterForRemoteNotificationsWithError: error)
    }
}

extension AppDelegate: MessagingDelegate {
    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        MessagingPush.shared.messaging(messaging, didReceiveRegistrationToken: fcmToken)
    }
}