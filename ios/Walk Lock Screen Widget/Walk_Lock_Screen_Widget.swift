
import ActivityKit
import WidgetKit
import SwiftUI

struct TimerActivityView : View {
    let context: ActivityViewContext<WalkAttributes>

    var body: some View {
        ZStack{
            Color.white
            VStack{
                HStack(spacing: 0){
                    Image("wt").resizable().frame(width: 30, height: 30)
                    Spacer()
                    if(context.state.isPaused){
                        HStack{
                            Image("paused_circle").resizable().frame(width: 16, height: 16)
                            
                            Text("Walk paused")
                            .font(Font.custom("Montserrat", size: 16).weight(.semibold))
                            .foregroundColor(Color(red: 1, green: 0.62, blue: 0.14))
                        }
                    } else{
                        HStack{
                            Image("circle-check").resizable().frame(width: 16, height: 16)
                            
                            Text("Actively Walking")
                                .font(Font.custom("Montserrat", size: 16).weight(.semibold))
                                .foregroundColor(Color(red: 0.13, green: 0.51, blue: 0.35))
                        }
                    }
                }
                
                HStack(spacing: 0){
                    VStack{
                        Text(context.state.distance)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .font(.system(size: 20))
                        .fontWeight(.bold)
                        .foregroundColor(Color(red: 0.0, green: 0.0, blue: 0.0))

                        Text("Miles")
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .font(.system(size: 13)).fontWeight(.medium)
                        .foregroundColor(Color(red: 0.0, green: 0.0, blue: 0.0))
                    }
                    
                    VStack{
                        Text(context.state.duration)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .font(.system(size: 22)).fontWeight(.heavy)
                        .foregroundColor(Color(red: 0.0, green: 0.0, blue: 0.0))

                        Text("Duration")
                        .frame(maxWidth: .infinity, alignment: .center)
                        .font(.system(size: 13)).fontWeight(.medium)
                        .foregroundColor(Color(red: 0.0, green: 0.0, blue: 0.0))
                    }
                    
                    VStack{
                        Text(context.state.speed)
                        .frame(maxWidth: .infinity, alignment: .trailing)
                        .font(.system(size: 20)).fontWeight(.bold)
                        .foregroundColor(Color(red: 0.0, green: 0.0, blue: 0.0))

                        Text("MPH")
                        .frame(maxWidth: .infinity, alignment: .trailing)
                        .font(.system(size: 13)).fontWeight(.medium)
                        .foregroundColor(Color(red: 0.0, green: 0.0, blue: 0.0))
                    }
                }
                
                    
            }.padding(.all)
        }
    }
}

struct Walk_Lock_Screen_Widget: Widget {
    let kind: String = "Walk_Lock_Screen_Widget"

    var body: some WidgetConfiguration {
        ActivityConfiguration(for: WalkAttributes.self){ context in
            TimerActivityView(context: context)
        } dynamicIsland: { context in
            DynamicIsland {
//                DynamicIslandExpandedRegion(.leading) {
//                    TimerActivityView(context: context)
//                }
                DynamicIslandExpandedRegion(.center) {
                    VStack{
                        HStack(spacing: 0){
                            Image("wt_white").resizable().frame(width: 25, height: 25)
                            Spacer()
                            if(context.state.isPaused){
                                HStack{
                                    Image("paused_circle").resizable().frame(width: 12, height: 12)
                                    
                                    Text("Walk paused")
                                    .font(Font.custom("Montserrat", size: 12).weight(.semibold))
                                    .foregroundColor(Color(red: 1, green: 0.62, blue: 0.14))
                                }
                            } else{
                                HStack{
                                    Image("circle-check").resizable().frame(width: 12, height: 12)
                                    
                                    Text("Actively Walking")
                                        .font(Font.custom("Montserrat", size: 12).weight(.semibold))
                                        .foregroundColor(Color(red: 0.13, green: 0.51, blue: 0.35))
                                }
                            }
                        }
                        
                        HStack(spacing: 0){
                            VStack{
                                Text(context.state.distance).frame(maxWidth: .infinity, alignment: .leading).font(.system(size: 16)).fontWeight(.bold)
                                Text("Miles").frame(maxWidth: .infinity, alignment: .leading).font(.system(size: 11)).fontWeight(.medium)
                            }
                            
                            VStack{
                                Text(context.state.duration).frame(maxWidth: .infinity, alignment: .center).font(.system(size: 20)).fontWeight(.heavy)
                                Text("Duration").frame(maxWidth: .infinity, alignment: .center).font(.system(size: 11)).fontWeight(.medium)
                            }
                            
                            VStack{
                                Text(context.state.speed).frame(maxWidth: .infinity, alignment: .trailing).font(.system(size: 16)).fontWeight(.bold)
                                Text("MPH").frame(maxWidth: .infinity, alignment: .trailing).font(.system(size: 11)).fontWeight(.medium)
                            }
                        }
                        
                            
                    }.padding(.all)
                }
//                DynamicIslandExpandedRegion(.trailing) {
//                    TimerActivityView(context: context)
//                }
//                DynamicIslandExpandedRegion(.bottom, priority: 1) {
//                    TimerActivityView(context: context)
//                }
            } compactLeading: {
                if(context.state.isPaused){
                    HStack{
                        Image("paused_circle").resizable().frame(width: 12, height: 12)
                        
                        Text("Walk paused")
                        .font(Font.custom("Montserrat", size: 12).weight(.semibold))
                        .foregroundColor(Color(red: 1, green: 0.62, blue: 0.14))
                    }
                } else{
                    HStack{
                        Image("circle-check").resizable().frame(width: 12, height: 12)
                        
                        Text("Actively Walking")
                            .font(Font.custom("Montserrat", size: 12).weight(.semibold))
                            .foregroundColor(Color(red: 0.13, green: 0.51, blue: 0.35))
                    }
                }
            } compactTrailing: {
                Text(context.state.duration)
            } minimal: {
                if(context.state.isPaused){
                    Image("paused_circle").resizable().frame(width: 12, height: 12)
                } else{
                    Image("circle-check").resizable().frame(width: 12, height: 12)
                }
//                Image(systemName: "circle")
//                    .foregroundColor(.green)
            }
        }
    }
}
