//
//  NotificationService.swift
//  NotificationServiceExtension
//
//  Created by <PERSON><PERSON> on 23/05/2024.
//

import UserNotifications
import CioMessagingPushFCM
import CioTracking

class NotificationService: UNNotificationServiceExtension {

  override func didReceive(_ request: UNNotificationRequest,
                          withContentHandler contentHandler:
                          @escaping (UNNotificationContent) -> Void) {
    // It's required that you initialize the Customer.io SDK in this file, even though you also did so in your app.
    CustomerIO.initialize(siteId: "********************", apiKey: "be86f55fa1e40976f29a", region: .US) { config in
        // To confirm that `delivered` push metrics are tracked, set this to true.
        config.autoTrackPushEvents = true
        config.autoTrackDeviceAttributes = true
        config.logLevel = .debug
    }

    MessagingPush.shared.didReceive(request, withContentHandler: contentHandler)
  }

  override func serviceExtensionTimeWillExpire() {
    MessagingPush.shared.serviceExtensionTimeWillExpire()
  }

}
