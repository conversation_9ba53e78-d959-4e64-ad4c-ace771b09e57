PODS:
  - "app_settings (3.0.0+1)":
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - appsflyer_sdk (6.8.0):
    - AppsFlyerFramework (= 6.8.0)
    - Flutter
  - AppsFlyerFramework (6.8.0):
    - AppsFlyerFramework/Main (= 6.8.0)
  - AppsFlyerFramework/Main (6.8.0)
  - audio_session (0.0.1):
    - Flutter
  - awesome_notifications (0.0.5):
    - Flutter
    - IosAwnCore (= 0.7.3)
  - contacts_service (0.2.2):
    - Flutter
  - device_info (0.0.1):
    - Flutter
  - Firebase/CoreOnly (10.12.0):
    - FirebaseCore (= 10.12.0)
  - Firebase/Crashlytics (10.12.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.12.0)
  - Firebase/DynamicLinks (10.12.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 10.12.0)
  - Firebase/Messaging (10.12.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.12.0)
  - firebase_core (2.15.0):
    - Firebase/CoreOnly (= 10.12.0)
    - Flutter
  - firebase_crashlytics (3.3.4):
    - Firebase/Crashlytics (= 10.12.0)
    - firebase_core
    - Flutter
  - firebase_dynamic_links (5.3.4):
    - Firebase/DynamicLinks (= 10.12.0)
    - firebase_core
    - Flutter
  - firebase_messaging (14.6.5):
    - Firebase/Messaging (= 10.12.0)
    - firebase_core
    - Flutter
  - FirebaseCore (10.12.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.12.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseDynamicLinks (10.12.0):
    - FirebaseCore (~> 10.0)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.12.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_native_splash (0.0.1):
    - Flutter
  - flutter_native_timezone (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.12):
    - FMDB/standard (= 2.7.12)
  - FMDB/Core (2.7.12)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - Google-Mobile-Ads-SDK (10.9.0):
    - GoogleAppMeasurement (< 11.0, >= 7.0)
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_maps_flutter (0.0.1):
    - Flutter
    - GoogleMaps
  - google_mobile_ads (1.0.0):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 10.9.0)
    - webview_flutter_wkwebview
  - google_sign_in_ios (0.0.1):
    - Flutter
    - GoogleSignIn (~> 6.2)
  - GoogleAppMeasurement (10.29.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.29.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.29.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleSignIn (6.2.4):
    - AppAuth (~> 1.5)
    - GTMAppAuth (~> 1.3)
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
  - GoogleUserMessagingPlatform (3.0.0)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (1.3.1):
    - AppAuth/Core (~> 1.6)
    - GTMSessionFetcher/Core (< 3.0, >= 1.5)
  - GTMSessionFetcher/Core (2.3.0)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.6.1)
  - image_picker (0.0.1):
    - Flutter
  - IosAwnCore (0.7.3)
  - just_audio (0.0.1):
    - Flutter
  - location (0.0.1):
    - Flutter
  - move_to_background (0.0.1):
    - Flutter
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_ios (0.0.1):
    - Flutter
  - "permission_handler (5.1.0+2)":
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_ios (0.0.1):
    - Flutter
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - Toast (4.1.1)
  - TOCropViewController (2.6.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
  - wakelock (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - appsflyer_sdk (from `.symlinks/plugins/appsflyer_sdk/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - awesome_notifications (from `.symlinks/plugins/awesome_notifications/ios`)
  - contacts_service (from `.symlinks/plugins/contacts_service/ios`)
  - device_info (from `.symlinks/plugins/device_info/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_dynamic_links (from `.symlinks/plugins/firebase_dynamic_links/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_native_timezone (from `.symlinks/plugins/flutter_native_timezone/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - google_maps_flutter (from `.symlinks/plugins/google_maps_flutter/ios`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/ios`)
  - GoogleUtilities
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker (from `.symlinks/plugins/image_picker/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - move_to_background (from `.symlinks/plugins/move_to_background/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_ios (from `.symlinks/plugins/path_provider_ios/ios`)
  - permission_handler (from `.symlinks/plugins/permission_handler/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_ios (from `.symlinks/plugins/shared_preferences_ios/ios`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/ios`)
  - wakelock (from `.symlinks/plugins/wakelock/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppsFlyerFramework
    - Firebase
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseDynamicLinks
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseSessions
    - FMDB
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleSignIn
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - IosAwnCore
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - Toast
    - TOCropViewController

EXTERNAL SOURCES:
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  appsflyer_sdk:
    :path: ".symlinks/plugins/appsflyer_sdk/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  awesome_notifications:
    :path: ".symlinks/plugins/awesome_notifications/ios"
  contacts_service:
    :path: ".symlinks/plugins/contacts_service/ios"
  device_info:
    :path: ".symlinks/plugins/device_info/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_dynamic_links:
    :path: ".symlinks/plugins/firebase_dynamic_links/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_native_timezone:
    :path: ".symlinks/plugins/flutter_native_timezone/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  google_maps_flutter:
    :path: ".symlinks/plugins/google_maps_flutter/ios"
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker:
    :path: ".symlinks/plugins/image_picker/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  move_to_background:
    :path: ".symlinks/plugins/move_to_background/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_ios:
    :path: ".symlinks/plugins/path_provider_ios/ios"
  permission_handler:
    :path: ".symlinks/plugins/permission_handler/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_ios:
    :path: ".symlinks/plugins/shared_preferences_ios/ios"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/ios"
  wakelock:
    :path: ".symlinks/plugins/wakelock/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  app_settings: d103828c9f5d515c4df9ee754dabd443f7cedcf3
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  appsflyer_sdk: 6d03341418c1acf261fdfde2eb27fdeaf04fa2c2
  AppsFlyerFramework: a8de532f81b1d3c1dfc451f0ecded91ebb6a0e47
  audio_session: 4f3e461722055d21515cf3261b64c973c062f345
  awesome_notifications: d63d9a25f126860f9a600850d99772237895b3ba
  contacts_service: 849e1f84281804c8bfbec1b4c3eedcb23c5d3eca
  device_info: d7d233b645a32c40dfdc212de5cf646ca482f175
  Firebase: 07150e75d142fb9399f6777fa56a187b17f833a0
  firebase_core: e477125798fc37cd4ab43ca6a8536bf7e0929c00
  firebase_crashlytics: 6043ce85800f96e53f15ee5051f9cfad10cce73d
  firebase_dynamic_links: d85cf455646322fd101c8a5a5942c3d47132fe80
  firebase_messaging: 334d68c3a36b6d4d5cd91e4f42509e0d4ae49828
  FirebaseCore: f86a1394906b97ac445ae49c92552a9425831bed
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: c4d111b7430c49744c74bcc6346ea00868661ac8
  FirebaseDynamicLinks: 1a387da899779e5ef34f4d6f8bdba882f90d0e67
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: bb2c4f6422a753038fe137d90ae7c1af57251316
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_native_splash: 52501b97d1c0a5f898d687f1646226c1f93c56ef
  flutter_native_timezone: 5f05b2de06c9776b4cc70e1839f03de178394d22
  fluttertoast: 16fbe6039d06a763f3533670197d01fc73459037
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  Google-Mobile-Ads-SDK: e81e8b009a182dc8dd14951782efdbb30a5e4510
  google_maps_flutter: c59fc576c0d0c7f4dc4bd63832c862d22d5a7c6d
  google_mobile_ads: 035df0d095e1a196b52e3c91534d0718d3dacf98
  google_sign_in_ios: 4f85eb9f937450765c8573bb85fd8cd6a5af675c
  GoogleAppMeasurement: f9de05ee17401e3355f68e8fc8b5064d429f5918
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleSignIn: 5651ce3a61e56ca864160e79b484cd9ed3f49b7a
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMAppAuth: 0ff230db599948a9ad7470ca667337803b3fc4dd
  GTMSessionFetcher: 3a63d75eecd6aa32c2fc79f578064e1214dfdec2
  image_cropper: 60c2789d1f1a78c873235d4319ca0c34a69f2d98
  image_picker: 541dcbb3b9cf32d87eacbd957845d8651d6c62c3
  IosAwnCore: 6494e0e174d49f04f513e8a002187be226889a37
  just_audio: baa7252489dbcf47a4c7cc9ca663e9661c99aafa
  location: 3a2eed4dd2fab25e7b7baf2a9efefe82b512d740
  move_to_background: 39a5b79b26d577b0372cbe8a8c55e7aa9fcd3a2d
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  package_info_plus: 6c92f08e1f853dc01228d6f553146438dafcd14e
  path_provider_ios: 7d7ce634493af4477d156294792024ec3485acd5
  permission_handler: ccb20a9fad0ee9b1314a52b70b76b473c5f8dab0
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  share_plus: 056a1e8ac890df3e33cb503afffaf1e9b4fbae68
  shared_preferences_ios: aef470a42dc4675a1cdd50e3158b42e3d1232b32
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite: 6d358c025f5b867b29ed92fc697fd34924e11904
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  TOCropViewController: edfd4f25713d56905ad1e0b9f5be3fbe0f59c863
  url_launcher_ios: 02f1989d4e14e998335b02b67a7590fa34f971af
  video_player_avfoundation: e489aac24ef5cf7af82702979ed16f2a5ef84cff
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f
  webview_flutter_wkwebview: 2e2d318f21a5e036e2c3f26171342e95908bd60a

PODFILE CHECKSUM: a070c7ef517e547a3b450ea44904c36b8255bd95

COCOAPODS: 1.16.2
