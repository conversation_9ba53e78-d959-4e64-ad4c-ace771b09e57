# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  desc "Description of what the lane does"
  lane :custom_lane do
    # add actions here: https://docs.fastlane.tools/actions
  end

  desc "upload ios release to app center"
  lane :upload_to_appcenter do
      appcenter_upload(
          api_token: "061c8ec1fe7f6fc8a788251eb432d1be60a21efd",
          owner_name: "akhatri-1", # owner name to be shown in appcenter for the app
          owner_type: "user", # Default is user - set to organization for appcenter organizations
          app_name: "WoofTrax-iOS", # app name  is the name of your app registered with ios
          file: "../build/ios/ipa/WoofTrax.ipa", # path of the ipa that was built for upload
          notify_testers: false # Set to false if you don't want to notify testers of your new release (default: `false`)
      )
  end

end
