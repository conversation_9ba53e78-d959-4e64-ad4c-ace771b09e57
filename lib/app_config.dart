import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mybuddy/app_theme.dart';
import 'package:mybuddy/class/app_domain_delegate.dart';

class AppConfig extends GetxController {
  AppConfig(
      {Key? key,
      required this.themeData,
      required this.appDisplayName,
      required this.prefix,
      required this.isMyBuddy,
      required this.isWoofTrax});

  final String appDisplayName;
  final String prefix;
  final bool isMyBuddy;
  final bool isWoofTrax;
  final bool isProduction = AppDomainDelegate.inProduction;
  final ThemeData themeData;

  static AppConfig of(BuildContext? context) {
    return Get.find();
  }

  String get appId {
    if (isMyBuddy) {
      return '0';
    }
    if (isWoofTrax) {
      return '1';
    }

    return '';
  }
}
