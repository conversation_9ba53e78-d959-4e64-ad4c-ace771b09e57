import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:mybuddy/Tools/tools_local_notifications.dart';
import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/class/app_domain_delegate.dart';
import 'package:mybuddy/class/settings_delegate.dart';

import 'Tools/tools.dart';
import 'Tools/tools_native_splash.dart';
import 'app.dart';
import 'app_theme.dart';
import 'firebase_options.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  Tools().cioTools.showBackgroundNotification(message.toMap());
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  NativeSplashTools.initialize();

  AppDomainDelegate.initializePath(rootPathProdWt);

  // initialize the notification plugin
  LocalNotificationsTools().initNotification();

  // Loading settings on App Start
  await SettingsDelegate().load(const Locale('en', 'US'));

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  /// Update the iOS foreground notification presentation options to allow
  /// heads up notifications.
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );

  // Set the background messaging handler early on, as a named top-level function
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  /// Wooftrax app locked to portrait orientation
  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  await MobileAds.instance.initialize().then(
        (value) => MobileAds.instance.updateRequestConfiguration(
          RequestConfiguration(
            testDeviceIds: [
              '187f396ffd1ef68c45baf7e6c0e27e97',
              '43E577D6FFCD71C70F8DC749AD50FFC4',
              '829517C89FDCDCD44DAA3B9D97912252',
              '871C65C4A6C71083C043FD302AF85A92', //HUAWEI BAPTISTE
              'F44E9226D395802AC2BEB3B851FB8A9F', //HUAWEI STEPHANIE
            ],
          ),
        ),
      );

  MobileAds.instance.setAppMuted(true);

  runZonedGuarded<Future<void>>(() async {
    // Pass all uncaught errors from the framework to Crashlytics.
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;

    AppConfig appConfig = Get.put(AppConfig(
      appDisplayName: 'WoofTrax®',
      prefix: 'wooftrax',
      isMyBuddy: false,
      isWoofTrax: true,
      themeData: AppTheme.woofTraxTheme,
    ));

    runApp(MyApp());
  }, (error, stack) => FirebaseCrashlytics.instance.recordError(error, stack));
}
