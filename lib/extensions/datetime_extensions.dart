import 'package:intl/intl.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

extension MBDateTime on DateTime {
  static SettingsDelegate delegate = SettingsDelegate();
  static String lang = delegate.getLang();
  static dynamic locale = delegate.getLocaleString();

  String toDateFormat() {
    DateTime now = DateTime.now();
    int diff = now.difference(this).inDays;

    DateFormat dateFormat;
    String? prefix = '';

    assert(diff >= 0);

    if (diff > 7) {
      dateFormat = DateFormat.MMMEd(locale);
    } else if (diff > 1) {
      dateFormat = DateFormat.EEEE(locale);
    } else {
      prefix = hour > now.hour
          ? 'APPLICATION_MOBILE_LABEL_DATE_Yesterday_AT'.tr('Yesterday at')
          : 'APPLICATION_MOBILE_LABEL_DATE_TODAY_AT'.tr('Today at');
      prefix += ' ';
      dateFormat = lang == 'us' || lang == 'gb'
          ? DateFormat.jm(locale)
          : DateFormat.Hm(locale);
    }

    return prefix + dateFormat.format(this);
  }

  String? toDayFormat({bool shortForm = false}) {
    DateTime now = DateTime.now();
    int diff = now.difference(this).inDays;

    String icuName;

    assert(diff >= 0);

    if (diff > 7) {
      if (shortForm) {
        icuName = "yMMMd";
      } else {
        icuName = 'yMMMMd';
      }
    } else if (diff > 1) {
      icuName = 'EEEE';
    } else if (diff == 1 || hour > now.hour) {
      return 'APPLICATION_MOBILE_LABEL_DATE_YESTERDAY'.tr('Yesterday');
    } else {
      return 'APPLICATION_MOBILE_LABEL_DATE_TODAY'.tr('Today');
    }

    return DateFormat(icuName, locale).format(this);
  }

  String toTimeFormat({bool lowerCase = false}) {
    DateFormat dateFormat = lang == 'us' || lang == 'gb'
        ? DateFormat.jm(locale)
        : DateFormat.Hm(locale);

    if(lowerCase) {
      return dateFormat.format(this).toLowerCase();
    }

    return dateFormat.format(this);
  }

  /// This method returns the date in May 24, 2022
  String toDateFormatActivities() {
    DateFormat dateFormat = DateFormat.yMMMd(locale);

    return dateFormat.format(this);
  }

  int? inDaysDifference() {
    DateTime now = DateTime.now();
    int diff = this.difference(now).inDays;

    return diff<0?0:diff;
  }

  bool inRange(DateTime start, DateTime end){
    DateTime date = DateTime(this.year,this.month,this.day);

    bool between = date.isAfter(start) &&
        date.isBefore(end);
    bool startDate = date.difference(start).inDays==0;
    bool endDate = date.difference(end).inDays==0;

    return between || startDate || endDate;
  }
}
