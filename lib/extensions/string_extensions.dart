import 'package:html_unescape/html_unescape.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';


extension Translation on String {
  String tr([String? ifNullValue, Map<String, String>? parameters]) {
    String refTr = Ref().tr(this, ifNullValue);
    
    if(parameters != null) {
      parameters.forEach((k, v) => refTr = refTr.replaceAll(k, v));
    }

    return refTr;
  }

  bool isEmail() {
    String p =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    RegExp regExp = RegExp(p);
    return regExp.hasMatch(trim());
  }

  String changeBrTag() {
    return replaceAll('<br>', '\n');
  }

  String removeAllHtmlTags() {
    RegExp exp = RegExp(r'<[^>]*>', multiLine: true, caseSensitive: true);

    return replaceAll(exp, '').unescape();
  }

  String unescape() {
    var unescape = HtmlUnescape();
    String str = trim();
    return unescape.convert(str);
  }

  String clean(){
    return replaceAll('&nbsp;', ' ').trim();
  }

  String capitalize() {
    return isNotEmpty ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : this;
  }

  PhoneNumber? toPhoneNumber({String? isoCode}){
    if(isEmpty) {
      return null;
    }

    try {
      PhoneNumber phone = PhoneNumber.fromRaw(this);
      return phone;
    }catch(e){
      if(e is PhoneNumberException){
        Tools.debugPrint('phone number cannot be parsed alone');
      }
    }

    if(isoCode == null || (isoCode = isoCode.trim()) == ''){
      return null;
    }

    try {
      PhoneNumber phone = PhoneNumber.fromIsoCode(isoCode, this);
      return phone;
    }catch(e){
      if(e is PhoneNumberException){
        Tools.debugPrint('phone number cannot be parsed  with iso');
      }
    }
    return null;
  }

  bool isValidPostalCode(int countryId){
    //todo add regexp in country entity in database and so in ref
    if(trim() == '') return true;
    RegExp regex;
    switch(countryId){
      case 1:
        regex = RegExp(r'^(([1-95]{2}|2A|2B)[0-9]{3})$|^[971-974]$');
        break;
      case 2:
        regex = RegExp(r'^[0-9]{5}(?:-[0-9]{4})?$');
        break;
      case 4:
        regex = RegExp(r'([Gg][Ii][Rr] 0[Aa]{2})|((([A-Za-z][0-9]{1,2})|(([A-Za-z][A-Ha-hJ-Yj-y][0-9]{1,2})|(([A-Za-z][0-9][A-Za-z])|([A-Za-z][A-Ha-hJ-Yj-y][0-9][A-Za-z]?))))\s?[0-9][A-Za-z]{2})');
        break;
      default:
        return true;
    }

    return regex.hasMatch(this);
  }

  String truncateWithEllipsis({int maxLength = 15, String omission = '...'}) {
    if (length <= maxLength) {
      return this;
    }
    return replaceRange(maxLength, length, omission);
  }
}

extension StringExtension on String {
  String useCorrectEllipsis() {
    return replaceAll('', '\u200B');
  }
}
