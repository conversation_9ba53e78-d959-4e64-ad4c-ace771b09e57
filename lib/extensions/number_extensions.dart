import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/app_domain_delegate.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/models/unit.dart';

final Unit userUnit = Data().get().owner.units;

extension LocalizedToString on double {
  String toLocalizedString() {
    String str = toString();
    return _processString(str);
  }

  String toStringRounded() {
    return round().toString();
  }

  String toLocalStringAsPrecision([int precision = 3]) {
    if (this >= 1000) {
      precision = 4;
    }
    String str = toStringAsPrecision(precision);

    if (str.contains("e-")) {
      str = "0";
    }

    return _processString(str);
  }

  String toLocalStringAsFixed([int precision = 3, fixedForThousands = true]) {
    if (this >= 1000 && fixedForThousands) {
      precision = 4;
    }
    String str = toStringAsFixed(precision);
    return _processString(str);
  }

  String toTimeString([neglectSeconds = false]) {
    // check if double isInfinite or isNaN
    // because toInt conversion for these cases is unsupported
    if (this < 0 || isInfinite || isNaN) return 'Invalid Value';

    int sVal = ((this - toInt()) * 60).toInt();

    Duration time = Duration(minutes: toInt());

    String hours = time.inHours.toString().padLeft(2, '0');
    String minutes = (time.inMinutes % 60).toString().padLeft(2, '0');
    String seconds = sVal.toString().padLeft(2, '0');

    if (neglectSeconds) {
      return '$hours:$minutes min';
    }

    return '$hours:$minutes:$seconds';
  }

  String toENTimeString() {
    // check if double isInfinite or isNaN
    // because toInt conversion for these cases is unsupported
    if (this < 0 || isInfinite || isNaN) return 'Invalid Value';

    int sVal = ((this - toInt()) * 60).toInt();

    Duration time = Duration(minutes: toInt());

    String hours = time.inHours.toString();
    String minutes = (time.inMinutes % 60).toString();
    String seconds = sVal.toString();

    return hours == '0'
        ? seconds != "0"
            ? minutes != "0"
                ? '$minutes min $seconds sec'
                : '$seconds sec'
            : '$minutes min'
        : '$hours hr $minutes min';
  }

  String _processString(String str) {
    String currentLang = SettingsDelegate().getLang();
    switch (currentLang) {
      case 'us':
      case 'gb':
      case 'ie':
        break;
      default:
        str = str.replaceAll('.', ',');
    }
    return str;
  }
}

extension NetworkLink on int? {
  ImageProvider? toImageProvider() {
    String? url = toImageUrl();
    return this == null || url == null ? null : CachedNetworkImageProvider(url);
  }

  String? toImageUrl() {
    if (this == null) return null;
    String encodedImage = toFilename();
    String url = '${AppDomainDelegate.baseUrlImage}$encodedImage.jpeg';
    return url;
  }

  String toFilename() {
    if (this == null) {
      return '';
    }
    String imageStr = toString();

    return '$imageStr-${Tools.generateMd5(imageStr + 'foerzezvrbavpqzvrpoqorvo')}';
  }

  String toVideoUrl() {
    String imageStr = toString();
    String encodedImage =
        '$imageStr-${Tools.generateMd5(imageStr + 'foerzezvrbavpqzvrpoqorvo')}';
    String url = '${AppDomainDelegate.baseUrlImage}$encodedImage.mp4';
    return url;
  }

  String? toBreedName() {
    if (this == null) {
      return '';
    }
    return Ref().get().breeds.singleWhere((b) => b.id == this).name;
  }
}

extension NumberFormatIntExtension on int? {
  String toUs() {
    var f = NumberFormat('###,###,##0');

    return f.format(this).toString();
  }
}

extension NumberFormatDoubleExtension on double? {
  String toUs() {
    var f = NumberFormat('###,###,##0.00');

    return f.format(this).toString();
  }
}

extension DoubleLength on double? {
  double get fromKilometersFactor =>
      Data().get().owner.units.fromKilometersFactor;

  double toUserSpeed() {
    if (this == null) {
      return 0;
    }

    if (this! < 0) {
      return 0;
    }

    return this! * 3600 / (1000 * fromKilometersFactor);
  }

  double toKPHSpeed() {
    if (this == null) {
      return 0;
    }

    if (this! < 0) {
      return 0;
    }

    return this! * 3.6;
  }

  double toUserLength({src = LengthSource.kilometers}) {
    if (this == null) {
      return 0;
    }
    switch (src) {
      case LengthSource.meters:
        return this! / (fromKilometersFactor * 1000);
      case LengthSource.kilometers:
        return this! / fromKilometersFactor;
      default:
        return 0;
    }
  }

  double? toApiLength() {
    if (this == null) {
      return null;
    }
    return this! * fromKilometersFactor;
  }

  int? roundOrNull() {
    if (this == null) {
      return null;
    }
    return this!.round();
  }
}

extension DoubleWeight on double? {
  double get weightFactor => userUnit.unitWeightId == 2 ? 2.205 : 1.0;

  double toUserWeight() {
    if (this == null) {
      return 0.0;
    }
    return this! * weightFactor;
  }

  String toStringWithUnit() {
    String unit = Data().getUnitWeight().shortName;
    if (this == null) {
      return "0 $unit";
    }
    return "${this!.round()} $unit";
  }
}

extension IntLength on int? {
  double get fromKilometersFactor =>
      Data().get().owner.units.fromKilometersFactor;

  double toUserLength({src = LengthSource.kilometers}) {
    if (this == null) {
      return 0;
    }
    switch (src) {
      case LengthSource.meters:
        return this! / (fromKilometersFactor * 1000);
      case LengthSource.kilometers:
        return this! / fromKilometersFactor;
      default:
        return 0;
    }
  }

  double? toApiLength() {
    if (this == null) {
      return null;
    }
    return this! * fromKilometersFactor;
  }
}

extension PointsConversionExtension on int? {
  /// converts points into level information.
  /// Available parameters: level, goal, progress
  Map<String, dynamic> toLevel() {
    if (this == null) {
      return {'level': 0, 'goal': 100, 'progress': 0.0};
    }

    int points = this!;
    int level = _levelCalculator(points);

    return {
      'level': level,
      'goal': 0,
      'progress': 0,
    };
  }

  int _levelCalculator(int points) {
    if (points >= 0 && points < 10) {
      return 1;
    } else if (points >= 10 && points < 40) {
      return 2;
    } else if (points >= 40 && points < 70) {
      return 3;
    } else if (points >= 70 && points < 120) {
      return 4;
    } else if (points >= 120 && points < 170) {
      return 5;
    } else if (points >= 170 && points < 220) {
      return 6;
    } else if (points >= 220 && points < 300) {
      return 7;
    } else if (points >= 300 && points < 380) {
      return 8;
    } else if (points >= 380 && points < 460) {
      return 9;
    } else if (points >= 460 && points < 560) {
      return 10;
    }

    // This section calculates the level after 10 because there is a constant
    // 100 increase. Starting with seed points of 46 and incrementing
    int result = 10;
    int seedPoints = 46;
    int i = 0;
    while (true) {
      if (points >= ((seedPoints + (i * 10)) * 10) &&
          points < ((seedPoints + ((i + 1) * 10)) * 10)) {
        result += i;
        break;
      }
      i++;
    }

    return result;
  }
}

extension LevelConversionExtension on int {
  int toLevelPoints() {
    return _levelPointsCalculator(this);
  }

  int _levelPointsCalculator(int level) {
    if (level == 1) {
      return 0;
    } else if (level == 2) {
      return 10;
    } else if (level == 3) {
      return 40;
    } else if (level == 4) {
      return 70;
    } else if (level == 5) {
      return 120;
    } else if (level == 6) {
      return 170;
    } else if (level == 7) {
      return 220;
    } else if (level == 8) {
      return 300;
    } else if (level == 9) {
      return 380;
    } else if (level == 10) {
      return 460;
    }

    // This section calculates the level points after 10 because there is a constant 100 increase.
    int result = 460;
    for (int i = 11; i <= level; i++) {
      result += 100;
    }

    return result;
  }
}

extension PointsFormat on int? {
  String addCommas(String s) => s.replaceAllMapped(
        RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
        (Match m) => '${m[1]},',
      );

  String toPoints() =>
      addCommas(NumberFormat.compact(locale: "en_US").format(this));

  String toPointsLong() => addCommas(
        toString().length > 6
            ? NumberFormat.compact(locale: "en_US").format(this)
            : toString(),
      );
}

extension PointsFormats on double? {
  String addCommas(String s) => s.replaceAllMapped(
    RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
        (Match m) => '${m[1]},',
  );

  String toPoints() =>
      addCommas(NumberFormat.compact(locale: "en_US").format(this));

  String toPointsLong() => addCommas(
    toString().length > 6
        ? NumberFormat.compact(locale: "en_US").format(this)
        : toString(),
  );
}

extension IntTime on int {
  double millisecondToMinute() {
    return double.parse((this/60000).toStringAsFixed(2));
  }
}

extension DoubleTime on double {
  double minuteToMillisecond() {
    return this * 60000;
  }
}