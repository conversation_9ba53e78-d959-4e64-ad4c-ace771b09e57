import 'dart:async';

import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/notification.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class NotificationsBloc extends Bloc {
  final StreamController<List<MBNotification>> _streamController = BehaviorSubject();

  Sink<List<MBNotification>> get sink => _streamController.sink;

  Stream<List<MBNotification>> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      List<MBNotification> notifications = Data().get().notifications;
      notifications.sort((a, b) {
        if(a.timestamp == null) {
          return 1;
        } else if(b.timestamp == null) {
          return -1;
        } else {
          return b.timestamp!.compareTo(a.timestamp!);
        }
      });
      sink.add(notifications);
    }
  }
}
