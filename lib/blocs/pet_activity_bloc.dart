import 'dart:async';

import 'package:mybuddy/models/pet_activity.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class PetActivityBloc extends Bloc {
  final StreamController<PetActivity?> _streamController = BehaviorSubject();

  final int petActivityId;

  PetActivityBloc({required this.petActivityId}) : super();

  Sink<PetActivity?> get sink => _streamController.sink;

  Stream<PetActivity?> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.get().getPetActivity(petActivityId));
    }
  }
}
