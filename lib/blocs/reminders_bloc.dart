import 'dart:async';

import 'package:mybuddy/models/pet_activity.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class RemindersBloc extends Bloc {
  final StreamController<List<PetActivity>> _streamController = BehaviorSubject();

  Sink<List<PetActivity>> get sink => _streamController.sink;

  Stream<List<PetActivity>> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.getFilteredPetActivities(chronoFilter: ChronoFilter.future, typeFilter: 2));
    }
  }
}
