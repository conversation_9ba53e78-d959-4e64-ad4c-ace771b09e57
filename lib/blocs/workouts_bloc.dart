import 'dart:async';

import 'package:mybuddy/models/workout.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class WorkoutsBloc extends Bloc {
  final StreamController<List<Workout>> _streamController = BehaviorSubject();

  Sink<List<Workout>> get sink => _streamController.sink;

  Stream<List<Workout>> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    List<Workout> workouts = data.get().workouts;
    workouts.sort((a,b) {
      if(a.createdAt == null) {
        return 1;
      } else if(b.createdAt == null) {
        return -1;
      } else {
        return b.createdAt!.compareTo(a.createdAt!);
      }
    });
    sink.add(workouts);
  }
}
