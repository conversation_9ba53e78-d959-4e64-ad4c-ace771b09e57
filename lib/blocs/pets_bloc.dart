import 'dart:async';

import 'package:mybuddy/models/pet.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class PetsBloc extends Bloc {
  final StreamController<List<Pet>> _streamController = BehaviorSubject();

  final LivingState livingState;
  final bool onlyDog;

  PetsBloc({this.livingState = LivingState.alive, this.onlyDog = false}) : super();

  Sink<List<Pet>> get sink => _streamController.sink;

  Stream<List<Pet>> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.getPets(livingState: livingState, onlyDog: onlyDog));
    }
  }
}
