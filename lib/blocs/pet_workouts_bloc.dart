import 'dart:async';

import 'package:mybuddy/models/workout.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class PetWorkoutsBloc extends Bloc {
  final StreamController<List<Workout>> _streamController = BehaviorSubject();

  final int petId;

  PetWorkoutsBloc({required this.petId}) : super();

  Sink<List<Workout>> get sink => _streamController.sink;

  Stream<List<Workout>> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    sink.add(data.getPetWorkouts(petId: petId));
  }
}
