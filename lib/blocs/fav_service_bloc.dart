import 'dart:async';

import 'package:mybuddy/models/service.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class FavServiceBloc extends Bloc {
  final StreamController<MBService?> _streamController = BehaviorSubject();

  Sink<MBService?> get sink => _streamController.sink;

  Stream<MBService?> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.getFavoriteClinic());
    }
  }
}
