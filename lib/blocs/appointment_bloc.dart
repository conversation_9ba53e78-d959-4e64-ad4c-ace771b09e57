import 'dart:async';

import 'package:mybuddy/models/appointment_request.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class AppointmentBloc extends Bloc {
  final StreamController<AppointmentRequest?> _streamController = BehaviorSubject();

  final int appointmentId;

  AppointmentBloc({required this.appointmentId}) : super();

  Sink<AppointmentRequest?> get sink => _streamController.sink;

  Stream<AppointmentRequest?> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.get().getAppointmentRequest(appointmentId));
    }
  }
}
