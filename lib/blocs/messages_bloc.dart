import 'dart:async';

import 'package:mybuddy/models/message.dart';
import 'package:mybuddy/models/service.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class MessagesBloc extends Bloc {
    final StreamController<List<MBMessage>> _streamController = BehaviorSubject();


    Sink<List<MBMessage>> get sink => _streamController.sink;

    Stream<List<MBMessage>> get stream => _streamController.stream;
    final MBService? service;

    MessagesBloc({this.service});

    @override
    void dispose() {
        subscription!.cancel();
        _streamController.close();
    }

    @override
    void update() {
        if (!_streamController.isClosed) {
            sink.add(data.get().getMessagesGrouped(service: service));
        }
    }
}
