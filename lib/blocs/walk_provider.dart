import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:gpx/gpx.dart';
import 'package:intl/intl.dart';
import 'package:mybuddy/Api/mb_api_workout.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.cio.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/constants.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/location_manager.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/controllers/home_summary_controller.dart';
import 'package:mybuddy/controllers/home_tab_controller.dart';
import 'package:mybuddy/controllers/weekly_stats_controller.dart';
import 'package:mybuddy/db/models/workout_db.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/shelter.dart';
import 'package:mybuddy/models/walk_model.dart';
import 'package:mybuddy/models/workout.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../controllers/offline_workout_sync_controller.dart';
import '../controllers/workout_history_controller.dart';

class WalkProvider extends GetxController {
  static WalkProvider of([BuildContext? context]) {
    return Get.find();
  }

  late SharedPreferences prefs;

  Rx<Walk> _walk = Walk().obs;

  Walk get walk => _walk.value;

  int get petsCount => _walk.value.pets.length;

  List<Pet> get pets => _walk.value.pets;

  MoodType get mood => _walk.value.type;

  IconData get icon => _walk.value.type.toMap['iconData'];

  String get type => _walk.value.type.toMap['name'];

  Shelter get shelter => _walk.value.shelter!;

  bool get isPetsEmpty => walk.pets.isEmpty;

  // This is true if we are resuming an ongoing walk
  RxBool walkOngoing = false.obs;

  bool isPetSelected(Pet pet) => _walk.value.pets.contains(pet);

  bool _isAppStart = false; // This flag denotes if the app is started

  bool _isSubmitting = false; // This flag denotes if there is an api call

  List<Pet> initialSortedPet = []; // This is the initial sorted pet list

  // Check Local storage walk
  Future<void> debugLogLocalStorageWalk() async {
    Walk? localStorageWalk = _restoreOngoingWalkFromStorage();
    if (localStorageWalk != null) {
      CIOTools()
          .logDebugEvent(Event.SHARED_PREF_WALK, Walk.toMap(localStorageWalk));
    }
  }

  /// This method is called from the select shelter screen to update the
  /// walk shelter that is initialized
  void shelterChanged() {
    _walk.value.shelter = Data().get().owner.shelter;
    _walk.refresh();
  }

  /// This method is called from Add/Edit Pet screen to update the
  /// walk pets that is initialized
  void refreshDogs() {
    _walk.value.pets = _getDogsForTheWalk();
    _walk.refresh();
    update();
  }

  String getStartWalkCardTitle({bool firstWalk = false}) {
    if (walkOngoing.isTrue) {
      return "Continue Activity";
    } else {
      if (firstWalk) {
        return 'APPLICATION_MOBILE_BUTTON_TEXT_FIRST_WALK'.tr();
      }
      return 'APPLICATION_MOBILE_LABEL_START_DOING'
          .tr('Start %1!')
          .replaceAll('%1', _walk.value.type.toName);
    }
  }

  /// This methods returns the selected pets along with all the pets that
  /// the user has added. The selected ones are returned at the start
  List<Pet> getPetsWithSelectedAtBeginning() {
    ///restrict to resorting if the pets are already sorted
    ///if the pets are already sorted, then the initialSortedPet list will be empty
    ///if the pets are not sorted, then the initialSortedPet list will have the pets
    ///in the order they were added

    if (initialSortedPet.isEmpty) {
      List<Pet> notSelected = Data()
          .getPets(onlyDog: true, livingState: LivingState.both)
          .where((p) {
        return !isPetSelected(p);
      }).toList();

      List<Pet> selected = Data()
          .getPets(onlyDog: true, livingState: LivingState.both)
          .where((p) {
        return isPetSelected(p);
      }).toList();

      initialSortedPet = selected + notSelected;
      return [...selected, ...notSelected];
    } else {
      List<Pet> pets =
          Data().getPets(onlyDog: true, livingState: LivingState.both);

      //add the pets that are not in the initial list
      for (var pet in pets) {
        if (!initialSortedPet.contains(pet)) {
          initialSortedPet.add(pet);
        } else {
          // update the object
          initialSortedPet[initialSortedPet.indexOf(pet)] = pet;
        }
      }

      //remove the pets that are not in the pets list
      List.from(initialSortedPet).forEach((pet) {
        if (!pets.contains(pet)) {
          initialSortedPet.remove(pet);
        }
      });

      return initialSortedPet;
    }
  }

  List<Pet> getPetsWithSelectedAtBeginningOnlySelectable() {
    List<Pet> pets = getPetsWithSelectedAtBeginning()
        .where(
            (pet) => pet.activityStatus != ActivityWalkingStatus.notSelectable)
        .toList();
    return pets;
  }

  void setMood(MoodType type) {
    _walk.value.type = type;
    _walk.refresh();
  }

  void handlePet(Pet pet) {
    if (!_walk.value.pets.contains(pet)) {
      _walk.value.pets.add(pet);
    } else {
      // Restricting the pet removal to greater than 1
      if (_walk.value.pets.length != 1) {
        _walk.value.pets.remove(pet);
      }
    }
    _walk.refresh();
    update();
  }

  void _clear() async {
    final success = await _clearOngoingWalkToStorage();
    CIOTools().logDebugEvent(Event.CLEAR_SHARED_PREF, {'success': success});
    initializeWalk(true);
    update();
  }

  void initializeWalk([bool debugLog = false]) {
    Tools.debugPrint("Walk provider initalize walk asim");
    // Need to check if there is already a walk in progress or not
    Walk? restoreWalk = _restoreOngoingWalkFromStorage();
    if (restoreWalk != null) {
      _walk.value = restoreWalk;
      walkOngoing.value = true;
      _pauseOngoingWalkOnAppStart();
    } else {
      _walk.value = Walk();
      walkOngoing.value = false;

      DateTime date = DateTime.now();
      String trackName = DateFormat('yMMddHHmmss').format(date) +
          Random().nextInt(100000000).toString() +
          '.gpx';
      _walk.value.trackName = trackName;
      refreshDogs();
      _walk.value.shelter = Data().get().owner.shelter;
      _walk.value.ownerId = Data().get().owner.id;
    }
    if (debugLog) {
      CIOTools().logDebugEvent(Event.INITIALIZE_WALK, Walk.toMap(walk));
    }
  }

  Future<void> startWalk() async {
    _walk.value.createdAt = DateTime.now();
    _walk.value.updatedAt = _walk.value.createdAt;
    CIOTools().logDebugEvent(Event.WALK_START, Walk.toMap(walk));
    _syncWalkToServer(Status.started);
  }

  Future<void> resumeWalk() async {
    _walk.value.updatedAt = DateTime.now();
    _walk.value.positions = [];
    _walk.value.currentTrackNumber =
        (int.parse(_walk.value.currentTrackNumber) + 1).toString();

    CIOTools().logDebugEvent(Event.WALK_RESUME, Walk.toMap(walk));
    _syncWalkToServer(Status.started);
  }

  Future<void> pauseWalk(AutoEndReason pauseReason) async {
    _walk.value.updatedAt = DateTime.now();
    _walk.value.autoEndReason = pauseReason;
    CIOTools().logDebugEvent(Event.WALK_PAUSE, Walk.toMap(walk));
    _syncWalkToServer(Status.stopped);
  }

  Future<void> finishWalk(AutoEndReason autoEndReason) async {
    _walk.value.updatedAt = DateTime.now();
    // Forcefully marking is submitting to false to override the pause call
    _isSubmitting = false;
    _walk.value.autoEndReason = autoEndReason;
    _walk.value.status = Status.saved;
    _walk.value.gpx = _getGpxOfOngoingWalk();
    _walk.value.speed = _getAverageSpeedOfOngoingWalk();

    CIOTools().logDebugEvent(Event.WALK_END, Walk.toMap(walk));

    OfflineWorkoutSyncController offlineController =
        OfflineWorkoutSyncController.of();

    WorkoutDb? workoutDb = await offlineController.saveOfflineWalkInDB(walk);
    // update syncing status
    await offlineController.checkUnSyncWalksStatus();

    WeeklyStatsController.of().refreshHomeStats();
    HomeSummaryController.of().fetchHomeSummary();

    // Clean up
    _clear();

    if (workoutDb != null && _walk.value.workout != null) {
      await Tools().adMobTools.showInterstitialAd(dismissedCallback: () {
        Tools().common.showMessage(Get.context!, "Walk Finished!");
      });
    } else {
      Tools().common.showMessage(Get.context!, "Walk Finished!");
    }

    Tools().navigatorPopAllUntilHome();
    if (Get.isRegistered<WorkoutHistoryController>()) {
      WorkoutHistoryController.of().initialFetch();
    }

    Get.find<HomeTabController>().tabSwitcher(HomeTab.history);
  }

  Future<bool> deleteWalk() async {
    CIOTools().logDebugEvent(Event.WALK_DISCARD, Walk.toMap(walk));
    bool success = false;
    if (_walk.value.workout != null) {
      MBResponse response =
          await MbApiWorkout().deleteWorkout(Get.context, _walk.value.workout!);
      success = response.success;
    }

    // Clean up
    _clear();

    Tools().common.showMessage(Get.context!, "Walk Discarded!");
    Tools().navigatorPop();

    return success;
  }

  void addDataToOngoingWalk(
      UserLocation position, double distance, int duration, double speed) {
    // Storing the user location in local storage for future use
    _walk.value.positions.add(position);
    _walk.value.distance = distance;
    _walk.value.duration = duration;
    _walk.value.speed = speed;

    _walk.value.positionsWithTracks[_walk.value.currentTrackNumber] =
        _walk.value.positions;

    _saveOngoingWalkToStorage();
    Tools.debugPrint(
        "add distance: $distance , duration: $duration , speed: $speed");
    walkOngoing.value = true;
  }

  void addDurationToOngoingWalk(int duration) {
    _walk.value.duration = duration;
    walkOngoing.value = true;
    _saveOngoingWalkToStorage();
  }

  /// This method gets the dogs that should be auto selected for a walk
  /// First it checks if there are any dogs that are marked as auto selected
  /// if there is none, then it looks for dogs that are used in the last workout
  /// if there is no dog available, then it selects the dog that was recently
  /// added
  List<Pet> _getDogsForTheWalk() {
    // Selecting the dogs that are auto selected for a walk
    List<Pet> allDogs =
        Data().getPets(onlyDog: true, livingState: LivingState.both);
    List<Pet> autoSelectedDogs = allDogs.where((p) {
      return p.activityStatus == ActivityWalkingStatus.selected;
    }).toList();

    if (autoSelectedDogs.isEmpty && allDogs.isNotEmpty) {
      List<Pet> selectablePets = allDogs
          .where((element) =>
              element.activityStatus == ActivityWalkingStatus.selectable)
          .toList();
      if (selectablePets.isNotEmpty) {
        autoSelectedDogs.add(selectablePets[0]);
      }
    }

    return autoSelectedDogs;
  }

  Future<bool> _syncWalkToServer(Status newStatus) async {
    if (_isSubmitting) {
      return true;
    }

    _isSubmitting = true;

    _walk.value.status = newStatus;
    _walk.value.gpx = _getGpxOfOngoingWalk();
    _walk.value.speed = _getAverageSpeedOfOngoingWalk();

    var context = newStatus == Status.saved ? Get.context : null;

    Map<String, dynamic> body = await _walk.value.toJsonApi();

    MBResponse response = await MbApiWorkout().add(
      context,
      newStatus,
      body,
    );
    if (response.success) {
      Workout workout = response.entity as Workout;
      workout.points = response.body['points'];
      workout.winningWalk = response.body['winningWalk'];

      _walk.value.workout = workout;
    }

    _saveOngoingWalkToStorage();

    _isSubmitting = false;

    return response.success;
  }

  String _getGpxOfOngoingWalk() {
    Gpx _gpx = Gpx();
    _gpx.version = '1.1';
    _gpx.creator = 'Medi-productions';

    String trackName = _walk.value.trackName;
    List<Trkseg> trackSegments = <Trkseg>[];

    _walk.value.positionsWithTracks.forEach((key, value) {
      List<Wpt> wayPoints = value.map((p) {
        return Wpt(
          lat: p.latitude,
          lon: p.longitude,
          time: DateTime.fromMillisecondsSinceEpoch(p.time!.toInt()),
        ); //todo get time
      }).toList();

      List<Wpt> segment = wayPoints;
      if (segment.isNotEmpty) {
        trackSegments.add(Trkseg(trkpts: segment));
      }
    });

    _gpx.trks = [Trk(name: trackName, trksegs: trackSegments)];

    String content = GpxWriter().asString(_gpx, pretty: true);
    // log("Asim saving: $content");
    return content;
  }

  double _getAverageSpeedOfOngoingWalk() {
    double averageSpeed = 0.0;
    List<double> trackAverageSpeed = [];
    List<double> speeds;
    _walk.value.positionsWithTracks.forEach((key, value) {
      speeds = [];
      for (var element in value) {
        //if the speed value will be null or nan then 0 will be considered
        double speed = 0;
        if (element.speed != null && !element.speed!.isNaN) {
          speed = element.speed!;
        }
        speeds.add(speed);
      }

      if (speeds.isNotEmpty) {
        trackAverageSpeed.add(speeds.reduce((a, b) => a + b) / speeds.length);
      }
    });

    if (trackAverageSpeed.isNotEmpty) {
      //removing the nan values from the list and then calculating the average
      List<double> validSpeeds =
          trackAverageSpeed.where((speed) => !speed.isNaN).toList();
      if (validSpeeds.isNotEmpty) {
        averageSpeed = validSpeeds.reduce((a, b) => a + b) / validSpeeds.length;
      }
    }

    //if the average speed is nan then 0 will be considered
    if (averageSpeed.isNaN) averageSpeed = 0.0;

    return averageSpeed;
  }

  Future<void> _saveOngoingWalkToStorage() async {
    await prefs.setString(ongoingWalk, Walk.encode(_walk.value));
  }

  Walk? _restoreOngoingWalkFromStorage() {
    String? ongoingWalkEncoded = prefs.getString(ongoingWalk);
    if (ongoingWalkEncoded != null) {
      Walk walk = Walk.decode(ongoingWalkEncoded);
      return walk;
    }
    return null;
  }

  Future<bool> _clearOngoingWalkToStorage() async =>
      await prefs.remove(ongoingWalk);

  /// This method pauses the walk if we are starting the app and
  /// there is a walk in progress and its status is started.
  /// This is because we have to pause an ongoing walk when app is
  /// restarted
  Future<void> _pauseOngoingWalkOnAppStart() async {
    if (!_isAppStart) return;

    if (_walk.value.status == Status.started) {
      await pauseWalk(AutoEndReason.pauseAppStart);
    }

    _isAppStart = false;
  }

  Future<void> loadDependencies() async {
    prefs = SettingsDelegate().prefs;
    _isAppStart = true;
    debugLogLocalStorageWalk();
  }
}
