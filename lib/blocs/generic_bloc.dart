import 'dart:async';

import 'package:mybuddy/models/login_data.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class GenericBloc extends Bloc {
  final StreamController<LoginData> _streamController = BehaviorSubject();

  Sink<LoginData> get sink => _streamController.sink;

  Stream<LoginData> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.get());
    }
  }
}
