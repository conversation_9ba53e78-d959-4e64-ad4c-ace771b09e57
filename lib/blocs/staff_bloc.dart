import 'dart:async';

import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/models/staff.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class StaffBloc extends Bloc {
  final StreamController<Staff?> _streamController = BehaviorSubject();

  final int? staffId;

  final MBService service;

  StaffBloc({required this.service, required this.staffId}) : super();

  Sink<Staff?> get sink => _streamController.sink;

  Stream<Staff?> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(service.findStaff(staffId));
    }
  }
}
