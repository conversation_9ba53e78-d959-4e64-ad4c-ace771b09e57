import 'dart:async';

import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/models/staff.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class StaffsBloc extends Bloc {
  final StreamController<List<Staff>> _streamController = BehaviorSubject();

  final MBService service;

  StaffsBloc({required this.service}) : super();

  Sink<List<Staff>> get sink => _streamController.sink;

  Stream<List<Staff>> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      List<Staff> staffs = service.staffs;
      staffs.sort((a, b) {
        return a.getFullName().toLowerCase().compareTo(b.getFullName().toLowerCase());
      });
      sink.add(staffs);
    }
  }
}
