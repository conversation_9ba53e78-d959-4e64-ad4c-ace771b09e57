import 'dart:async';

import 'package:mybuddy/models/message.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class MessageBloc extends Bloc {
  final StreamController<MBMessage?> _streamController = BehaviorSubject();

  final int? messageId;

  MessageBloc({required this.messageId}) : super();

  Sink<MBMessage?> get sink => _streamController.sink;

  Stream<MBMessage?> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.getMessage(messageId));
    }
  }
}
