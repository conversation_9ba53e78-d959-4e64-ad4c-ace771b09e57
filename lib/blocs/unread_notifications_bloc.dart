import 'dart:async';

import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class UnreadNotificationsBloc extends Bloc {
  final StreamController<int> _streamController = BehaviorSubject();

  Sink<int> get sink => _streamController.sink;

  Stream<int> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.getUnreadNotificationCount());
    }
  }
}
