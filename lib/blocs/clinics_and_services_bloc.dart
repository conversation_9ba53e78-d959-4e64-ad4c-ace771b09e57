import 'dart:async';

import 'package:mybuddy/models/service.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class ClinicsAndServicesBloc extends Bloc {
  final StreamController<List<MBService>> _streamController = BehaviorSubject();

  ClinicsAndServicesBloc();

  Sink<List<MBService>> get sink => _streamController.sink;

  Stream<List<MBService>> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.allServicesGrouped());
    }
  }
}
