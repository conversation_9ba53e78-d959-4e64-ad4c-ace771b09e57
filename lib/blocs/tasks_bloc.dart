import 'dart:async';

import 'package:mybuddy/models/task.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class TasksBloc extends Bloc {
  final StreamController<List<Task>> _streamController = BehaviorSubject();

  final List<Task>? list;

  TasksBloc({this.list}) : super();

  Sink<List<Task>> get sink => _streamController.sink;

  Stream<List<Task>> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      List<Task> tasks = <Task>[];
      if(list != null) {
        for (Task element in list!) {
          if(data.get().tasks.any((t) => t.code == element.code)) {
            tasks.add(data.get().tasks.singleWhere((t) => t.code == element.code));
          } else {
            tasks.add(element);
          }
        }
      } else {
        tasks.addAll(data.get().tasks);
      }
      sink.add(tasks);
    }
  }
}
