import 'dart:async';

import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class ContractsBloc extends Bloc {
  final Pet? pet;
  final int? contractType;

  final StreamController<List<PetActivity>> _streamController = BehaviorSubject();

  ContractsBloc({this.contractType, this.pet}) : super();

  Sink<List<PetActivity?>> get sink => _streamController.sink;

  Stream<List<PetActivity>> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.getContractsForPet(typeFilter: contractType, petId: pet!.id));
    }
  }
}
