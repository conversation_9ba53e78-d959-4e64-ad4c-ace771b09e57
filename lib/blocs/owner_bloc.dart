import 'dart:async';

import 'package:mybuddy/models/owner.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class OwnerBloc extends Bloc {
  final StreamController<Owner> _streamController = BehaviorSubject();

  Sink<Owner> get sink => _streamController.sink;

  Stream<Owner> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.get().owner);
    }
  }
}
