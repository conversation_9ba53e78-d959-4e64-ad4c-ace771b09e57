import 'dart:async';

import 'package:mybuddy/models/appointment_request.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class AppointmentsBloc extends Bloc {
  final StreamController<List<AppointmentRequest>> _streamController = BehaviorSubject();

  Sink<List<AppointmentRequest>> get sink => _streamController.sink;

  Stream<List<AppointmentRequest>> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.getActiveAppointmentRequests());
    }
  }
}
