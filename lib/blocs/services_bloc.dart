import 'dart:async';

import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/service.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class ServicesBloc extends Bloc {
  final StreamController<List<MBService>> _streamController = BehaviorSubject();

  ServicesBloc(this.isClinic, this.serviceParent);

  Sink<List<MBService>> get sink => _streamController.sink;

  Stream<List<MBService>> get stream => _streamController.stream;

  final bool isClinic;
  final MBService? serviceParent;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      List<MBService> services = <MBService>[];
      if (serviceParent?.services != null) {
        services = serviceParent!.services!;
      } else if (isClinic) {
        services = Data().getClinics();
      } else {
        services = Data().getServices();
      }

      sink.add(services);
    }
  }
}
