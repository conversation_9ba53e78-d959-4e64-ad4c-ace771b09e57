import 'dart:async';

import 'package:mybuddy/models/service.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class ServiceBloc extends Bloc {
  final StreamController<MBService?> _streamController = BehaviorSubject();

  final int serviceId;

  ServiceBloc({required this.serviceId}) : super();

  Sink<MBService?> get sink => _streamController.sink;

  Stream<MBService?> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.get().getService(serviceId));
    }
  }
}
