import 'dart:async';

import 'package:mybuddy/class/petcarepack.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/models/petcarepacks.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class PetCarePacksBloc extends Bloc {
  final StreamController<List<PetCarePack>> _streamController = BehaviorSubject();

  final PetCarePackDelegate petCarePacks = PetCarePackDelegate();

  final Ref refs = Ref();
  final bool isHomeCare;
  String filter = '';

  PetCarePacksBloc(this.isHomeCare) : super();

  Sink<List<PetCarePack>> get sink => _streamController.sink;

  Stream<List<PetCarePack>> get stream => _streamController.stream;

  void advancedUpdate(String filter) {
    this.filter = filter;
    update();
  }

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      List<PetCarePack> list;
      if (isHomeCare) {
        list = petCarePacks.homeCare.homeCarePacks;
        list.sort();
      } else {
        if (filter == '') {
          list = petCarePacks.homeCare.petCarePacks;
        } else {
          list = petCarePacks.homeCare.petCarePacks.where((pcp) {
            if (pcp.breedId == null) return false;
            Breed? breed = refs.get().getBreed(pcp.breedId);
            if (breed == null) return false;
            if (breed.name.toLowerCase().contains(filter.toLowerCase())) {
              return true;
            }
            return false;
          }).toList();
        }
      }
      sink.add(list);
    }
  }
}
