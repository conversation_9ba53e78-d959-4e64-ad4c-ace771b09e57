import 'package:flutter/material.dart';

import 'bloc.dart';

class Bloc<PERSON>rovider<T extends Bloc> extends StatefulWidget {
  /// Bloc
  final T bloc;

  /// bloc child
  final Widget child;

  /// Constructor
  const BlocProvider({Key? key, required this.bloc, required this.child}) : super(key: key);

//  ///  type value
//  static Type _providerType<T>() => T;

  @override
  State createState() => _BlocProviderState();

  /// Bloc configurator
  static T of<T extends Bloc>(BuildContext context) {
    final BlocProvider<T> _provider = context.findAncestorWidgetOfExactType<BlocProvider<T>>()!;
    return _provider.bloc;
  }
}

class _BlocProviderState extends State<BlocProvider> {
  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  @override
  void dispose() {
    widget.bloc.dispose();
    super.dispose();
  }
}
