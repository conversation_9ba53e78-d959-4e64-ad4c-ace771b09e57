import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:json_api/client.dart';
import 'package:json_api/document.dart';
import 'package:mybuddy/Api/captainvet_api.dart';
import 'package:mybuddy/Api/mb_api_pet_activity.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/service.dart';

class _CVInherited extends InheritedWidget {
  const _CVInherited({
    Key? key,
    required Widget child,
    required this.data,
  }) : super(key: key, child: child);

  final CVInheritedWidgetState data;

  @override
  bool updateShouldNotify(_CVInherited oldWidget) {
    return true;
  }
}

class CVInheritedWidget extends StatefulWidget {
  const CVInheritedWidget({Key? key, required this.child, required this.clinic})
      : super(key: key);

  final Widget child;
  final MBService clinic;

  @override
  CVInheritedWidgetState createState() => CVInheritedWidgetState();

  static CVInheritedWidgetState of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<_CVInherited>()!.data;
  }
}

class CVInheritedWidgetState extends State<CVInheritedWidget> {
  /// stepper current step
  int _currentStep = 0;

  int get currentStep => _currentStep;

  /// created apt
  late AppointmentRequest? _appointment;

  AppointmentRequest? get appointment => _appointment;

  /// apt is Complete
  bool _isComplete = false;

  bool get isComplete => _isComplete;

  void goTo(int step) {
    setState(() {
      if (step == 3) {
        _isComplete = true;
      } else {
        _currentStep = step;
      }
    });
  }

  void next() {
    goTo(_currentStep + 1);
  }

  /// rdv duration
  int _rdvDuration = 5;

  int get duration => _rdvDuration;

  /// chosen one
  late Pet _pet;

  Pet get pet => _pet;

  bool get isPetReconciled =>
      _pet.captainvetId != null && _pet.captainvetId!.isNotEmpty;

  void setPet(Pet pet) {
    setState(() {
      _pet = pet;
    });
    addData('animal-types', pet.getCaptainVetSpeciesId());
  }

  /// prepare list of data for apt
  final Map<String, dynamic> _sendData = <String, dynamic>{};

  Map<String, dynamic> get data => _sendData;

  void addData(String key, dynamic value) {
    setState(() {
      _sendData[key] = value;
      if (key.startsWith('rdv-types')) {
        _vetRdvTypesSelected = _vetRdvTypes
            .where((vetRdvType) => vetRdvType.attributes['rdvTypeId'] == value)
            .toList();
      }
      if (key.startsWith('vets')) {
        _rdvDuration = _retrieveGoodDuration() ?? 5;
      }
    });
  }

  Future getAvailabilities(DateTime startDate) async {
    return await CaptainVetApi().getMeetingAvailabilities(
        id: widget.clinic.captainvetId!,
        startDate: DateFormat('yyyy-MM-dd').format(startDate),
        filters: <String?>[
          _pet.getCaptainVetSpeciesId(),
          _sendData['rdv-types'],
          _sendData['vets']
        ]);
  }

  Future<Pet> createCaptainVetPet(BuildContext context) async {
    ResourceCreated response =
        await CaptainVetApi().createPet(context: context, pet: _pet);

    if (!response.http.isFailed) {
      Pet responseReconciliation =
          await reconcileCaptainVetPet(context, response.resource.id);

      return responseReconciliation;
    } else {
      return Future.error('http error');
    }
  }

  Future<Pet> reconcileCaptainVetPet(BuildContext context, String cvId) async {
    MBResponse response = await CaptainVetApi()
        .reconcilePet(context: context, pet: _pet, captainvetId: cvId);

    if (response.success) {
      return Pet.fromJson(response.body['pet']);
    } else {
      return Future.error(response.errorMessage);
    }
  }

  Future<void> createApt(BuildContext context) async {
    var captainvetResponse =
        await CaptainVetApi().createMeeting(data: _sendData);

    var myBuddyResponse = await MbApiPetActivity().addAppointment(context, {
      'serviceId': widget.clinic.id,
      'date': _sendData['startAt'],
      'petId': _pet.id,
      'staffCaptainvetId': _sendData['vets'],
      'aptCaptainvetId': captainvetResponse.resource.id
    });

    if (myBuddyResponse.success) {
      setState(() {
        _appointment = AppointmentRequest.fromJson(
            myBuddyResponse.body['appointmentRequest']);
      });
    }
  }

  /// captainvet lists
  List<Resource> _allRdvTypes = <Resource>[];
  List<Resource> _animalTypes = <Resource>[];
  List<Resource> _vetRdvTypes = <Resource>[];
  List<Resource> _vets = <Resource>[];
  List<Resource> _rdvExceptions = <Resource>[];
  List<Resource> _vetRdvTypesSelected = <Resource>[];

  /// myBuddy pets alive
  final List<Pet> _myBuddyPets = Data().getPets(livingState: LivingState.alive);

  List<Pet> get filteredPets => _myBuddyPets
      .where((pet) => _animalTypes
          .any((animalType) => animalType.id == pet.getCaptainVetSpeciesId()))
      .toList();

  List<Resource> get rdvTypesByVetRdvTypeAndAnimalType =>
      _allRdvTypes.where((rdvType) {
        return _vetRdvTypes.any((vetRdvType) {
          bool hasRdvType = vetRdvType.attributes['rdvTypeId'] == rdvType.id;
          List<dynamic> availableAnimalTypes =
              vetRdvType.attributes['availableAnimalTypesIds'] as List<dynamic>;
          bool hasAnimalType =
              availableAnimalTypes.contains(_pet.getCaptainVetSpeciesId());

          return hasRdvType && hasAnimalType;
        });
      }).toList();

  List<Resource> get vetsByAnimalTypeAndRdvType => _vets.where((vet) {
        List vetAnimalTypes =
            vet.relationships['animal-types']!.toJson()['data'];
        bool hasAnimalType = vetAnimalTypes.any((vetAnimalType) =>
            vetAnimalType.id == _pet.getCaptainVetSpeciesId());

        List vetVetRdvTypes =
            vet.relationships['vet-rdv-types']!.toJson()['data'];
        bool hasVetRdvType = false;
        for (var vetVetRdvType in vetVetRdvTypes) {
          if (_vetRdvTypesSelected.any((vetRdvTypeSelected) =>
              vetRdvTypeSelected.id == vetVetRdvType.id)) {
            hasVetRdvType = true;
          }
        }

        return hasAnimalType && hasVetRdvType;
      }).toList();

  @override
  void initState() {
    super.initState();
    _sendData['offices'] = widget.clinic.captainvetId;
    CaptainVetApi().getAllRdvTypes().then(
        (values) => setState(() => _allRdvTypes = values.collection.toList()));
    CaptainVetApi().getOffice(id: widget.clinic.captainvetId!).then((values) {
      setState(() {
        _animalTypes = values.included
            .where((element) => element.type == 'animal-types')
            .toList();
        _vetRdvTypes = values.included
            .where((element) => element.type == 'vet-rdv-types')
            .toList();
        _vets =
            values.included.where((element) => element.type == 'vets').toList();
        _rdvExceptions = values.included
            .where((element) => element.type == 'rdv-type-exceptions')
            .toList();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return _CVInherited(
      data: this,
      child: widget.child,
    );
  }

  /// search matching rdv-type + animal-type
  /// if first method has no result -> search in vet-rdv-type with duration not null
  /// if second method has no result -> pick default duration
  int? _retrieveGoodDuration() {
    Resource theOne = _rdvExceptions.singleWhere((rdvException) {
      bool hasRdvType =
          rdvException.attributes['rdvTypeId'] == _sendData['rdv-types'];
      List<dynamic> availableAnimalTypes =
          rdvException.attributes['availableAnimalTypesIds'] as List<dynamic>;
      bool hasAnimalType =
          availableAnimalTypes.contains(_pet.getCaptainVetSpeciesId());

      return hasRdvType && hasAnimalType;
    },
        orElse: () => _vetRdvTypes.singleWhere((vetRdvType) {
              bool hasrdvType =
                  vetRdvType.attributes['rdvTypeId'] == _sendData['rdv-types'];
              bool hasDuration = vetRdvType.attributes['duration'] != null;
              List<dynamic> availableAnimalTypes = vetRdvType
                  .attributes['availableAnimalTypesIds'] as List<dynamic>;
              bool hasAnimalType =
                  availableAnimalTypes.contains(_pet.getCaptainVetSpeciesId());

              return hasrdvType && hasAnimalType && hasDuration;
            },
                orElse: () => _allRdvTypes.singleWhere(
                    (rdvType) => rdvType.id == _sendData['rdv-types'])));

    return theOne.attributes['duration'] as int?;
  }
}
