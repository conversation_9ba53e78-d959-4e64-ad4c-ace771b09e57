import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart' show CupertinoIcons;
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/geolocation.dart';
import 'package:mybuddy/class/kalman_filter.dart';
import 'package:mybuddy/class/location_manager.dart';
import 'package:mybuddy/class/stop_watch.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/unit.dart';
import 'package:mybuddy/models/walk_model.dart';
import 'package:mybuddy/ui/templates/workout/widgets/stacked_pet_images_widget.dart';

import '../class/walk_lock_screen_widget_ios.dart';
import 'walk_provider.dart';

const distanceMinInMeter = 0;
const speedMaxInMilesPerHour = 30;
const speedViolationThresholdInMS = 25000;

enum WalkPauseReason { none, user, speed_limit }

class TrackingProvider extends GetxController implements LocationListener {
  static TrackingProvider of([BuildContext? context]) {
    return Get.find();
  }

  static const String TAG_WALK_SCREEN_LISTENER = "Walk_Screen";

  RxMap<MarkerId, Marker> markers = <MarkerId, Marker>{}.obs;
  RxMap<PolylineId, Polyline> polylines = <PolylineId, Polyline>{}.obs;

  RxBool inNavigationMode = false.obs;
  RxBool isWalking = false.obs;
  RxBool isUploading = false.obs;
  RxDouble speed = (0.0).obs;
  RxDouble distance = (0.0).obs;
  Rx<Marker> _positionMarker = Marker(markerId: MarkerId("")).obs;
  int _overSpeedTimeInMS = 0;
  late WalkProvider _walkState;
  late KalmanFilter kalmanFilter;
  late double kalmanCurrentSpeed;
  late DateTime start;
  // Symbol paw;

  GoogleMapController? _mapController;
  late LocationManager _locationManager;

  UserLocation? _currentPosition;
  UserLocation? _prevPosition;
  late StopWatchWithInitialTime _sWatch;

  late BitmapDescriptor _startingPoint;
  late BitmapDescriptor _currentLocation;

  Rx<WalkPauseReason> walkPausedReason = (WalkPauseReason.none).obs;

  Duration? getDuration() {
    try {
      return _sWatch.elapsedDuration;
    } catch (e) {
      return null;
    }
  }

  String get getDistance {
    return distance.value
        .toUserLength(src: LengthSource.meters)
        .toLocalStringAsFixed(2);
  }

  String get getSpeed {
    return speed.value
        .toUserSpeed()
        .toLocalStringAsPrecision(3);
  }

  String get getTime {
    Duration? elapsed = getDuration();
    String hours = "00";
    String minutes = "00";
    String seconds = "00";

    if (elapsed != null) {
      hours = elapsed.inHours.toString().padLeft(2, '0');
      minutes = (elapsed.inMinutes % 60).toString().padLeft(2, '0');
      seconds = (elapsed.inSeconds % 60).toString().padLeft(2, '0');
    }

    return '$hours:$minutes:$seconds';
  }

  void enableNavigationMode() {
    inNavigationMode.value = true;
  }

  void disableNavigationMode() {
    inNavigationMode.value = false;
  }

  Future<void> onStartPause(
      {AutoEndReason pauseReason = AutoEndReason.noAutoEnd,
      bool tellServer = false}) async {
    /// toggle walking
    if (isWalking.isTrue) {
      // Mark on server that the walk is paused
      if (pauseReason == AutoEndReason.pauseSpeedLimit) {
        walkPausedReason.value = WalkPauseReason.speed_limit;
      } else {
        walkPausedReason.value = WalkPauseReason.user;
      }
      _sWatch.stopTimer();
      LockScreenWalkWidget.updateWalkStats(getDistance, getTime, getSpeed, WalkState.paused);
      if (tellServer == true) {
        await _pauseWalk(pauseReason);
      }
    } else {
      // Mark on server that the walk is started
      walkPausedReason.value = WalkPauseReason.none;
      _sWatch.startTimer();
      if (tellServer == true) {
        await _resumeWalk();
      }
    }
    // Resetting it to 0, as the user is pausing or resuming a walk
    _overSpeedTimeInMS = 0;
    isWalking(!isWalking.value);
  }

  Future<void> _resumeWalk() async {
    await _walkState.resumeWalk();
  }

  Future<void> startWalk() async {
    if (!_sWatch.isTimerRunning()) {
      _sWatch.startTimer();
    }
    await _walkState.startWalk();
    LockScreenWalkWidget.initiateWidgetAndStartWalk();
  }

  Future<void> _pauseWalk(AutoEndReason pauseReason) async {
    await _walkState.pauseWalk(pauseReason);
  }

  Future<void> finishWalk() async {

    if (isWalking.isTrue) {
      onStartPause();
    }

    if (_workoutIsNotValid()) {
      Tools().common.showCustomDialogWithThreeButtons(
          prefixContent: [
            Image.asset(
              "assets/images/ic_no_movement.png",
              height: 50,
              width: 46,
            ),
            const SizedBox(height: 8,),
          ],
          title: "No Movement Detected",
          thirdButtonColor: const Color(0xff419563),
          secondButtonColor: const Color(0xff419563),
          firstTitle: 'APPLICATION_MOBILE_BUTTON_FINISH_SAVE_WALK'
              .tr("Finish & Save"),
          secondTitle: 'APPLICATION_MOBILE_BUTTON_RESUME_WALK'
              .tr("Resume Walk"),
          thirdTitle: 'APPLICATION_MOBILE_BUTTON_DISCARD_WALK'
              .tr("Discard Walk"),
          onFirst: () async {
            await _finish(AutoEndReason.normal);
          },
          onSecond: () {
            onStartPause();
          },
          onThird: () {
            _discardWorkout();
          }
      );
    } else {
      Tools().common.showCustomDialog(
          prefixContent: [
            PetStackedAvatar(pets: _walkState.walk.pets),
            const SizedBox(height: 8,),
          ],
          title: "Are you sure you want to end this walk?",
          description: "Your walking activity will be saved and visible in your walk history.",
          buttonColor: const Color(0xff419563),
          validTitle: 'APPLICATION_MOBILE_BUTTON_YES_FINISH_WALK'
              .tr("Yes, Finish"),
          rejectTitle: 'APPLICATION_MOBILE_BUTTON_LABEL_CANCEL'
              .tr("Cancel"),
          onValid: () async {
            await _finish(AutoEndReason.normal);
          },
          onReject: () {
          }
      );
    }
  }

  Future<void> _finish(AutoEndReason autoEndReason) async {

    isUploading.value=true;
    cancelListeners();

    Tools.debugPrint('workout is valid. Now saving');

    await _walkState.finishWalk(autoEndReason);

    clearData();

    LockScreenWalkWidget.dismissWidget();
  }

  Future<void> _discardWorkout() async {

    isUploading.value=true;
    cancelListeners();

    Tools.debugPrint('Discarding workout!');

    bool success = await _walkState.deleteWalk();

    clearData();

    LockScreenWalkWidget.dismissWidget();
  }

  void onMapCreated(GoogleMapController controller) async {
    _mapController = controller;
    //to remove first wrong value (android ????)
    await _drawPathOfOngoingWalkOnMap();
  }

  Marker _addMarker(LatLng position, String id, BitmapDescriptor descriptor,
      {double rotation = 0}) {
    MarkerId markerId = MarkerId(id);
    Marker marker = Marker(
        markerId: markerId,
        icon: descriptor,
        position: position,
        rotation: rotation);
    markers[markerId] = marker;
    return marker;
  }

  Map getPauseBanner(){
    Map pauseBanner = {};

    if (walkPausedReason.value == WalkPauseReason.user) {
      pauseBanner = {
        "bgColor": const Color(0xffF3AE56),
        "text": "APPLICATION_MOBILE_TEXT_WALK_PAUSE_BY_USER".tr()
      };
    } else if (walkPausedReason.value ==
        WalkPauseReason.speed_limit) {
      pauseBanner = {
        "bgColor": const Color(0xffE14646),
        "text": "APPLICATION_MOBILE_TEXT_WALK_PAUSE_BY_SPEED_LIMIT".tr(),
        "prefixIcon": CupertinoIcons.exclamationmark_circle_fill,
      };
    }

    return pauseBanner;
  }

  @override
  void dispose() {
    cancelListeners();
    _mapController?.dispose();
    Tools().adMobTools.disposeInterstitialAd();
    _sWatch.dispose();
    super.dispose();
  }

  LatLng _positionToLatLng(UserLocation position) => LatLng(
        position.latitude!,
        position.longitude!,
      );

  void _reDrawTrack() {
    polylines.value = {};
    _walkState.walk.positionsWithTracks.forEach((key, value) {
      PolylineId id = PolylineId("polyline$key");

      List<LatLng> points = value.map((e) => _positionToLatLng(e)).toList();

      Polyline newLine = Polyline(
          polylineId: id,
          points: points,
          color: const Color(0xfffdc635),
          visible: true,
          width: 8);

      polylines[id] = newLine;
    });
    polylines.refresh();
  }

  void _addStartPoint(UserLocation position) {
    if (_walkState.walk.positionsWithTracks.isEmpty) {
      // First location point
      _walkState.addDataToOngoingWalk(position, 0, 0, 0);

      _addMarker(_positionToLatLng(position), "starting_point", _startingPoint);
    }
  }

  Future<void> _addPoint(UserLocation position) async {
    if (_walkState.walk.positionsWithTracks.isEmpty) return;

    _reDrawTrack();

    await _movePointer(position);

    speed.value = position.speed ?? 0.0;

    if (_walkState.walk.positions.isNotEmpty) {
      double diff = GeoLocation.distanceBetweenInMeters(
          _walkState.walk.positions.last, position);
      distance.value += diff;
    }

    // Storing the user location in local storage for future use
    _walkState.addDataToOngoingWalk(
        position, distance.value, _sWatch.elapsedMillis, speed.value);
  }

  /// This method has been ported from the old xamarin app to run with
  /// the Kalman Filter that is used to fix the geo locations
  UserLocation _getPredictedLocation(UserLocation position) {
    /// start kalman filter process
    int elapsedTimeInMillis =
        position.time!.toInt() - start.millisecondsSinceEpoch;
    double qValue = 3;
    if (kalmanCurrentSpeed != 0) {
      qValue = kalmanCurrentSpeed;
    }

    ///for next iteration
    kalmanCurrentSpeed = position.speed ?? 0.0;
    kalmanFilter.process(
      position.latitude!,
      position.longitude!,
      position.accuracy!,
      elapsedTimeInMillis,
      qValue,
    );
    double predictedLat = kalmanFilter.getLat();
    double predictedLng = kalmanFilter.getLng();

    UserLocation predictedLocation = UserLocation(
      longitude: predictedLng,
      latitude: predictedLat,
      altitude: position.altitude,
      bearing: position.bearing,
      accuracy: position.accuracy,
      time: position.time,
      speed: position.speed,
      isMock: position.isMock,
    );

    double predictedDeltaInMeters =
        GeoLocation.distanceBetweenInMeters(predictedLocation, position);
    if (predictedDeltaInMeters > 60) {
      kalmanFilter.consecutiveRejectCount += 1;

      if (kalmanFilter.consecutiveRejectCount > 3) {
        kalmanFilter = KalmanFilter(); //reset Kalman Filter if it rejects more than 3 times in raw.
      }
    } else {
      kalmanFilter.consecutiveRejectCount = 0;
    }

    UserLocation newLocation =
        GeoLocation.createLocation(predictedLocation, _prevPosition);
    _prevPosition = predictedLocation;

    return newLocation;
  }

  Future<void> init() async {
    _walkState = WalkProvider.of();
    _locationManager = LocationManager();

    _startingPoint = await BitmapDescriptor.fromAssetImage(
        ImageConfiguration(
            size: const Size(12, 12),
            devicePixelRatio: MediaQuery.of(Get.context!).devicePixelRatio),
        Platform.isAndroid
            ? 'assets/images/woof_marker_green.png'
            : 'assets/images/ic_woof_marker_green.png');

    _currentLocation = await BitmapDescriptor.fromAssetImage(
        ImageConfiguration(
            size: const Size(10, 10),
            devicePixelRatio: MediaQuery.of(Get.context!).devicePixelRatio),
        Platform.isAndroid
            ? 'assets/icon/current_location.png'
            : 'assets/icon/ic_current_location.png');

    if (isFreshWalk()) {
      isWalking.value = true;
      speed.value = 0.0;
      distance.value = 0.0;
      _overSpeedTimeInMS = 0;

      start = DateTime.now();
      kalmanCurrentSpeed = 0;
      kalmanFilter = KalmanFilter();

      _sWatch = StopWatchWithInitialTime(onTimeTick: onTimerTick);
      _resumeListeners();
    } else {
      // This checks is for making sure that the walk is not already in progress
      // therefore we don't want to reset the stop watch, and use it as is.
      // If it is false, this means the walk is resumed after the app was killed,
      // and we set the milliseconds to the stop watch timer.
      if (isWalking.isFalse) {
        _sWatch = StopWatchWithInitialTime(onTimeTick: onTimerTick);
        _sWatch.milliseconds = _walkState.walk.duration;

        start = DateTime.now();
        kalmanCurrentSpeed = 0;
        kalmanFilter = KalmanFilter();

        _resumeListeners();
      }

      speed.value = _walkState.walk.speed;
      distance.value = _walkState.walk.distance;
    }

    enableNavigationMode();

    await Tools().adMobTools.loadInterstitialAd();
  }

  /// This method tells if the walk is fresh by checking the walk status
  /// it should be unknown
  bool isFreshWalk() {
    // This means that the walk is fresh
    return _walkState.walk.status == Status.unknown;
  }

  /// This method is called when the walk screen is no longer active
  Future<void> walkPageDisposed() async {
    _mapController = null;
  }

  void onTimerTick(int time) {
    if (isWalking.isTrue) {
      Tools.debugPrint("Timer tick $time");
      _walkState.addDurationToOngoingWalk(_sWatch.elapsedMillis);
      LockScreenWalkWidget.updateWalkStats(getDistance, getTime, getSpeed, WalkState.started);
    }
  }

  /// This method is responsible for restoring an already started walk
  Future<void> _drawPathOfOngoingWalkOnMap() async {
    Map<String,List<UserLocation>> samplePoints = _walkState.walk.positionsWithTracks;
    Tools.debugPrint("asim sample points size ${samplePoints.length}");
    if (samplePoints.isNotEmpty) {
      await _populateOngoingWalkLocationPoints(samplePoints);
    }
    Tools.debugPrint("Walk restored");
  }

  /// This method is responsible for filling the location points of an already
  /// started walk in maps to draw lines
  Future<void> _populateOngoingWalkLocationPoints(
      Map<String,List<UserLocation>> positions) async {

    UserLocation first = positions[positions.keys.first]!.first;

    _addMarker(_positionToLatLng(first), "starting_point", _startingPoint);

    _reDrawTrack();

    update();
  }

  Future<void> _onLocationUpdate(UserLocation position) async {
    if (isWalking.isTrue) {
      UserLocation predictedLocation = _getPredictedLocation(position);

      if (predictedLocation.accuracy != -1) {
        _addCurrentPositionMarker(predictedLocation);

        _addStartPoint(predictedLocation);

        _currentPosition = predictedLocation;

        await _addPoint(predictedLocation);

        _checkSpeed(predictedLocation.speed);
      }
    } else {

      _addCurrentPositionMarker(position);

      _movePointer(position);
    }
    update();
  }

  void _addCurrentPositionMarker(UserLocation position) {
    if (_positionMarker.value.markerId.value.isEmpty) {
      _positionMarker.value =
          _addMarker(_positionToLatLng(position), "current", _currentLocation);
    }
  }

  Future<void> _movePointer(UserLocation position) async {
    if (_positionMarker.value.markerId.value.isNotEmpty) {
      _addMarker(_positionToLatLng(position),
          _positionMarker.value.mapsId.value, _positionMarker.value.icon,
          rotation: position.bearing ?? 0);
    }

    if (inNavigationMode.isTrue) {
      await _mapController
          ?.moveCamera(CameraUpdate.newLatLng(_positionToLatLng(position)));
    }
  }

  void _checkSpeed(double? currentSpeedInMeters) {
    if (currentSpeedInMeters == null) {
      return;
    }
    bool overSpeeding = false;
    double currentSpeedInMiles = currentSpeedInMeters * 2.237;
    if (currentSpeedInMiles < speedMaxInMilesPerHour) {
      // 45 mph
      _overSpeedTimeInMS = 0;
      return;
    } else {
      if (_overSpeedTimeInMS == 0) {
        _overSpeedTimeInMS = DateTime.now().millisecondsSinceEpoch;
        Tools.debugPrint(
            "Speed limit violated, time started: $_overSpeedTimeInMS");
      } else {
        if (DateTime.now().millisecondsSinceEpoch - _overSpeedTimeInMS >
            speedViolationThresholdInMS) {
          overSpeeding = true;
        }
      }
    }

    /// end reasons
    if (isWalking.isTrue && overSpeeding) {
      onStartPause(
          pauseReason: AutoEndReason.pauseSpeedLimit, tellServer: true);
    }
  }

  bool _workoutIsNotValid() {
    return distance <= distanceMinInMeter;
  }

  void cancelListeners() {
    Tools.debugPrint('cancel listeners');
    _locationManager.unregisterListener(TAG_WALK_SCREEN_LISTENER);
    _locationManager.disableBackgroundMode();
  }

  Future<void> _resumeListeners() async {
    Tools.debugPrint('resume listeners');
    _locationManager.registerListener(TAG_WALK_SCREEN_LISTENER, this);
    await _locationManager.enableBackgroundMode();
  }

  void clearData() {
    polylines.value = {};
    polylines.refresh();
    markers.value = {};
    markers.refresh();

    isWalking.value = false;
    isUploading.value=false;
    _currentPosition = null;
    _prevPosition = null;
    _sWatch.resetTimer();
    walkPausedReason.value = WalkPauseReason.none;
  }

  void checkInAppOngoingWalk() {
    if (isWalking.isFalse) {
      cancelListeners();
    }
    _walkState.refresh();
  }

  @override
  void onUserLocationError(err) {
    Tools.debugPrint("Walk map eror ${err.toString()}");
  }

  @override
  void onUserLocationUpdated(UserLocation location) {
    _onLocationUpdate(location);
  }
}
