import 'dart:async';

import 'package:mybuddy/models/pet.dart';
import 'package:rxdart/rxdart.dart';

import 'bloc.dart';

class PetBloc extends Bloc {
  final StreamController<Pet?> _streamController = BehaviorSubject();

  final int petId;

  PetBloc({required this.petId}) : super();

  Sink<Pet?> get sink => _streamController.sink;

  Stream<Pet?> get stream => _streamController.stream;

  @override
  void dispose() {
    subscription!.cancel();
    _streamController.close();
  }

  @override
  void update() {
    if (!_streamController.isClosed) {
      sink.add(data.get().getPet(petId));
    }
  }
}
