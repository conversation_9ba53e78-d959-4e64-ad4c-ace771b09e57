import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';

class MbApiShelter extends MbBaseApi {
  Future<MBResponse> getShelters({int page = 1, int? perPageCount, String? terms}) async {
    String route = 'Shelter/get?page=$page&perPage=$perPageCount';

    Map<String, dynamic> _body = <String, dynamic>{
      'terms': terms
    };

    MBResponse response = await dioRequest(null, route, _body);

    if (response.success) {
      return response;
    } else {
      return Future.error(response.errorMessage);
    }
  }
}
