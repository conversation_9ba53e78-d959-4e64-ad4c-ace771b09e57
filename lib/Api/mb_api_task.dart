import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/offline_service.dart';
import 'package:mybuddy/models/petcarepacks.dart';
import 'package:mybuddy/models/workout.dart';

class MbApiTask extends MbBaseApi {

  Future<MBResponse> setAnnouncementRead(int? id) async {
    return await _taskRequest('Task/announcement/setRead', id);
  }

  Future<MBResponse> setPetCarePackRead(PetCarePack? petCarePack) async {
    if(petCarePack == null) {
      return MBResponse(null);
    }
    return await _taskRequest('Task/petCarePack/setRead', petCarePack.id);
  }

  Future<MBResponse> vetCall(int id) async {
    return await _taskRequest('Task/clinic/call', id);
  }

  Future<MBResponse> _taskRequest(String route, int? id) async {
    Map<String, dynamic> body = {
      'id': id,
      'releaseDate': Data.dateTimeToApiDateTimeStr(DateTime.now(), toUtc: true),
    };

    if (await Tools().common.isOnline()) {
      MBResponse response = await dioRequest(null, route, body);

      if (response.success) {
        Tools.debugPrint('task request is successful');
        Data().updateTasks(response.body['tasks']);
      } else {
        Tools.debugPrint(response.body['message']);
      }

      return response;
    } else {
      OfflineService.storeUndoneJob(route, body);
    }

    return MBResponse(null);
  }

  Future<MBResponse> doubleWorkoutPoint(Workout workout) async {
    String route = 'Task/workout/double';
    Map<String, dynamic> body = {
       'id': workout.id,
    };

    if (await Tools().common.isOnline()) {
      MBResponse response = await dioRequest(null, route, body);

      if (response.success) {

      }

      return response;
    } else {
      OfflineService.storeUndoneJob(route, body);
    }

    return MBResponse(null);
  }
}
