import 'package:flutter/material.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/models/service.dart';

class MbApiPims extends MbBaseApi {

  Future<MBResponse> askVetopartnerConsent(BuildContext context, MBService service) async {
    String route = 'Service/reconciliation_consent';

    MBResponse response = await dioRequest(context, route, {'serviceId': service.id});
//    if (response.success) {
//    }

    return response;
  }

  Future<MBResponse> getPimsState(BuildContext context, MBService service) async {
    String route = 'Pet/reconciliate';

    MBResponse response = await dioRequest(context, route, {'serviceId': service.id});
//    if (response.success) {
//    }

    return response;
  }



  Future<MBResponse> userReconciliationRequest(BuildContext context, MBService service,
      {String? pimsOwnerId, String? phone, String? email}) async {
    String route = 'User/reconciliate';
    Map<String, dynamic> body = {'serviceId': service.id};
    if (pimsOwnerId != null && pimsOwnerId != '') {
      body['pimsOwnerId'] = pimsOwnerId;
    }
    if (phone != null && phone != '') {
      body['phone'] = phone;
    }
    if (email != null && email != '') {
      body['email'] = email;
    }

    MBResponse response = await dioRequest(context, route, body);
//    if (response.success) {
//    }

    return response;
  }
}
