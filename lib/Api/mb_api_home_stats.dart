import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';

class MbApiHomeStats extends MbBaseApi {
  Future<MBResponse> getHomeStats(String date) async {
    String route = 'User/home-screen-stats';

    Map<String, dynamic> _body = <String, dynamic>{'date': date};

    MBResponse response = await dioRequest(null, route, _body);

    if (response.success) {
      return response;
    } else {
      return Future.error(response.errorMessage);
    }
  }
}
