import 'package:flutter/material.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/service.dart';

class MbApiService extends MbBaseApi{


  Future<MBResponse> addAffiliateClinicRequest(BuildContext? context, String code) async {
    String route = 'Service/link';
    MBResponse response = await dioRequest(context, route, {'code': code});


    if (response.success) {
      MBService service = MBService.fromJson(response.body['service']);
      if(!Data().get().services.contains(service)) {
        Data().get().services.add(service);
        Data().storeData();
      }
    }

    return response;
  }

  Future<MBResponse> addPrivateClinicRequest(BuildContext context, MBService _service) async {
    String route = 'Service/add';
    Map<String, dynamic> _body = _service.toJson(updated: false);

    _body['releaseDate'] = Data.dateTimeToApiDateTimeStr(DateTime.now(), toUtc: true);

    if(await Tools().common.isOnline(alertContext: context)) {
      MBResponse response = await dioRequest(context, route, _body);

      if (response.success) {
        if(response.body.containsKey('tasks') && response.body['tasks'] != null) {
          Data().updateTasks(response.body['tasks']);
        }
        MBResponse retrieveService = await dioRequest(
          context,
          'Service/get',
          {'id': response.body['id']},
        );
        if(retrieveService.success) {
          Data().get().services.add(MBService.fromJson(retrieveService.body['service']));
        }
        Data().storeData();
      }

      return response;
    }

    return MBResponse(null);
  }

  Future<MBResponse> editPrivateClinicRequest(BuildContext context, MBService _service) async {
    String route = 'Service/update';
    Map<String, dynamic> body = _service.toJson();

    MBResponse response = await dioRequest(context, route, body);

    if (response.success) {
      Data().get().services.removeWhere((item) => item.id == _service.id);
      Data().get().services.add(MBService.fromJson(response.body['service']));

      Data().storeData();
    }

    return response;
  }



  Future<MBResponse> getClinicWithCodeRequest(BuildContext? context, String code) async {
    String route = 'Service/checkCode';

    MBResponse response = await dioRequest(context, route, {'code': code});

//    if (response.success) {
//    }

    return response;
  }

  Future<MBResponse> removeClinicRequest(BuildContext context, MBService _service) async {
    String route = 'Service/remove';

    MBResponse response = await dioRequest(context, route, {'id': _service.id});

    if (response.success) {
      Data().get().services.removeWhere((s) => s.id == _service.id);
      Data().storeData();
    }

    return response;
  }
}