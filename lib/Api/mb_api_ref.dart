import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/class/settings_delegate.dart';

class MbApiRef extends MbBaseApi {
  Future<MBResponse> getPetCarePackRequest(
      BuildContext context, int? id) async {
    String route = 'PetCarePack/get';

    MBResponse response = await dioRequest(context, route, {'id': id});
//    if (response.success) {
//    }

    return response;
  }

  Future<MBResponse> refRequest(BuildContext? context) async {
    String route = 'References/all';
    SettingsDelegate settingsDelegate = SettingsDelegate();
    Map<String, dynamic> body = {
      'countryShortLabel': settingsDelegate.getLang().toLowerCase()
    };
    MBResponse response = await dioRequest(context, route, body,
        tokened: false, reducedTimeout: true);
    if (response.success) {
      var _refs = jsonEncode(response.body);
      await Ref().setRefs(_refs);
      unawaited(SettingsDelegate().prefs.setString('refs', _refs));
      Data().sendEvent();
    } else {
      await Ref().setRefs(null);
    }

    return response;
  }

  Future<MBResponse> refSettingsRequest(BuildContext? context) async {
    String route = 'References/open-app-settings';

    MBResponse response = await dioRequest(context, route, {},
        tokened: false, reducedTimeout: true);
    if (response.success) {
      var _refs = jsonEncode(response.body);
      await Data().setRefSettings(_refs);
      unawaited(SettingsDelegate().prefs.setString('refs-settings', _refs));
    } else {
      await Data().setRefSettings(null);
    }

    return response;
  }
}
