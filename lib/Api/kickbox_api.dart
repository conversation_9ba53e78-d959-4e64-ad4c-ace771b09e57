import 'package:mybuddy/class/constants.dart';
import 'package:mybuddy/models/ke_email_verification_response.dart';

import 'base_api.dart';

class KickBoxApi extends MbBaseApi {
  final String apiKey = kickboxAPIKey;
  final String baseUrl = "https://api.kickbox.com/v2";

  Future<KBEmailVerificationResponse> verifyEmail(String email) async {
    Map<String, dynamic> _queryParams = <String, dynamic>{
      'email': email,
      'apikey': apiKey,
    };

    final response = await dioOtherServerGetRequest(
      '$baseUrl/verify',
      queryParams: _queryParams,
    );

    if (response.statusCode == 200) {
      var data = KBEmailVerificationResponse.fromJson(response.body);
      return data;
    } else {
      throw Exception('Failed to verify email');
    }
  }
}
