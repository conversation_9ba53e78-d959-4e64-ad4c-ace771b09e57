import 'package:flutter/material.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/power.dart';
import 'package:mybuddy/models/walk_model.dart';
import 'package:mybuddy/models/workout.dart';

class MbApiWorkout extends MbBaseApi {
  Future<MBResponse> add(BuildContext? context, Status newStatus,
      Map<String, dynamic> body) async {
    String route = 'Workout/add';
    Map<String, dynamic> _body = body;

    _body['releaseDate'] =
        Data.dateTimeToApiDateTimeStr(DateTime.now(), toUtc: true);
    _body['powerSaverStatus'] = await Power.isLowPowerMode;

    if (await Tools().common.isOnline()) {
      MBResponse response = await dioRequest(context, route, _body);

      if (response.success) {
        Workout w = Workout.fromJson(response.body['workout']);
        response.entity = w;
        // If walk is finished then we only add it in the local workout list
        if (newStatus == Status.saved) {
          if (double.parse(_body["distance"].toString()) > 0.0) {
            Data().get().workouts.add(w);
          }
          if (response.body.containsKey('tasks') &&
              response.body['tasks'] != null) {
            Data().updateTasks(response.body['tasks']);
          }

          Data().storeData();
        }

        return response;
      }
    }

    return MBResponse();
  }

  Future<MBResponse> getWorkoutHistory({
    String? startDate,
    String? endDate,
    List<int>? moodType,
    required int page,
  }) async {
    String route = 'Workout/workouts?page=$page';

    Map<String, dynamic> _body = <String, dynamic>{};

    _body["startDate"] = startDate;
    _body["endDate"] = endDate;
    _body["recordsPerPage"] = 50;

    if (moodType != null) {
      for (int i = 0; i < moodType.length; i++) {
        _body["moodType[$i]"] = moodType[i];
      }
    }

    MBResponse response = await dioRequest(null, route, _body);

    return response;
  }

  Future<MBResponse> deleteWorkout(
      BuildContext? context, Workout _workout) async {
    String route = 'Workout/delete';

    MBResponse response = await dioRequest(context, route, {'id': _workout.id});

    if (response.success) {
      Data().get().workouts.removeWhere((item) => item.id == _workout.id);
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse> workoutDetails(int workoutId) async {
    String route = 'Workout/detail';
    Map<String, dynamic> body = {'id': workoutId};
    MBResponse res = await dioRequest(null, route, body);
    return res;
  }
}
