import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/class/app_storage.dart';
import 'package:mybuddy/class/constants.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/offline_service.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/owner.dart';

class MbApiOwner extends MbBaseApi {
  Future<MBResponse> refreshLoginRequest([
    BuildContext? context,
  ]) async {
    //TODO LOW change login/password with jwt token

    Tools.debugPrint('🔑');
    String? login = SettingsDelegate().prefs.getString('login');
    String? password = SettingsDelegate().prefs.getString('password');
    String? facebookToken = SettingsDelegate().prefs.getString('facebookToken');
    String? appleToken = SettingsDelegate().prefs.getString('appleToken');
    String? googleToken = SettingsDelegate().prefs.getString('googleToken');

    if (login != null && password != null) {
      Tools.debugPrint('use regular');
      return await loginRequest(context, login, password);
    } else if (login != null && facebookToken != null) {
      Tools.debugPrint('use facebook');
      return await facebookConnectRequest(
          context, Data().get().owner, facebookToken);
    } else if (login != null && appleToken != null) {
      Tools.debugPrint('use apple');
      return await appleConnectRequest(context, Data().get().owner, appleToken);
    } else if (login != null && googleToken != null) {
      Tools.debugPrint('use google');
      return await googleConnectRequest(
        context,
        Data().get().owner,
        googleToken,
      );
    }
    Tools.debugPrint('use none');
    // Tools.debugPrint(login);
    // Tools.debugPrint(password);
    // Tools.debugPrint(facebookToken);
    // Tools.debugPrint(appleToken);

    return MBResponse(null);
  }

  Future<MBResponse> checkEmailRequest(
      BuildContext? context, String email) async {
    String route = 'User/checkEmail';
    Map<String, dynamic> body = {'email': email};
    MBResponse response =
        await dioRequest(context, route, body, tokened: false);

    return response;
  }

  Future<MBResponse> loginRequest(
      BuildContext? context, String login, String password,
      {bool refresh = true}) async {
    String route = 'User/login';
//    route = 'User/backdoor';
//    password = 'db49dccb9ce6ff0985194d05e2f12e5d8b60b119eb68f1feb0f8183528f09a4f';
    Map<String, dynamic> body = {
      'email': login.trim(),
      'password': password,
      'refresh': refresh
    };
    body['applicationId'] = AppConfig.of(context).appId;
    body.addAll(await Tools.deviceDataForCIO());

    MBResponse response =
        await dioRequest(context, route, body, tokened: false);
    if (response.success) {
      await setLoginData(response, login, password: password);

      // Add user to the pending packs if any
      await Tools().team.addMeToTeams();
    }

    return response;
  }

  Future<MBResponse> logoutRequest(BuildContext context) async {
    String route = 'User/logout';

    String? pushToken = SettingsDelegate().prefs.getString('pushToken');

    //TODO LOW facebook logout
    MBResponse response =
        await dioRequest(null, route, {'pushToken': pushToken}, tokened: true);
    // if (response.success) {
    //   unawaited(SettingsDelegate().prefs.remove('data'));
    //   unawaited(SettingsDelegate().prefs.remove('pushToken'));
    //   unawaited(SettingsDelegate().prefs.remove('password'));
    //   unawaited(SettingsDelegate().prefs.remove('token'));
    //   unawaited(SettingsDelegate().prefs.remove('facebookToken'));
    //   unawaited(SettingsDelegate().prefs.remove('appleToken'));
    // }

    ///logout even if it fails
    ///todo improve by accepting only the fails for not valid token
    response.success = true;
    unawaited(SettingsDelegate().prefs.remove('data'));
    unawaited(SettingsDelegate().prefs.remove('pushToken'));
    unawaited(SettingsDelegate().prefs.remove('password'));
    unawaited(SettingsDelegate().prefs.remove('token'));
    unawaited(SettingsDelegate().prefs.remove('facebookToken'));
    unawaited(SettingsDelegate().prefs.remove('appleToken'));
    unawaited(SettingsDelegate().prefs.remove('googleToken'));
    unawaited(SettingsDelegate().prefs.remove(isFirstLogin));
    unawaited(Tools().google.logout());
    unawaited(SettingsDelegate().prefs.remove(ongoingWalk));
    unawaited(AppStorage().deleteAllGpx());
    // Logging out facebook
    Tools().facebook.logout();
    OfflineService.clear();
    Tools().cioTools.clear();

    return response;
  }

  Future<MBResponse> recoverPasswordRequest(
      BuildContext context, String login) async {
    String route = 'User/recoverPassword';

    MBResponse response =
        await dioRequest(context, route, {'email': login}, tokened: false);
    if (response.success) {
      showMessage(context,
          message: 'APPLICATION_MOBILE_MESSAGE_NEW_PASSWORD_SEND'.tr());
    }

    return response;
  }

  Future<MBResponse> forgotPasswordRequest(BuildContext? context, String email,
      {String? successMsg}) async {
    String route = 'User/forgot-password';

    MBResponse response = await dioRequest(context, route, {'email': email},
        tokened: false, showSuccessMsg: false);

    return response;
  }

  Future<MBResponse> resetPasswordRequest(
      BuildContext context, Map<String, dynamic> body) async {
    String route = 'User/reset-password';

    MBResponse response = await dioRequest(context, route, body,
        tokened: false, showSuccessMsg: false);

    return response;
  }

  Future<MBResponse> resendRequest(
      BuildContext? context, String email, String linkType,
      {String? successMsg}) async {
    String route = 'User/resend-link';

    MBResponse response = await dioRequest(
        context, route, {"email": email, "linkType": linkType},
        tokened: false, showSuccessMsg: false);

    if (response.success) {
      showMessage(context, message: successMsg ?? response.body["messages"]);
    }

    return response;
  }

  /// This api is used to attempt the change email link different times
  Future<MBResponse> resendChangeEmailLinkRequest(
      BuildContext? context, String oldEmail, String newEmail,
      {String? successMsg}) async {
    String route = 'User/resend-link-change-email';

    MBResponse response = await dioRequest(
        context, route, {"oldEmail": oldEmail, "newEmail": newEmail},
        showSuccessMsg: false);

    if (response.success) {
      showMessage(context, message: successMsg ?? response.body["messages"]);
    }

    return response;
  }

  Future<MBResponse> facebookConnectRequest(
      BuildContext? context, Owner owner, String facebookToken,
      {bool refresh = true}) async {
    String route = 'User/facebookConnect';
    var _body = owner.toApiRegisterJson();
    _body['facebookAccessToken'] = facebookToken;
    _body['applicationId'] = AppConfig.of(context).appId;
    _body['refresh'] = refresh;
    _body.addAll(await Tools.deviceDataForCIO());
    MBResponse response =
        await dioRequest(context, route, _body, tokened: false);

    if (response.success) {
      await setLoginData(response, owner.email, facebookToken: facebookToken);
    }

    return response;
  }

  Future<MBResponse> registerRequest(
      BuildContext context, Owner owner, String? password) async {
    String route = 'User/register';
    var _body = owner.toApiRegisterJson();
    _body['password'] = password;
    _body['applicationId'] = AppConfig.of(context).appId;
    _body.addAll(await Tools.deviceDataForCIO());
    MBResponse response = await dioRequest(context, route, _body,
        tokened: false, showLoading: true);

    if (response.success) {
      Tools().common.showMessage(context, response.body["messages"]);
    }

    return response;
  }

  Future<MBResponse> googleConnectRequest(
      BuildContext? context, Owner owner, String? accessToken,
      {bool refresh = true}) async {
    const String _route = 'User/googleConnect';
    final Map<String, dynamic> _body = owner.toApiRegisterJson();
    _body['googleAccessToken'] = accessToken;
    _body['applicationId'] = AppConfig.of(context).appId;
    _body['refresh'] = refresh;
    _body.addAll(await Tools.deviceDataForCIO());

    MBResponse res = await dioRequest(context, _route, _body, tokened: false);
    if (res.success) {
      await setLoginData(res, owner.email, googleToken: accessToken);
    }
    return res;
  }

  Future<MBResponse> appleConnectRequest(
      BuildContext? context, Owner owner, String? appleToken,
      {bool refresh = true}) async {
    String route = 'User/appleConnect';
    var _body = owner.toApiRegisterJson();
    _body['appleAccessToken'] = appleToken;
    _body['applicationId'] = AppConfig.of(context).appId;
    _body['refresh'] = refresh;
    _body.addAll(await Tools.deviceDataForCIO());
    MBResponse response =
        await dioRequest(context, route, _body, tokened: false);

    if (response.success) {
      await setLoginData(response, owner.email, appleToken: appleToken);
    }

    return response;
  }

  Future<MBResponse> changePasswordRequest(
      BuildContext? context, String password) async {
    String route = 'User/changePassword';
    Map<String, dynamic> body = {'newPassword': password};
    MBResponse response = await dioRequest(context, route, body);

    if (response.success) {
      unawaited(SettingsDelegate().prefs.setString('password', password));
    }

    return response;
  }

  /// This api will request a new email change link to be sent to the new email
  Future<MBResponse> changeEmailRequest(
      BuildContext? context, String email, String password) async {
    String route = 'User/change-email-request';
    Map<String, dynamic> body = {
      'oldEmail': Data().get().owner.email,
      "newEmail": email,
      'password': password
    };
    MBResponse response = await dioRequest(context, route, body);

    return response;
  }

  Future<void> setLoginData(
    MBResponse response,
    String? login, {
    String? password,
    String? facebookToken,
    String? appleToken,
    String? googleToken,
  }) async {
    if (!response.success) return;

    String token = response.body['token'];
    unawaited(SettingsDelegate().prefs.setString('token', token));
    if (login != null) {
      unawaited(SettingsDelegate().prefs.setString('login', login));
    }
    if (password != null) {
      unawaited(SettingsDelegate().prefs.setString('password', password));
    }
    if (facebookToken != null) {
      unawaited(
          SettingsDelegate().prefs.setString('facebookToken', facebookToken));
    }
    if (appleToken != null) {
      unawaited(SettingsDelegate().prefs.setString('appleToken', appleToken));
    }
    if (googleToken != null) {
      unawaited(SettingsDelegate().prefs.setString('googleToken', googleToken));
    }

    if (MbBaseApi.debug) {
      Tools.debugPrint('token: $token');
    }
    String dataJson = response.dataString!;
    unawaited(SettingsDelegate().prefs.setString('data', dataJson));
    Data().setData(dataJson);

    if (response.body['owner']['firstLoginDate'] == null) {
      SettingsDelegate().prefs.setBool(isFirstLogin, true);
    }
  }

  Future<MBResponse> updateUserRequest(BuildContext? context,
      {Owner? owner, File? avatar}) async {
    String route = 'User/update';
    Map<String, dynamic> body;
    if (owner == null) {
      body = Data().get().owner.toApiUpdateJson();
    } else {
      body = owner.toApiUpdateJson();
    }

    if (avatar != null) {
      body['image'] = await createDioMultipartFile(avatar);
    }

    MBResponse response = await dioRequest(context, route, body);

    if (response.success) {
      owner = Owner.fromJson(response.body['owner']);
      Data().get().owner = owner;
      Data().storeData();
    }

    return response;
  }

  /// This api sets the password of a WoofTrax user
  Future<MBResponse> initializeWtUserPassword(
      BuildContext? context, Map<String, dynamic> body) async {
    String route = 'User/initializeWtUserPassword';

    MBResponse response =
        await dioRequest(context, route, body, tokened: false);

    return response;
  }

  /// This api confirms the email of a WoofTrax user
  Future<MBResponse> userEmailVerification(
      BuildContext? context, String code, String email) async {
    String route = 'User/email-verification';

    Map<String, dynamic> body = {
      'code': code,
      'email': email,
    };
    MBResponse response = await dioRequest(context, route, body,
        showLoading: false, tokened: false);

    return response;
  }

  /// This api confirms the changed email of a WoofTrax user
  Future<MBResponse> userChangedEmailVerification(BuildContext? context,
      String code, String oldEmail, String newEmail) async {
    String route = 'User/change-email';

    Map<String, dynamic> body = {
      'code': code,
      'oldEmail': oldEmail,
      'newEmail': newEmail,
    };
    MBResponse response =
        await dioRequest(context, route, body, showLoading: false);

    if (response.success) {
      // After successful email change, updating the email in local storage and owner email
      unawaited(SettingsDelegate().prefs.setString('login', newEmail));
      refreshLoginRequest();
    }

    return response;
  }

  /// This api verifies if the link is expired or not
  Future<MBResponse> verifyLinkCodeExpiration(
      BuildContext? context, String code, String email, String linkType) async {
    String route = 'User/code-verification';

    Map<String, dynamic> body = {
      'email': email,
      'code': code,
      'linkType': linkType,
    };
    MBResponse response = await dioRequest(context, route, body,
        showLoading: false, tokened: false);

    return response;
  }

  /// This api verifies if the change email link is expired or not
  Future<MBResponse> verifyChangeEmailLinkCodeExpiration(
      BuildContext? context, String code, String email) async {
    String route = 'User/change-email-code-verification';

    Map<String, dynamic> body = {
      'email': email,
      'code': code,
    };
    MBResponse response =
        await dioRequest(context, route, body, showLoading: false);

    return response;
  }

  Future<MBResponse> getHomeSummary() async {
    String route = 'User/home-screen-summary';

    MBResponse response = await dioRequest(null, route, {});

    if (response.success) {
      return response;
    } else {
      return Future.error(response.errorMessage);
    }
  }
}
