import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/controllers/teammates_controller.dart';
import 'package:mybuddy/models/community.dart';

import '../controllers/teams_controller.dart';

class MbApiCommunity extends MbBaseApi {
  Future<MBResponse> getCommunity(
      {String? slug,
      required int page,
      int? perPageCount,
      String? query}) async {
    assert(slug == 'all' || slug == 'package');

    String route = 'Community/walkers/$slug?page=$page';
    if (perPageCount != null) {
      route = '$route&perPage=$perPageCount';
    }
    if (query != null && query.isNotEmpty) {
      route = '$route&query=$query';
    }

    Map<String, dynamic> _body = <String, dynamic>{};

    MBResponse response = await dioRequest(null, route, _body);

    if (response.success) {
      return response;
    }
    return MBResponse();
  }

  Future<MBResponse> countActiveWalkers({String? slug}) async {
    assert(slug == 'all' || slug == 'package');

    String route = 'Community/count/$slug';

    Map<String, dynamic> _body = <String, dynamic>{};

    MBResponse response = await dioRequest(null, route, _body);

    if (response.success) {
      return response;
    }
    return MBResponse();
  }

  Future<List?> checkMWOwners(List<Map<String, dynamic>> data,
      {int? teamId}) async {
    String route = 'Community/search';

    Map<String, dynamic> _body = <String, dynamic>{'data': jsonEncode(data)};

    if (teamId != null) {
      _body["teamId"] = teamId;
    }

    MBResponse response = await dioRequest(null, route, _body);

    if (response.success) {
      return response.body['results'] as List?;
    }
    return null;
  }

  Future<MBResponse> handleTeam(BuildContext? context, Team team,
      {File? avatar}) async {
    String route = team.id != null
        ? 'Community/team/${team.id}/edit'
        : 'Community/team/new';

    Map<String, dynamic> body = team.toJsonApi();

    if (avatar != null) {
      body['file'] = await createDioMultipartFile(avatar);
    }

    MBResponse response =
        await dioRequest(context, route, body, showSuccessMsg: false);

    if (response.success) {
      TeamsController controller = Get.put(TeamsController());
      Team _team = Team.fromJson(response.body['team']);
      if (team.id != null) {
        controller.replaceTeam(_team);
      } else {
        controller.initialFetch();
      }
    }

    return response;
  }

  Future<MBResponse> getTeamWalks(int? teamId,
      {int page = 1, int? perPageCount}) async {
    String route = 'Community/team/$teamId/get/walks?page=$page';
    if (perPageCount != null) {
      route = '$route&perPage=$perPageCount';
    }

    Map<String, dynamic> _body = <String, dynamic>{};

    MBResponse response = await dioRequest(null, route, _body);

    if (response.success) {
      return response;
    }
    return MBResponse();
  }

  Future<MBResponse> getTeamMessage(int teamId) async {
    String route = 'Community/team/$teamId/get/messaging';

    Map<String, dynamic> _body = <String, dynamic>{};

    MBResponse response = await dioRequest(null, route, _body);

    if (response.success) {
      return response;
    }
    return MBResponse();
  }

  Future<MBResponse> getTeam(int teamId) async {
    String route = 'Community/team/$teamId';

    MBResponse response = await dioRequest(null, route, {});

    if (response.success) {
      response.entity = Team.fromJson(response.body['team']);
    }

    return response;
  }

  Future<MBResponse> getTeamMembers(int teamId) async {
    String route = 'Community/team/$teamId/getMembers';

    Map<String, dynamic> _body = <String, dynamic>{};

    MBResponse response = await dioRequest(null, route, _body);
    return response;
  }

  Future<MBResponse> inviteFriendByMail(
      BuildContext context, int? teamId, List<Friend> friends) async {
    String route = 'Community/team/$teamId/invite';

    Map<String, dynamic> _body = <String, dynamic>{
      'emails': friends.map((f) => f.ownerEmail).join(',')
    };

    MBResponse response = await dioRequest(context, route, _body);

    if (response.success) {
      return response;
    }
    return MBResponse();
  }

  Future<MBResponse> inviteSingleFriendByMail(
      BuildContext context, int? teamId, Friend friend) async {
    String route = 'Community/team/$teamId/inviteSingle';

    Map<String, dynamic> _body = <String, dynamic>{'email': friend.ownerEmail};

    MBResponse response =
        await dioRequest(context, route, _body, showLoading: false);

    if (response.success) {
      return response;
    }
    return MBResponse();
  }

  Future<MBResponse> addWooftraxUser(
      BuildContext context, int? teamId, Friend friend) async {
    String route = 'Community/team/$teamId/addMember';

    Map<String, dynamic> _body = <String, dynamic>{
      'user': friend.ownerId,
    };

    MBResponse response =
        await dioRequest(context, route, _body, showLoading: false);

    if (response.success) {
      Get.find<TeamsController>().increaseTeamMemberCount(teamId!);

      return response;
    }
    return MBResponse();
  }

  Future<MBResponse> joinTeam(BuildContext context, Team team) async {
    String route = 'Community/team/${team.id}/join'; //todo confirm route

    MBResponse response = await dioRequest(context, route, {});

    return response;
  }

  Future<MBResponse> removeTeam(BuildContext context, Team team) async {
    String route = 'Community/team/${team.id}/delete';

    MBResponse response = await dioRequest(context, route, {});

    if (response.success) {
      Get.find<TeamsController>().removeTeam(team.id!);
    }

    return response;
  }

  Future<MBResponse> removeTeammateFromTeam(
      BuildContext context, Team team, Friend teammate) async {
    String route = 'Community/team/${team.id}/remove/${teammate.ownerId}';

    MBResponse response = await dioRequest(context, route, {});

    if (response.success) {
      Get.find<TeammatesController>().removeMember(teammate.ownerId!);
      Get.find<TeamsController>().decreaseTeamMemberCount(team.id!);
    }

    return response;
  }

  Future<MBResponse> makeTeammateAdmin(
      BuildContext context, Team team, Friend teammate) async {
    String route = 'Community/team/${team.id}/makeAdmin/${teammate.ownerId}';

    MBResponse response = await dioRequest(context, route, {});

    if (response.success) {
      TeammatesController controller = Get.put(TeammatesController());
      controller.initialFetch();
    }
    return response;
  }

  Future<MBResponse> getInvitedUsers(BuildContext? context, int teamId,
      [String searchQuery = ""]) async {
    String route = 'Community/team/$teamId/invitedUsers';

    Map<String, dynamic> body = {'search': searchQuery};

    MBResponse response =
        await dioRequest(context, route, body, showLoading: false);

    if (response.success) {
      response.entity =
          InvitedUser.parseInvitedUsers(response.body['invitedUsers']);
    }

    return response;
  }

  Future<String> getPublicLink(BuildContext? context, Team team) async {
    String route = 'Community/team/${team.id}/shareableLink';

    MBResponse response =
        await dioRequest(context, route, {}, showLoading: false);

    String url = "";
    if (response.success) {
      url = response.body['url'];
    }

    return url;
  }

  Future<MBResponse> addMeToTeam(BuildContext? context, Team team) async {
    String route = 'Community/team/${team.id}/addMe';

    MBResponse response = await dioRequest(context, route, {});
    if (response.success) {
      TeamsController controller = Get.put(TeamsController());
      controller.initialFetch();
    }

    return response;
  }

  Future<MBResponse> addMeToTeamFromDynamicLink(
      BuildContext? context, Map<String, dynamic> data) async {
    String route = 'Community/team/addMe';

    MBResponse response = await dioRequest(context, route, data);
    if (response.success) {
      TeamsController controller = Get.put(TeamsController());
      controller.initialFetch();
    }

    return response;
  }

  Future<MBResponse> getTeams(int page) async {
    String route = 'Community/teams';
    Map<String, dynamic> body = {"page": page};

    MBResponse response = await dioRequest(null, route, body);

    return response;
  }

  Future<MBResponse> getPublicTeams(int page, String query) async {
    String route = 'Community/publicTeams';
    Map<String, dynamic> body = {"page": page};

    if (query.isNotEmpty) route += '?search=$query';

    MBResponse response = await dioRequest(null, route, body);

    return response;
  }

  Future<MBResponse> getTeammates(int page, int teamId, String query) async {
    String route = 'Community/teammates/$teamId';

    if (query.isNotEmpty) route += '?search=$query';

    Map<String, dynamic> body = {"page": page};

    MBResponse response = await dioRequest(null, route, body);

    return response;
  }

  Future<MBResponse> getTeamLeaderboard(
      int teamId, String? start, String? end, int page) async {
    String route = 'Community/leaderboard/$teamId';
    Map<String, dynamic> body = {
      "startDate": start,
      "endDate": end,
      "page": page
    };

    MBResponse response = await dioRequest(null, route, body);

    return response;
  }

  Future<MBResponse> getLeaderboardUserDetails(int userId, int teamId) async {
    String route = 'Community/leaderboard/member';
    Map<String, dynamic> body = {"memberId": userId, "teamId": teamId};

    MBResponse response = await dioRequest(null, route, body);

    return response;
  }
}
