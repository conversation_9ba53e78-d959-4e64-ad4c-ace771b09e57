import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:json_api/client.dart';
import 'package:json_api/document.dart';
import 'package:json_api/query.dart';
import 'package:json_api/routing.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_api_pet.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/pet.dart';

class CaptainVetApi {
  static const String baseUrlCaptainVet = 'https://captainvet-staging.herokuapp.com';

  final Owner _owner = Data().get().owner.copy();
  final String? _token = Data().get().owner.captainvetToken

      /*SettingsDelegate().prefs.getString('captainvetAccessToken')*/;

  Dio dio = Dio(BaseOptions(
    baseUrl: baseUrlCaptainVet,
  ));

  final baseUri = '$baseUrlCaptainVet/api/v2/';

  RoutingClient _getClient() {
    final uriDesign = StandardUriDesign(Uri.parse(baseUri));

    return RoutingClient(uriDesign);
  }

  Future<bool> loginCaptainVet(
      BuildContext context, Map<String, dynamic> login) async {
    Map<String, dynamic> bodyMapped = {
      'client_id': '204',
      'client_secret': 'JThbjYEy6P36E9xVt3ne9vY687Vgy8zqaNyF7mK5a',
      'grant_type': 'password'
    };
    bodyMapped.addEntries(login.entries);

    var response =
        await dio.post('/oauth/token', data: FormData.fromMap(bodyMapped));

    if (response.statusCode == HttpStatus.ok ||
        response.statusCode == HttpStatus.created) {
      _owner.captainvetToken = response.data['access_token'];
      var myBuddyResponse =
          await MbApiOwner().updateUserRequest(context, owner: _owner);
      return myBuddyResponse.success;
    } else {
      return Future.error(
          'http request failed : ${response.statusCode.toString()}');
    }
  }

  /// private future ResourceFetched
  Future<ResourceFetched> getOffice({required String id}) async {
    var response = await _getClient().fetchResource(
      'offices',
      id,
      headers: {'Authorization': 'Bearer $_token'},
      include: [
        'vets.vet-rdv-types',
        'vets.rdv-type-exceptions',
        'vets.animal-types'
      ],
    );

    return response;
  }

  /// fetch all rdvTypes
  Future<CollectionFetched> getAllRdvTypes() async {
    var response = await _getClient().fetchCollection('rdv-types',
        headers: {'Authorization': 'Bearer $_token'});

    return response;
  }

  /// get office opening hours todo useless ?
  Future<CollectionFetched> getOpeningHours({required String id}) async {
    var response = await _getClient().fetchRelatedCollection(
        'offices', id, 'opening-hours',
        headers: {'Authorization': 'Bearer $_token'});

    return response;
  }

  /// get availabilities with animal type, rdv type and vet filters
  Future<CollectionFetched> getMeetingAvailabilities(
      {required String id, String? startDate, required List<String?> filters}) async {
    late Filter _filter;
    if (filters.length == 3) {
      _filter = Filter({
        'animalTypeId': filters[0]!,
        'rdvTypeId': filters[1]!,
        'vetId': filters[2]!,
        'days': '7',
        'startDate': startDate!
      });
    }
    var response = await _getClient().fetchRelatedCollection(
        'offices', id, 'meeting-availabilities',
        headers: {'Authorization': 'Bearer $_token'}, filter: _filter);

    return response;
  }

  /// get pets for reconciliation
  Future<CollectionFetched> getCaptainVetPets() async {
    var response = await _getClient().fetchCollection('animals',
        headers: {'Authorization': 'Bearer $_token'}, include: ['animal-type']);

    return response;
  }

  /// create appointment
  Future<ResourceCreated> createMeeting({required Map<String, dynamic> data}) async {
    String? startAt;
    bool? isFirstInOffice;
    Map<String, Identifier> toOne = <String, Identifier>{};

    data.forEach((i, v) {
      if (i.startsWith('startAt')) {
        startAt = v;
      } else if (i.startsWith('isFirstInOffice')) {
        isFirstInOffice = v;
      } else {
        String key = i.substring(0, i.length - 1);
        toOne[key] = Identifier(i, v);
      }
    });

    // Resource resource = Resource(
    //     'meetings',
    //     null);
    // resource.attributes= <String, Object>{
    //         'startAt': startAt,// required
    //         'confirmed': true,// required
    //         'isFirstInOffice': isFirstInOffice,// required
    //         'problems': [],
    //         'isAnyVets': false// required
    //     },
    //     toOne: toOne
    // );
    var response = await _getClient().createNew('meetings',
        attributes: {
          'startAt': startAt, // required
          'confirmed': true, // required
          'isFirstInOffice': isFirstInOffice, // required
          'problems': [],
          'isAnyVets': false // required
        },
        headers: {'Authorization': 'Bearer $_token'},
        one: toOne);
    // var response = await _getClient().createResource(resource, headers: {'Authorization': 'Bearer $_token'});

    if (response.http.statusCode == HttpStatus.created) {
      return response;
    }
    //todo handle error with a special http handler
    return Future.error(response.http.statusCode.toString());
  }

  /// create appointment
  Future<ResourceCreated> createPet({BuildContext? context, required Pet pet}) async {
    var response = await _getClient().createNew(
      'animals',
      attributes: {
        'name': pet.name,
        'isMale': pet.genderId == 1,
        'birthday': pet.birthDate != null ? DateFormat('yyyy-MM-dd').format(pet.birthDate!) : '',//TODO NS check sending empty string to CV api
      },
      one: {
        'animal-type': Identifier('animal-types', pet.getCaptainVetSpeciesId()),
        // todo breed
        // 'breed': Identifier('breeds', '086d380c-8f0f-11e7-b0b3-f40f242f80fc')
      },
      headers: {'Authorization': 'Bearer $_token'},
    );

    if (response.http.statusCode == HttpStatus.created) {
      return response;
    }
    //todo handle error with a special http handler
    return Future.error(response.http.statusCode.toString());
  }

  Future<MBResponse> reconcilePet(
      {BuildContext? context, required Pet pet, String? captainvetId}) async {
    pet.captainvetId = captainvetId;

    return await MbApiPet().editPetRequest(context, pet);
  }

  /// create user todo attributes control
  Future<bool?> createCaptainVetUser(BuildContext context) async {
    // todo fix password
    String password = '${_owner.id}.${_owner.email}';

    var response = await _getClient().createNew('users',
    attributes: {
      'isMale': true, // missing in MB
      'firstName': _owner.firstName,
      'lastName': _owner.lastName,
      'email': _owner.email,
      'phone': _owner.phone.isNotEmpty ? _owner.phone.replaceRange(0, 1, '+33') : '',
      'password': password,
      'picture': null
    },);

    if (response.http.statusCode != HttpStatus.created) {
      return Future.error(response.http.statusCode.toString());
    }

    var responseLogin = await loginCaptainVet(
        context, {'username': _owner.email, 'password': password});

    return responseLogin;
  }


  void close() {
  }
}
