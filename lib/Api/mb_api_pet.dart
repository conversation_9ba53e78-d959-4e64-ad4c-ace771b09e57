import 'dart:io';

import 'package:flutter/material.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/reconciliation.dart';

class MbApiPet extends MbBaseApi {
  Future<MBResponse> deletePet(BuildContext context, Pet _pet) async {
    String route = 'Pet/delete';

    MBResponse response = await dioRequest(context, route, {'id': _pet.id});

    if (response.success) {
      Data().get().pets.removeWhere((item) => item.id == _pet.id);
      Data().get().deletedPets.add(_pet);
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse> addPetRequest(BuildContext? context, Pet _pet,
      {ReconcilablePet? pimsPet, File? avatar}) async {
    String route = 'Pet/add';

    Map<String, dynamic> _body = _pet.toJsonApi(updated: false);

    switch (_pet.activityStatus) {
      case ActivityWalkingStatus.selected:
        _body['activityStatus'] = 0;
        break;
      case ActivityWalkingStatus.selectable:
        _body['activityStatus'] = 1;
        break;
      case ActivityWalkingStatus.notSelectable:
        _body['activityStatus'] = 2;
        break;
    }

    if (pimsPet != null) {
      _body['pimsPetId'] = pimsPet.pimsPetId;
      _body['serviceId'] = pimsPet.serviceId;
    }

    if (avatar != null) {
      _body['content'] = await createDioMultipartFile(avatar);
    }

    if (await Tools().common.isOnline(alertContext: context)) {
      MBResponse response = await dioRequest(context, route, _body);

      if (response.success) {
        _pet = Pet.fromJson(response.body['pet']);
        Data().get().pets.add(_pet);
        if (response.body.containsKey('tasks') &&
            response.body['tasks'] != null) {
          Tools.debugPrint(
              '--------------------------------------------------------------------tasks update !!');
          Tools.debugPrint(response.body['tasks'].toString());
          Data().updateTasks(response.body['tasks']);
        }
        Data().storeData();
        response.entity = _pet;
      }

      return response;
    }

    return MBResponse();
  }

  Future<MBResponse> editPetRequest(BuildContext? context, Pet _pet,
      {ReconcilablePet? pimsPet, File? avatar}) async {
    String route = 'Pet/update';

    Map<String, dynamic> _body = _pet.toJsonApi();

    switch (_pet.activityStatus) {
      case ActivityWalkingStatus.selected:
        _body['activityStatus'] = 0;
        break;
      case ActivityWalkingStatus.selectable:
        _body['activityStatus'] = 1;
        break;
      case ActivityWalkingStatus.notSelectable:
        _body['activityStatus'] = 2;
        break;
    }

    if (pimsPet != null) {
      _body['pimsPetId'] = pimsPet.pimsPetId;
      _body['serviceId'] = pimsPet.serviceId;
    }

    if (avatar != null) {
      _body['content'] = await createDioMultipartFile(avatar);
    }

    MBResponse response = await dioRequest(context, route, _body);

    if (response.success) {
      Pet _oldPet = Data().get().pets.firstWhere((item) => item.id == _pet.id);
      Pet _newPet = Pet.fromJson(response.body['pet']);
      _newPet.totalMiles = _oldPet.totalMiles;
      _newPet.totalWalks = _oldPet.totalWalks;
      Data().get().pets.remove(_oldPet);
      Data().get().pets.add(_newPet);
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse> petCharityStates(BuildContext context) async {
    String route = '/Pet/states';

    MBResponse response = await dioRequest(context, route, {});

    if (response.success) {
      return response;
    }

    return MBResponse();
  }
}
