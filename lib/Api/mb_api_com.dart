import 'dart:async';
import 'dart:io';

import 'package:device_info/device_info.dart';
import 'package:flutter/material.dart';
import 'package:ios_utsname_ext/extension.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/models/message.dart';
import 'package:mybuddy/models/notification.dart';


class MbApiCom extends MbBaseApi {
  Future<MBResponse> addMessageItemRequest(
      BuildContext? context, MBMessage? message,
      {String content = '', File? image}) async {
    String route = 'Messaging/add';

    if (message == null || (content.trim() == '' && image == null)) {
      return MBResponse(null);
    }

    Map<String, dynamic> body = {'id': message.id, 'content': content};

    body['image'] = image != null ? await createDioMultipartFile(image) : null;

    MBResponse response = await dioRequest(context, route, body);

    if (response.success) {
      message = MBMessage.fromJson(response.body['message']);
      Data().get().messages.removeWhere((item) => item.id == message!.id);
      Data().get().messages.add(message);
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse?> addMessageRequest(
    BuildContext context,
    MBMessage message,
  ) async {
    String route = 'Messaging/create';

    if (message.serviceId == null || message.title.trim() == '') {
      // Tools.debugPrint('is null');
      return null;
    }
    Map<String, dynamic> body = {
      'serviceId': message.serviceId,
      'title': message.title.trim()
    };

    MBResponse response = await dioRequest(context, route, body);

    if (response.success) {
      message = MBMessage.fromJson(response.body['message']);
      Data().get().messages.add(message);
      Data().storeData();
      response.entity = message;
    }

    return response;
  }

  Future<MBResponse?> setReadMessageRequest(
      BuildContext? context, MBMessage? message) async {
    String route = 'Messaging/setRead';

    if (message == null || message.items.isEmpty) {
      return null;
    }
    Map<String, dynamic> body = {
      'id': message.items.last.id,
      'messageId': message.id
    };

    MBResponse response = await dioRequest(context, route, body);

    if (response.success) {
      message = MBMessage.fromJson(response.body['message']);
      Data().get().messages.removeWhere((item) => item.id == message!.id);
      Data().get().messages.add(message);
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse?> setMessageReadAt(int? messageId) async {
    String route = 'Messaging/setRead';

    if (messageId == null) {
      return null;
    }
    Map<String, dynamic> body = {
      'messageId': messageId
    };

    MBResponse response = await dioRequest(null, route, body);

    if (response.success) {
      MBMessage message = MBMessage.fromJson(response.body['message']);
      Data().get().messages.removeWhere((item) => item.id == message.id);
      Data().get().messages.add(message);
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse> setReadNotificationRequest(
      BuildContext? context, MBNotification _notification) async {
    String route = 'Notification/setRead';

    Map<String, dynamic> body;
    if (_notification.id != 0) {
      body = {'id': _notification.id};
    } else {
      body = {'timestamp': _notification.timestamp, 'type': _notification.type};
    }
    MBResponse response = await dioRequest(context, route, body);

    if (response.success) {
//      refreshLoginRequest();
      _notification.read = true;
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse?> sendPushTokenRequest(
      BuildContext? context, String? pushToken) async {
    String route = 'Push/setToken';
    if (pushToken == null ||
        pushToken.trim() == '' ||
        pushToken.trim() == 'null') return null;

    int phonePlatform = 0;
    String phoneModel = '';
    String phoneManufacturer = 'web';
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      phonePlatform = 1;
      AndroidDeviceInfo phone = await deviceInfo.androidInfo;
      phoneModel = phone.model;
      phoneManufacturer = phone.manufacturer;
    } else if (Platform.isIOS) {
      phonePlatform = 3;
      IosDeviceInfo phone = await deviceInfo.iosInfo;
      phoneModel = phone.utsname.machine.iOSProductName;
      phoneManufacturer = 'Apple';
    }

    Map<String, dynamic> body = {
      'pushToken': pushToken,
      'version': Platform.operatingSystemVersion,
      'model': phoneModel,
      'manufacturer': phoneManufacturer,
      'platform': phonePlatform,
    };

    MBResponse response = await dioRequest(context, route, body, tokened: true);
    if (response.success) {
      unawaited(SettingsDelegate().prefs.setString('pushToken', pushToken));
    }

    return response;
  }
}
