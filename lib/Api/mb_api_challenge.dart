import 'package:flutter/cupertino.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/models/challenge.dart';

class MbApiChallenge extends MbBaseApi {
  Future<MBResponse> getChallenges(double? latitude, double? longitude) async {
    String route = 'Challenge/all';

    Map<String, dynamic> _body = <String, dynamic>{};
    if (latitude != null && longitude != null) {
      _body['latitude'] = latitude;
      _body['longitude'] = longitude;
    }

    MBResponse response = await dioRequest(null, route, _body);

    return response;
  }

  Future<Challenge?> challengeDetail(String challengeId) async {
    String route = 'Challenge/$challengeId/detail';

    Map<String, dynamic> _body = <String, dynamic>{};

    MBResponse response = await dioRequest(null, route, _body);

    if (response.success) {
      Challenge details = Challenge.fromJson(response.body["challenge"]);

      return details;
    }
    return null;
  }

  Future<MBResponse> join(Challenge challenge, BuildContext context) async {
    String route = 'Challenge/${challenge.id}/join';

    Map<String, dynamic> _body = <String, dynamic>{};

    MBResponse response = await dioRequest(context, route, _body);

    return response;
  }

  Future<MBResponse> leave(Challenge challenge) async {
    String route = 'Challenge/${challenge.id}/leave';

    Map<String, dynamic> _body = <String, dynamic>{};

    MBResponse response = await dioRequest(null, route, _body);

    return response;
  }
}
