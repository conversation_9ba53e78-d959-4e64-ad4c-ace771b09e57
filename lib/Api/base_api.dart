import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/app_domain_delegate.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/controllers/internet_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/file.dart';
import 'package:mybuddy/ui/templates/debug_error_page.dart';
import 'package:pretty_json/pretty_json.dart';

class MbBaseApi {
  static const bool debug = kDebugMode;

  bool get isInDebugMode {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }

  Future<MultipartFile> createDioMultipartFile(File file) async {
    return MultipartFile.fromFile(file.path);
  }

  Future<MBResponse> dioRequest(
      BuildContext? context, String route, Map<String, dynamic> body,
      {bool tokened = true,
      bool reducedTimeout = false,
      bool showLoading = true,
      bool showSuccessMsg = true,
      bool increasedTimeout = false}) async {
    if (!(await InternetController.of().checkInternet(context: context))) {
      return MBResponse.notOnline();
    }

    if (tokened) {
      String? token = SettingsDelegate().prefs.getString('token');
      body['token'] = token;
    }

    if (debug) {
      Tools.debugPrint('################################################# URL');
      Tools.debugPrint('${AppDomainDelegate.baseUrlPetOwner}$route');
      Tools.debugPrint(
          '################################################# DATA');
    }

    FormData formData = processBody(body);

    if (showLoading) {
      showMessage(context, showLoading: true);
    }

    Response? response;
    bool caught = false;

    // Adding Headers to every request
    DateTime now = DateTime.now();
    Map<String, String> headers = {};
    String timeZoneName = now.timeZoneName;
    String timeZoneOffset = now.timeZoneOffset.toString();

    headers['timeZone'] = timeZoneName;
    // Sending offset in the form of 5:00 or -7:00
    headers['offset'] =
        timeZoneOffset.substring(0, timeZoneOffset.lastIndexOf(":"));

    int connectTimeout = reducedTimeout ? 2000 : 30000;
    int receiveTimeout = reducedTimeout ? 5000 : 30000;
    receiveTimeout = increasedTimeout
        ? 305000
        : receiveTimeout; // Using this large timeout to match it with the server timeout

    BaseOptions options = BaseOptions(
      baseUrl: AppDomainDelegate.baseUrlPetOwner,
      connectTimeout: connectTimeout,
      receiveTimeout: receiveTimeout,
      headers: headers,
    );

    Dio dio = Dio(options);

    dio.interceptors.add(
      QueuedInterceptorsWrapper(
          onError: (DioError error, ErrorInterceptorHandler handler) {
        // Assume 401 stands for token expired
        if (error.response?.statusCode == 401) {
          Tools.debugPrint(
              '@@@ !!!!! TOKEN is expired need to refresh ${error.requestOptions.path} !!!!! @@@');
          var options = error.response!.requestOptions;

          refreshToken().then((String? refreshedToken) {
            if (refreshedToken == null) {
              handler.reject(error);
              return;
            }
            body['token'] = refreshedToken;
            options.data = processBody(body);
            //repeat
            Dio().fetch(options).then(
                  (r) => handler.resolve(r),
                  onError: (e) => handler.reject(e),
                );
          });
          return;
        }
        return handler.next(error);
      }),
    );

    try {
      response = await dio.post(route, data: formData);
      if (debug) {
        RegExp exp = RegExp(r'oken: [a-zA-Z\.0-9\-_]*');
        String responseDataDebug =
            response.data.toString().replaceAll(exp, 'oken: HIDDEN');
        Tools.debugPrint(
            '################################################# STATUS');
        Tools.debugPrint(response.statusCode?.toString());
        Tools.debugPrint(
            '################################################# RESPONSE ROUTE: $route');

        if (responseDataDebug.toString().length > 400) {
          Tools.debugPrint(responseDataDebug.toString().substring(0, 400));
        } else {
          Tools.debugPrint(responseDataDebug);
        }
        Tools.debugPrint('#################################################');
      }

      hideMessage(context);
    } on DioError catch (e) {
      caught = true;
      Tools.debugPrint(
          '#################################################DIO ERROR ROUTE: $route');
      if (debug && isInDebugMode && context != null) {
        Tools.debugPrint('display in page debug');
        Tools.debugPrint(e.toString());
        await Tools()
            .navigatorPush(DebugErrorPage(contentHtml: e.response.toString()));
      } else {
        if (debug) {
          String _response = e.response.toString();
          if (_response.length > 5000) {
            _response = _response.substring(0, 4000);
          }
          Tools.debugPrint(_response);
        }
      }
      Tools.debugPrint('#################################################');

      if (showLoading && context != null) {
        if (e.type == DioErrorType.connectTimeout) {
          showMessage(context,
              message:
                  'We cannot connect to the server. Please try again later');
        } else if (e.type == DioErrorType.receiveTimeout) {
          showMessage(context,
              message:
                  'We cannot connect to the server. Please try again later');
        }
      }
    }
    MBResponse mbResponse = MBResponse(response);

    if (!mbResponse.success && mbResponse.errorMessage == 'TOKEN_INVALID') {
      unawaited(SettingsDelegate().prefs.remove('data'));
      unawaited(SettingsDelegate().prefs.remove('pushToken'));
      unawaited(SettingsDelegate().prefs.remove('password'));
      unawaited(SettingsDelegate().prefs.remove('token'));
      unawaited(SettingsDelegate().prefs.remove('facebookToken'));
      unawaited(SettingsDelegate().prefs.remove('appleToken'));
      unawaited(SettingsDelegate().prefs.remove('googleToken'));
      Tools().navigatorPopAll();
    } else if (!mbResponse.success && !caught) {
      String errorMessage = mbResponse.errorMessage;
      if (debug &&
          isInDebugMode &&
          context != null &&
          !errorMessage.startsWith('APPLICATION_MOBILE_')) {
        await Tools()
            .navigatorPush(DebugErrorPage(contentTxt: mbResponse.dataString));
      } else {
        try {
          Tools.debugPrint(prettyJson(mbResponse.body));
        } catch (e) {
          Tools.debugPrint('cant print json response');
        }
        showMessage(context, message: errorMessage.tr(), showError: true);
      }
    }
    // else if(mbResponse.success && mbResponse.body.containsKey('totalPoints')){
    //   Data().setOwnerPoints(mbResponse.body['totalPoints'] as int);
    //   if (mbResponse.body.containsKey('feedBackMessage')) {
    //     String message = mbResponse.body['feedBackMessage'];
    //     if(showSuccessMsg) {
    //       showMessage(Tools().homeContext, message: message.tr('Success'));
    //     }
    //   }
    // }
    return mbResponse;
  }

  //write a get api method for other servers apis which are not part of wooftrax server
  Future<MBResponse> dioOtherServerGetRequest(String route,
      {Map<String, dynamic>? queryParams,
      BuildContext? context,
      bool reducedTimeout = false,
      bool showLoading = true,
      bool showSuccessMsg = true,
      bool increasedTimeout = false}) async {
    if (!(await InternetController.of().checkInternet(context: context))) {
      return MBResponse.notOnline();
    }

    if (debug) {
      Tools.debugPrint('################################################# URL');
      Tools.debugPrint(route);
      Tools.debugPrint(
          '################################################# DATA');
    }

    if (showLoading) {
      showMessage(context, showLoading: true);
    }

    Response? response;
    bool caught = false;

    // Adding Headers to every request
    Map<String, String> headers = {};

    int connectTimeout = reducedTimeout ? 2000 : 30000;
    int receiveTimeout = reducedTimeout ? 5000 : 30000;
    receiveTimeout = increasedTimeout
        ? 305000
        : receiveTimeout; // Using this large timeout to match it with the server timeout

    BaseOptions options = BaseOptions(
      connectTimeout: connectTimeout,
      receiveTimeout: receiveTimeout,
      headers: headers,
    );

    Dio dio = Dio(options);

    dio.interceptors.add(
      QueuedInterceptorsWrapper(
          onError: (DioError error, ErrorInterceptorHandler handler) {
        return handler.next(error);
      }),
    );

    try {
      response = await dio.get(route, queryParameters: queryParams);
      if (debug) {
        RegExp exp = RegExp(r'oken: [a-zA-Z\.0-9\-_]*');
        String responseDataDebug =
            response.data.toString().replaceAll(exp, 'oken: HIDDEN');
        Tools.debugPrint(
            '################################################# STATUS');
        Tools.debugPrint(response.statusCode?.toString());
        Tools.debugPrint(
            '################################################# RESPONSE ROUTE: $route');

        if (responseDataDebug.toString().length > 400) {
          Tools.debugPrint(responseDataDebug.toString().substring(0, 400));
        } else {
          Tools.debugPrint(responseDataDebug);
        }
        Tools.debugPrint('#################################################');
      }

      hideMessage(context);
    } on DioError catch (e) {
      caught = true;
      Tools.debugPrint(
          '#################################################DIO ERROR ROUTE: $route');
      if (debug && isInDebugMode && context != null) {
        Tools.debugPrint('display in page debug');
        Tools.debugPrint(e.toString());
        await Tools()
            .navigatorPush(DebugErrorPage(contentHtml: e.response.toString()));
      } else {
        if (debug) {
          String _response = e.response.toString();
          if (_response.length > 5000) {
            _response = _response.substring(0, 4000);
          }
          Tools.debugPrint(_response);
        }
      }
      Tools.debugPrint('#################################################');

      if (showLoading && context != null) {
        if (e.type == DioErrorType.connectTimeout) {
          showMessage(context,
              message:
                  'We cannot connect to the server. Please try again later');
        } else if (e.type == DioErrorType.receiveTimeout) {
          showMessage(context,
              message:
                  'We cannot connect to the server. Please try again later');
        }
      }
    }
    MBResponse mbResponse = MBResponse(response);

    return mbResponse;
  }

  String formatDateForApi(DateTime dateTime) {
    var formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(dateTime);
  }

  String formatDateTimeForApi(DateTime dateTime) {
    var formatter = DateFormat('yyyy-MM-dd HH:mm:ss');
    return formatter.format(dateTime);
  }

  String getFileUrl(MBFile file) {
    String url =
        '${AppDomainDelegate.baseUrlPetOwner}file/${file.id}/${Data().get().owner.id}/${file.filename}';
    url = Uri.encodeFull(url);
    return url;
  }

  void hideMessage(context) {
    ScaffoldMessengerState _scaffoldMessengerState;
    try {
      _scaffoldMessengerState = ScaffoldMessenger.of(context);
    } catch (e) {
      return;
    }
    _scaffoldMessengerState.removeCurrentSnackBar();
  }

  FormData processBody(body) {
    Map<String, dynamic> newBody = {};
    body.forEach((k, v) {
      switch (v.runtimeType) {
        case DateTime:
          v = formatDateTimeForApi(v);
          break;
      }

      v ??= '';
      newBody[k] = v;
    });
    if (debug) {
      try {
        RegExp exp = RegExp(r'oken":"[a-zA-Z\.0-9\-_]*"');
        String dataDebug =
            jsonEncode(newBody).replaceAll(exp, 'oken": "HIDDEN"');
        log(dataDebug);
      } catch (e) {
        Tools.debugPrint('can\'t encode');
      }
    }
    return FormData.fromMap(newBody);
  }

  void showMessage(BuildContext? context,
      {String message = '', bool showLoading = false, bool showError = false}) {
    if (context == null) return;
    hideMessage(context);
    ScaffoldMessengerState _scaffoldMessengerState;
    try {
      _scaffoldMessengerState = ScaffoldMessenger.of(context);
    } catch (e) {
      Tools.debugPrint(
          '------------------------------------------------no scaffold');
      return;
    }

    Color? textColor = showError
        ? Colors.white
        : Theme.of(context).textSelectionTheme.cursorColor;
    _scaffoldMessengerState.removeCurrentSnackBar();
    _scaffoldMessengerState.showSnackBar(
      SnackBar(
        backgroundColor: showError ? Theme.of(context).colorScheme.error : null,
        duration: Duration(seconds: showLoading && !showError ? 60 : 4),
        content: Row(
          children: <Widget>[
            Visibility(
              visible: showLoading && !showError,
              child: CircularProgressIndicator(
                color: textColor,
              ),
            ),
            Visibility(
              visible: showError,
              child: Icon(
                FontAwesomeIcons.exclamationTriangle,
                color: textColor,
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 20),
                child: Text(
                  message,
                  style: Theme.of(context)
                      .snackBarTheme
                      .contentTextStyle
                      ?.copyWith(
                        color: textColor,
                      ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // static bool inProduction() {
  //   return selected == rootPathProd;
  // }

  Future<String?> refreshToken() async {
    // TODO MID => REST refreshToken ?
    final response =
        await Dio().post('${AppDomainDelegate.baseUrlPetOwner}User/login',
            data: FormData.fromMap({
              'email': SettingsDelegate().prefs.getString('login'),
              'password': SettingsDelegate().prefs.getString('password'),
              'refresh': true
            }));
    if (response.data['success']) {
      await SettingsDelegate().prefs.setString('token', response.data['token']);
      return response.data['token'];
    }
    return null;
  }
}
