
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';

class MbApiCharityPoints extends MbBaseApi {

  Future<MBResponse> getCharityPointsHistory(
      {String? to,
      String? from,
        required int page}) async {

    String route = 'Workout/charity-history';

    Map<String, dynamic> _body = <String, dynamic>{
      "page":page,
    };

    if(from!=null){
      _body["from_date"]=from;
    }

    MBResponse response = await dioRequest(null, route, _body);

    if (response.success) {
      return response;
    }
    return MBResponse();
  }

}
