import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:mybuddy/Tools/tools.dart';

class MBResponse {
  late int statusCode = 0;

//  final http.Response
  Response? originalResponse;
  late bool success = false;
  late Map<String, dynamic> body = <String, dynamic>{};
  String errorMessage = '';
  String? dataString;
  Object? entity;

  MBResponse([this.originalResponse]) {
    if (originalResponse == null) {
      success = false;
      errorMessage = 'ERROR - 404';
      return;
    }
    int? originalStatusCode = originalResponse!.statusCode;
    if (originalStatusCode != null) {
      statusCode = originalStatusCode;
    }
    if (statusCode == 200) {
      try {
        body = originalResponse!.data;
        dataString = json.encode(originalResponse!.data);
      } catch (e) {
        Tools.debugPrint('response error');
        Tools.debugPrint(e.toString());
      }
    }
    success = (statusCode == 200 && body['success'] == true);
    if (success == false) {
      if (originalResponse!.data.runtimeType == String && originalResponse!.data.contains('TOKEN_INVALID')) {
        errorMessage = 'TOKEN_INVALID';
      } else {
        errorMessage = body['message'] ?? 'ERROR';
      }
      // if (errorMessage == 'TOKEN_INVALID'){
      //
      //   unawaited(SettingsDelegate().prefs.remove('data'));
      //   unawaited(SettingsDelegate().prefs.remove('pushToken'));
      //   unawaited(SettingsDelegate().prefs.remove('password'));
      //   unawaited(SettingsDelegate().prefs.remove('token'));
      //   unawaited(SettingsDelegate().prefs.remove('facebookToken'));
      //   unawaited(SettingsDelegate().prefs.remove('appleToken'));
      //   Tools().navigatorPopAll();
      //   return;
      // }
    }
  }

  MBResponse.notOnline() {
    success = false;
    errorMessage = 'NOT ONLINE';
  }
}
