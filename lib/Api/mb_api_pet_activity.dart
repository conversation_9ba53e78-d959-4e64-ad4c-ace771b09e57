import 'dart:io';

import 'package:flutter/material.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/order.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';

class MbApiPetActivity extends MbBaseApi {
  Future<MBResponse> removePetActivityRequest(
      BuildContext context, PetActivity _petActivity) async {
    String route = 'Activity/remove';
    MBResponse response =
        await dioRequest(context, route, {'id': _petActivity.id});

    if (response.success) {
      Data()
          .get()
          .petActivities
          .removeWhere((item) => item.id == _petActivity.id);
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse> addImageActivityRequest(
      BuildContext context, Pet pet, File image) async {
    String route = 'Activity/add';
    Map<String, dynamic> body = {
      'petIds': pet.id,
      'activityId': 5,
      'content': await createDioMultipartFile(image),
    };

    MBResponse response = await dioRequest(context, route, body);

    if (response.success) {
      Data()
          .get()
          .petActivities
          .add(PetActivity.fromAddImageRequest(response.body, pet));
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse> addPetActivityRequest(
    BuildContext context,
    PetActivity pa, {
    List<File>? images,
  }) async {
    String route = 'Activity/add';
    Map<String, dynamic> body = pa.toApiJson();

    if (images != null && images.isNotEmpty) {
      var i = 0;
      for (var image in images) {
        body['imageContent$i'] = await createDioMultipartFile(image);
        i++;
      }
    }

    MBResponse response = await dioRequest(context, route, body);
    if (response.success) {
      pa = PetActivity.fromJson(response.body['petActivity']);
      Data().get().petActivities.add(pa);
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse> addTreatmentRequest(
      BuildContext context, PetActivity pa, isNew) async {
    String route = 'Treatment/';
    if (isNew) {
      route += 'add';
    } else {
      route += 'update';
    }
    Map<String, dynamic> body = pa.toApiJson();

    MBResponse response = await dioRequest(context, route, body);
    if (response.success) {
      if (!isNew) {
        PetActivity? oldPa = Data().get().getPetActivity(pa.id);
        if (oldPa != null) {
          Data().get().petActivities.remove(oldPa);
          if (oldPa.reminderId != null) {
            PetActivity? oldReminder =
                Data().get().getPetActivity(oldPa.reminderId);
            if (oldReminder != null) {
              Data().get().petActivities.remove(oldReminder);
            }
          }
        }
      }

      Data()
          .get()
          .petActivities
          .add(PetActivity.fromJson(response.body['petActivity']));
      if (response.body['reminder'] != null) {
        Data()
            .get()
            .petActivities
            .add(PetActivity.fromJson(response.body['reminder']));
      }
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse> editPetActivityRequest(
      BuildContext context, PetActivity pa) async {
    String route = 'Activity/update';
    Map<String, dynamic> body = pa.toApiJson();

    MBResponse response = await dioRequest(context, route, body);
    if (response.success) {
      Data().get().petActivities.removeWhere((item) => item.id == pa.id);
      Data()
          .get()
          .petActivities
          .add(PetActivity.fromJson(response.body['petActivity']));
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse> addAppointmentRequest(
    BuildContext context,
    AppointmentRequest ar, {
    List<File>? images,
  }) async {
    String route = 'AppointmentRequest/add';
    Map<String, dynamic> body = ar.toApiJson();

    if (images != null && images.isNotEmpty) {
      var i = 0;
      for (var image in images) {
        body['imageContent$i'] = await createDioMultipartFile(image);
        i++;
      }
    }

    MBResponse response = await dioRequest(context, route, body);
    if (response.success) {
      Data().get().appointmentRequests.add(
          AppointmentRequest.fromJson(response.body['appointmentRequest']));
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse> addAppointment(
      BuildContext context, Map<String, dynamic> data) async {
    String route = 'Captainvet/create';

    MBResponse response = await dioRequest(context, route, data);
    if (response.success) {
      Data().get().appointmentRequests.add(
          AppointmentRequest.fromJson(response.body['appointmentRequest']));
      Data()
          .get()
          .appointmentRequests
          .add(AppointmentRequest.fromJson(response.body['petActivity']));
      Data().storeData();
    }

    return response;
  }

  Future<MBResponse> getPetActivity(
      {required int petId, required int type, required int page}) async {
    String route = 'Pet/pet-activities/$petId';

    Map<String, dynamic> _body = <String, dynamic>{"type": type, "page": page};

    MBResponse response = await dioRequest(null, route, _body);

    return response;
  }
}
