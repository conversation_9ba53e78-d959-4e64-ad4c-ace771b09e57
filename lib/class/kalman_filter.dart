import 'dart:math';

class KalmanFilter {
  final double minAccuracy = 1;

  double qMeterPerSecond;
  late int timeStampMilliseconds;
  late double lat;
  late double lng;
  double variance; // P matrix. Negative means object uninitialised.
  // NB: units irrelevant, as long as same units used
  // throughout
  int consecutiveRejectCount;

  KalmanFilter(
      {this.qMeterPerSecond = 3,
        this.variance = -1,
        this.consecutiveRejectCount = 0});

  int getTimeStamp() {
    return timeStampMilliseconds;
  }

  double getLat() {
    return lat;
  }

  double getLng() {
    return lng;
  }

  double getAccuracy() {
    return sqrt(variance);
  }

  void setState(
      double lat, double lng, double accuracy, int timeStampMilliseconds) {
    this.lat = lat;
    this.lng = lng;
    variance = accuracy * accuracy;
    this.timeStampMilliseconds = timeStampMilliseconds;
  }

  void process(double latMeasurement, double lngMeasurement, double accuracy,
      int timeStampMilliseconds, double qMeterPerSecond) {
    this.qMeterPerSecond = qMeterPerSecond;

    if (accuracy < minAccuracy) {
      accuracy = minAccuracy;
    }
    if (variance < 0) {
      // if variance < 0, object is unitialised, so initialise with
      // current values
      this.timeStampMilliseconds = timeStampMilliseconds;
      lat = latMeasurement;
      lng = lngMeasurement;
      variance = accuracy * accuracy;
    } else {
      // else apply Kalman filter methodology

      int timeIncMilliseconds =
          timeStampMilliseconds - this.timeStampMilliseconds;
      if (timeIncMilliseconds > 0) {
        // time has moved on, so the uncertainty in the current position
        // increases
        variance +=
            timeIncMilliseconds * qMeterPerSecond * qMeterPerSecond / 1000;
        this.timeStampMilliseconds = timeStampMilliseconds;
        // TO DO: USE VELOCITY INFORMATION HERE TO GET A BETTER ESTIMATE
        // OF CURRENT POSITION
      }

      // Kalman gain matrix K = Covariance * Inverse(Covariance +
      // MeasurementVariance)
      // NB: because K is dimensionless, it doesn't matter that variance
      // has different units to lat and lng
      double K = variance / (variance + accuracy * accuracy);
      // apply K

      lat += K * (latMeasurement - lat);
      lng += K * (lngMeasurement - lng);

      // new Covariance matrix is (IdentityMatrix - K) * Covariance
      variance = (1 - K) * variance;
    }
  }

  int getConsecutiveRejectCount() {
    return consecutiveRejectCount;
  }

  void setConsecutiveRejectCount(int consecutiveRejectCount) {
    this.consecutiveRejectCount = consecutiveRejectCount;
  }
}
