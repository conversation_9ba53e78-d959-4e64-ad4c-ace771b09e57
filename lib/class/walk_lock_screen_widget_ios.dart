import 'dart:async';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:mybuddy/Tools/tools.dart';

enum WalkState { started, paused }

class LockScreenWalkWidget {
  /// Method channel for interacting with ios native platform.
  static const MethodChannel _methodChannel =
      MethodChannel('com.mediproductions/wooftrax/walkLockScreenWidget');

  /// Uses method channel to initiate lock screen widget.
  static Future<bool> initiateWidgetAndStartWalk() async {
    try {
      if (Platform.isAndroid) return false;
      return await _methodChannel.invokeMethod('show_walk_widget');
    } catch (e) {
      Tools.debugPrint(
          "iOS version should be greater than 16 for lock screen widget.");
      return false;
    }
  }

  /// Uses method channel to dismiss all lock screen widget.
  static Future<bool> dismissAllWidgets() async {
    try {
      if (Platform.isAndroid) return false;
      return await _methodChannel.invokeMethod('dismiss_all_widgets');
    } catch (e) {
      Tools.debugPrint(
          "iOS version should be greater than 16 for lock screen widget.");
      return false;
    }
  }

  /// Uses method channel to dismiss lock screen widget.
  static Future<bool> dismissWidget() async {
    try {
      if (Platform.isAndroid) return false;
      return await _methodChannel.invokeMethod('dismiss_walk_widget');
    } catch (e) {
      Tools.debugPrint(
          "iOS version should be greater than 16 for lock screen widget.");
      return false;
    }
  }

  /// Uses method channel to update walk stats.
  static Future<bool> updateWalkStats(
      String distance, String time, String speed, WalkState state) async {
    try {
      if (Platform.isAndroid) return false;
      String isPaused = state.index.toString();
      return await _methodChannel
          .invokeMethod('update_walk_stats', [distance, time, speed, isPaused]);
    } catch (e) {
      Tools.debugPrint(
          "iOS version should be greater than 16 for lock screen widget.");
      return false;
    }
  }
}
