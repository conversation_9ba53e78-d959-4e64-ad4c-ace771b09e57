import 'dart:io' show Platform;

import 'package:latlong2/latlong.dart';
import 'package:mybuddy/class/location_manager.dart';

class GeoLocation {
  static String? getGooglePlaceLink(String? googlePlaceId) {
    if (googlePlaceId == null || googlePlaceId.trim() == '') return null;
    return 'https://search.google.com/local/writereview?placeid=$googlePlaceId';
  }

  static String? getMapLink(String addr) {
    addr.replaceAll('[^\\p{IsAlphabetic}\\p{IsDigit}|\\p{IsPunct]', ' ');

    List<String> addrArray = addr.split(' ');
    addrArray = addrArray.where((s) => s.trim() != '').toList();

    String? result;
    if (Platform.isAndroid) {
      result = 'https://www.google.com/maps/place/' + addrArray.join('+');
    } else if (Platform.isIOS) {
      result = 'https://maps.apple.com/?q=' + addrArray.join('+');
    }
    return result;
  }

  /// Calculates the distance between the supplied coordinates in meters.
  ///
  /// The distance between the coordinates is calculated using the Haversine
  /// formula (see https://en.wikipedia.org/wiki/Haversine_formula). The
  /// supplied coordinates [startLatitude], [startLongitude], [endLatitude] and
  /// [endLongitude] should be supplied in degrees.
  static double distanceBetweenInMeters(
      UserLocation start,
      UserLocation end) {
    return const Distance(calculator: Haversine()).as(
        LengthUnit.Meter,
        LatLng(start.latitude!, start.longitude!),
        LatLng(end.latitude!, end.longitude!));
  }

  /// Calculates the initial bearing between two points
  ///
  /// The initial bearing will most of the time be different than the end
  /// bearing, see https://www.movable-type.co.uk/scripts/latlong.html#bearing.
  /// The supplied coordinates [startLatitude], [startLongitude], [endLatitude]
  /// and [endLongitude] should be supplied in degrees.
  static double bearingBetween(
      UserLocation start,
      UserLocation end) {
    return const Distance(calculator: Haversine()).bearing(LatLng(start.latitude!, start.longitude!),
        LatLng(end.latitude!, end.longitude!));
  }

  // This method is part of the port of the KalmanFilter that is used
  // to normalize the user locations
  static UserLocation createLocation(
      UserLocation predictedLocation, UserLocation? previousLocation) {
    double age = _getLocationAge(predictedLocation);
    double preAge = _getLocationAge(previousLocation);

    double? accuracy = predictedLocation.accuracy;
    double newAccuracy = accuracy ?? 0;

    if (accuracy != null) {
      if (Platform.isAndroid) {
        if (accuracy <= 0 || accuracy > 35) {
          newAccuracy = -1;
        }
      } else {
        if (age > 10 * 60 * 1000) { // If the location age is older than 10 minutes
          newAccuracy = -1;
        }
      }
    }

    if (previousLocation != null) {
      double delTaV = predictedLocation.speed! - previousLocation.speed!;
      double deltaT = (preAge - age) / 1000;
      double acceleration = (delTaV / deltaT).abs();
      if (acceleration >= 9) {  //reject new point with acceleration of  9 m/s2
        newAccuracy = -1;
      }
    }
    return UserLocation(
        longitude: predictedLocation.longitude,
        latitude: predictedLocation.latitude,
        altitude: predictedLocation.altitude,
        accuracy: newAccuracy,
        bearing: predictedLocation.bearing,
        speed: predictedLocation.speed,
        time: predictedLocation.time,
        isMock: predictedLocation.isMock);
  }

  static double _getLocationAge(UserLocation? newLocation) {
    if (newLocation != null) {
      double locationAge;

      int currentTimeInMilli = DateTime.now().millisecond;
      double locationTimeInMilli = newLocation.time!;
      locationAge = currentTimeInMilli - locationTimeInMilli;
      return locationAge;
    }
    return 0;
  }
}
