import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:location/location.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:permission_handler/permission_handler.dart' hide PermissionStatus;

import '../controllers/gps_indication_controller.dart';

const double DEFAULT_LOCATION_LATITUDE = 39.833333;
const double DEFAULT_LOCATION_LONGITUDE = -98.583333;

/// Location listener class that acts as a listener interface for different
/// modules to get the location updates
class LocationListener {
  /// This method will get called everytime there is a new location update
  void onUserLocationUpdated(UserLocation location) {}

  /// This method will get called when some error occurs, like location permission
  /// not granted
  void onUserLocationError(dynamic err) {}
}

class LocationManager {
  static final LocationManager _singleton = LocationManager._internal();
  StreamSubscription<LocationData>? _locationSubscription;
  late Location location;
  late UserLocation _userLocation;
  // final LocalNotificationsTools localNotification = LocalNotificationsTools();
  final walkNotificationId = 320;
  late Map<String, LocationListener> locationListenerMap;
  late GpsIndicationController _gpsController;

  factory LocationManager() => _singleton;

  LocationManager._internal() {
    location = Location();
    _userLocation = UserLocation(
      // Random Location
      latitude: DEFAULT_LOCATION_LATITUDE,
      longitude: DEFAULT_LOCATION_LONGITUDE,
      bearing: null,
      accuracy: null,
      altitude: null,
      time: null,
      speed: null,
      isMock: null,
    );
    locationListenerMap = {};
    _gpsController = Get.put(GpsIndicationController());
  }

  UserLocation get userLocation => _userLocation;

  void registerListener(String tag, LocationListener listener) {
    if (locationListenerMap.containsKey(tag)) {
      Tools.debugPrint("$tag is already registered, overriding ");
    }

    locationListenerMap[tag] = listener;

    // Sending the latest location as soon as anyone wants to listen to
    if (_userLocation.accuracy != null) {
      listener.onUserLocationUpdated(_userLocation);
    }
  }

  void unregisterListener(String tag) {
    if (!locationListenerMap.containsKey(tag)) {
      Tools.debugPrint("$tag is not present, doing nothing");
      return;
    }

    locationListenerMap.remove(tag);
  }

  Future<bool> _checkIfLocationServiceEnabled() async {
    bool _serviceEnabled;

    _serviceEnabled = await location.serviceEnabled();
    if (!_serviceEnabled) {
      _serviceEnabled = await location.requestService();
    }
    return _serviceEnabled;
  }

  Future<bool> _checkIfLocationPermissionGranted() async {
    bool granted = await Permission.location.request().isGranted;
    if (Platform.isIOS) {
      granted = await location.hasPermission() == PermissionStatus.granted;
    }
    return granted;
  }

  Future<bool> _requestLocationPermission() async {
    bool locationServiceEnabled = await _checkIfLocationServiceEnabled();
    if (locationServiceEnabled) {
      bool checkPermission = await _checkIfLocationPermissionGranted();
      if (!checkPermission) {
        return Future.error(Exception("Location permission not granted"));
      }
    } else {
      return Future.error(Exception("Location service not enabled"));
    }

    return Future.value(true);
  }

  void getQuickLocationSilently() async {
    if (await location.serviceEnabled() &&
        await Permission.location.isGranted) {
      try {
        _userLocation = await _getUserLocationWithFallbackLocation();
        Tools.debugPrint(
            'on app initialization user location: ${_userLocation.latitude} ${_userLocation.longitude}');
      } catch (e) {
        Tools.debugPrint('initialization user location failed: $e');
      }
    }
  }

  Future<UserLocation> getCurrentLocation() async {
    try {
      await _requestLocationPermission();
    } catch (e) {
      rethrow;
    }
    _userLocation = await _getUserLocationWithFallbackLocation();
    return _userLocation;
  }

  Future<bool> initLocationListening() async {
    bool started = false;
    try {
      started = await _requestLocationPermission();
    } catch (e) {
      rethrow;
    }

    _startLocationUpdatesAppWide();

    return started;
  }

  /// This method assumes that the we have the location permissions granted and
  /// location services are enabled, otherwise location services won't work as intended
  void _startLocationUpdatesAppWide() {
    location.changeSettings(
        accuracy: LocationAccuracy.high, interval: 3000, distanceFilter: 3.0);

    if (_locationSubscription != null) {
      _locationSubscription?.cancel();
      _locationSubscription = null;
    }

    _locationSubscription =
        location.onLocationChanged.handleError((dynamic err) {
          Tools.debugPrint(err.toString());
          if (locationListenerMap.values.isNotEmpty) {
            locationListenerMap.values.forEach((e) =>
                e.onUserLocationError(err));
          }
        }).listen((LocationData currentLocation) {
          Tools.debugPrint("Location milna start hai");
          _userLocation = _fromLocationData(currentLocation);
          _gpsController.updateAccuracy(_userLocation.accuracy);
          if (locationListenerMap.values.isNotEmpty) {
            locationListenerMap.values.forEach((e) =>
                e.onUserLocationUpdated(_userLocation));
          }
        });
  }

  void stopLocationUpdatesAppWide() {
    _locationSubscription?.cancel();
    _locationSubscription = null;
    locationListenerMap.clear();
  }

  Future<void> enableBackgroundMode() async {
    await location.enableBackgroundMode(enable: true);

    await location.changeNotificationOptions(
        channelName: "WoofTrax Walk",
        title: "WoofTrax",
        subtitle: "WoofTrax is tracking your walk.",
        iconName: "@drawable/wooftrax_notification_icon",
        onTapBringToFront: true);
  }

  Future<void> disableBackgroundMode() async {
    await location.enableBackgroundMode(enable: false);
  }

  /// This method is responsible for fetching the location, at times it takes forever
  /// to return because of the location unavailability, hence we have created a
  /// workaround to return the last location that was fetched.
  Future<UserLocation> _getUserLocationWithFallbackLocation() async {
    // Approach is taken from here:
    // https://stackoverflow.com/questions/64171551/location-getlocation-is-taking-too-long-on-a-real-android-device-flutter
    UserLocation userLocation = _userLocation;
    LocationData? _locationData = await Future.any([
      location.getLocation(),
      Future.delayed(const Duration(seconds: 5), () => null),
    ]);

    if (_locationData != null) {
      userLocation = _fromLocationData(_locationData);
    }

    return userLocation;
  }

  static UserLocation _fromLocationData(LocationData locationData) {
    return UserLocation(
        longitude: locationData.longitude,
        latitude: locationData.latitude,
        altitude: locationData.altitude,
        accuracy: locationData.accuracy,
        bearing: locationData.heading,
        // need to find a way to calculate this
        speed: locationData.speed,
        time: locationData.time,
        isMock: locationData.isMock);
  }
}

class UserLocation {
  double? latitude;
  double? longitude;
  double? altitude;
  double? bearing;
  double? accuracy;
  double? speed;
  double? time;
  bool? isMock;

  UserLocation(
      {@required this.longitude,
      @required this.latitude,
      @required this.altitude,
      @required this.accuracy,
      @required this.bearing,
      @required this.speed,
      @required this.time,
      @required this.isMock});

  factory UserLocation.fromJson(Map<String, dynamic> jsonData) {
    return UserLocation(
      latitude: jsonData['latitude'],
      longitude: jsonData['longitude'],
      altitude: jsonData['altitude'],
      accuracy: jsonData['accuracy'],
      bearing: jsonData['bearing'],
      speed: jsonData['speed'],
      time: jsonData['time'],
      isMock: jsonData['isMock'],
    );
  }

  static Map<String, dynamic> toMap(UserLocation location) => {
    'latitude': location.latitude,
    'longitude': location.longitude,
    'altitude': location.altitude,
    'accuracy': location.accuracy,
    'bearing': location.bearing,
    'speed': location.speed,
    'time': location.time,
    'isMock': location.isMock,
  };

  static String encode(List<UserLocation> locations) => jsonEncode(
    locations
        .map<Map<String, dynamic>>((location) => UserLocation.toMap(location))
        .toList(),
  );

  static String encodeLocationMap(Map<String, List<UserLocation>> locations) {
    if(locations.isEmpty) {
      return jsonEncode({});
    } else {
      return jsonEncode(
        locations.map((key, value) =>
            MapEntry(key, value.map<Map<String, dynamic>>((e) => UserLocation.toMap(e)).toList())),
      );
    }
  }

  static List<UserLocation> decode(String locations) =>
      (jsonDecode(locations) as List<dynamic>)
          .map<UserLocation>((item) => UserLocation.fromJson(item))
          .toList();

  static Map<String, List<UserLocation>> decodeLocationMap(String locations) =>
      (jsonDecode(locations) as Map).map((key, value) =>
          MapEntry(key, ((value) as List<dynamic>).map((e) => UserLocation.fromJson(e)).toList()));
}
