import 'dart:convert';
import 'dart:ui';
import 'package:flutter/services.dart' show rootBundle;
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/models/reward.dart';

class Ref {
  static final Ref _singleton = Ref._internal();

  factory Ref() {
    return _singleton;
  }

  Ref._internal();

  /// keep this method to get the translation, quicker than list model method
  Map<String, dynamic>? refs;
  Map<String, dynamic>? fallbackRefs;
  Refs? mbRefs;
  Refs? mbFallbackRefs;
  String? currentLang;

  Refs get() {
    if (mbRefs == null) {
      return mbFallbackRefs!;
    }
    return mbRefs!;
  }

  List<Country> getActiveCountries() {
    List<Country> countries = get().countries.where((c) => c.isActive).toList();
    countries.sort((a, b) {
      return a.name.toLowerCase().compareTo(b.name.toLowerCase());
    });
    return countries;
  }

  List<AppointmentRequestCause> getCauses() {
    List<AppointmentRequestCause> causes = get().appointmentRequestCauses;
    causes.sort((a, b) {
      return a.label.toLowerCase().compareTo(b.label.toLowerCase());
    });
    return causes;
  }

  List<NoteType> getNoteTypes() {
    List<NoteType> types = get().petNoteTypes;
    types.sort((a, b) {
      return a.label.toLowerCase().compareTo(b.label.toLowerCase());
    });
    return types;
  }

  Color getPetSmileLevelColor(int? pslId) {
    switch (pslId) {
      case 5:
        return const Color.fromRGBO(119, 51, 255, 1);
      case 4:
        return const Color.fromRGBO(201, 176, 55, 1);
      case 3:
        return const Color.fromRGBO(215, 215, 215, 1);
      case 2:
        return const Color.fromRGBO(173, 138, 86, 1);
      case 1:
      default:
        return const Color.fromRGBO(255, 255, 240, 1);
    }
  }

  List<ReminderType> getReminderTypes() {
    List<ReminderType> types = get().reminderTypes;
    types.sort((a, b) {
      return a.label.toLowerCase().compareTo(b.label.toLowerCase());
    });
    return types;
  }

  List<Treatment> getTreatmentForPetAndType(Pet? pet, int? type) {
    if (pet == null) return <Treatment>[];

    /// a vaccine is not available in some countries
    return get()
        .treatments
        .where((tr) => (tr.speciesId == pet.speciesId &&
            tr.valence?.trim() != '' &&
            tr.type == type))
        .toList();
  }

  List<Reward> get referenceBadges {
    return get().rewards.where((reward) => reward.type == 0).toList();
  }

  List<Reward> get referenceAvatars {
    return get().rewards.where((reward) => reward.type == 1).toList();
  }

  Future<void> setRefs(String? refJson) async {
    if (refJson == null || refJson.trim() == '') {
      refJson = SettingsDelegate().prefs.getString('refs');
    }
    try {
      initRef(refJson!);
    } catch (e) {
      Tools.debugPrint('cant load ==> use fallback default');
    }
  }

  Future<void> initFallBackRef() async {
    var refJson = await rootBundle.loadString('assets/json/ref.json');
    Map<String, dynamic> refObject = jsonDecode(refJson);
    fallbackRefs = refObject['guiTexts'];
    mbFallbackRefs = Refs.fromJson(refObject);
  }

  void initRef(String ref) {
    Map<String, dynamic> refObject = jsonDecode(ref);
    refs = refObject['guiTexts'];
    mbRefs = Refs.fromJson(refObject);
  }

  String tr(String code, [String? ifNullValue]) {
    if (refs != null && refs!.containsKey(code)) {
      return refs![code];
    }
    if (fallbackRefs != null && fallbackRefs!.containsKey(code)) {
      return fallbackRefs![code];
    }

    return ifNullValue ?? code;
  }
}
