import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart' show IterableExtension;
import 'package:intl/intl.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/login_data.dart';
import 'package:mybuddy/models/message.dart';
import 'package:mybuddy/models/notification.dart';
import 'package:mybuddy/models/period.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/models/ref_settings.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/models/task.dart';
import 'package:mybuddy/models/workout.dart';

class Data {
  static final Data _singleton = Data._internal();

  factory Data() {
    return _singleton;
  }

  Data._internal();

  LoginData? data;

  Ref ref = Ref();
  RefSettings refSettings = RefSettings();

  final DateTime start = DateTime.now();
  final StreamController<LoginData?> controller =
      StreamController<LoginData?>.broadcast();

  void addNotificationFromPush(MBNotification _notification) {
    bool found = false;

    /// can be already added (received and accepted)
    get().notifications.forEach((n) => found |= _notification == n);
    if (!found) {
      get().notifications.add(_notification);
      sendEvent();
    }
  }

  double convertLengthFromUserUnitStr(String distanceStr) {
    UnitLength unit = getUnitLength();
    return double.parse(distanceStr) * unit.toMeters;
  }

  String convertLengthToUserUnitStr(double distance, {bool withUnit = true}) {
    UnitLength unit = getUnitLength();
    distance = distance / unit.toMeters;
    String value = distance.toStringAsFixed(0);
    if (!withUnit) return value;
    return '$value ${unit.shortName}';
  }

  double convertWeightFromUserUnitStr(String weightStr) {
    UnitWeight unit = getUnitWeight();
    weightStr = weightStr.replaceAll(',', '.');
    return double.parse(weightStr) * unit.toKilos;
  }

  double convertWeightToUserUnit(double weight) {
    UnitWeight unit = getUnitWeight();
    return weight / unit.toKilos;
  }

  String convertWeightToUserUnitStr(double weight, {bool withUnit = true}) {
    UnitWeight unit = getUnitWeight();
    weight = weight / unit.toKilos;
    String value = weight.toLocalStringAsPrecision(3);
    if (!withUnit) return value;
    return '$value ${unit.shortName}';
  }

  DateTime? dateTimeFromUserDateStr(String input) {
    try {
      var d = DateFormat(getUserDateFormat()).parse(input);
      return d;
    } catch (e) {
      return null;
    }
  }

  DateTime? dateTimeFromUserDateTimeStr(String input) {
    try {
      var d = DateFormat(getUserDateFormat() + ' ' + getUserTimeFormat())
          .parseStrict(input);
      return d;
    } catch (e) {
      return null;
    }
  }

  String dateTimeToUserDateStr(DateTime? date, {bool dayOfWeek = false}) {
    if (date == null) return '';
    try {
      String format = dayOfWeek ? 'EEEE ' : '';
      format += getUserDateFormat();

      return DateFormat(format, SettingsDelegate().getLocaleString())
          .format(date);
    } catch (e) {
      return '';
    }
  }

  String dateTimeToUserDateTimeStr(DateTime? date) {
    if (date == null) return '';
    try {
      return DateFormat(getUserDateFormat() + ' ' + getUserTimeFormat())
          .format(date);
    } catch (e) {
      return '';
    }
  }

  String dateTimeToUserTimeStr(DateTime? date) {
    if (date == null) return '';
    try {
      return DateFormat(getUserTimeFormat()).format(date);
    } catch (e) {
      return '';
    }
  }

  void dispose() {
    controller.close();
  }

  String formatDateFromStr(String date) {
    try {
      var d = getDate(date)!;
      return DateFormat(getUserDateFormat()).format(d);
    } catch (e) {
      return '';
    }
  }

  String formatDuration(DateTime from, DateTime to, {int maxDurationType = 3}) {
    /// need to calc, years, then month, then days
    int years, months, days;

    /// CALC YEARS
    days = to.difference(from).inDays;
    years = days ~/ 365; //integer division

    DateTime birthPlusYears = DateTime(from.year + years, from.month, from.day);
    if (birthPlusYears.isAfter(to)) {
      years -= 1;
    }
    birthPlusYears = DateTime(from.year + years, from.month, from.day);

    /// CALC MONTHS
    days = to.difference(birthPlusYears).inDays;
    months = days ~/ 30; //integer division
    DateTime birthPlusYearsPlusMonths = DateTime(
        birthPlusYears.year + (birthPlusYears.month + months > 12 ? 1 : 0),
        birthPlusYears.month +
            months -
            (birthPlusYears.month + months > 12 ? 12 : 0),
        birthPlusYears.day);
    if (birthPlusYearsPlusMonths.isAfter(to)) {
      months -= 1;
    }
    birthPlusYearsPlusMonths = DateTime(
        birthPlusYears.year + (birthPlusYears.month + months > 12 ? 1 : 0),
        birthPlusYears.month +
            months -
            (birthPlusYears.month + months > 12 ? 12 : 0),
        birthPlusYears.day);

    /// CALC DAYS
    days = to.difference(birthPlusYearsPlusMonths).inDays;
    List<String> stringList = [];
    if (years != 0) {
      stringList.add(getDurationYear(years));
    }
    if (months != 0) {
      stringList.add(getDurationMonth(months));
    }
    if (days != 0 || years == 0 && months == 0) {
      stringList.add(getDurationDay(days));
    }

    if (stringList.length > maxDurationType) {
      stringList = stringList.sublist(0, maxDurationType);
    }
    return stringList.join(' ');
  }

  String formatDurationFromNowTo(DateTime? to,
      {int maxDurationType = 3, bool limitTodDay = false, String prefix = ''}) {
    if (to == null) {
      return '-';
    }
    DateTime now = DateTime.now();
    Duration diff = to.difference(now);
    if (!limitTodDay && diff.inDays < 2) {
      return prefix + getDurationHour(diff.inHours);
    } else if (limitTodDay && diff.inDays == 1) {
      switch (SettingsDelegate().getLang()) {
        case 'fr':
        case 'lu':
        case 'be':
        case 'ch':
        case 'ca':
          return 'Demain';
        default:
          return 'Tomorrow';
      }
    } else if (limitTodDay && diff.inDays == 0) {
      switch (SettingsDelegate().getLang()) {
        case 'fr':
        case 'lu':
        case 'be':
        case 'ch':
        case 'ca':
          return "Aujourd'hui";
        default:
          return 'Today';
      }
    }
    return prefix + formatDuration(now, to, maxDurationType: maxDurationType);
  }

  String formatDurationToNowFrom(DateTime date, {int maxDurationType = 3}) {
    try {
      /// CALC YEARS
//      DateTime birthDate = getDate(date, format);
      DateTime now = DateTime.now();
      return formatDuration(date, now, maxDurationType: maxDurationType);
    } catch (e) {
      return '';
    }
  }

  LoginData get() {
    if (data == null) {
      return LoginData();
    }
    return data!;
  }

  List<AppointmentRequest> getActiveAppointmentRequests() {
    DateTime _now = DateTime.now();

    /// set to the end of the day
    _now = DateTime(_now.year, _now.month, _now.day, 23, 59, 59);
//    _now = new DateTime(2020, 2, 1, 23, 59, 59);//TODO REMOVE
    return get()
        .appointmentRequests
        .where((appointment) {
          bool _display = false;
          if (appointment.petActivityId != null) {
            PetActivity? pa = get().getPetActivity(appointment.petActivityId);
            if (pa != null) {
              _display = _now.isBefore(pa.dateStart!);
            }
          } else {
            for (Period p in appointment.periods) {
              if (p.dateStart != null) {
                _display |= _now.isBefore(
                    p.dateStart!); //no need to check for dateEnd, same day
              }
            }
          }
          return _display;
        })
        .toList()
        .reversed
        .toList();
  }

  List<PetActivity> getAllWeightsForPet(int? petId) {
    List<PetActivity> weightActivities = <PetActivity>[];
    if (petId == null) return weightActivities;

    var petActivities =
        getFilteredPetActivities(petId: petId, chronoFilter: ChronoFilter.past)
            .toList();
    for (var pa in petActivities) {
      if ((pa.activityId == 6 && pa.typeId == 2 || pa.activityId == 8) &&
          pa.weight != null) {
        weightActivities.insert(0, pa);
      }
    }
    return weightActivities;
  }

  AppointmentRequest? getAppointmentWithPetActivity(int? petActivityId) {
    if (petActivityId == null) return null;
    return get()
        .appointmentRequests
        .singleWhereOrNull((ar) => ar.petActivityId == petActivityId);
  }

  List<MBService> getClinics() {
    List<MBService> result =
        get().services.where((f) => f.serviceTypeId == 1).toList();
    result.sort();
    return result;
  }

  List<MBService> getServices() {
    List<MBService> result =
        get().services.where((f) => f.serviceTypeId == 2).toList();

    getClinics()
        .forEach((clinic) => result.addAll(getInternalServices(clinic)));
    result.sort();
    return result;
  }

  List<MBService> getInternalServices(MBService clinic) {
    List<MBService> result = <MBService>[];

    if (clinic.services != null && clinic.services!.isNotEmpty) {
      for (MBService s in clinic.services!) {
        if (s.serviceTypeId == 2) {
          result.add(s);
        }
      }
    }
    result.sort();
    return result;
  }

  List<MBService> allServicesGrouped() {
    List<MBService> results = <MBService>[];
    List<MBService> internals = <MBService>[];

    MBService? realFavService = get()
        .services
        .singleWhereOrNull((s) => get().owner.favoriteClinicId == s.id);
    if (realFavService != null) {
      results.add(realFavService);
    }

    getClinics().forEach((s) {
      if (s.id != realFavService?.id) {
        results.add(s);
      }
      internals.addAll(getInternalServices(s));
    });

    List<MBService> unofficial = get().services.where((f) {
      return f.serviceTypeId == 2 && f.id != realFavService?.id;
    }).toList();

    results.addAll(unofficial);

    results.addAll(internals);

    return results;
  }

  List<MBService> getClinicsForAppointment() {
    List<MBService> result = get()
        .services
        .where((f) => (f.official && f.acceptAppointment == true))
        .toList();
    result.sort();
    return result;
  }

  List<MBService> getClinicsForMessage() {
    List<MBService> result = get()
        .services
        .where((f) => (f.official && f.acceptMessage == true))
        .toList();
    result.sort();
    return result;
  }

  List<MBService> getClinicsForOrder() {
    List<MBService> result = get()
        .services
        .where((f) => (f.official && f.acceptOrder == true))
        .toList();
    result.sort();
    return result;
  }

  List<PetActivity> getContractsForPet({int? petId, int? typeFilter = 1}) {
    List<PetActivity> list = [];
    for (PetActivity pa in get().petActivities) {
      if (pa.activityId != 3) continue;
      if (pa.typeId != typeFilter) continue;

      /// filter if pet indicated
      if (petId != null) {
        var isThisPet = false;
        for (var pet in pa.pets) {
          if (pet.id == petId) {
            isThisPet = true;
          }
        }

        /// remove if not for this pet
        if (!isThisPet) continue;
      }

      list.add(pa);
    }

    /// sort list
    list.sort((a, b) {
      if (a.dateStart == null) {
        return 1;
      } else if (b.dateStart == null) {
        return -1;
      } else {
        return b.dateStart!.compareTo(a.dateStart!);
      }
    });
    return list;
  }

  Country? getCountry() {
    return ref.get().getCountry(get().owner.countryId);
  }

  String getDurationDay(int day) {
    String tr;
    if (day < 1) {
      switch (SettingsDelegate().getLang()) {
        case 'fr':
        case 'lu':
        case 'be':
        case 'ch':
        case 'ca':
          return 'moins d\'un jour';
        default:
          return 'less than a day';
      }
    } else if (day <= 1) {
      switch (SettingsDelegate().getLang()) {
        case 'fr':
        case 'lu':
        case 'be':
        case 'ch':
        case 'ca':
          tr = ' jour';
          break;
        default:
          tr = ' day';
      }
    } else {
      switch (SettingsDelegate().getLang()) {
        case 'fr':
        case 'lu':
        case 'be':
        case 'ch':
        case 'ca':
          tr = ' jours';
          break;
        default:
          tr = ' days';
      }
    }
    return day.toString() + tr;
  }

  String getDurationHour(int hours) {
    String tr;
    if (hours < 1) {
      switch (SettingsDelegate().getLang()) {
        case 'fr':
        case 'lu':
        case 'be':
        case 'ch':
        case 'ca':
          return 'moins d\'une heure';
        default:
          return 'less than an hour';
      }
    } else if (hours == 1) {
      switch (SettingsDelegate().getLang()) {
        case 'fr':
        case 'lu':
        case 'be':
        case 'ch':
        case 'ca':
          tr = ' heure';
          break;
        default:
          tr = ' hour';
      }
    } else {
      switch (SettingsDelegate().getLang()) {
        case 'fr':
        case 'lu':
        case 'be':
        case 'ch':
        case 'ca':
          tr = ' heures';
          break;
        default:
          tr = ' hours';
      }
    }
    return hours.toString() + tr;
  }

  String getDurationMonth(int month) {
    String tr;
    if (month == 1) {
      switch (SettingsDelegate().getLang()) {
        case 'fr':
        case 'lu':
        case 'be':
        case 'ch':
        case 'ca':
          tr = ' mois';
          break;
        default:
          tr = ' month';
      }
    } else {
      switch (SettingsDelegate().getLang()) {
        case 'fr':
        case 'lu':
        case 'be':
        case 'ch':
        case 'ca':
          tr = ' mois';
          break;
        default:
          tr = ' months';
      }
    }
    return month.toString() + tr;
  }

  String getDurationYear(int year) {
    String tr;
    if (year == 1) {
      switch (SettingsDelegate().getLang()) {
        case 'fr':
        case 'lu':
        case 'be':
        case 'ch':
        case 'ca':
          tr = ' an';
          break;
        default:
          tr = ' year';
      }
    } else {
      switch (SettingsDelegate().getLang()) {
        case 'fr':
        case 'lu':
        case 'be':
        case 'ch':
        case 'ca':
          tr = ' ans';
          break;
        default:
          tr = ' years';
      }
    }
    return year.toString() + tr;
  }

  MBService? getFavoriteClinic() {
    int? favoriteClinicId = get().owner.favoriteClinicId;
    MBService? service;
    if (favoriteClinicId != null) {
      service = get().getService(favoriteClinicId);
    }

    if (service == null && get().services.isNotEmpty) {
      service = get().services.first;
    }

    return service;
  }

  List<PetActivity> getFilteredPetActivities(
      {int? petId,
      ChronoFilter chronoFilter = ChronoFilter.none,
      int? typeFilter = 0,
      LivingState livingState = LivingState.both}) {
    /// get allowed pet id list
    List<int?> petIds = [];
    if (petId != null) {
      petIds.add(petId);
    } else {
      petIds = getPets(livingState: livingState).map((p) => p.id).toList();
    }

    List<PetActivity> list = [];
    var now = DateTime.now();
    for (PetActivity pa in get().petActivities) {
      /// filter if pet indicated or pet filtered
      if (petId != null || livingState != LivingState.both) {
        bool found = false;
        for (int? _petId in petIds) {
          found |= pa.forThisPet(petId: _petId);
        }

        /// remove if not for these pets
        if (!found) continue;
      }

      /// specific past/coming
      if (chronoFilter == ChronoFilter.past) {
        if (pa.activityId == 2 && pa.nextOccurrence != null) continue;
        if (pa.activityId != 2 && pa.dateStart!.isAfter(now)) continue;
      } else if (chronoFilter == ChronoFilter.future) {
        if (pa.activityId == 2 && pa.nextOccurrence == null) continue;
        if (pa.activityId != 2 && pa.dateStart!.isBefore(now)) continue;
      }

      /// remove ignored activity (images, etc...)
      if (![3, 4, 5].contains(typeFilter) &&
          [3, 4, 5].contains(pa.activityId)) {
        continue;
      }

      /// filter
      if (typeFilter == 999) {
        if (pa.serviceId == null) continue;
      } else if (typeFilter != 0) {
        if (typeFilter != pa.activityId) continue;
      }
      list.add(pa);
    }

    /// sort list
    if (chronoFilter == ChronoFilter.past ||
        chronoFilter == ChronoFilter.none) {
      list.sort((a, b) {
        if (a.dateStart == null) {
          return 1;
        } else if (b.dateStart == null) {
          return -1;
        } else {
          return b.dateStart!.compareTo(a.dateStart!);
        }
      });
    } else if (chronoFilter == ChronoFilter.future) {
      list.sort((a, b) {
        DateTime? aDate = a.activityId == 2 ? a.nextOccurrence : a.dateStart;
        DateTime? bDate = b.activityId == 2 ? b.nextOccurrence : b.dateStart;
        if (aDate == null) {
          return 1;
        } else if (bDate == null) {
          return -1;
        } else {
          return aDate.compareTo(bDate);
        }
      });
    }

    return list;
  }

  List<MBService> getOfficialClinics() {
    List<MBService> result = get().services.where((f) => f.official).toList();
    result.sort();
    return result;
  }

  List<Pet> getPets(
      {LivingState livingState = LivingState.alive,
      bool onlyDog = false,
      bool onlySelectable = false}) {
    List<Pet> result;
    switch (livingState) {
      case LivingState.alive:
        result = get().pets.where((f) => (f.deceased == false)).toList();
        break;
      case LivingState.dead:
        result = get().pets.where((f) => (f.deceased == true)).toList();
        break;
      case LivingState.both:
        result = get().pets;
        break;
      default:
        result = <Pet>[];
    }
    result.sort();
    if (onlyDog) {
      result = result.where((pet) => pet.speciesId == 2).toList();
    }
    if (onlySelectable) {
      result = result
          .where((pet) =>
              pet.activityStatus != ActivityWalkingStatus.notSelectable)
          .toList();
    }
    return result;
  }

  List<MBService> getPimsService() {
    List<MBService> _services =
        get().services.where((s) => s.pimsId != null).toList();
    _services.sort();
    return _services;
  }

  UnitLength getUnitLength() {
    return ref.get().getUnitLength(get().owner.units.unitLengthId);
  }

  UnitWeight getUnitWeight() {
    return ref.get().getUnitWeight(get().owner.units.unitWeightId);
  }

  List<Pet> getUnlinkPets(MBService? service) {
    List<Pet> result = get().pets.where((pet) {
      return pet.deceased == false && !pet.pimsServices.contains(service);
    }).toList();
    result.sort();

    return result;
  }

  int getUnreadMessageCount() {
    return get().messages.where((n) => n.read() == false).length;
  }

  int getUnreadNotificationCount() {
    return get().notifications.where((n) => n.read == false).length;
  }

  String getUserDateFormat({bool shortyear = false, bool isLiteral = false}) {
    Country userCountry = ref.get().getCountry(get().owner.countryId)!;
    String dateFormat;
    switch (userCountry.dateFormat) {
      case 'm/d/Y':
        dateFormat = isLiteral ? 'EEEE, MMMM d y' : 'MM/dd/y';
        break;
      case 'd/m/Y':
      default:
        dateFormat = isLiteral ? 'EEEE d MMMM y' : 'dd/MM/y';
        break;
    }
    if (shortyear) dateFormat = dateFormat.replaceFirst('y', 'yy');
    return dateFormat;
  }

  String getUserTimeFormat() {
    Country userCountry = ref.get().getCountry(get().owner.countryId)!;
    String timeFormat;
    switch (userCountry.timeFormat) {
      case 'h:i A':
        timeFormat = 'hh:mm a';
        break;
      case 'H:i':
      default:
        timeFormat = 'HH:mm';
        break;
    }
    return timeFormat;
  }

  String getWeightForPet(int? petId) {
    if (petId != null) {
      List<PetActivity> petActivities = getFilteredPetActivities(
        petId: petId,
        chronoFilter: ChronoFilter.past,
      );
      for (PetActivity pa in petActivities) {
        if (((pa.activityId == 6 && pa.typeId == 2) || pa.activityId == 8) &&
            pa.weight != null) {
          return convertWeightToUserUnitStr(pa.weight!);
        }
      }
    }
    return 'APPLICATION_MOBILE_LABEL_WEIGHT_ADD_WEIGHT'.tr();
  }

  List<Workout> getPetWorkouts({int? petId}) {
    if (petId == null) return <Workout>[]; // TODO control

    return get()
        .workouts
        .where((w) => w.pets.any((p) => p.id == petId))
        .toList();
  }

  MBMessage? getMessage(int? messageId) {
    // Tools.debugPrint('messageId ' + messageId.toString());
    if (messageId == null) {
      return null;
    }
    return get().messages.singleWhereOrNull((m) => m.id == messageId) ??
        MBMessage.light(messageId);
  }

  void sendEvent() {
    Tools.debugPrint('fire event');
    controller.sink.add(data);
  }

  void setOwnerPoints(int points) {
    if (data != null) {
      data!.owner.points = points;
      sendEvent();
    }
  }

  void storeData() {
    String json = jsonEncode(data!.toJson());
    sendEvent();
    unawaited(SettingsDelegate().prefs.setString('data', json));
    Tools.debugPrint('😀');
  }

  void setData(String? dataJson) {
    if (dataJson != null) {
      // TODO add verification test
      try {
        data = LoginData.fromJson(json.decode(dataJson));
        sendEvent();
        setNativeBadge();
      } catch (exception) {
        Tools.debugPrint('JSON ERROR');
      }
    }
//    if(ref.get() !=null && ref.get().currentLang != get().currentLang){
//      MbApiRef().Request(null);
//    }
  }

  void setNativeBadge() {
    int value = getUnreadNotificationCount();
  }

  void updateTasks(json) {
    json.forEach((t) {
      Task newTask = Task.fromJson(t);
      Task? oldTask =
          get().tasks.singleWhereOrNull((t) => t.code == newTask.code);
      if (oldTask != newTask) {
        if (oldTask != null) {
          data!.tasks.remove(oldTask);
        }
        data!.tasks.add(newTask);
      }
    });
  }

  static String dateTimeToApiDateStr(DateTime? date) {
    if (date == null) return '';
    try {
      return DateFormat('y-MM-dd').format(date);
    } catch (e) {
      return '';
    }
  }

  static String dateTimeToApiDateTimeStr(DateTime? date, {bool toUtc = false}) {
    if (date == null) return '';
    try {
      if (toUtc) {
        return DateFormat('y-MM-dd HH:mm:ss').format(date.toUtc());
      }
      return DateFormat('y-MM-dd HH:mm:ss').format(date);
    } catch (e) {
      return '';
    }
  }

  static DateTime? getDate(String? date,
      {String format = 'y-MM-dd HH:mm:ss', bool utc = false}) {
    try {
      if (date == null || date.trim() == '') {
        return null;
      }
      return DateFormat(format).parse(date, utc).toLocal();
    } catch (e) {
      return null;
    }
  }

  Future<void> setRefSettings(String? refJson) async {
    if (refJson == null || refJson.trim() == '') {
      refJson = SettingsDelegate().prefs.getString('refs-settings');
    }
    try {
      initRefSettings(refJson!);
    } catch (e) {
      Tools.debugPrint('cant load ==> use fallback default');
    }
  }

  void initRefSettings(String ref) {
    refSettings = referenceSettingsFromJson(ref);
  }
}
