import 'dart:async';
import 'dart:convert';
import 'dart:ui';

import 'package:mybuddy/Api/mb_api_ref.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/settings.dart';

import 'package:shared_preferences/shared_preferences.dart';

class SettingsDelegate {
  static final SettingsDelegate _singleton = SettingsDelegate._internal();

  factory SettingsDelegate() {
    return _singleton;
  }

  SettingsDelegate._internal();

  Settings? _settings;
  late SharedPreferences prefs;
  late Locale locale;

  Settings get() {
    return _settings!;
  }

  String getLang() {
    if (_settings == null || _settings!.lang == null) {
      return locale.countryCode!.toLowerCase();
    }
    return _settings!.lang!.toLowerCase();
  }

  String getLocaleString() {
    switch (getLang()) {
      case 'fr':
        return 'fr_FR';
      case 'es':
        return 'es_ES';
      case 'gb':
        return 'en_GB';
      case 'lu':
        return 'fr_LU';
      case 'be':
        return 'fr_BE';
      case 'ch':
        return 'fr_CH';
      case 'ca':
        return 'fr_CA';
      case 'ie':
        return 'en_IE';
      case 'us':
      default:
        return 'en_US';
    }
  }

  Future<void> load(Locale loc) async {
    prefs = await SharedPreferences.getInstance();
    String? _settingsJson = prefs.getString('settings');
    if (_settingsJson != null && _settingsJson != '') {
      _settings = Settings.fromJson(jsonDecode(_settingsJson));
    } else {
      _settings = Settings();
      await save();
    }
    locale = loc;
  }

  Future<void> save() async {
    unawaited(prefs.setString('settings', jsonEncode(_settings!.toJson())));
  }

  void setHomeInPetMode(bool showPet) {
    _settings!.forcePetView = showPet;
    Data().sendEvent();
    save();
  }

  Future<void> setLang(String? lang) async {
    // print('setLang');
    // print('old: ${_settings.lang} new: $lang');
    bool shouldRefreshRef = _settings!.lang != lang;
    _settings!.lang = lang;
    await save();
    if (shouldRefreshRef) {
      await MbApiRef().refRequest(null);
    }
  }

  void setPetProfileBirthDatePreferredMode(int mode) {
    _settings!.petProfileBirthDatePreferredMode = mode;
    save();
  }
}
