import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/stop_watch_timer.dart';

class StopWatchWithInitialTime {
  late StopWatchTimer stopWatch;

  StopWatchWithInitialTime({Function(int)? onTimeTick}) {
    stopWatch = StopWatchTimer(onChangeRawSecond: onTimeTick);
  }

  get elapsedDuration{
    return Duration(milliseconds: elapsedMillis);
  }

  get elapsedMillis{
    return (stopWatch.secondTime.value * 1000);
  }

  set milliseconds(int timeInMilliseconds){
    int timeInSeconds = timeInMilliseconds ~/ 1000;
    stopWatch.setPresetSecondTime(timeInSeconds);
  }

  void startTimer() {
    Tools.debugPrint("Starting time");
    stopWatch.onStartTimer();
  }

  void stopTimer() {
    stopWatch.onStopTimer();
  }

  bool isTimerRunning() {
    return stopWatch.isRunning;
  }

  void resetTimer() {
    stopWatch.onResetTimer();
  }

  void dispose() {
    stopWatch.dispose();
  }
}