import 'package:flutter/foundation.dart';
import 'package:mybuddy/Tools/tools.dart';

const String rootPathProdWt = 'https://wfadapiebs.wooftrax.com';
const String rootPathf3Dev = 'http://demo12.folio3.com:3000';
const String rootPathf3QA = 'http://qa-wooftrax.folio3.com:8080';
const String rootPathf3Staging = 'https://wooftrax-staging.folio3.com';

/// ##################################################

const String pathApi = '/api/v6/mobile/';
const String pathImage = '/api/image/';
const String signInWithAppleCallback = '/us/apple_callback';

class AppDomainDelegate {
  static final AppDomainDelegate _singleton = AppDomainDelegate._internal();

  factory AppDomainDelegate() {
    return _singleton;
  }

  AppDomainDelegate._internal();

  static late String rootPath;

  static String get baseUrlPetOwner => rootPath + pathApi;

  static String get baseUrlImage => rootPath + pathImage;

  static bool get inProduction => rootPath == rootPathProdWt;

  static String get signWithAppleCallback => rootPath + signInWithAppleCallback;

  static void initializePath(String path) {
    Tools.debugPrint('on app initialization base url: $path');
    if (kReleaseMode) {
      rootPath = path;
    } else {
      rootPath = rootPathf3Staging;
      // rootPath = path;
    }
  }
}
