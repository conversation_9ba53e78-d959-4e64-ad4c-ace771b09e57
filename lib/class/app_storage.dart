import 'dart:io';
import 'dart:ui' as ui;

import 'package:image/image.dart' as im;
import 'dart:math' as math;

import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

class AppStorage {
  Future<String> get _localPath async {
    final Directory directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  Future<String> get _gpxFilesFolder async {
    final String localPath = await _localPath;
    final String gpxPath = '$localPath/gpx';
    final Directory dir = Directory(gpxPath);

    if (await dir.exists()) return gpxPath;

    // recursive: true ensures all folders exist in the path
    await dir.create(recursive: true);
    return gpxPath;
  }

  Future<String> get _tmpPath async {
    final Directory directory = await getTemporaryDirectory();

    return directory.path;
  }

  Future<File> getFileFromApplicationDocumentsDirectory(String filename) async {
    final String path = await _localPath;
    try {
      return File('$path/$filename');
    } catch (e) {
      throw FileSystemException(e.toString(), path);
    }
  }

  Future<String?> readGpx(String fileName) async {
    if (fileName.trim().isEmpty) return null;
    final String gpxFolder = await _gpxFilesFolder;
    try {
      return await File('$gpxFolder/$fileName').readAsString();
    } on FileSystemException catch (_) {
      return null;
    }
  }

  Future<void> writeGpx(String fileName, String gpxString) async {
    final String gpxFolder = await _gpxFilesFolder;
    await File('$gpxFolder/$fileName').writeAsString(gpxString);
  }

  Future<void> deleteAllGpx() async =>
      Directory(await _gpxFilesFolder).delete(recursive: true);

  Future<String?> read(String filename) async {
    if (filename.trim() == '') {
      return null;
    }
    final File file = await getFileFromApplicationDocumentsDirectory(filename);

    if (await file.exists()) {
      return await file.readAsString();
    }

    return null;
  }

  Future<bool> exists(String filename) async {
    if (filename.trim() == '') {
      return false;
    }
    final File file = await getFileFromApplicationDocumentsDirectory(filename);

    return file.exists();
  }

  Future<File?> write(String filename, String contents) async {
    if (filename.trim() == '') {
      return null;
    }
    final File file = await getFileFromApplicationDocumentsDirectory(filename);

    return file.writeAsString(contents);
  }

  Future<File> convertImageToFile(ui.Image img) async {
    final String tmpPath = await _tmpPath;
    final int rand = math.Random().nextInt(100000000);
    ByteData? byteData;
    try {
      byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    } catch (e) {
      return Future.error(e);
    }
    Uint8List buffer = byteData!.buffer.asUint8List(
      byteData.offsetInBytes,
      byteData.lengthInBytes,
    );

    _CompressObject compressObject = _CompressObject(buffer, tmpPath, rand);

    try {
      return await _compressImage(compressObject);
    } catch (e) {
      return Future.error(e);
    }
  }

  Future<bool> createDirectory(String directoryName) async {
    final path = await _localPath;
    final myDir = Directory('$path/$directoryName');

    bool exists = await myDir.exists();

    if (exists) {
      return true;
    }

    await myDir.create(recursive: true);

    return await myDir.exists();
  }

  Future<List<String>> listDirectory(String directoryName) async {
    try {
      final path = await _localPath;
      final myDir = Directory('$path/$directoryName');

      bool exists = await myDir.exists();

      if (!exists) {
        return Future.error('directory not exists');
      }

      List<FileSystemEntity> entities = myDir.listSync();
      List<String> files = [];

      for (var entity in entities) {
        bool isThere = await entity.exists();
        if (isThere && entity is File) {
          String path = entity.path;
          String filename = path.split('/').last;
          files.add(filename);
        }
      }

      return files;
    } catch (e) {
      return Future.error(e);
    }
  }
}

/// compress image avatar with an isolate
Future<File> _compressImage(_CompressObject object) async {
  return compute(_decodeImage, object);
}

File _decodeImage(_CompressObject object) {
  im.Image? image;
  try {
    image = im.decodeImage(object.uint8list);
  } catch (e) {
    throw im.ImageException(e.toString());
  }

  File decodedImageFile =
      File('${object.path}/mybuddy_wooftrax_avatar_${object.rand}.png');
  decodedImageFile.writeAsBytesSync(im.encodePng(image!, level: 8));

  return decodedImageFile;
}

class _CompressObject {
  final Uint8List uint8list;
  final String path;
  final int rand;

  _CompressObject(this.uint8list, this.path, this.rand);
}
