import 'dart:convert';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:shared_preferences/shared_preferences.dart';

class OfflineService {
  static bool running = false;

  static Future<void> syncData() async {
    if (running) {
      Tools.debugPrint('already running');
      return;
    }

    running = true;

    if (Data().data == null) {
      // Tools.debugPrint('not logged, nothing to do');
      running = false;
      return;
    }
    if (!(await Tools().common.isOnline())) {
      // Tools.debugPrint('not connecter, can\'t work');
      running = false;
      return;
    }

    ///search not synced data

    List<dynamic> jobs = getUndoneJobs();
    if (jobs.isNotEmpty) {
      MBResponse response =
          await MbBaseApi().dioRequest(null, jobs[0]['route'], jobs[0]['data'], increasedTimeout: true);
      if (response.success) {
        if (response.body.containsKey('tasks') &&
            response.body['tasks'] != null) {
          Data().updateTasks(response.body['tasks']);
        }
        jobs.removeAt(0);
        storeUndoneJobs(jobs);
      }
    } else {
      // Tools.debugPrint('nothing to do');
    }

    running = false;
    return;
  }

  static void storeUndoneJob(String route, Map<String, dynamic> data) {
    List<dynamic> jobs = getUndoneJobs();
    jobs.add({'route': route, 'data': data});
    storeUndoneJobs(jobs);
  }

  static List<dynamic> getUndoneJobs() {
    List<dynamic> undoneJobs = <dynamic>[];
    SharedPreferences prefs = SettingsDelegate().prefs;
    String? jobsStr;
    if (prefs.containsKey('jobs')) {
      jobsStr = prefs.getString('jobs');
    }
    if (jobsStr != null) {
      undoneJobs.addAll(jsonDecode(jobsStr));
    }

    return undoneJobs;
  }

  static void storeUndoneJobs(List<dynamic> jobs) {
    SettingsDelegate settings = SettingsDelegate();
    settings.prefs.setString('jobs', jsonEncode(jobs));
  }

  static void clear() {
    storeUndoneJobs([]);
  }
}
