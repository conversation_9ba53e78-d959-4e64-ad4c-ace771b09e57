
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:flutter/services.dart';

class NumericalRangeFormatter extends TextInputFormatter {
  final double min;
  final double max;

  NumericalRangeFormatter({required this.min, required this.max});

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue,
      TextEditingValue newValue,
      ) {
    double parsed;
    try {
      parsed = double.parse(newValue.text);
    } catch (e) {
      return newValue;
    }

    if (newValue.text == '') {
      return oldValue;
    } else if (parsed < min) {
      return const TextEditingValue().copyWith(text: min.toStringRounded());
    } else {
      return parsed > max ? oldValue : newValue;
    }
  }
}