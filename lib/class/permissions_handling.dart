import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:permission_handler/permission_handler.dart';

Future<dynamic> askContactPermissionsAndPush(Widget widget) async {
  PermissionStatus permissionStatus = await _getContactPermission();
  if (permissionStatus == PermissionStatus.granted) {
    return await Tools().navigatorPush(widget);
  } else {
    handleInvalidPermissions(permissionStatus,'WOOFTRAX_ALERT_CONTACT_PERMISSION_DENIED_TITLE'
        .tr());
  }
}

Future<bool> askContactPermissions() async {
  PermissionStatus permissionStatus = await _getContactPermission();
  if (permissionStatus == PermissionStatus.granted) {
    return true;
  } else {
    handleInvalidPermissions(permissionStatus,'WOOFTRAX_ALERT_CONTACT_PERMISSION_DENIED_TITLE'
        .tr());
    return false;
  }
}

Future<bool> checkContactPermission() async {
  PermissionStatus permission = await Permission.contacts.status;

  return permission==PermissionStatus.granted;
}

Future<PermissionStatus> _getContactPermission() async {
  PermissionStatus permission = await Permission.contacts.status;
  if (permission != PermissionStatus.granted) {
    PermissionStatus permissionStatus = await Permission.contacts.request();
    return permissionStatus;
  } else {
    return permission;
  }
}

Future<void> handleInvalidPermissions(
    PermissionStatus permissionStatus,String text) async {
  return await showDialog(
    barrierColor: Colors.transparent,
    context: Tools().homeContext,
    builder: (BuildContext ctx) {
      return AlertDialog(
        title: Text(
          text,
        ),
        // content: Text(text!),
        actions: <Widget>[
          TextButton(
            onPressed: () async {
              await AppSettings.openAppSettings();
              Tools().navigatorPop(removeLast: false, value: false);
            },
            child: Text('WOOFTRAX_ALERT_PERMISSION_DENIED_OPEN_SETTINGS'
                .tr('Change app settings')),
          ),
          TextButton(
            onPressed: () {
              Tools().navigatorPop(removeLast: false, value: false);
            },
            child: Text('WOOFTRAX_ALERT_PERMISSION_DENIED_IGNORE'.tr('Ignore')),
          ),
        ],
      );
    },
  );

  String snackBarContent = '';
  if (permissionStatus == PermissionStatus.denied) {
    snackBarContent = 'Access to contact data denied';
  } else if (permissionStatus == PermissionStatus.permanentlyDenied) {
    snackBarContent = 'Contact data not available on device';
  }
  CommonTools().showMessage(Tools().homeContext, snackBarContent);
}
