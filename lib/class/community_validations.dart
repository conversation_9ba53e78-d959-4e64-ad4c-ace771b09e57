
import 'package:mybuddy/extensions/mybuddy_extensions.dart';

import '../models/community.dart';

class CommunityValidations{

  /// This method tries to find out the time difference between now and
  /// the last invite sent date and if the difference is greater than 24 hours
  /// then we let the user send the invitaion again


  static int resendDurationInMinutes = 1440;

  static bool checkResendValidity(LastInvite? lastInvite) {
    int difference = differenceResendValidity(lastInvite);
    return difference < 1;
  }

  static int differenceResendValidity(LastInvite? lastInvite) {
    if (lastInvite == null) {
      return 0;
    }

    DateTime invitedTime = DateTime.parse(
        lastInvite.date!.contains('Z')
            ? lastInvite.date!
            : lastInvite.date! + 'Z');
    DateTime now = DateTime.now().toUtc();

    Duration duration = now.difference(invitedTime);

    if (duration.inMinutes > resendDurationInMinutes) {
      return 0;
    }
    return resendDurationInMinutes - duration.inMinutes;

    // if (duration.inHours > 23) {
    //   return 0;
    // }
    // return 24 - duration.inHours;
  }

  static String resendMessage(LastInvite? lastInvite){
    int difference = (differenceResendValidity(lastInvite)/60).ceil();
    return 'APPLICATION_MOBILE_COMMUNITY_INVITATION_LIMIT'.tr(
        'Next Invite will be available in $difference ${difference>1?"hours":"hour"}');
  }

}