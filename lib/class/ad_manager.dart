import 'dart:io';

import 'package:flutter/foundation.dart';

/// temporary deactivated class
// TODO reactivate for production
class AdManager {
  /// !!!! DemoAdUnitId: /6499/example/banner
  static String get bannerAdUnitId {
    if (Platform.isAndroid) {
      /// wooftrax android banner: ca-app-pub-9068079999050700/6390308712
      return kReleaseMode ? 'ca-app-pub-9068079999050700/6390308712' : 'ca-app-pub-3940256099942544/6300978111';
    } else if (Platform.isIOS) {
      /// Wooftrax ios banner: ca-app-pub-9068079999050700/2592701996
      return kReleaseMode ? 'ca-app-pub-9068079999050700/2592701996' : 'ca-app-pub-3940256099942544/2934735716';
    } else {
      throw UnsupportedError('Unsupported platform');
    }
  }
  /// !!!! DemoAdUnitId: /6499/example/rewarded
  static String get videoAdUnitId {
    if (Platform.isAndroid) {
      /// wooftrax android rewarded: ca-app-pub-9068079999050700/3221521216
      return kReleaseMode ? 'ca-app-pub-9068079999050700/3221521216' : '/6499/example/rewarded';
    } else if (Platform.isIOS) {
      /// wooftrax ios rewarded: ca-app-pub-9068079999050700/5600889159
      return kReleaseMode ? 'ca-app-pub-9068079999050700/5600889159' : '/6499/example/rewarded';
    } else {
      throw UnsupportedError('Unsupported platform');
    }
  }

  static String get interstitialAdUnitId {
    if (Platform.isAndroid) {
      return kReleaseMode ? 'ca-app-pub-9068079999050700/2068221648' : 'ca-app-pub-3940256099942544/1033173712';
    } else if (Platform.isIOS) {
      /// wooftrax ios rewarded: ca-app-pub-9068079999050700/5600889159
      return kReleaseMode ? 'ca-app-pub-9068079999050700/8447857422' : 'ca-app-pub-3940256099942544/4411468910';
    } else {
      throw UnsupportedError('Unsupported platform');
    }
  }
}
