const mapBoxGlAccessToken =
    'pk.eyJ1IjoibWVkaXByb2R1Y3Rpb25zIiwiYSI6ImNramlyM2F0ZTR4ejQyc25wZmI2YXl5aWwifQ.wkATdnqLS0R-7CZbEHov0g';

/// SharedPreferences keys
const vaccinesInformationSharedPrefsKey = 'vaccines_information';
const isFirstLogin = 'is_first_login';

/// Wooftrax constants
const wooftraxPrivacyPolicyUrl = 'https://www.wooftrax.com/privacy';
const wooftraxAppLink = 'https://wooftraxwalkforadog.page.link/oZYN';

const wooftraxDataRead = "WoofTraxDataRead";
const wooftraxEmail = "WoofTraxEmail";
const wooftraxFirstName = "WoofTraxFirstName";
const wooftraxLastName = "WoofTraxLastName";
const wooftraxObjectId = "WoofTraxObjectId";
const dynamicLinkData = "DynamicLinkData";
const notificationPermissionAsked = "NotificationPermissionAsked";

const ongoingWalk = "OngoingWalk";
const freshSyncDone = "FreshSyncDone";
const pageNumberSynced = "PageNumberSynced";
const totalRecordsSynced = "TotalRecordsSynced";
const totalRecordsCount = "TotalRecordsCount";

const appsFlyerDevKey = "w46t8ZGJ3XNuBStAfmTPre";
const appsFlyerAppIdIos = "643857704";
const appsFlyerAppIdAndroid = "";

const customerIOSandBoxSiteID = "********************";
const customerIOSandBoxAPIKey = "********************";

const customerIOProductionSiteID = "********************";
const customerIOProductionAPIKey = "********************";

const customerIOSiteID = customerIOProductionSiteID;
const customerIOAPIKey = customerIOProductionAPIKey;

// CIO screen names
const cioScreenNameHome = "home";
const cioScreenNameCommunity = "community";
const cioScreenNameCommunityMyGroups = "communitymygroups";
const cioScreenNameChallengesAvailable = "challengesavailable";
const cioScreenNameChallengesJoined = "challengesjoined";
const cioScreenNameHistory = "history";

//Kickbox API Keys
const kickboxSandboxAPIKey =
    "test_e532f43a5da8314cd75d2b5ed769267b15e3a0570df28dfc7e5d360bb8e26215";
const kickboxProductionAPIKey =
    "*********************************************************************";

const kickboxAPIKey = kickboxProductionAPIKey;
