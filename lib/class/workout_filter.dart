import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/walk_model.dart';
import '../models/pet.dart';
import 'data.dart';

enum DateSelectionType { allTime, week, month, sixMonths, custom }

extension DateSelectionExtension on DateSelectionType {
  Map<String, dynamic> get toMap {
    switch (this) {
      case DateSelectionType.allTime:
        return {
          'name': 'APPLICATION_MOBILE_LABEL_WORKOUT_DATE_SELECTION_ALL_TIME'
              .tr('All Time'),
          'value': 1,
        };
      case DateSelectionType.week:
        return {
          'name': 'APPLICATION_MOBILE_LABEL_WORKOUT_DATE_SELECTION_PAST_WEEK'
              .tr('Past Week'),
          'value': 2,
        };
      case DateSelectionType.month:
        return {
          'name': 'APPLICATION_MOBILE_LABEL_WORKOUT_DATE_SELECTION_PAST_MONTH'
              .tr('Past Month'),
          'value': 3
        };
      case DateSelectionType.sixMonths:
        return {
          'name':
              'APPLICATION_MOBILE_LABEL_WORKOUT_DATE_SELECTION_PAST_SIX_MONTHS'
                  .tr('Past 6 Months'),
          'value': 4
        };
      case DateSelectionType.custom:
        return {
          'name': 'APPLICATION_MOBILE_LABEL_WORKOUT_DATE_SELECTION_CUSTOM'
              .tr('Custom Range'),
          'value': 5,
          'iconData': Icons.calendar_today_outlined
        };
      default:
        return {
          'name': 'APPLICATION_MOBILE_ERROR_DEFAULT_UNKNOWN'.tr('Unknown'),
          'value': null,
          'iconData': Icons.do_not_disturb_on
        };
    }
  }

  String get toLowercaseName {
    return toMap['name'].toString().toLowerCase();
  }

  String get toName {
    return toMap['name'].toString();
  }

  IconData? get toIcon {
    return toMap['iconData'];
  }
}

class WorkoutFilter {
  List<MoodType>? activities;
  List<Pet> pets = <Pet>[];
  DateSelectionType? dateType;
  DateTimeRange? range;
  DateTime? startDate, endDate;

  WorkoutFilter(
      {this.dateType,
      this.activities,
      this.startDate,
      this.endDate,
      this.range,
      this.pets = const <Pet>[]});

  WorkoutFilter copyWith({
    List<MoodType>? activities,
    DateSelectionType? dateType,
    DateTimeRange? range,
    DateTime? startDate,
    DateTime? endDate,
    List<Pet>? pets,
  }) =>
      WorkoutFilter(
        activities: activities ?? this.activities,
        dateType: dateType?? this.dateType,
        startDate: startDate ?? this.startDate,
        endDate:
            endDate ?? this.endDate,
        range: range ?? this.range,
        pets: pets ?? this.pets,
      );

  static WorkoutFilter defaultFilter() {
    return WorkoutFilter(
        activities: MoodType.values.toList(),
        dateType: DateSelectionType.allTime,
        startDate: DateTime.now().subtract(const Duration(days: 30)),
        endDate: DateTime.now(),
        pets: Data().get().pets.map((e) => e.copy()).toList());
  }

  String get startDateString => startDate!.toIso8601String().split("T")[0];

  String get endDateString => endDate!.toIso8601String().split("T")[0];

  List<int>? get moodTypeList => activities?.map((e) => e.toValue).toList();

  bool isMoodSelected(MoodType mood) {
    return activities?.contains(mood) ?? false;
  }

  bool isDateTypeSelected(DateSelectionType type) {
    return dateType == type;
  }

  bool isPetSelected(Pet pet) {
    return pets.contains(pet);
  }

  bool isFilterApplied() {
    return activities!.length != MoodType.values.length ||
        dateType != DateSelectionType.allTime ||
        pets.length != Data().get().pets.length;
  }

  bool isDogFilterApplied() {
    return pets.length != Data().get().pets.length;
  }

  int getFilterCount() {
    int filterCount = 0;
    if (activities!.length != MoodType.values.length) {
      filterCount++;
    }
    if (dateType != DateSelectionType.allTime) {
      filterCount++;
    }
    if (pets.length != Data().get().pets.length) {
      filterCount++;
    }
    return filterCount;
  }

  void assignValues(WorkoutFilter filter) {
    activities = filter.activities;
    startDate = filter.startDate;
    endDate = filter.endDate;
    range = filter.range;
    dateType = filter.dateType;
  }

  void setActivities(MoodType moodType) {
    if (activities?.contains(moodType) ?? false) {
      if (activities!.length > 1) {
        activities!.remove(moodType);
      }
    } else {
      activities!.add(moodType);
    }
  }

  void setDateRange(DateSelectionType type, {DateTimeRange? range}) {
    dateType = type;
    if (type == DateSelectionType.allTime) {
      startDate = DateTime.now().subtract(const Duration(days: 30));
      endDate = DateTime.now();
    } else if (type == DateSelectionType.month) {
      startDate = DateTime.now().subtract(const Duration(days: 30));
      endDate = DateTime.now();
    } else if (type == DateSelectionType.week) {
      startDate = DateTime.now().subtract(const Duration(days: 7));
      endDate = DateTime.now();
    } else if (type == DateSelectionType.sixMonths) {
      startDate = DateTime.now().subtract(const Duration(days: 180));
      endDate = DateTime.now();
    } else {
      startDate = range!.start;
      endDate = range.end;
    }
  }

  void setPets(Pet pet) {
    if (pets.contains(pet)) {
      if (pets.length > 1) {
        pets.remove(pet);
      }
    } else {
      pets.add(pet);
    }
  }

  void setAllPets(List<Pet> pets) {
    this.pets = pets.map((e) => e.copy()).toList();
  }
}
