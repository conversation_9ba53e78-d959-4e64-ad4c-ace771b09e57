import 'dart:async';
import 'dart:convert';

import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/models/petcarepacks.dart';


class PetCarePackDelegate {
  static final PetCarePackDelegate _singleton = PetCarePackDelegate._internal();

  factory PetCarePackDelegate() {
    return _singleton;
  }

  PetCarePackDelegate._internal();

  PetCarePacks homeCare = PetCarePacks();

  final StreamController<PetCarePacks> controller = StreamController<PetCarePacks>.broadcast();

  void dispose() {
    controller.close();
  }

  void sendEvent() {
    // print('fire event');
    controller.sink.add(homeCare);
  }

  void setPacks(String? dataJson) {
    if (dataJson != null) {
      // TODO add verification test
      homeCare = PetCarePacks.fromJson(json.decode(dataJson));
      sendEvent();
    }
  }

  Future<void> setPacksFromResponse(MBResponse response) async {
    String? data = response.dataString;
    if(data != null) {
      unawaited(SettingsDelegate().prefs.setString('homecare', data));
      setPacks(data);
    }

    return;
  }
}
