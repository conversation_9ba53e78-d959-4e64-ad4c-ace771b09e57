import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';
import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/walk_provider.dart';
import 'package:mybuddy/class/location_manager.dart';
import 'package:mybuddy/class/offline_service.dart';
import 'package:mybuddy/controllers/offline_workout_sync_controller.dart';
import 'package:mybuddy/ui/templates/root_page.dart';

import 'blocs/googlemap_tracking_provider.dart';

class MyApp extends StatelessWidget {
  WalkProvider walkProvider = Get.put(WalkProvider());
  TrackingProvider trackingProvider = Get.put(TrackingProvider());

  MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Tools().setContext(context, 'myapp');
    AppConfig config = AppConfig.of(context);
    return GetMaterialApp(
      onInit: () {
        walkProvider.loadDependencies();
        Tools().localNotification.setNotificationListener();
      },
      onDispose: () {
        LocationManager().stopLocationUpdatesAppWide();
      },
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      title: config.appDisplayName,
      theme: config.themeData,
      builder: (context, child) {
        return MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
            child: child!);
      },
      home: RootPage(),
      supportedLocales: const [
        Locale('fr', 'FR'),

        ///  1
        Locale('en', 'US'),

        ///  2
        Locale('es', 'ES'),

        ///  3
        Locale('en', 'GB'),

        ///  4
        Locale('fr', 'LU'),

        ///  5
        Locale('fr', 'BE'),

        ///  6
        Locale('fr', 'CH'),

        ///  7
        Locale('fr', 'CA'),

        ///  8
        Locale('en', 'IE'),

        ///  9
        // ... other locales the app supports
      ],
    );
  }
}
