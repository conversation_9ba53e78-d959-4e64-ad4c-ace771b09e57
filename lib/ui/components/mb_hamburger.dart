import 'package:flutter/material.dart';
import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/ui/templates/account/owner_profile_page.dart';
import 'package:mybuddy/ui/templates/account/owner_settings_page.dart';

import 'mb_stackable_avatar.dart';

class MBHamburger extends StatefulWidget {
  const MBHamburger({Key? key}) : super(key: key);

  @override
  State<MBHamburger> createState() => _MBHamburgerState();
}

class _MBHamburgerState extends State<MBHamburger> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        MBAvatar(
          onTap: () async {
            await Tools().navigatorPush(const OwnerSettingsPage());
            setState(() {});
          },
          size: 40.0,
          foregroundColor: Theme.of(context).disabledColor,
          imageUrl: Data().get().owner.privateAvatarUrl,
          noImageUrl: Text(Data().get().owner.initials),
        ),
        if (Data().get().owner.isActiveWalker)
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.secondary,
                size: 15,
              ),
            ),
          ),
      ],
    );
  }
}
