

import 'package:flutter/material.dart';

class ChoiceChipFormField extends StatelessWidget {
  final String label;
  final double width;
  final double height;
  final bool isSelected;
  final ValueChanged<bool> onSelected;

  const ChoiceChipFormField(
      {Key? key,
        required this.label,
        this.width = 50.0,
        this.height = 30.0,
        required this.onSelected,
        required this.isSelected})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChoiceChip(
      label: Container(
        width: width,
        height: height,
        alignment: Alignment.center,
        child: Text(label),
      ),
      labelStyle: Theme.of(context).textTheme.bodyText1?.copyWith(
        color: isSelected
            ? Theme.of(context).colorScheme.secondary
            : Theme.of(context).primaryColor,
        fontSize: 13,
        fontWeight: FontWeight.w500
      ),
      selected: isSelected,
      selectedColor: const Color(0xffDEF9ED),
      backgroundColor: Colors.white,
      disabledColor: Colors.white,
      shape: StadiumBorder(
        side: BorderSide(
          color: isSelected
              ? Theme.of(context).colorScheme.secondary
              : Theme.of(context).primaryColor,
        ),
      ),
      onSelected: onSelected,
    );
  }
}