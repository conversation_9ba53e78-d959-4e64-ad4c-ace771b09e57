import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class ElevatedSubmitButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final String? title;
  final Duration? deactivatedDuration;
  const ElevatedSubmitButton({Key? key, required this.onPressed, this.title, this.deactivatedDuration}) : super(key: key);

  @override
  _ElevatedSubmitButtonState createState() => _ElevatedSubmitButtonState();
}

class _ElevatedSubmitButtonState extends State<ElevatedSubmitButton> {
  bool submitted = false;
  late Duration _deactivatedDuration;

  void _toggleDisabledSubmitButton() {
    if (mounted) {
      setState(() => submitted = !submitted);
    }
  }

  @override
  void initState() {
    super.initState();
    _deactivatedDuration = widget.deactivatedDuration ?? const Duration(seconds: 5);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: submitted || widget.onPressed == null ? null : () {
          _toggleDisabledSubmitButton();
          widget.onPressed!();
          Future.delayed(_deactivatedDuration , _toggleDisabledSubmitButton);
        },
        child: submitted ? SizedBox(
          width: 18.0,
          height: 18.0,
          child: CircularProgressIndicator(
            color: Theme.of(context).disabledColor,
            strokeWidth: 2.0,
          ),
        ) : Text(
          widget.title ?? 'APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr(''),
        ),
      ),
    );
  }
}
