import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class ElevatedOutlineButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final String? title;
  final Duration? deactivatedDuration;
  final Color? titleColor;
  final Color? borderColor;
  const ElevatedOutlineButton({Key? key, required this.onPressed, this.title, this.deactivatedDuration, this.borderColor, this.titleColor}) : super(key: key);

  @override
  _ElevatedOutlineButtonState createState() => _ElevatedOutlineButtonState();
}

class _ElevatedOutlineButtonState extends State<ElevatedOutlineButton> {
  bool submitted = false;
  late Duration _deactivatedDuration;

  void _toggleDisabledSubmitButton() {
    if (mounted) {
      setState(() => submitted = !submitted);
    }
  }

  @override
  void initState() {
    super.initState();
    _deactivatedDuration = widget.deactivatedDuration ?? const Duration(seconds: 5);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: submitted || widget.onPressed == null ? null : () {
          _toggleDisabledSubmitButton();
          widget.onPressed!();
          Future.delayed(_deactivatedDuration , _toggleDisabledSubmitButton);
        },
        style: ElevatedButton.styleFrom(
          primary: Colors.white,
          onPrimary: widget.titleColor,
          side: BorderSide(color: widget.borderColor!,),
          elevation: 0,
        ),
        child: submitted ? SizedBox(
          width: 18.0,
          height: 18.0,
          child: CircularProgressIndicator(
            color: Theme.of(context).disabledColor,
            strokeWidth: 2.0,
          ),
        ) : Text(
          widget.title ?? 'APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr(''),
        ),
      ),
    );
  }
}
