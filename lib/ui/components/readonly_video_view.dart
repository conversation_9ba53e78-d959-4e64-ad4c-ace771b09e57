// import 'package:cached_video_player/cached_video_player.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:video_player/video_player.dart';

class VideoView extends StatefulWidget {
  final String videoUrl;
  final String title;

  const VideoView({Key? key, required this.videoUrl, required this.title})
      : super(key: key);

  @override
  _VideoViewState createState() => _VideoViewState();
}

//'${_pageController == null ? 1 : _pageController.page}/${petActivities.length}'
class _VideoViewState extends State<VideoView> {
  late VideoPlayerController _controller;

  // double _playedValue = 0.0;
  // int _totalDuration = 0;

  void _initPlayer() async {
    // print(url);
    _controller = VideoPlayerController.network(widget.videoUrl);
    await _controller.initialize();
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    _initPlayer();
  }

  // @override
  // void didChangeDependencies() {
  //   super.didChangeDependencies();
  //   _controller.addListener(() {
  //     _totalDuration = _controller.value.duration?.inMilliseconds;
  //     if (mounted && _totalDuration != null && _totalDuration != 0) {
  //       setState(() {
  //          // _playedValue = _controller.value.position.inMilliseconds / _totalDuration;
  //         // print('Played $_playedValue - Total $_totalDuration');
  //       });
  //     }
  //   });
  // }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(widget.title, maxLines: 2),
      ),
      body: Container(
        child: _controller.value.isInitialized
            ? Stack(
                alignment: Alignment.center,
                children: <Widget>[
                  Center(
                    child: AspectRatio(
                      aspectRatio: _controller.value.aspectRatio,
                      child: VideoPlayer(_controller),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        if (_controller.value.isPlaying) {
                          _controller.pause();
                        } else {
                          if (_controller.value.position >=
                              _controller.value.duration) {
                            _controller.seekTo(const Duration(seconds: 0));
                          }
                          _controller.play();
                        }
                      });
                    },
                    child: _videoControlsIcons(),
                  ),
                ],
              )
            : const Center(child: CircularProgressIndicator()),
      ),
    );
  }

  Widget _videoControlsIcons() {
    if (_controller.value.position >= _controller.value.duration) {
      return Icon(
        Icons.replay,
        color: Colors.white.withOpacity(0.67),
        size: 100,
      );
    }
    return Icon(
      _controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
      color: Colors.white.withOpacity(0.67),
      size: 100,
    );
  }
}
