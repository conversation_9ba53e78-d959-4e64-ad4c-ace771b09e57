import 'package:flutter/material.dart';

class MbSignButton extends StatelessWidget {
  final String? label;
  final IconData? icon;
  final void Function()? onPressed;
  final Color? backgroundColor;
  final Color? textColor;

  const MbSignButton(
      {Key? key,
      this.label,
      this.onPressed,
      this.backgroundColor,
      this.textColor,
      this.icon})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: ElevatedButton(
        onPressed: onPressed,
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Icon(
                icon,
                color: textColor,
              ),
              const SizedBox(width: 5.0),
              Center(
                child: Text(
                  label ?? '',
                  style: TextStyle(
                    color: textColor,
                    fontWeight: FontWeight.w700,
                    fontSize: 18,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
