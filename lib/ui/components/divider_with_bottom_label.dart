

import 'package:flutter/material.dart';

class DividerWithBottomLabel extends StatelessWidget {
  final String label;
  final IconData? icon;
  const DividerWithBottomLabel({Key? key, this.icon, required this.label}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Column(
        children: [
          const Divider(),
          const SizedBox(height: 10.0),
          Row(
            children: [
              if(icon != null)
                Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: Icon(
                    icon,
                    size: 20.0,
                    color: Theme.of(context).disabledColor,
                  ),
                ),

              ///endif
              Text(
                label,
                style: Theme.of(context).textTheme.headline6,
              ),
            ],
          ),
        ],
      ),
    );
  }
}