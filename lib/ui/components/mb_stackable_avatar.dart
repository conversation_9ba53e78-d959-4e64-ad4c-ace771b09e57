import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/app_config.dart';

class MBAvatar extends StatelessWidget {
  final Color backgroundColor;
  final Color foregroundColor;
  final double size, radius;
  final String? imageUrl;
  final void Function()? onTap;
  final Widget? noImageUrl;
  final Widget? stackedChild;

  const MBAvatar({
    Key? key,
    this.backgroundColor = Colors.white,
    this.foregroundColor = Colors.white54,
    this.imageUrl,
    this.onTap,
    this.size = 60,
    this.radius = 100,
    this.stackedChild,
    this.noImageUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(radius),
            child: Container(
              height: size * 1.07,
              width: size * 1.07,
              color: backgroundColor,
              child: Center(
                child: MBCachedImage(
                  size: size,
                  radius: radius,
                  imageUrl: imageUrl,
                  foregroundColor: foregroundColor,
                  noImageUrl: noImageUrl,
                ),
              ),
            ),
          ),
          if (stackedChild != null)
            Positioned(
              right: -(size * 0.05),
              bottom: -(size * 0.05),
              child: CircleAvatar(
                radius: size * 0.23,
                backgroundColor: Colors.transparent,
                foregroundColor: Theme.of(context).primaryColor,
                child: stackedChild,
              ),
            ),

          /// endif
        ],
      ),
    );
  }
}

class MBCachedImage extends StatelessWidget {
  final double size;
  final double radius;
  final double aspectRatio;
  final String? imageUrl;
  final String? assetImage;
  final Color foregroundColor;
  final Widget? noImageUrl;

  const MBCachedImage(
      {Key? key,
      this.size = 60.0,
      this.imageUrl,
      this.foregroundColor = Colors.white54,
      this.radius = 100,
      this.noImageUrl,
      this.assetImage,
      this.aspectRatio = 1.0})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(radius),
      child: Container(
        height: size / aspectRatio,
        width: size,
        color: foregroundColor,
        alignment: Alignment.center,
        child: _buildChild(),
      ),
    );
  }

  Widget _buildChild() {
    if (imageUrl == null || imageUrl == '') {
      return noImageUrl ??
          Image.asset(
            assetImage ?? 'assets/images/noConnexionPicture.png',
            // fit: BoxFit.cover,
            width: size,
            height: size / aspectRatio,
          );
    }
    return CachedNetworkImage(
      imageUrl: imageUrl!,
      placeholder: (BuildContext context, _) {
        return Center(
          child: FittedBox(
            fit: BoxFit.fitWidth,
            child: Text(AppConfig.of(context).appDisplayName),
          ),
        );
      },
      errorWidget: (BuildContext context, _, __) {
        return Center(
            child: Image.asset(
          'assets/images/noConnexionPicture.png',
          width: size,
          height: size / aspectRatio,
        ));
      },
      fit: BoxFit.cover,
      width: size,
      height: size / aspectRatio,
    );
  }
}

class MBDefaultImage extends StatelessWidget {
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double size;
  final String? imageUrl;

  const MBDefaultImage({
    Key? key,
    this.backgroundColor,
    this.foregroundColor,
    this.size = 60,
    this.imageUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipOval(
      child: Container(
        height: size * 1.1,
        width: size * 1.1,
        color: backgroundColor ?? const Color(0xffeeeeee),
        // padding: EdgeInsets.all(size * 0.10),
        child: Center(
          child: MBCachedImage(
            size: size,
            assetImage: imageUrl,
            foregroundColor: foregroundColor ?? const Color(0xffeeeeee),
          ),
        ),
      ),
    );
  }
}

class MBDefaultDogImage extends StatelessWidget {
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double size;
  final bool bottom;

  const MBDefaultDogImage({
    Key? key,
    this.backgroundColor,
    this.foregroundColor,
    this.size = 60,
    this.bottom = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      MBDefaultImage(
          size: size,
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          imageUrl: "assets/images/ic_dog_lexi.png"),
      Positioned(
        top: 0.0,
        right: 0.0,
        child: Image.asset(
          'assets/images/ic_dog_lexi_watermark.png',
          width: size * 0.4,
          height: size * 0.4,
        ),
      )
    ]);
  }
}

class MBDogAvatar extends StatelessWidget {
  const MBDogAvatar(
      {Key? key,
      required this.avatarURL,
      this.deceased = false,
      this.size = 60.0,
      this.backgroundColor = Colors.transparent})
      : super(key: key);
  final String? avatarURL;
  final bool deceased;
  final double size;
  final Color backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        MBAvatar(
          imageUrl: avatarURL,
          size: size,
          backgroundColor: backgroundColor,
        ),
        if (deceased)
          Padding(
            padding: EdgeInsets.all(size * 0.05),
            child: Image.asset(
              "assets/icon/rainbow.png",
              height: 12,
              width: 12,
            ),
          )
      ],
    );
  }
}
