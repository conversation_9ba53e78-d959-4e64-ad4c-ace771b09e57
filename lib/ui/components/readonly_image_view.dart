import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:photo_view/photo_view.dart';

class ImageView extends StatelessWidget {
  final String url;
  final String title;

  const ImageView({Key? key, required this.url, required this.title}) : super(key: key);

//'${_pageController == null ? 1 : _pageController.page}/${petActivities.length}'
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(title, maxLines: 2),
        // actions: <Widget>[
        //   MBHamburger(),
        // ],
      ),
      // endDrawer: MBDrawer(),
      body: PhotoView(
        imageProvider: CachedNetworkImageProvider(url),
      ),
    );
  }
}
