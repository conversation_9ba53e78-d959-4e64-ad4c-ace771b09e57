import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class ListHeaderWidget extends StatelessWidget {
  final String title;
  final void Function()? onPressed;
  final String? onPressedLabel;

  const ListHeaderWidget(
      {Key? key,
      required this.title,
      this.onPressed,
      this.onPressedLabel})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50.0,
      alignment: Alignment.centerLeft,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.bodyText1,
          ),
          if (onPressed != null)
            TextButton(
              onPressed: onPressed,
              child: Text(
                onPressedLabel ?? 'APPLICATION_MOBILE_BUTTON_LABEL_ADD'.tr(),
              ),
            ),

          ///endif
        ],
      ),
    );
  }
}
