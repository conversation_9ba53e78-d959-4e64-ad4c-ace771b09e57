import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/fav_service_bloc.dart';
import 'package:mybuddy/blocs/generic_bloc.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/earning_points_footer_component.dart';
import 'package:mybuddy/ui/templates/service/add_edit_service.dart';
import 'package:mybuddy/ui/templates/service/service_page.dart';

import 'package:mybuddy/extensions/string_extensions.dart';

import 'mb_hexa_badge_widget.dart';

class MBClinicCardWidget extends StatelessWidget {
  final MBService service;

  const MBClinicCardWidget({Key? key, required this.service}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.only(top: 10.0),
        child: Column(
          children: <Widget>[
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                Tools().navigatorPush(ServicePage(clinic: service));
              },
              child: Text(
                service.name ?? '',
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.headline6,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                IconButton(
                  icon: const Icon(Icons.local_phone),
                  onPressed: () async {
                    String url =
                        'tel:' + (service.phone?.replaceAll(' ', '') ?? '');
                    Tools().common.launchURL(url, context);
                  },
                ),
                Visibility(
                  visible: service.canMakeAppointment(),
                  child: IconButton(
                    icon: const Icon(FontAwesomeIcons.calendarAlt),
                    onPressed: () => Tools().appointment.appointmentAction(
                          service: service,
                          context: context,
                        ),
                  ),
                ),
                Visibility(
                  visible: service.canMakeOrder(),
                  child: IconButton(
                    icon: const Icon(Icons.shopping_cart),
                    onPressed: () => Tools().order.orderAction(
                          service: service,
                          context: context,
                        ),
                  ),
                ),
                Visibility(
                  visible: service.acceptMessage,
                  child: IconButton(
                    icon: const Icon(Icons.message),
                    onPressed: () => Tools().message.addNewMessage(context, service: service),
                  ),
                ),
                Visibility(
                  visible: service.phoneEmergency != null &&
                      service.phoneEmergency!.trim() != '',
                  child: IconButton(
                    icon: const Icon(Icons.local_phone),
                    color: Colors.red,
                    onPressed: () async {
                      String url =
                          'tel:' + service.phoneEmergency!.replaceAll(' ', '');
                      Tools().common.launchURL(url, context);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class ClinicHomeWidget extends StatelessWidget {
  final bool withHeader;

  /// Need to be wrapped in a [BlocProvider] of [FavServiceBloc]
  const ClinicHomeWidget({Key? key, this.withHeader = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<FavServiceBloc>(context);

    return StreamBuilder<MBService?>(
      stream: bloc.stream,
      builder: (BuildContext context, AsyncSnapshot<MBService?> snapshot) {
        MBService? clinic;

        if (!snapshot.hasData) {
          return _FavoriteClinicEmptyWidget();
        }

        clinic = snapshot.data;
        return SizedBox(
            width: double.infinity,
            height:
                MediaQuery.of(context).size.height * (withHeader ? 0.3 : 0.25),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (withHeader) _FavoriteClinicHeaderWidget(clinic: clinic),

                ///endif
                _FavoriteClinicTitleWidget(clinic: clinic),
                if (clinic == null)
                  _FavoriteClinicEmptyWidget()
                else
                  _FavoriteClinicContentWidget(clinic: clinic),

                ///endif

                if (clinic == null)
                  const EarningPointsWidget(size: 12.0, points: 1000),

                ///endif
              ],
            ));
      },
    );
  }
}

class _FavoriteClinicHeaderWidget extends StatelessWidget {
  final MBService? clinic;

  const _FavoriteClinicHeaderWidget({Key? key, this.clinic}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 10.0),
          child: Text(
            'APPLICATION_MOBILE_LABEL_SERVICE_CLINIC'.tr(),
            style: Theme.of(context).textTheme.bodyText1,
          ),
        ),
        if (clinic != null && !clinic!.official)
          TextButton(
            onPressed: () {
              Tools().navigatorPush(
                AddEditServiceForm(service: clinic!),
              );
            },
            child: Text(
              'APPLICATION_MOBILE_BUTTON_LABEL_EDIT'.tr(),
            ),
          ),

        /// endif
      ],
    );
  }
}

class _FavoriteClinicEmptyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<GenericBloc>(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10.0),
      padding: const EdgeInsets.all(10.0),
      decoration: BoxDecoration(
        color: Theme.of(context).backgroundColor,
        borderRadius: const BorderRadius.all(Radius.circular(15.0)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ListTile(
            leading: MBHexaBadge(
              size: 50.0,
              icon: FontAwesomeIcons.briefcaseMedical,
              color: Theme.of(context).colorScheme.secondary.withOpacity(0.25),
            ),
            title: Text(
              'APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_CLINIC'.tr(),
              style: Theme.of(context).textTheme.bodyText1,
            ),
            subtitle: Text(
              'APPLICATION_MOBILE_LABEL_NO_CLINIC_SENTENCE'.tr(),
              maxLines: 2,
              style: Theme.of(context).textTheme.subtitle1,
            ),
            isThreeLine: true,
          ),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () async {
                await Tools().clinic.addClinicAction(context);
                bloc.update();
              },
              child: Text(
                'APPLICATION_MOBILE_BUTTON_LABEL_ADD'.tr(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _FavoriteClinicTitleWidget extends StatelessWidget {
  final MBService? clinic;
  final bool centered;

  const _FavoriteClinicTitleWidget(
      {Key? key, this.clinic, this.centered = true})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return clinic != null
        ? InkWell(
            onTap: () => Tools().navigatorPush(ServicePage(clinic: clinic!)),
            child: Column(
              crossAxisAlignment: centered
                  ? CrossAxisAlignment.center
                  : CrossAxisAlignment.start,
              children: [
                Text(
                  clinic!.name ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: centered ? TextAlign.center : TextAlign.left,
                  style: Theme.of(context).textTheme.bodyText1,
                ),
                Text(
                  clinic!.getFullAddress(),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: centered ? TextAlign.center : TextAlign.left,
                  style: Theme.of(context).textTheme.subtitle1,
                ),
              ],
            ),
          )
        : const SizedBox.shrink();
  }
}

class _FavoriteClinicContentWidget extends StatelessWidget {
  final MBService clinic;

  const _FavoriteClinicContentWidget({Key? key, required this.clinic})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    /// set base size of item
    double itemSize = MediaQuery.of(context).size.width * 0.22;
    List<ClinicItem> items = [
      ClinicItem(
        'APPLICATION_MOBILE_ICON_CALL_TITLE'.tr('Call'),
        Icons.phone,
        () async {
          String url = 'tel:' + clinic.phone!.replaceAll(' ', '');
          Tools().common.launchURL(url, context, clinic.id);
        },
      ),
      if (clinic.canMakeAppointment())
        ClinicItem(
          'APPLICATION_MOBILE_ICON_APT_TITLE'.tr('Appointment'),
          Icons.calendar_today_outlined,
          () => Tools()
              .appointment
              .appointmentAction(service: clinic, context: context),
        ),

      /// endif
      if (clinic.canMakeOrder())
        ClinicItem(
          'APPLICATION_MOBILE_ICON_ORDER_TITLE'.tr('Order'),
          Icons.shopping_cart,
          () => Tools().order.orderAction(service: clinic, context: context),
        ),

      /// endif
      ClinicItem(
        'APPLICATION_MOBILE_ICON_EMAIL_TITLE'.tr('Email'),
        Icons.email,
        () async {
          String url = 'mailto:' + clinic.email!.trim();
          Tools().common.launchURL(url, context);
        },
      ),
      if (clinic.phoneEmergency != null && clinic.phoneEmergency!.trim() != '')
        ClinicItem(
          'APPLICATION_MOBILE_ICON_EMERGENCY_TITLE'.tr('Emergency'),
          Icons.local_phone,
          () async {
            String url = 'tel:' + clinic.phoneEmergency!.replaceAll(' ', '');
            Tools().common.launchURL(url, context, clinic.id);
          },
        ),
      if (clinic.acceptMessage)
        ClinicItem(
          'APPLICATION_MOBILE_ICON_MESSAGE_TITLE'.tr('Message'),
          Icons.message,
          () => Tools().message.addNewMessage(context, service: clinic),
        ),

      /// endif
    ];

    return SizedBox(
      width: double.infinity,
      height: itemSize,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        padding: const EdgeInsets.fromLTRB(20.0, 0.0, 20.0, 0.0),
        itemCount: items.length,
        itemBuilder: (context, index) {
          ClinicItem item = items[index];
          return SizedBox(
            width: itemSize,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                InkWell(
                  onTap: item.onPressed,
                  child: CircleAvatar(
                    backgroundColor: Theme.of(context).backgroundColor,
                    radius: itemSize * 0.38,
                    child: Icon(
                      item.icon,
                      color: index == 4
                          ? Colors.red
                          : Theme.of(context).primaryColor,
                      size: itemSize * 0.33,
                    ),
                  ),
                ),
                Text(
                  item.label,
                  // softWrap: true,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontSize: itemSize / 9,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class ClinicItem {
  final String label;
  final IconData icon;
  final VoidCallback onPressed;

  ClinicItem(this.label, this.icon, this.onPressed);
}
