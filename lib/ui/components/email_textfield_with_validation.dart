import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/controllers/email_verification_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class EmailTextFieldWithValidation extends StatelessWidget {
  EmailTextFieldWithValidation(this.controller, {Key? key, this.labelText})
      : super(key: key);

  EmailVerificationController controller;
  String? labelText;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(
          () => TextFormField(
            decoration: InputDecoration(
              hintText: 'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'.tr(),
              labelText: labelText,
              errorText: controller.emailErrorText,
              errorMaxLines: 3,
              suffixIcon: controller.isEmailChecking
                  ? Container(
                      margin: const EdgeInsets.all(15.0),
                      child: Center(
                          child: CircularProgressIndicator(
                        strokeWidth: 3,
                        valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).colorScheme.secondary),
                      )),
                      height: 20.0,
                      width: 20.0,
                    )
                  : null,
            ),
            keyboardType: TextInputType.emailAddress,
            controller: controller.emailController,
            inputFormatters: [LengthLimitingTextInputFormatter(256)],
            autovalidateMode: AutovalidateMode.onUserInteraction,
            onChanged: (val) {
              controller.email = val;
              controller.isEmailTypingStarted = true;
            },
            onSaved: (val) => controller.email = val!.trim(),
          ),
        ),
        Obx(() => _emailSuggestion(context)),
      ],
    );
  }

  Widget _emailSuggestion(BuildContext context) {
    if (controller.didYouMeanSuggestion.isEmpty) return const SizedBox.shrink();

    TextStyle? linkStyle = Theme.of(context).textTheme.bodySmall?.copyWith(
        color: Theme.of(context).colorScheme.secondary,
        fontStyle: FontStyle.italic,
        fontWeight: FontWeight.w600);
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: RichText(
        text: TextSpan(
          style: Theme.of(context)
              .textTheme
              .bodySmall
              ?.copyWith(color: Colors.black),
          children: <TextSpan>[
            const TextSpan(text: 'Did you mean '),
            TextSpan(
                text: controller.didYouMeanSuggestion,
                style: linkStyle,
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    controller.applySuggestion();
                  }),
            const TextSpan(text: '?'),
          ],
        ),
      ),
    );
  }
}
