import 'package:flutter/material.dart';

class DetailTable extends StatelessWidget {
  const DetailTable({Key? key, required this.data}) : super(key: key);
  final List<DetailTableRow> data;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
            decoration: BoxDecoration(
              color: const Color(0xFFFCFCFC),
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(
                color: const Color(0xFFDAE2EB),
                width: 1.0,
              ),
            ),
            child: ListView.separated(
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemBuilder: (BuildContext ctx, int index) {
                  return Container(
                    height: 50,
                    margin: const EdgeInsets.symmetric(horizontal: 15),
                    child: Row(
                      children: [
                        Expanded(
                            child: Text(
                              data[index].title,
                              style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xff474747)),
                            )),
                        Expanded(
                            child: Text(data[index].value,
                                style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xff474747)))),
                      ],
                    ),
                  );
                },
                separatorBuilder: (BuildContext ctx, int index) {
                  return const Divider(
                    height: 1,
                  );
                },
                itemCount: data.length),
          ),
        ],
      ),
    );
  }
}

class DetailTableRow {
  final String title;
  final String value;

  DetailTableRow(this.title, this.value);
}
