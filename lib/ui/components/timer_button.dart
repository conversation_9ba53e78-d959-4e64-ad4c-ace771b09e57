import 'dart:async';

import 'package:flutter/material.dart';

class TimerButton extends StatefulWidget {
  const TimerButton(
      {Key? key,
      this.counter = 30,
      required this.onPressed,
      this.kickOffTimer = true,
      this.timerCompleted})
      : super(key: key);
  final int counter;
  final bool kickOffTimer;
  final Function onPressed;
  final Function(bool)? timerCompleted;

  @override
  State<TimerButton> createState() => _TimerButtonState();
}

class _TimerButtonState extends State<TimerButton> {
  Timer? _timer;
  int _start = 0;

  void startTimer() {
    const oneSec = Duration(seconds: 1);
    _start = widget.counter;
    _timer = Timer.periodic(
      oneSec,
      (Timer timer) {
        if (_start == 0) {
          setState(() {
            timer.cancel();
          });
          if (widget.timerCompleted != null) {
            widget.timerCompleted!(true);
          }
        } else {
          setState(() {
            _start--;
          });
        }
      },
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (widget.kickOffTimer) {
      setState(() {
        _start = widget.counter;
      });
      startTimer();
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 15),
      child: ElevatedButton(
        onPressed: _start != 0
            ? null
            : () async {
                widget.onPressed();
              },
        child: Text(
          "Resend${_start == 0 ? "" : " ($_start)"}",
          style: const TextStyle(fontSize: 14),
        ),
      ),
    );
  }
}
