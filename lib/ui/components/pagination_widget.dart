import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/controllers/base_pagination_class.dart';

class PaginationWidget extends StatelessWidget {
  PaginationWidget(
      {Key? key,
      required this.controller,
      required this.child,
      this.emptyWidget,
      this.errorWidget,
      this.loadingWidget,
      this.leadingWidget,
      this.separator = false,
      this.gridView = false,
      this.gridDelegate})
      : super(key: key);

  BasePaginationClass controller;
  Widget Function(BuildContext context, int index) child;
  Widget? emptyWidget;
  Widget? errorWidget;
  Widget? loadingWidget;
  Widget? leadingWidget;
  bool separator;
  int extraWidgets = 0;
  bool gridView;
  SliverGridDelegate? gridDelegate;

  @override
  Widget build(BuildContext context) {
    if (leadingWidget != null) extraWidgets = 1;
    return RefreshIndicator(
      onRefresh: () async {
        controller.initialFetch();
      },
      child: Obx(
        () {
          if (controller.loading) {
            return _loadingWidget(context);
          }

          if (controller.shouldShowEmptyWidget) {
            return _emptyWidget();
          }

          if (gridView) {
            return _gridView();
          }

          return _listView();
        },
      ),
    );
  }

  GridView _gridView() {
    return GridView.builder(
      primary: false,
      shrinkWrap: true,
      physics: const AlwaysScrollableScrollPhysics(),
      gridDelegate: gridDelegate ??
          const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            mainAxisSpacing: 10.0,
            crossAxisSpacing: 10.0,
            childAspectRatio: 1.0,
          ),
      itemCount: controller.itemCount,
      itemBuilder: (context, index) {
        if (controller.shouldNextPageFetch(index)) {
          controller.pageFetch();
        }
        if (index == controller.dataList.length) {
          if (controller.error == true) {
            return _errorWidget(context);
          } else {
            return _loadingWidget(context);
          }
        }

        return child(context, index);
      },
    );
  }

  ListView _listView() {
    return ListView.separated(
      primary: false,
      shrinkWrap: true,
      padding: const EdgeInsets.only(top: 10),
      physics: const AlwaysScrollableScrollPhysics(),
      separatorBuilder: (context, index) =>
          separator ? const Divider() : const SizedBox(height: 0.0),
      itemCount: controller.itemCount + extraWidgets,
      itemBuilder: (context, index) {
        if (index == 0 && leadingWidget != null) return leadingWidget!;
        if (controller.shouldNextPageFetch(index)) {
          controller.pageFetch();
        }
        if (index ==
            controller.dataList.length + (leadingWidget != null ? 1 : 0)) {
          if (controller.error) {
            return _errorWidget(context);
          } else if (controller.hasMore) {
            return _loadingWidget(context);
          }
        }
        return child(context, index - extraWidgets);
      },
    );
  }

  Widget _errorWidget(BuildContext context) {
    if (errorWidget != null) {
      return errorWidget!;
    }
    return Center(
      child: TextButton(
        onPressed: () {
          controller.loading = true;
          controller.error = false;
          controller.pageFetch();
        },
        child: Text(
          'Error while loading data, tap to try again',
          style: Theme.of(context).textTheme.headline6,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _loadingWidget(BuildContext context) {
    if (loadingWidget != null) {
      return loadingWidget!;
    }
    return Center(
      child: Text(
        'Loading...',
        style: Theme.of(context).textTheme.headline6,
      ),
    );
  }

  Widget _emptyWidget() {
    if (emptyWidget != null) {
      return emptyWidget!;
    }
    return const Center(
      child: Text("No record found"),
    );
  }
}
