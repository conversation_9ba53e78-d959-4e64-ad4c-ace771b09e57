import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class NoInternetWidget extends StatelessWidget {
  const NoInternetWidget(
      {Key? key, this.retryMethod, this.title, this.description})
      : super(key: key);
  final Function? retryMethod;
  final String? title, description;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Center(
              child: Image.asset(
                'assets/icon/no_internet.png',
                fit: BoxFit.fill,
                width: 165,
              ),
            ),
            const SizedBox(
              height: 25,
            ),
            if(title?.isNotEmpty??true)
            Text(
              title ??
                  'APPLICATION_MOBILE_TITLE_NO_INTERNET'
                      .tr("Looks like you’re offline!"),
              style: const TextStyle(fontSize: 21, fontWeight: FontWeight.w400),
            ),
            const SizedBox(
              height: 10,
            ),
            if(description?.isNotEmpty??true)
            Text(
              description ??
                  'APPLICATION_MOBILE_TEXT_NO_INTERNET'
                      .tr("Check your internet connection and try again."),
              style: const TextStyle(
                  fontSize: 14, height: 2, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
            const SizedBox(
              height: 20,
            ),
            if (retryMethod != null)
              TextButton(
                onPressed: () {
                  retryMethod!();
                },
                child: Text(
                  'APPLICATION_MOBILE_BUTTON_RETRY'.tr("Tap to retry"),
                  style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.secondary,
                      fontWeight: FontWeight.w700),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
