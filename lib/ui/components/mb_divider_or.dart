import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class DividerOrWidget extends StatelessWidget {
  final double indent;

  const DividerOrWidget({Key? key, this.indent = 0.0}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: <Widget>[
          _expandedDivider(0),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5.0),
            child: Text(
              'APPLICATION_MOBILE_OR'.tr('OR'),
              style: Theme.of(context).textTheme.overline,
            ),
          ),
          _expandedDivider(1),
        ],
      ),
    );
  }

  Widget _expandedDivider(int index) => Expanded(
        child: Divider(
          thickness: 1,
          indent: index == 0 ? indent : 0.0,
          endIndent: index == 1 ? indent : 0.0,
        ),
      );
}
