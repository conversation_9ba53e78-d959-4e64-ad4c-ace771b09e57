import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/models/reward.dart';

class AssetReward extends StatelessWidget {
  final Reward reward;
  final RewardState state;
  final double size;

  const AssetReward(
      {Key? key,
      required this.reward,
      this.state = RewardState.earned,
      this.size = 50.0})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    ///https://medium.com/@samarth_agarwal/turn-images-to-grayscale-in-flutter-the-colorfiltered-widget-16de44cf8045
    return ColorFiltered(
      colorFilter: state == RewardState.earned
          ? const ColorFilter.mode(
              Colors.transparent,
              BlendMode.multiply,
            )
          : const ColorFilter.matrix(<double>[
                0.2126, 0.7152, 0.0722, 0, 0,
                0.2126, 0.7152, 0.0722, 0, 0,
                0.2126, 0.7152, 0.0722, 0, 0,
                0,      0,      0,      1, 0,
            ]),
      child: reward.rewardImageUrl != null ? CachedNetworkImage(
        imageUrl: reward.rewardImageUrl!,
        // placeholder: (BuildContext context, _) => Center(
        //   child: Text(
        //     AppConfig.of(context).appDisplayName,
        //     style: TextStyle(fontSize: size / 10),
        //   ),
        // ),
        errorWidget: (BuildContext context, _, __) => _unknownReward(reward.type),
        fit: BoxFit.contain,
        width: size,
        height: size,
      ) : Image.asset(
        'assets/${reward.type == 0 ? 'badges/badge-' : 'avatars/'}${reward.code.toLowerCase()}.png',
        width: size,
        height: size,
        fit: BoxFit.contain,
        errorBuilder: (_, __, ___) => _unknownReward(),
      ),
    );
  }

  Image _unknownReward([int? type]) {
    return Image.asset(
      type != null
          ? 'assets/${type == 0 ? 'badges/badge-unknown' : 'avatars/wt-avatar-unknown'}.png'
          : 'assets/badges/badge-unknown',
      width: size,
      height: size,
      fit: BoxFit.contain,
    );
  }
}
