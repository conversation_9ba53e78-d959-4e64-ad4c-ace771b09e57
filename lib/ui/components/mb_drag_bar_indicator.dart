import 'package:flutter/material.dart';

class MBDragBarIndicator extends StatelessWidget {
  const MBDragBarIndicator({Key? key, this.width = 80}) : super(key: key);
  final double width;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 15,
      alignment: Alignment.center,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: const BorderRadius.all(Radius.circular(2)),
        ),
        height: 3,
        width: width,
      ),
    );
  }
}