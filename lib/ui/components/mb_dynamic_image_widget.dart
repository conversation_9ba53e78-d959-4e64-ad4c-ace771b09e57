import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/models/image.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/templates/pet/pet_pageview.dart';
import 'package:mybuddy/ui/templates/service/service_page.dart';

class DynamicImageWidget extends StatefulWidget {
  final List<dynamic> entities;

  const DynamicImageWidget(this.entities, {Key? key}) : super(key: key);

  @override
  _ImagesWidgetState createState() => _ImagesWidgetState();
}

class _ImagesWidgetState extends State<DynamicImageWidget> {
  int? index;
  Timer? timer;
  List<ImageProvider> images = <ImageProvider>[];
  List visibleEntities = [];

  @override
  Widget build(BuildContext context) {
    if (index == null) return Container();
    return AnimatedSwitcher(
      duration: const Duration(seconds: 2),
      child: SizedBox(
        key: ValueKey<int>(index!),
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: GestureDetector(
          onTap: () async => await navigate(index!),
          child: Image(
            image: images[index!],
            fit: BoxFit.cover,
            errorBuilder: (_, __, ___) => const Icon(Icons.error),
          ),
        ),
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    for (ImageProvider img in images) {
      precacheImage(img, context);
    }
  }

  @override
  void dispose() {
    super.dispose();
    timer?.cancel();
  }

  @override
  void initState() {
    super.initState();
    for (dynamic entity in widget.entities) {
      if(entity == null) continue;
      if(entity.runtimeType == MBImage) {
        String? url = entity.url;
        if (url != null) {
          images.add(CachedNetworkImageProvider(url));
          visibleEntities.add(entity);
        }
      } else {
        int? imgId = getImageId(entity);
        if (imgId == null) {
          continue;
        }
        ImageProvider? ip = imgId.toImageProvider();
        if (ip != null) {
          images.add(ip);
          visibleEntities.add(entity);
        }
      }
    }

    switch (visibleEntities.length) {
      case 0:
        break;
      case 1:
        index = 0;
        break;
      default:
        index = 0;
        ///defines a timer
        timer = Timer.periodic(const Duration(seconds: 3), (Timer t) {
          index = index! + 1;
          setState(() {
            index = index! % visibleEntities.length;
          });
        });
    }
  }

  int? getImageId(entity) {
    switch (entity.runtimeType) {
      case MBService:
        return entity.getImageId();
      case Pet:
        return entity.imageId;
      case int:
        return entity;
    }
    return null;
  }

  Future<void> navigate(int index) async {
    Widget? widget;
    var entity = visibleEntities[index];
    switch (entity.runtimeType) {
      case MBService:
        widget = ServicePage(clinic: entity);
        break;
      case Pet:
        widget = PetPageView(pet: entity);
        break;
    }
    if (widget != null) {
      await Tools().navigatorPush(widget);
    }
    return;
  }
}
