import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import '../../Tools/tools.dart';
import '../../controllers/social_login_controller.dart';
import '../templates/account/auth_login_button.dart';
import '../templates/account/login_page.dart';
import '../templates/account/register_page.dart';
import 'social_error_message.dart';

class LandingPageBottomWidget extends StatelessWidget {
  final SocialLoginController _socialLoginController =
      SocialLoginController.of();

  LandingPageBottomWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      width: MediaQuery.of(context).size.width,
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Obx(() {
              if (_socialLoginController.isError) {
                return Column(
                  children: [
                    const SizedBox(height: 5),
                    SocialErrorMessage(
                      message: _socialLoginController.errorMessage,
                      icon: _socialLoginController.errorIcon,
                    ),
                    const SizedBox(height: 5),
                  ],
                );
              } else {
                return const SizedBox.shrink();
              }
            }),
            Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(vertical: 10.0),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(elevation: 1),
                onPressed: () {
                  _socialLoginController.hideErrorAndPerformSpecificAction(
                      () => Tools().navigatorPush(const RegisterPage()));
                },
                child: Text('APPLICATION_MOBILE_BUTTON_LABEL_CREATE_ACCOUNT'
                    .tr('Sign Up')),
              ),
            ),
            const SizedBox(height: 5),
            AuthLoginButton(
              label: 'APPLICATION_MOBILE_BUTTON_LABEL_CONTINUE_WITH_APPLE'.tr(),
              icon: const Icon(
                FontAwesomeIcons.apple,
                size: 20,
              ),
              onPressed: _socialLoginController.appleLogin,
            ),
            const SizedBox(height: 10),
            AuthLoginButton(
              label:
                  'APPLICATION_MOBILE_BUTTON_LABEL_CONTINUE_WITH_GOOGLE'.tr(),
              icon: SvgPicture.asset(
                'assets/icon/google_logo.svg',
                width: 17,
                height: 17,
              ),
              onPressed: _socialLoginController.googleLogin,
            ),
            const SizedBox(height: 10),
            AuthLoginButton(
              label:
                  'APPLICATION_MOBILE_BUTTON_LABEL_CONTINUE_WITH_FACEBOOK'.tr(),
              icon: SvgPicture.asset(
                'assets/icon/fb_logo.svg',
              ),
              onPressed: _socialLoginController.fbLogin,
            ),
            Padding(
              padding: const EdgeInsets.only(top: 15.0, bottom: 30.0),
              child: RichText(
                text: TextSpan(
                  style: Theme.of(context).textTheme.bodyMedium,
                  children: <TextSpan>[
                    const TextSpan(text: 'Have an account already?'),
                    TextSpan(
                      text: ' Sign in',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.secondary),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          _socialLoginController.hideErrorAndPerformSpecificAction(
                              () => Tools().navigatorPush(const LoginPage()));
                        },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
