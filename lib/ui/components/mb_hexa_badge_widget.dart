import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class MBHexaBadge extends StatelessWidget {
  final IconData? icon;
  final double size;
  final Color? color;

  const MBHexaBadge({Key? key, this.icon, this.size = 50.0, this.color}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size * 0.866,
      height: size,
      child: ClipPath(
        clipper: _MyClipper(),
        child: Container(
          color: color ?? Colors.amber,
          child: Icon(
            icon ?? FontAwesomeIcons.question,
            size: size * 0.4,
          ),
        ),
      ),
    );
  }
}

class _MyClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(size.width / 2, 0);
    path.lineTo(size.width, size.height * 0.25);
    path.lineTo(size.width, size.height * 0.75);
    path.lineTo(size.width / 2, size.height);
    path.lineTo(0, size.height * 0.75);
    path.lineTo(0, size.height * 0.25);
    path.lineTo(size.width / 2, 0);

    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
