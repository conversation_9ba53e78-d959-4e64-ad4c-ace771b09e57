import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';

class SimpleImageDialog extends StatelessWidget {
  final File? image;
  final String? imageUrl;
  final bool deleteIcon;

  const SimpleImageDialog(
      {Key? key, this.image, this.imageUrl, this.deleteIcon = true})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Stack(
        children: <Widget>[
          if (image != null)
            Image(
              image: FileImage(image!),
              fit: BoxFit.cover,
            )
          else
            CachedNetworkImage(
              imageUrl: imageUrl ?? '',
              fit: BoxFit.contain,
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),

          ///endif
          Visibility(
            visible: deleteIcon,
            child: Positioned(
              top: 0,
              left: 0,
              child: IconButton(
                icon: const Icon(
                  Icons.delete,
                  color: Colors.red,
                ),
                onPressed: () {
                  Tools().navigatorPop(removeLast: false, value: true);
                },
              ),
            ),
          ),
          Positioned(
            top: 0,
            right: 0,
            child: IconButton(
              icon: const Icon(
                Icons.close,
                color: Colors.red,
              ),
              onPressed: () {
                Tools().navigatorPop(removeLast: false, value: false);
              },
            ),
          ),
        ],
      ),
    );
  }
}
