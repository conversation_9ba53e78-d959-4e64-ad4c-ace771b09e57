import 'package:flutter/material.dart';

class MBNoRecordFound extends StatelessWidget {
  const MBNoRecordFound({Key? key, required this.title, this.otherWidget})
      : super(key: key);
  final String title;
  final Widget? otherWidget;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          "assets/images/no_activity.png",
          width: MediaQuery.of(context).size.width * 0.95,
        ),
        const SizedBox(
          height: 35,
        ),
        Text(
          title,
          style: Theme.of(context).textTheme.displayMedium?.copyWith(
              fontSize: 21, fontWeight: FontWeight.w700, color: Colors.black),
        ),
        if (otherWidget != null)
          const SizedBox(
            height: 25,
          ),
        otherWidget ?? const SizedBox.shrink()
      ],
    );
  }
}
