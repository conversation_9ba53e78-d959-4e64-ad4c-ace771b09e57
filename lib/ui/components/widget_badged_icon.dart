import 'package:flutter/material.dart';

class BadgedIcon extends StatelessWidget {
  final int? badgeValue;
  final IconData? iconData;
  final double? iconSize;

  const BadgedIcon({Key? key, this.badgeValue, this.iconData, this.iconSize})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    int value = badgeValue ?? 0;
    double size = 10;
    // if (value >= 100) {
    //   size = 30;
    // } else if (value >= 10) {
    //   size = 20;
    // } else {
    //   size = 16;
    // }
    return Stack(
      clipBehavior: Clip.none,
      children: <Widget>[
        Icon(iconData, size: iconSize),
        Visibility(
          visible: value > 0,
          child: Positioned(
            top: 9.0,
            left:-size/2,
            // right: -6.0 - size + 16,
            child: Container(
              width:size,
              height:size,
    decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(size / 2),
      color:Colors.red,
      ),
            )
            // Container(
            //   decoration: BoxDecoration(
            //     borderRadius: BorderRadius.circular(size / 4),
            //     color: Colors.red,
            //   ),
            //   width: size,
            //   child: Center(
            //     child: Text(
            //       value.toString(),
            //       style: const TextStyle(color: Colors.white, fontSize: 12),
            //     ),
            //   ),
            // ),
          ),
        ),
      ],
    );
  }
}
