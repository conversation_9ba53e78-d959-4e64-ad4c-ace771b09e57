
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/earning_points_footer_component.dart';
import 'package:mybuddy/ui/templates/apt/appointment_page.dart';
import 'package:mybuddy/ui/templates/pet/diary/petactivity_page.dart';

class MBPetActivityItem extends StatelessWidget {
  final PetActivity pa;
  final bool showPets;
  final bool showPoints;
  final bool showType;
  final Function? onChanged;

  const MBPetActivityItem(this.pa, {Key? key,
    this.showPets = false,
    this.onChanged,
    this.showPoints = false,
    this.showType = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    late String title = '';
    late String subTitle = '';
    late String type = '';
    Widget? page;
    IconData? icon;

    String dateText = Data().dateTimeToUserDateStr(pa.dateStart);
    Color? dateColor;
//    if (pa.dateEnd != null) {
//      dateText += ' - ' + Data().dateTimeToUserDateStr(pa.dateEnd);
//    }

    switch (pa.activityId) {
      case 1:
        type = 'APPLICATION_MOBILE_TITLE_APPOINTMENT'.tr();
        try {
          AppointmentRequest? aptRequest = Data().getAppointmentWithPetActivity(pa.id);
          if(aptRequest == null) break;
          title =
              Ref().get().getAppointmentStatusLabel(aptRequest.causes[0].id);
          subTitle =
              AppointmentRequest.getTextCodeForStatus(aptRequest.status).tr();
          page = AppointmentPage(appointment: aptRequest);
          dateText = Data().dateTimeToUserDateTimeStr(pa.dateStart);
        } catch (e) {
          title = '';
        }
        icon = FontAwesomeIcons.clock;
        break;
      case 2:
        title = pa.title ?? '';
        ReminderType? reminderType = Ref().get().getReminderType(pa.typeId);
        type = 'APPLICATION_MOBILE_TITLE_REMINDER'.tr();
        if (reminderType != null) {
          subTitle = Ref().get().getReminderType(pa.typeId)!.label;
        }
        dateText = Data().dateTimeToUserDateStr(pa.nextOccurrence);
        if (pa.nextOccurrence != null &&
            pa.nextOccurrence!.isBefore(DateTime.now())) {
          dateColor = Colors.red;
        }
        icon = FontAwesomeIcons.bell;
        break;
      case 3:
        title = pa.title ?? '';
        type = pa.company ?? '';
        dateText = Data().dateTimeToUserDateStr(pa.dateStart) + ' - ';
        dateText += Data().dateTimeToUserDateStr(pa.dateEnd);
        icon = FontAwesomeIcons.bandAid;
        break;
//      case 5:
//        return GestureDetector(
//          behavior: HitTestBehavior.translucent,
//          onTap: () {
//            Tools().navigatorPush(
//              context,
//              MaterialPageRoute(builder: (_) {
//                return PetActivityPage(petActivity: pa);
//              }),
//            );
//          },
//          child: Container(
//            height: 50,
//            child: CachedNetworkImage(
//              imageUrl: api.getImageUrl(pa.imageId),
//              fit: BoxFit.contain,
//            ),
//          ),
//        );
      case 6:
        type = 'APPLICATION_MOBILE_FIELD_LABEL_NOTE'.tr();
        title = pa.comment ?? '';
        subTitle = Ref().get().getNoteType(pa.typeId)?.label ?? '';
        icon = FontAwesomeIcons.stickyNote;
        break;
      case 7:
        type = 'APPLICATION_MOBILE_LABEL_HOSPITALIZATION'.tr();
        title = pa.sentence ?? '';
        icon = FontAwesomeIcons.hospitalSymbol;
        break;
      case 8:
        title = 'APPLICATION_MOBILE_LABEL_HEALTH_VISIT'.tr();
        type = '';
        icon = FontAwesomeIcons.userNurse;
        break;
      case 9:
        title = pa.treatmentName ?? '';
        type = 'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_PREVENTION'.tr();
        icon = FontAwesomeIcons.userNurse;
        break;
    }
    title = title.trim(); //+' ${index.toString()}';
    type = type.trim();

    return ActivityCardWidget(
      header: type,
      showHeader: showType,
      showFooter: showPoints,
      onTap: () async {
        bool needRefresh = (await Tools()
            .navigatorPush(page ?? PetActivityPage(petActivity: pa))) as bool;
        if (needRefresh && onChanged != null) {
          onChanged!();
        }
      },
      leading: Icon(
        icon,
        color: pa.activityId == 7
            ? Theme.of(context).errorColor
            : Theme.of(context).disabledColor,
        size: 23.0,
      ),
      title: title.isNotEmpty ? Text(title) : null,
      subTitle: subTitle.isNotEmpty ? Text(subTitle) : null,
      children: [
        Text(
          dateText,
          style: dateColor != null
              ? Theme.of(context).textTheme.subtitle2?.copyWith(color: dateColor)
              : Theme.of(context).textTheme.subtitle2,
        ),
        if (pa.serviceId != null)
          _PetActivityServiceWidget(petActivity: pa),

        /// endif
        if (showPets)
          Text(
            pa.getPetListString(),
            style: const TextStyle(
              color: Colors.green,
            ),
          ),

        ///endif
        // SizedBox(
        //   height: 16.0,
        //   child: Row(
        //     children: <Widget>[
        //       Padding(
        //         padding: EdgeInsets.only(left: 8),
        //         child: Visibility(
        //           visible: pa.images != null && pa.images.isNotEmpty,
        //           child: Icon(FontAwesomeIcons.images, color: Colors.green, size: 13),
        //         ),
        //       ),
        //       Padding(
        //         padding: EdgeInsets.only(left: 8),
        //         child: Visibility(
        //           visible: pa.file != null,
        //           child: Icon(FontAwesomeIcons.file, color: Colors.orange, size: 13),
        //         ),
        //       ),
        //     ],
        //   ),
        // ),
      ],
    );

    // return GestureDetector(
    //   behavior: HitTestBehavior.translucent,
    //   onTap: () async {
    //     bool needRefresh = await Tools().navigatorPush(page ?? PetActivityPage(petActivity: pa));
    //     if (needRefresh && onChanged != null) {
    //       onChanged();
    //     }
    //   },
    //   child: Padding(
    //     padding: EdgeInsets.fromLTRB(15, 5, 8, 5),
    //     child: Row(
    //       children: <Widget>[
    //         Expanded(
    //           child: Column(
    //             crossAxisAlignment: CrossAxisAlignment.start,
    //             children: <Widget>[
    //               title != ''
    //                   ? Text(title,
    //                       overflow: TextOverflow.ellipsis,
    //                       style: TextStyle(color: Colors.blue, fontWeight: FontWeight.bold))
    //                   : Container(),
    //               Row(
    //                 children: <Widget>[
    //                   type != '' ? Text(type, style: TextStyle(color: Colors.blue)) : Container(),
    //                   Padding(
    //                     padding: EdgeInsets.only(left: 8),
    //                     child: Visibility(
    //                       visible: pa.images != null && pa.images.isNotEmpty,
    //                       child: Icon(FontAwesomeIcons.images, color: Colors.green, size: 13),
    //                     ),
    //                   ),
    //                   Padding(
    //                     padding: EdgeInsets.only(left: 8),
    //                     child: Visibility(
    //                       visible: pa.file != null,
    //                       child: Icon(FontAwesomeIcons.file, color: Colors.orange, size: 13),
    //                     ),
    //                   ),
    //                 ],
    //               ),
    //               Row(
    //                 children: <Widget>[
    //                   Expanded(
    //                     child: Text(
    //                       dateText,
    //                       style: TextStyle(
    //                         color: dateColor,
    //                       ),
    //                     ),
    //                   ),
    //                   Visibility(
    //                     visible: showPets,
    //                     child: Text(pa.getPetListString(), style: TextStyle(color: Colors.green)),
    //                   )
    //                 ],
    //               )
    //             ],
    //           ),
    //         ),
    //         Padding(
    //             padding: EdgeInsets.only(left: 10),
    //             child: Icon(FontAwesomeIcons.userMd,
    //                 color: pa.serviceId != null ? Colors.blue : Colors.white, size: 15)),
    //         Icon(Icons.arrow_right, color: Colors.blue)
    //       ],
    //     ),
    //   ),
    // );
  }
}

class ActivityCardWidget extends StatelessWidget {
  final String? header;
  final void Function()? onTap;
  final Widget? leading;
  final Widget? title;
  final Widget? subTitle;
  final Widget? trailing;
  final List<Widget>? children;
  final bool showHeader;
  final bool showFooter;

  const ActivityCardWidget({
    Key? key,
    this.header,
    this.onTap,
    this.leading,
    this.title,
    this.subTitle,
    this.trailing,
    this.children,
    this.showHeader = false,
    this.showFooter = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    List<Widget> activityChildren = [
      if (title != null)
        DefaultTextStyle(
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: Theme.of(context).textTheme.bodyText1!,
          child: title!,
        ),

      /// endif
      if (subTitle != null)
        DefaultTextStyle(
          style: Theme.of(context).textTheme.bodyText2!,
          child: subTitle!,
        ),

      /// endif
    ];

    if (children != null) {
      activityChildren.addAll(children!);
    }

    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          /// header
          if(showHeader && header != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  header!,
                  style: Theme.of(context).textTheme.bodyText1,
                ),
                //TODO add date diff since now
              ],
            ),

          /// endif

          /// Card
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: onTap,
            child: Container(
              margin:
                  const EdgeInsets.symmetric(vertical: 5.0, horizontal: 5.0,),
              padding:
                  const EdgeInsets.symmetric(vertical: 15.0, horizontal: 10.0,),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).backgroundColor),
                borderRadius: BorderRadius.circular(10.0),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if(leading != null)
                    Padding(
                      padding: const EdgeInsets.fromLTRB(0.0, 5.0, 15.0, 0.0),
                      child: leading,
                    ),

                  /// endif
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: activityChildren,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 5.0),
                    child: trailing ?? const Icon(
                      FontAwesomeIcons.chevronRight,
                      size: 15.0,
                    ),
                  ),
                ],
              ),
            ),
          ),

          /// Footer
          if(showFooter)
            const EarningPointsWidget(points: 15),

          /// endif
        ],
      ),
    );
  }
}

class _PetActivityServiceWidget extends StatelessWidget {
  final PetActivity petActivity;

  const _PetActivityServiceWidget({Key? key, required this.petActivity})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    MBService petActivityService = Data().get().services.firstWhere((s) {
            return s.id == petActivity.serviceId;
          },
          orElse: () => MBService.unknown(),
        );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            petActivityService.name ?? '',
            style: Theme.of(context).textTheme.bodyText1,
          ),
          Text(
            petActivityService.getFullAddress(),
            style: Theme.of(context).textTheme.caption,
          ),
        ],
      ),
    );
  }
}
