import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class CardHeaderWidget extends StatelessWidget {
  final String title;
  final void Function()? onPressed;

  const CardHeaderWidget({Key? key, required this.title, this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.bodyText1,
        ),
        if (onPressed != null)
          TextButton(
            onPressed: onPressed,
            child: Text('APPLICATION_MOBILE_LABEL_FILTER_SEE_ALL'.tr()),
          ),

        /// endif
      ],
    );
  }
}
