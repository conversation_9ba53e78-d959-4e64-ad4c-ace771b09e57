import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';

class MBHtmlWidget extends StatefulWidget {
  final String htmlSrc;

  const MBHtmlWidget({Key? key, required this.htmlSrc}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _MBHtmlWidgetState();
}

class _MBHtmlWidgetState extends State<MBHtmlWidget> {
  @override
  Widget build(BuildContext context) {
    String html = widget.htmlSrc;
    html = html.replaceAll('showinfo=0 ', 'showinfo=0" ');
    // Tools.debugPrint(html);
    return Padding(
      padding: const EdgeInsets.fromLTRB(10.0, 15.0, 10.0, 25.0),
      child: HtmlWidget(
        html,
        // ignore: deprecated_member_use
      ),
    );
  }
}
