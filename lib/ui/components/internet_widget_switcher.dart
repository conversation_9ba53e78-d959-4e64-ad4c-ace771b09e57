import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/internet_controller.dart';
import 'no_internet_widget.dart';

class InternetWidgetSwitcher extends StatelessWidget {
  InternetWidgetSwitcher(
      {Key? key,
      required this.onlineWidget,
      this.offlineWidget,
      required this.retryMethod,
      this.internetErrorTitle,
      this.internetErrorDescription})
      : super(key: key);

  final Widget onlineWidget;
  final Widget? offlineWidget;
  final Function retryMethod;
  final String? internetErrorTitle, internetErrorDescription;

  final InternetController controller = InternetController.of();

  @override
  Widget build(BuildContext context) {
    controller.checkInternet();
    return Obx(() => controller.isConnected
        ? onlineWidget
        : offlineWidget ??
            NoInternetWidget(
              retryMethod: () {
                controller.checkInternet();
                retryMethod();
              },
              title: internetErrorTitle,
              description: internetErrorDescription,
            ));
  }
}
