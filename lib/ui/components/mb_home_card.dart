import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';

class MBHomeCard extends StatelessWidget {
  final String? text;
  final IconData icon;
  final String? subtitleText;
  final void Function()? onTap;
  final Color? color;
  final Color? backgroundColor;
  final Color? subtitleColor;

  const MBHomeCard(this.text,
      {Key? key,
      this.icon = FontAwesomeIcons.plus,
      this.subtitleText,
      this.onTap,
      this.color,
      this.backgroundColor,
      this.subtitleColor})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap ??
          () {
            Tools().common.showNotImplemented(context);

            /// nothing to do here
          },
      child: Card(
        margin: const EdgeInsets.all(8),
        color: backgroundColor != null
            ? backgroundColor!.withOpacity(0.87)
            : Colors.white.withOpacity(0.87),
        child: ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 10.0,
            vertical: 10.0,
          ),
          leading: SizedBox(
            width: 48.0,
            height: 48.0,
            child: Icon(icon, color: color ?? Theme.of(context).primaryColor),
          ),
          title: Text(
            text ?? '',
            style: backgroundColor != null
                ? Theme.of(context)
                    .textTheme
                    .headline6
                    ?.copyWith(color: Colors.white)
                : Theme.of(context).textTheme.headline6,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: subtitleText != null
              ? Row(
                  children: <Widget>[
                    Expanded(
                      child: Text(
                        subtitleText!,
                        style: backgroundColor != null
                            ? Theme.of(context)
                                .textTheme
                                .subtitle2
                                ?.copyWith(color: Colors.white)
                            : Theme.of(context).textTheme.subtitle2,
                      ),
                    ),
                  ],
                )
              : null,
          trailing: onTap != null
              ? Icon(
                  Icons.keyboard_arrow_right,
                  color: color ?? Theme.of(context).primaryColor,
                  size: 30.0,
                )
              : null,
        ),
      ),
    );
  }
}

class MBCard extends StatelessWidget {
  final Widget title;
  final Widget? icon;
  final Widget? subtitle;
  final Widget? trailing;
  final bool isThreeLine;
  final Color borderColor;
  final void Function()? onTap;

  const MBCard(
      {Key? key,
      required this.title,
      this.icon,
      this.subtitle,
      this.trailing,
      this.borderColor = const Color(0xffCECECE),
      this.isThreeLine = false,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
        side: BorderSide(
          color: borderColor,
          width: 1.0,
        ),
      ),
      margin: const EdgeInsets.all(2.0),
      color: Colors.white.withOpacity(0.87),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 10.0,
          vertical: 10.0,
        ),
        leading: icon,
        title: title,
        subtitle: subtitle,
        isThreeLine: isThreeLine,
        trailing: trailing,
        onTap: onTap,
      ),
    );
  }
}

class MBDenseHomeCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final Color? color;
  final Color? backgroundColor;
  final void Function()? onTap;

  const MBDenseHomeCard({
    Key? key,
    this.onTap,
    this.color,
    required this.title,
    this.subtitle,
    required this.icon,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () => Tools().common.showNotImplemented(context),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: const BorderRadius.all(Radius.circular(5.0)),
        ),
        padding: const EdgeInsets.fromLTRB(15.0, 5.0, 25.0, 5.0),
        child: Row(
          children: [
            Icon(
              icon,
              color: color ?? Theme.of(context).primaryColor,
              size: 24.0,
            ),
            const SizedBox(width: 25.0),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: backgroundColor != null
                        ? Theme.of(context)
                            .textTheme
                            .bodyText1
                            ?.copyWith(color: Colors.white)
                        : Theme.of(context).textTheme.bodyText1,
                  ),
                  if (subtitle != null)
                    Text(
                      subtitle!,
                      style: backgroundColor != null
                          ? Theme.of(context)
                              .textTheme
                              .subtitle2
                              ?.copyWith(color: Colors.white)
                          : Theme.of(context).textTheme.subtitle2,
                    ),

                  ///endif
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios_sharp,
              size: 12.0,
              color: color ?? Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }
}
