import 'package:flutter/material.dart';
import 'package:multi_select_flutter/multi_select_flutter.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';

class MBPetMultiFormField extends StatelessWidget {
  final dynamic initialValue;
  final String? Function(dynamic)? validator;
  final void Function(dynamic)? onSaved;

  const MBPetMultiFormField(
      {Key? key, this.initialValue, this.validator, this.onSaved})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    return MultiSelectDialogField(
      title: Text(
        'APPLICATION_MOBILE_FIELD_LABEL_WHICH_PET'.tr() + ' *',
        style: theme.textTheme.bodyLarge,
      ),
      items: Data().getPets().map((Pet p) {
        return MultiSelectItem<Pet?>(p, p.name);
      }).toList(),
      chipDisplay: MultiSelectChipDisplay<Pet>(
        chipColor: theme.colorScheme.secondary,
        textStyle: theme.textTheme.labelLarge,
      ),
      buttonText: Text(
        'APPLICATION_MOBILE_FIELD_LABEL_WHICH_PET'.tr() + ' *',
        style: theme.textTheme.headlineSmall,
      ),
      buttonIcon: const Icon(Icons.checklist_outlined),
      confirmText: Text('APPLICATION_MOBILE_BUTTON_LABEL_OK'.tr()),
      cancelText: Text('APPLICATION_MOBILE_BUTTON_LABEL_CANCEL'.tr()),
      listType: MultiSelectListType.CHIP,
      selectedColor: theme.colorScheme.secondary,
      unselectedColor: theme.disabledColor,
      selectedItemsTextStyle: theme.textTheme.labelLarge,
      itemsTextStyle: theme.textTheme.labelLarge,
      validator: validator,
      initialValue: initialValue,
      onConfirm: (_) {},
      onSaved: onSaved,
    );
  }
}
