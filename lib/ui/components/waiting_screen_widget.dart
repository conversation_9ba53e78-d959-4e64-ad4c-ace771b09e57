import 'package:flutter/material.dart';

class WaitingScreenWidget extends StatelessWidget {
  const WaitingScreenWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Scaffold(
        // backgroundColor: Theme.of(context).backgroundColor,
        // body: Center(
        //   child: Padding(
        //     padding: EdgeInsets.only(left: size.width*0.15,right: size.width*0.15),
        //     child: Image.asset('assets/images/wooftrax_color.png'),
        //   ),
        // ),
        // body: Stack(
        //   fit: StackFit.expand,
        //   alignment: AlignmentDirectional.center,
        //   children: <Widget>[
        //     Center(
        //       child: Container(
        //         width: 112.0,
        //         height: 112.0,
        //         alignment: Alignment.center,
        //         // padding: EdgeInsets.all(15.0),
        //         decoration: BoxDecoration(
        //           color: Theme.of(context).colorScheme.secondary,
        //           shape: BoxShape.circle,
        //         ),
        //         child: SvgPicture.asset(
        //           'assets/images/bg-${AppConfig.of(context).prefix}.svg',
        //           color: Colors.white,
        //           width: 70.0,
        //           height: 60.0,
        //         ),
        //       ),
        //     ),
        //     Center(
        //       child: Container(
        //         width: 112.0,
        //         height: 112.0,
        //         padding: const EdgeInsets.all(2.0),
        //         child: CircularProgressIndicator(
        //           strokeWidth: 2.0,
        //           valueColor: AlwaysStoppedAnimation<Color>(
        //               Theme.of(context).backgroundColor),
        //         ),
        //       ),
        //     ),
        //   ],
        // ),
        );
  }
}
