import 'package:flutter/material.dart';

class MBSimpleTextWidget extends StatelessWidget {
  final String? text;
  final IconData? icon;
  final Color? color;
  final Color? strColor;
  final Color? bgColor;
  final void Function()? onTap;

  const MBSimpleTextWidget(
      {Key? key,
      this.text,
      this.icon,
      this.color = Colors.grey,
      this.strColor,
      this.bgColor,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (text == null || text!.trim() == '') {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.only(top: 20),
        child: Container(
          color: bgColor,
          child: Padding(
            padding: EdgeInsets.only(
              left: 20,
              top: bgColor == null ? 0 : 10,
              right: 20,
              bottom: 10,
            ),
            child: Row(
              children: <Widget>[
                if (icon != null) Icon(icon, color: color),

                ///endif
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 15),
                    child: Text(
                      text!,
                      textAlign: TextAlign.justify,
                      style: TextStyle(color: strColor),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
