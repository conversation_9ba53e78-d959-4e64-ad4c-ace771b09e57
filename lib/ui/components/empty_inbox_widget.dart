import 'package:flutter/material.dart';
import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

enum InboxType {messages, messageItems, notifications}

class EmptyInboxWidget extends StatelessWidget {
  final InboxType type;

  const EmptyInboxWidget({Key? key, required this.type}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Icon(_typeToIcon),
        Text(
          _typeToTitle,
          style: Theme.of(context).textTheme.bodyLarge,
          textAlign: TextAlign.center,
        ),
        if (AppConfig.of(context).isWoofTrax)
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: 15.0,
              horizontal: 50.0,
            ),
            child: Text(
              _typeToAdditionalInfo,
              style: Theme.of(context).textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
          ),

        ///endif
      ],
    );
  }

  IconData get _typeToIcon {
    switch(type) {
      case InboxType.messages:
        return Icons.forum;
      case InboxType.messageItems:
        return Icons.chat_bubble;
      case InboxType.notifications:
        return Icons.notifications;
      default:
        return Icons.mood_bad;
    }
  }

  String get _typeToTitle {
    switch(type) {
      case InboxType.messages:
        return 'APPLICATION_MOBILE_HOME_MESSAGE_EMPTY'.tr();
      case InboxType.messageItems:
        return 'APPLICATION_MOBILE_MESSAGE_EMPTY_ITEMS'.tr('Write your first message');
      case InboxType.notifications:
        return 'APPLICATION_MOBILE_HOME_NOTIFICATION_EMPTY'.tr();
      default:
        return 'APPLICATION_MOBILE_TEXT_EMPTY_DATA'.tr();
    }
  }

  //todo complete translations
  String get _typeToAdditionalInfo {
    switch(type) {
      case InboxType.messages:
        return 'APPLICATION_MOBILE_HOME_MESSAGE_EMPTY_ADDITIONAL_INFO'.tr(
            'This is where you will find messages once you are in contact with a clinic, or with your friends on Wooftrax.'
        );
      case InboxType.messageItems:
          return '';
      case InboxType.notifications:
        return 'APPLICATION_MOBILE_HOME_NOTIFICATION_EMPTY_ADDITIONAL_INFO'.tr(
            'This is where you will find alerts when our animal charity earns a donation and also optional pet health notifications.'
        );
    default:
        return '';
    }
  }
}
