import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/models/image.dart';
import 'package:mybuddy/ui/components/readonly_image_view.dart';
import 'package:mybuddy/ui/components/readonly_video_view.dart';

class CachedImageClickable extends StatelessWidget {
  final MBImage image;
  final String title;

  const CachedImageClickable(
      {Key? key, required this.image, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (image.isVideo) {
      return Container(
        decoration: BoxDecoration(border: Border.all()),
        child: IconButton(
          icon: const Icon(FontAwesomeIcons.playCircle),
          onPressed: () {
            Tools().navigatorPush(
              VideoView(
                videoUrl: image.url ?? image.id.toVideoUrl(),
                title: '',
              ),
            );
          },
        ),
      );
    }
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Tools().navigatorPush(
          ImageView(url: image.url ?? '', title: ''),
        );
      },
      child: CachedNetworkImage(
        imageUrl: image.url ?? '',
        fit: BoxFit.cover,
        placeholder: (context, url) => const Center(
          child: CircularProgressIndicator(),
        ),
        errorWidget: (context, url, error) => const Icon(Icons.error),
      ),
    );
  }
}
