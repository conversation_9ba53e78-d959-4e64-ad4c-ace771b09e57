import 'package:flutter/material.dart';

class MBListTile extends StatelessWidget {
  final String? text;
  final IconData? iconData;
  final Color color;
  final Color? bgColor;
  final Color? iconColor;
  final void Function()? onPressed;
  final bool visible;

  const MBListTile(
      {Key? key,
      this.iconData,
      this.iconColor,
      this.color = Colors.grey,
      this.bgColor,
      this.visible = true,
      this.onPressed,
      this.text})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!visible || text == null || text!.trim() == '') {
      return const SizedBox.shrink();
    }
    return Container(
      color: bgColor,
      child: ListTile(
//          contentPadding: EdgeInsets.symmetric(horizontal: 16.0,vertical: 0),
        leading: iconData != null ? Icon(
          iconData,
          color: iconColor ?? color,
          size: 18,
        ) : null,
        title: text != null ? Text(
          text!,
          style: TextStyle(color: color),
        ) : null,
        onTap: onPressed,
      ),
    );
  }
}
