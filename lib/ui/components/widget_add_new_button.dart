import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class AddNewButton extends StatelessWidget {
  final String label;
  final Function onPressed;
  final String? svgAssetPath;

  const AddNewButton(
      {Key? key,
      required this.label,
      required this.onPressed,
      this.svgAssetPath})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onPressed(),
      child: Container(
        height: 35,
        margin: const EdgeInsets.only(left: 15, right: 15, bottom: 10),
        decoration: BoxDecoration(
          border: Border.all(
            color: const Color(0xff218359),
            width: 0.75,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (svgAssetPath != null) ...[
              SvgPicture.asset(
                svgAssetPath!,
                width: 13,
                height: 13,
                color: const Color(0xff218359),
              ),
              const SizedBox(width: 5),
            ],
            Text(
              label,
              style: const TextStyle(
                color: Color(0xff218359),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
