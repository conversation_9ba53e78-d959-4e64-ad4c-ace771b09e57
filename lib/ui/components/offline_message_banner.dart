import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

import '../../controllers/internet_controller.dart';

class OfflineMessageBanner extends StatelessWidget {
  const OfflineMessageBanner({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (InternetController.of().isConnected) {
        return const SizedBox.shrink();
      }
      return Container(
        height: 25,
        decoration: const BoxDecoration(color: Color(0xffF3AE56), boxShadow: [
          BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.02),
              offset: Offset(0, 2.77),
              blurRadius: 2.21),
          BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.03),
              offset: Offset(0, 6.65),
              blurRadius: 5.32),
          BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.04),
              offset: Offset(0, 12.52),
              blurRadius: 10.02),
          BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.04),
              offset: Offset(0, 22.34),
              blurRadius: 17.87),
          BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.05),
              offset: Offset(0, 41.78),
              blurRadius: 33.42),
          BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.07),
              offset: Offset(0, 100),
              blurRadius: 80)
        ]),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                "assets/icon/cloud_slash_filled.png",
                height: 14,
              ),
              const SizedBox(
                width: 5,
              ),
              const Text(
                'You’re offline!',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
      );
    });
  }
}
