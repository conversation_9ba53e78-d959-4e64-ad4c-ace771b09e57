import 'package:flutter/material.dart';

class MBTextShadow extends StatelessWidget {
  final String? label;
  final double fontSize;
  final double opacity;
  final TextAlign textAlign;
  final double? width;

  const MBTextShadow({
    Key? key,
    this.label,
    this.fontSize = 15.0,
    this.opacity = 1.0,
    this.textAlign = TextAlign.center,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      width: width ?? MediaQuery.of(context).size.width * 0.66,
      child: Text(
        label ?? '',
        maxLines: 3,
        textAlign: textAlign,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white.withOpacity(opacity),
          shadows: <Shadow>[
            Shadow(
              offset: const Offset(0.1, 0.1),
              blurRadius: 3.0,
              color: Color.fromARGB((255 * opacity.toInt()), 0, 0, 0),
            ),
          ],
        ),
      ),
    );
  }
}
