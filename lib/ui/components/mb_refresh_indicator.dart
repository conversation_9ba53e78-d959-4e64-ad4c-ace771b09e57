import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Tools/tools.dart';

class MBRefreshIndicator extends StatelessWidget{
  final Widget child;

  const MBRefreshIndicator({Key? key, required this.child}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        if (await Tools().common.isOnline(alertContext: context)) {
          await MbApiOwner().refreshLoginRequest();
        }
      },
      child: child,
    );
  }

}