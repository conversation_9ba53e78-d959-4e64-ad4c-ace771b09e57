import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class MBTextFormFieldUnit extends StatelessWidget {
  final TextEditingController? controller;
  final String? initialValue;
  final FocusNode? focusNode;
  final InputDecoration decoration;
  final TextInputType? keyboardType;
  final TextCapitalization textCapitalization = TextCapitalization.none;
  final TextInputAction? textInputAction;
  final TextStyle? style;
  final StrutStyle? strutStyle;
  final TextDirection? textDirection;
  final TextAlign textAlign;
  final TextAlignVertical? textAlignVertical;
  final bool autofocus;
  final bool readOnly;
  final ToolbarOptions? toolbarOptions;
  final bool? showCursor;
  final bool obscureText;
  final bool autocorrect;
  final bool enableSuggestions;
  final AutovalidateMode autovalidateMode;
  final MaxLengthEnforcement? maxLengthEnforcement;
  final int maxLines;
  final int? minLines;
  final bool expands;
  final int? maxLength;
  final ValueChanged<String>? onChanged;
  final GestureTapCallback? onTap;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onFieldSubmitted;
  final FormFieldSetter<String>? onSaved;
  final FormFieldValidator<String>? validator;
  final List<TextInputFormatter>? inputFormatters;
  final bool enabled;
  final double cursorWidth;
  final Radius? cursorRadius;
  final Color? cursorColor;
  final Brightness? keyboardAppearance;
  final EdgeInsets scrollPadding;
  final bool enableInteractiveSelection;
  final InputCounterWidgetBuilder? buildCounter;
  final String unitText;

  const MBTextFormFieldUnit(this.unitText,
      {Key? key,
      this.enableInteractiveSelection = true,
      this.decoration = const InputDecoration(),
      this.textAlign = TextAlign.start,
      this.autofocus = false,
      this.readOnly = false,
      this.obscureText = false,
      this.autocorrect = true,
      this.enableSuggestions = true,
      this.autovalidateMode = AutovalidateMode.disabled,
      this.maxLengthEnforcement,
      this.maxLines = 1,
      this.expands = false,
      this.enabled = true,
      this.cursorWidth = 2.0,
      this.scrollPadding = const EdgeInsets.all(20.0),
      this.controller,
      this.initialValue,
      this.focusNode,
      this.keyboardType,
      this.textInputAction,
      this.style,
      this.strutStyle,
      this.textDirection,
      this.textAlignVertical,
      this.toolbarOptions,
      this.showCursor,
      this.minLines,
      this.maxLength,
      this.onChanged,
      this.onTap,
      this.onEditingComplete,
      this.onFieldSubmitted,
      this.onSaved,
      this.validator,
      this.inputFormatters,
      this.cursorRadius,
      this.cursorColor,
      this.keyboardAppearance,
      this.buildCounter})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Expanded(
          child: TextFormField(
            enableInteractiveSelection: enableInteractiveSelection,
            decoration: decoration,
            textAlign: textAlign,
            autofocus: autofocus,
            readOnly: readOnly,
            obscureText: obscureText,
            autocorrect: autocorrect,
            enableSuggestions: enableSuggestions,
            autovalidateMode: autovalidateMode,
            maxLengthEnforcement: maxLengthEnforcement ??
                LengthLimitingTextInputFormatter
                    .getDefaultMaxLengthEnforcement(),
            maxLines: maxLines,
            expands: expands,
            enabled: enabled,
            cursorWidth: cursorWidth,
            scrollPadding: scrollPadding,
            key: key,
            controller: controller,
            initialValue: initialValue,
            focusNode: focusNode,
            keyboardType: keyboardType,
            textInputAction: textInputAction,
            style: style,
            strutStyle: strutStyle,
            textDirection: textDirection,
            textAlignVertical: textAlignVertical,
            toolbarOptions: toolbarOptions,
            showCursor: showCursor,
            minLines: minLines,
            maxLength: maxLength,
            onChanged: onChanged,
            onTap: onTap,
            onEditingComplete: onEditingComplete,
            onFieldSubmitted: onFieldSubmitted,
            onSaved: onSaved,
            validator: validator,
            inputFormatters: inputFormatters,
            cursorRadius: cursorRadius,
            cursorColor: cursorColor,
            keyboardAppearance: keyboardAppearance,
            buildCounter: buildCounter,
          ),
        ),
        Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(border: Border.all(color: Colors.grey)),
          child: Text(unitText),
        )
      ],
    );
  }
}
