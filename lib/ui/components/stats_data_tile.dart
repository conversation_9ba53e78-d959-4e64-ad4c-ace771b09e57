import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';

class StatsDataTile extends StatelessWidget {
  const StatsDataTile({
    Key? key,
    required this.icon,
    required this.title,
    required this.value,
    this.tooltip,
  }) : super(key: key);

  final String icon, title, value;
  final String? tooltip;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Card(
        margin: EdgeInsets.zero,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset(
                    icon,
                    width: 16.0,
                    height: 16.0,
                  ),
                  const SizedBox(width: 5.0),
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyText2?.copyWith(
                          color: const Color(0xFF939393),
                          fontSize: 12.0,
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                  const Spacer(),
                  if (tooltip != null)
                    GestureDetector(
                      onTap: () {
                        Tools().common.showCustomDialog(
                              description: tooltip,
                              validTitle: 'Okay',
                            );
                      },
                      child: Icon(
                        FontAwesomeIcons.infoCircle,
                        size: 15.0,
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 5.0),
              Text(
                value,
                style: Theme.of(context).textTheme.subtitle1?.copyWith(
                      fontSize: 16.0,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xff474747),
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
