import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

class MBCarousel extends StatefulWidget {
  final List<Widget> items;
  final double? height;
  final double viewportFraction;
  final bool autoPlay;
  final Duration? autoPlayInterval;
  final Duration? pauseAutoPlayOnTouch;
  final bool? enableInfiniteScroll;

  const MBCarousel(
      {Key? key,
      this.height,
      this.viewportFraction = 0.8,
      this.autoPlay = false,
      this.autoPlayInterval,
      this.pauseAutoPlayOnTouch,
      this.enableInfiniteScroll,
      required this.items})
      : super(key: key);

  @override
  _MBCarouselState createState() => _MBCarouselState();
}

class _MBCarouselState extends State<MBCarousel> {
  int _current = 0;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        CarouselSlider(
          items: widget.items,
          options: CarouselOptions(
            autoPlay: widget.autoPlay,
            aspectRatio: 2.0,
            height: widget.height,
            viewportFraction: widget.viewportFraction,
            autoPlayInterval: const Duration(seconds: 6),
            enableInfiniteScroll: widget.items.length > 1,
            scrollDirection: Axis.horizontal,
            onPageChanged: (index, reason) {
              setState(() {
                _current = index;
              });
            },
          ),
        ),
        Positioned(
          bottom: -4,
          left: 0.0,
          right: 0.0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: widget.items.asMap().entries.map((entry) {
              return Container(
                width: 8.0,
                height: 8.0,
                margin: const EdgeInsets.symmetric(
                  vertical: 10.0,
                  horizontal: 2.0,
                ),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _current == entry.key
                      ? Colors.blue[900]
                      : Colors.blue[100],
                ),
              );
            }).toList(),
          ),
        )
      ],
    );
  }
}
