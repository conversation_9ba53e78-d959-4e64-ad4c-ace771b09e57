import 'package:flutter/material.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';

class MBSpeedDial extends StatelessWidget {
  final List<MBSpeedDialChild> children;
  final String? heroTag;

  const MBSpeedDial({Key? key, required this.children, this.heroTag})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SpeedDial(
      animatedIcon: AnimatedIcons.menu_close,
      animatedIconTheme: const IconThemeData(size: 22.0),
      visible: children.isNotEmpty,
      closeManually: false,
      curve: Curves.bounceIn,
      overlayColor: Theme.of(context).backgroundColor,
      overlayOpacity: 0.75,
      tooltip: 'Speed Dial',
      heroTag: heroTag ?? 'speed-dial-hero-tag',
      backgroundColor: Theme.of(context).colorScheme.secondary,
      foregroundColor: Colors.white,
      elevation: 8.0,
      shape: const CircleBorder(),
      children: children,
    );
  }
}

class MBSpeedDialChild extends SpeedDialChild {
  MBSpeedDialChild(
      {String? label,
      IconData? iconData,
      Color? backgroundColor,
      VoidCallback? onTap,
      Widget? labelWidget})
      : super(
          backgroundColor: backgroundColor,
          onTap: onTap,
          child: Icon(
            iconData,
            color: Colors.white,
            size: 18.0,
          ),
          labelWidget: labelWidget ??
              Builder(
                builder: (BuildContext context) {
                  return Container(
                    width: MediaQuery.of(context).size.width * 0.7,
                    alignment: Alignment.centerRight,
                    child: Text(
                      label ?? '',
                      style: Theme.of(context).textTheme.bodyText2,
                      maxLines: 2,
                    ),
                  );
                },
              ),
        );
}
