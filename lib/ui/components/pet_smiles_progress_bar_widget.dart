import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/workouts_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/workout.dart';

class PetSmilesProgressBarWidget extends StatelessWidget {
  /// shows a progress bar of owner petSmiles with number of points, owner level
  /// total number of walks, total distance.
  /// It needs to be nested into a bloc [BlocProvider] of [WorkoutsBloc]
  const PetSmilesProgressBarWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<WorkoutsBloc>(context);
    Owner owner = Data().get().owner;
    int points = owner.points;
    Map<String, dynamic> buildLevel = points.toLevel();

    return Padding(
      padding: const EdgeInsets.all(15.0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CircleAvatar(
                    radius: 10.0,
                    backgroundColor: Colors.orangeAccent,
                    foregroundColor: Colors.white,
                    child: Icon(
                      Icons.pets,
                      size: 10.0,
                    ),
                  ),
                  const SizedBox(
                    width: 3.0,
                  ),
                  Text(
                    points.toString(),
                    style: Theme.of(context)
                        .textTheme
                        .headlineMedium
                        ?.copyWith(color: Colors.orangeAccent, height: 1.0),
                  ),
                  Text(
                    '/',
                    style: Theme.of(context)
                        .textTheme
                        .headlineSmall
                        ?.copyWith(color: Theme.of(context).disabledColor),
                  ),
                  Text(
                    '${buildLevel['goal']} points',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                ],
              ),
              Text(
                'Level ${buildLevel['level']}',
                style: Theme.of(context).textTheme.titleSmall,
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: 5.0,
            ),
            child: ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(4.0)),
              child: LinearProgressIndicator(
                value: buildLevel['progress'],
                minHeight: 8.0,
                valueColor:
                    const AlwaysStoppedAnimation<Color>(Colors.orangeAccent),
                backgroundColor: Colors.orangeAccent.withOpacity(0.15),
              ),
            ),
          ),
          StreamBuilder(
            stream: bloc.stream,
            builder: (context, AsyncSnapshot<List<Workout>> snapshot) {
              List<Workout> workouts = <Workout>[];
              double totalDistance = 0.0;
              if (snapshot.hasData) {
                workouts = snapshot.data!;
                for (var w in workouts) {
                  totalDistance += w.distance;
                }
              }
              return Row(
                children: [
                  Row(mainAxisSize: MainAxisSize.min, children: [
                    const Icon(
                      FontAwesomeIcons.paw,
                      size: 12.0,
                    ),
                    const SizedBox(width: 3.0),
                    Text(
                      '${workouts.length} walks',
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ]),
                  const SizedBox(width: 6.0),
                  Row(mainAxisSize: MainAxisSize.min, children: [
                    const Icon(
                      FontAwesomeIcons.shoePrints,
                      size: 12.0,
                    ),
                    const SizedBox(width: 3.0),
                    Text(
                      '${(totalDistance / 1.609).toStringAsFixed(1)} miles walks',
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ]),
                  const SizedBox(width: 6.0),
                  Row(mainAxisSize: MainAxisSize.min, children: [
                    const CircleAvatar(
                      radius: 6.0,
                      backgroundColor: Colors.orangeAccent,
                      foregroundColor: Colors.white,
                      child: Icon(
                        Icons.pets,
                        size: 6.0,
                      ),
                    ),
                    const SizedBox(width: 3.0),
                    Text(
                      '$points PetSmiles',
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium
                          ?.copyWith(color: Colors.orangeAccent),
                    ),
                  ]),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
