import 'dart:io';

import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/ui/components/widget_simple_image_dialog.dart';

class ImagesFormField extends FormField<List<File>> {
  ImagesFormField({
    Key? key,
    FormFieldSetter<List<File>?>? onSaved,
    FormFieldValidator<List<File>?>? validator,
    List<File>? initialValue,
    AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
    double height = 30,
    required BuildContext context,
    numberItems = 5,
    String? text = '',
  }) : super(
    key: key,
    onSaved: onSaved,
    validator: validator,
    initialValue: initialValue,
    autovalidateMode: autovalidateMode,
    builder: (FormFieldState<List<File>> state) {
            //use to create the row with correct size (maybe it can be done with the listview.builder)
            List<int> positions = [];
            for (int i = 0; i < numberItems; i++) {
              positions.add(i);
            }
            return Container(
              padding: const EdgeInsets.fromLTRB(5.0, 10.0, 5.0, 5.0),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColorLight,
                border: Border.all(color: Theme.of(context).primaryColor),
                borderRadius: const BorderRadius.all(Radius.circular(8.0)),
              ),
              height: height,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  if (text != null && text.trim() != '') Text(text),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Row(
                        children: <Widget>[
                          if(state.value != null)
                            Expanded(
                              child: Row(
                                children: positions.map<Widget>((position) {
                                  if (position >= state.value!.length) {
                                    return Expanded(
                                      flex: 1,
                                      child: Padding(
                                        padding:
                                            const EdgeInsets.only(left: 5, right: 5),
                                        child: AspectRatio(
                                          aspectRatio: 1,
                                          child: Container(),
                                        ),
                                      ),
                                    );
                                  }
                                  File image = state.value![position];
                                  return Expanded(
                                    flex: 1,
                                    child: Padding(
                                      padding: const EdgeInsets.only(left: 5, right: 5),
                                      child: AspectRatio(
                                        aspectRatio: 1,
                                        child: GestureDetector(
                                          behavior: HitTestBehavior.translucent,
                                          onTap: () async {
                                            bool shouldRemove = (await showDialog(
                                              barrierColor: Colors.transparent,
                                              context: context,
                                              builder: (_) => SimpleImageDialog(
                                                image: image,
                                              ),
                                            )) as bool;
                                            if (shouldRemove) {
                                              List<File> images = state.value!;
                                              images.remove(image);
                                              state.didChange(images);
                                            }
                                          },
                                          child: Image(
                                            image: FileImage(image),
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),

                          ///endif
                          Visibility(
                            visible: state.value == null || state.value!.length < 5,
                            child: IconButton(
                              icon: const Icon(Icons.add_a_photo),
                              onPressed: () async {
                                Tools tools = Tools();
                                File? newImage =
                                    await tools.image.pickAndCropAvatar(context);
                                List<File>? images = state.value;
                                images ??= <File>[];
                                if (newImage != null) {
                                  images.add(newImage);
                                  state.didChange(images);
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
}
