import 'package:flutter/material.dart';

class SocialErrorMessage extends StatelessWidget {
  const SocialErrorMessage(
      {Key? key, required this.message, required this.icon})
      : super(key: key);

  final String icon, message;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFDEFEC),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Image.asset(
              icon,
              width: 36,
              height: 36,
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: const Color(0xFF474747),
                  fontSize: 14.0,
                  height: 1.5,
                  fontWeight: FontWeight.normal),
            ),
          ),
        ],
      ),
    );
  }
}
