import 'package:flutter/material.dart';

enum PopupItem { removeUser, makeAdmin }

class PopupButtonMenu extends StatelessWidget {
  final Function(PopupItem) onItemSelected;
  final List<PopupItemMenu> items;

  const PopupButtonMenu({
    Key? key,
    required this.onItemSelected,
    required this.items,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<PopupItem>(
      onSelected: onItemSelected,
      icon: const Icon(Icons.more_vert, color: Color(0xff218359)),
      itemBuilder: (BuildContext context) => items.map((item) {
        return PopupMenuItem<PopupItem>(
          value: item.item,
          child: Text(
            item.displayText,
            style: const TextStyle(color: Colors.black),
          ),
        );
      }).toList(),
    );
  }
}

class PopupItemMenu {
  final PopupItem item;
  final String displayText;

  const PopupItemMenu({
    required this.item,
    required this.displayText,
  });
}
