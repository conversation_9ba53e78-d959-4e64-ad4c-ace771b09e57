import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_text_shadow_widget.dart';

import '../../controllers/internet_controller.dart';

class HomeSliverScreen extends StatefulWidget {
  final String? title;
  final Widget? background;
  final Widget? centerWidget;
  final double expandedHeight;
  final List<String>? tabs;
  final bool tabsPinned;
  final bool tabsFloating;
  final Widget? subHeader;
  final bool subHeaderPinned;
  final bool subHeaderFloating;
  final List<Widget>? actions;
  final Widget child;
  final TabController? tabController;
  final bool automaticallyImplyLeading;
  final Color? primaryColor, backgroundColor;
  final double? tabFontSize;
  final double? tabWidth, tabHeight;
  final FontWeight? tabFontWeight;

  const HomeSliverScreen(
      {Key? key,
      this.title,
      this.tabFontSize,
      this.tabFontWeight,
      this.tabs,
      this.subHeader,
      required this.child,
      this.tabController,
      this.primaryColor,
      this.backgroundColor,
      this.actions,
      this.background,
      this.expandedHeight = 110.0,
      this.tabsPinned = false,
      this.tabsFloating = false,
      this.subHeaderPinned = true,
      this.subHeaderFloating = false,
      this.automaticallyImplyLeading = false,
      this.centerWidget,
      this.tabWidth,
      this.tabHeight})
      : assert(
            (tabs == null && tabController == null) ||
                (tabs != null && tabController != null),
            'You must provide a tabController if you define a list of tabs!'),
        super(key: key);

  @override
  _HomeSliverScreenState createState() => _HomeSliverScreenState();
}

class _HomeSliverScreenState extends State<HomeSliverScreen>
    with SingleTickerProviderStateMixin {
  int selectedIndex = 0;
  double opacity = 0.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.backgroundColor,
      appBar: MBAppBar(
        leading: widget.automaticallyImplyLeading
            ? BackButton(
                onPressed: () => Tools().navigatorPop(),
                color: widget.primaryColor,
              )
            : const SizedBox.shrink(),
        automaticallyImplyLeading: widget.automaticallyImplyLeading,
        // elevation: 5.0,
        flexibleSpace: widget.centerWidget ??
            FlexibleSpaceBar(
              centerTitle: true,
              title: widget.background == null
                  ? Text(
                      widget.title ?? '',
                      style: Theme.of(context).textTheme.headline5?.copyWith(
                          color: widget.primaryColor ??
                              Theme.of(context)
                                  .primaryColor
                                  .withOpacity(1.0 - opacity),
                          fontSize: 20,
                          fontWeight: FontWeight.w700),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    )
                  : widget.title != null
                      ? MBTextShadow(
                          label: widget.title,
                          fontSize: 20.0,
                          opacity: 1.0 - opacity,
                        )
                      : null,
              titlePadding: const EdgeInsets.only(
                left: 40.0,
                right: 40.0,
                bottom: 15.0,
              ),
              background: widget.background,
            ),
        actions: [
          if (widget.actions != null) ...widget.actions!,

          ///endif
          if (AppConfig.of(context).isWoofTrax &&
              !widget.automaticallyImplyLeading)
            const SizedBox(width: 48.0),

          /// endif
        ],
      ),
      body: Column(
        children: [
          const SizedBox(
            height: 10,
          ),
          Obx(() => InternetController.of().isConnected && widget.tabs != null
              ? Container(
                  width: MediaQuery.of(context).size.width,
                  padding: const EdgeInsets.only(left: 10.0),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    physics: const BouncingScrollPhysics(),
                    child: Row(
                      children: List.generate(
                          widget.tabs?.length ?? 0,
                          (index) => Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 5.0),
                                child: ChoiceChip(
                                  label: SizedBox(
                                    width: widget.tabWidth,
                                    height: widget.tabHeight,
                                    child: Center(
                                      child: Text(
                                        widget.tabs![index],
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                                  labelStyle: Theme.of(context)
                                      .textTheme
                                      .bodyText1
                                      ?.copyWith(
                                        fontSize: widget.tabFontSize ?? 12.0,
                                        fontWeight: widget.tabFontWeight,
                                        color: selectedIndex == index
                                            ? Colors.white
                                            : Theme.of(context).disabledColor,
                                      ),
                                  labelPadding: EdgeInsets.zero,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20.0),
                                  pressElevation: 5,
                                  backgroundColor: Theme.of(context)
                                      .disabledColor
                                      .withOpacity(0.25),
                                  selectedColor: widget.primaryColor ??
                                      Theme.of(context).primaryColor,
                                  selected: index == selectedIndex,
                                  onSelected: (bool selected) {
                                    if (selected) {
                                      widget.tabController!.animateTo(index);
                                      setState(() {
                                        selectedIndex = index;
                                      });
                                    }
                                  },
                                ),
                              )),
                    ),
                  ),
                )
              : const SizedBox()),

          /// endif

          if (widget.subHeader != null) widget.subHeader!,

          if (widget.tabs != null || widget.subHeader != null)
            const SizedBox(
              height: 5,
            ),
          Expanded(child: widget.child)
        ],
      ),
    );
  }
}

class MBSliverHeader extends StatelessWidget {
  final Widget child;
  final double maxExtent;
  final double minExtent;
  final bool pinned;
  final bool floating;

  const MBSliverHeader(
      {Key? key,
      required this.child,
      this.maxExtent = 60.0,
      this.minExtent = 40.0,
      this.pinned = true,
      this.floating = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SliverPersistentHeader(
      pinned: pinned,
      floating: floating,
      delegate: MBHeaderDelegate(child, maxExtent, minExtent),
    );
  }
}

class MBHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double max;
  final double min;

  MBHeaderDelegate(this.child, this.max, this.min);

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => max;

  @override
  double get minExtent => min;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
