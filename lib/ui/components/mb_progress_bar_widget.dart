import 'package:flutter/material.dart';

class MBProgressBar extends StatelessWidget {
  final double? value;

  const MBProgressBar({Key? key, this.value}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 5.0,
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(5.0)),
        child: LinearProgressIndicator(
          value: value,
          minHeight: 10.0,
          valueColor: const AlwaysStoppedAnimation<Color>(Colors.orangeAccent),
          backgroundColor: Colors.orangeAccent.withOpacity(0.15),
        ),
      ),
    );
  }
}
