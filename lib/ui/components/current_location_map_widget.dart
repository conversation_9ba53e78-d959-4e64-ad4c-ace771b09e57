import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mybuddy/controllers/location_map_controller.dart';

class CurrentLocationMapWidget extends StatelessWidget {
  final LocationMapController controller = Get.put(LocationMapController());

  CurrentLocationMapWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        return Stack(
          children: [
            GoogleMap(
              onMapCreated: controller.onMapCreated,
              initialCameraPosition: controller.initialCameraPosition(),
              myLocationEnabled: false,
              myLocationButtonEnabled: false,
              zoomControlsEnabled: false,
              compassEnabled: true,
              markers: Set<Marker>.of(controller.markers.values),
              polylines: Set<Polyline>.of(controller.polylines.values),
            ),
            if (controller.locationDisabled)
              _locationDisabledWidget(context)
            else
              _showCurrentLocationButton()
          ],
        );
      },
    );
  }

  Widget _locationDisabledWidget(context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: MediaQuery.of(context).size.width * 0.7,
            decoration: BoxDecoration(
                color: const Color(0xffFFF9FB).withOpacity(0.8),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(color: const Color(0xffDA9595), width: 0.5)),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: const Text(
              "Please enable your location so we can locate you on the map.",
              style: TextStyle(
                  fontSize: 13,
                  color: Color(0xffCB1F1F),
                  fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          Card(
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(100)),
            elevation: 5,
            child: Container(
              height: 54,
              width: 54,
              padding: const EdgeInsets.all(8),
              decoration: const BoxDecoration(
                  color: Colors.white, shape: BoxShape.circle),
              child: Image.asset(
                "assets/icon/unknown_location.png",
                height: 24,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _showCurrentLocationButton() {
    return Positioned(
        right: 10,
        bottom: 30,
        child: FloatingActionButton(
          mini: true,
          backgroundColor: Colors.white,
          onPressed: () {
            controller.animateToCurrentPosition();
          },
          child: const Icon(
            Icons.gps_fixed_outlined,
            color: Colors.black,
            size: 18,
          ),
        ));
  }
}
