import 'package:flutter/material.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/owner.dart';

class EarningPointsWidget extends StatelessWidget {
  final double size;
  final int? points;
  final bool centered;
  final Color avatarColor;

  const EarningPointsWidget({
    Key? key,
    this.size = 10.0,
    this.points,
    this.centered = false,
    this.avatarColor = Colors.white,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Owner owner = Data().get().owner;
    int ownerPoints = owner.points;

    return Container(
      height: size * 1.25,
      alignment: Alignment.topLeft,
      child: Row(
        mainAxisAlignment: centered ? MainAxisAlignment.center : MainAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: size * 0.5,
            backgroundColor: Colors.orangeAccent,
            foregroundColor: avatarColor,
            child: Icon(
              Icons.pets,
              size: size * 0.6,
            ),
          ),
          const SizedBox(width: 4.0),
          Text(
            '${points != null ? '+' : ''}${points ?? ownerPoints} points',
            style: TextStyle(
              color: Colors.orangeAccent,
              fontSize: size,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

class EarnedPointsWidget extends StatelessWidget {
  final int? totalPoints;
  final double size;
  final bool reversed;

  /// show total points earned by a pet owner
  const EarnedPointsWidget({Key? key, this.totalPoints, this.reversed = false, this.size = 14.0}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      textDirection: reversed ? TextDirection.rtl : TextDirection.ltr,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '${totalPoints ?? 0}',
          style: TextStyle(
            color: Colors.orangeAccent,
            fontSize: size,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 3.0),
        CircleAvatar(
          radius: size * 0.5,
          backgroundColor: Colors.orangeAccent,
          foregroundColor: Colors.white,
          child: Icon(
            Icons.pets,
            size: size * 0.6,
          ),
        ),
      ],
    );
  }
}
