import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/cv_appointment_bloc.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/captainvet/cv_stepper.dart';

class CVAppointment extends StatelessWidget {
  final MBService clinic;

  const CVAppointment({Key? key, required this.clinic}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text('APPLICATION_MOBILE_CAPTAINVET_16'.tr('Renseignez les informations suivantes')),
      ),
      body: CVInheritedWidget(
        clinic: clinic,
        child: const CVStepper(),
      ),
    );
  }
}
