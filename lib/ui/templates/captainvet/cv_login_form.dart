import 'package:flutter/material.dart';
import 'package:mybuddy/Api/captainvet_api.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class CVLoginForm extends StatefulWidget {
  const CVLoginForm({Key? key}) : super(key: key);

  @override
  _CVLoginFormState createState() => _CVLoginFormState();
}

class _CVLoginFormState extends State<CVLoginForm> {
  final formKey = GlobalKey<FormState>();
  Map<String, dynamic> connexionForm = <String, dynamic>{};

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          TextFormField(
            decoration: InputDecoration(labelText: 'APPLICATION_MOBILE_CAPTAINVET_13'.tr('Email')),
            keyboardType: TextInputType.emailAddress,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            validator: (value) {
              if (value!.isEmpty) {
                return 'APPLICATION_MOBILE_MESSAGE_USER_FORM_EMAIL_ADDRESS_REQUIRED'.tr();
              }
              return null;
            },
            onSaved: (value) {
              connexionForm['username'] = value;
            },
          ),
          const SizedBox(height: 10.0),
          TextFormField(
            decoration:
                InputDecoration(labelText: 'APPLICATION_MOBILE_CAPTAINVET_14'.tr('mot de passe'),),
            keyboardType: TextInputType.visiblePassword,
            obscureText: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_REQUIRED'.tr();
              }
              return null;
            },
            onSaved: (value) {
              connexionForm['password'] = value;
            },
          ),
          const SizedBox(height: 10.0),
          ElevatedButton(
            onPressed: () async {
              FormState? form = formKey.currentState;
              if (form != null && form.validate()) {
                form.save();
                bool captainvetResponse =
                    await CaptainVetApi().loginCaptainVet(context, connexionForm);
                Tools().navigatorPop(value: captainvetResponse);
              }
            },
            child: Text('APPLICATION_MOBILE_CAPTAINVET_15'.tr('Connexion')),
          ),
        ],
      ),
    );
  }
}
