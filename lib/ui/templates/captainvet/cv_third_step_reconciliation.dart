import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:json_api/document.dart';
import 'package:mybuddy/Api/captainvet_api.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/cv_appointment_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';

class CVThirdStepReconciliation extends StatefulWidget {
  const CVThirdStepReconciliation({Key? key}) : super(key: key);

  @override
  _CVThirdStepReconciliationState createState() => _CVThirdStepReconciliationState();
}

class _CVThirdStepReconciliationState extends State<CVThirdStepReconciliation> {
  bool isFirstTime = false;
  late List<Resource> captainvetPets;
  String? selectedCaptainvetPet;
  String? startAt;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final CVInheritedWidgetState inheritedState = CVInheritedWidget.of(context);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Icon(
                Icons.error,
                color: Colors.deepOrangeAccent,
                size: 36.0,
              ),
              const SizedBox(
                width: 10.0,
              ),
              Expanded(
                child: Text(
                  'APPLICATION_MOBILE_CAPTAINVET_35'
                      .tr('Votre rendez-vous n\'est pas encore confirmé'),
                  maxLines: 2,
                  softWrap: true,
                  style: const TextStyle(fontSize: 18.0, color: Colors.deepOrangeAccent),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15.0),
          Padding(
            padding: const EdgeInsets.all(5.0),
            child: Text('APPLICATION_MOBILE_CAPTAINVET_36'
                .tr('Pour %1')
                .replaceAll('%1', inheritedState.pet.name)),
          ),
          Padding(
            padding: const EdgeInsets.all(5.0),
            child: Text('APPLICATION_MOBILE_CAPTAINVET_37'
                .tr('Dans la clinique %1')
                .replaceAll('%1', inheritedState.widget.clinic.name ?? '')),
          ),
          Padding(
            padding: const EdgeInsets.all(5.0),
            child: Text('APPLICATION_MOBILE_CAPTAINVET_38'
                .tr('à la date de %1')
                .replaceAll('%1', inheritedState.data['startAt'])),
          ),
          const Divider(height: 25),
          CheckboxListTile(
              title: Text('APPLICATION_MOBILE_CAPTAINVET_39'.tr('première prise de rendez-vous')),
              value: isFirstTime,
              onChanged: (bool? value) {
                if (value != null) {
                  setState(() {
                    isFirstTime = value;
                  });
                }
              }),
          Center(
            child: ElevatedButton(
              onPressed: () async {
                if (!inheritedState.isPetReconciled) {
                  String? captainvetPetId = await _showDialog(context, inheritedState);

                  if (captainvetPetId == null) {
                    ///aborted
                    return;
                  }
                  Pet petReconciled = (captainvetPetId == '')
                      ? await inheritedState.createCaptainVetPet(context)
                      : await inheritedState.reconcileCaptainVetPet(context, captainvetPetId);
                  inheritedState.setPet(petReconciled);
                }
                inheritedState.addData('animals', inheritedState.pet.captainvetId);
                inheritedState.addData('isFirstInOffice', isFirstTime);
                await inheritedState
                    .createApt(context)
                    .then((value) => inheritedState.next())
                    .catchError((error) {
                  Tools().message.showMessage(context, error.toString());
                });
              },
              child: Text('APPLICATION_MOBILE_LABEL_CONFIRMATION'.tr()),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _initCaptainVetsPet(String? animalType) async {
    List<Pet?> pets = Data().getPets(livingState: LivingState.alive);
    captainvetPets = (await CaptainVetApi().getCaptainVetPets()).collection.where((element) {
      Identifier? object;
      element.relationships.forEach((key, value) {
        if (key == 'animal-type') {
          object = value.toJson()['data'];
        }
      });
      return (object == null || object!.id == animalType) &&
          !pets.any((pet) {
            return pet!.captainvetId != null && pet.captainvetId == element.id;
          });
    }).toList();
  }

  Future<String?> _showDialog(BuildContext context, CVInheritedWidgetState inheritedState) async {
    await _initCaptainVetsPet(inheritedState.data['animal-types']);
    return await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(8),
              titleTextStyle:
                  const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.black),
              title: Text(
                'APPLICATION_MOBILE_CAPTAINVET_40'.tr(
                    "Le compte de votre animal myBuddy pet app® n'est pas relié à un animal dans le portail CaptainVet."),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  captainvetPets.isEmpty
                      ? ListTile(
                          title: Text(inheritedState.pet.name),
                          subtitle: Text(
                            inheritedState.pet.birthDate != null
                                ? 'date : ${DateFormat('dd MM yyyy').format(inheritedState.pet.birthDate!)}'
                                : '',
                          ),
                        )
                      : Text(
                    'APPLICATION_MOBILE_CAPTAINVET_41'.tr('Choisissez parmi la liste des animaux existants dans CaptainVet :'),
                  ),
                  Column(
                    children: captainvetPets.map((e) => _reconcileItem(e, setDialogState)).toList(),
                  ),
                  captainvetPets.isNotEmpty && selectedCaptainvetPet == null
                      ? Text('APPLICATION_MOBILE_CAPTAINVET_43'.tr('ou'))
                      : Container(),
                  ElevatedButton(
                    onPressed: () {
                      Tools().navigatorPop(value: selectedCaptainvetPet ?? '');
                    },
                    child: Text(
                      captainvetPets.isNotEmpty && selectedCaptainvetPet != null
                          ? 'APPLICATION_MOBILE_CAPTAINVET_FIRST_APT_CHECKBOX'
                              .tr('Valider la sélection')
                          : 'APPLICATION_MOBILE_CAPTAINVET_44'
                              .tr('Créer votre animal dans CaptainVet'),
                    ),
                  )
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _reconcileItem(Resource captainvetPet, setDialogState) {
    return ListTile(
      title: Text(
        '${captainvetPet.attributes['name']}',
        style: TextStyle(
            fontWeight: selectedCaptainvetPet == captainvetPet.id ? FontWeight.bold : null),
      ),
      subtitle: Text(
        'APPLICATION_MOBILE_CAPTAINVET_42'
            .tr('date : %1')
            .replaceAll('%1', captainvetPet.attributes['birthday'] as String),
        style: TextStyle(
            fontWeight: selectedCaptainvetPet == captainvetPet.id ? FontWeight.bold : null),
      ),
      trailing: captainvetPet.id == selectedCaptainvetPet
          ? const Icon(
              Icons.check,
              color: Colors.greenAccent,
            )
          : null,
      onTap: () {
        setDialogState(() {
          selectedCaptainvetPet =
              selectedCaptainvetPet == captainvetPet.id ? null : captainvetPet.id;
        });
      },
      selected: captainvetPet.id == selectedCaptainvetPet,
    );
  }
}
