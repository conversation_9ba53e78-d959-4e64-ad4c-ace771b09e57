import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/cv_appointment_bloc.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/captainvet/cv_first_step_form.dart';
import 'package:mybuddy/ui/templates/captainvet/cv_third_step_reconciliation.dart';

class CVStepper extends StatelessWidget {
  const CVStepper({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final CVInheritedWidgetState inheritedState = CVInheritedWidget.of(context);
    return !inheritedState.isComplete
        ? Stepper(
            currentStep: inheritedState.currentStep,
            controlsBuilder: controls,
            type: StepperType.horizontal,
            onStepTapped: (step) => inheritedState.goTo(step),
            onStepContinue: () {},
            onStepCancel: () {},
            steps: [
              /// post first appointments details
              Step(
                  title: Text('APPLICATION_MOBILE_CAPTAINVET_17'.tr('details')),
                  content: const FirstStepForm(),
                  isActive: inheritedState.currentStep == 0,
                  state: inheritedState.currentStep == 0
                      ? StepState.indexed
                      : StepState.complete),

              /// reconciliation and confirm apt
              Step(
                title:
                    Text('APPLICATION_MOBILE_CAPTAINVET_19'.tr('Confirmation')),
                content: const CVThirdStepReconciliation(),
                isActive: inheritedState.currentStep == 2,
                state: inheritedState.currentStep < 2
                    ? StepState.disabled
                    : StepState.indexed,
              ),
            ],
          )
        : _terminateDialog();
  }

  /// stepper controls buttons deactivated
  Widget controls(BuildContext context, ControlsDetails controlDetail) {
    return const SizedBox.shrink();
  }

  /// dialog box when stepper is complete
  Widget _terminateDialog() {
    return Center(
      child: AlertDialog(
        title: Text(
          'APPLICATION_MOBILE_CAPTAINVET_20'
              .tr('Félicitations, votre rendez-vous est confirmé.'),
        ),
        content: Text(
          'APPLICATION_MOBILE_CAPTAINVET_21'.tr(
              'Nous venons de vous envoyer un email de confirmation de rendez-vous. Vous recevrez également un sms, de rappel avant le rendez-vous.'),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () {
              Tools().navigatorPopAll();
            },
            child: Text(
              'APPLICATION_MOBILE_CAPTAINVET_22'.tr('Revenir à l\'accueil'),
            ),
          ),
        ],
      ),
    );
  }
}
