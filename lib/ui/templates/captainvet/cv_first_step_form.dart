import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/cv_appointment_bloc.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';

class FirstStepForm extends StatefulWidget {
  const FirstStepForm({Key? key}) : super(key: key);

  @override
  _FirstStepFormState createState() => _FirstStepFormState();
}

class _FirstStepFormState extends State<FirstStepForm> {
  final formKey = GlobalKey<FormState>();

  late Pet? animalValue;
  String rdvTypeValue = '';
  String vetValue = '';

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final CVInheritedWidgetState inheritedState = CVInheritedWidget.of(context);
    return Form(
      key: formKey,
      child: Column(
        children: [
          /// pick a pet which its species is in office captain vet
          DropdownButtonFormField<Pet>(
            value: animalValue,
            icon: const Icon(Icons.add),
            iconSize: 18,
            elevation: 16,
            style: const TextStyle(color: Colors.black),
            isExpanded: true,
            isDense: false,
            onChanged: (Pet? newValue) {
              if (newValue != null) {
                inheritedState.setPet(newValue);
                setState(() {
                  animalValue = newValue;
                  rdvTypeValue = '';
                });
              }
            },
            validator: (val) {
              return val != null
                  ? null
                  : 'APPLICATION_MOBILE_CAPTAINVET_23'
                      .tr('Choisissez un animal');
            },
            items: inheritedState.filteredPets.map((value) {
              return DropdownMenuItem<Pet>(
                value: value,
                child: Text(
                  value.name,
                  maxLines: 2,
                ),
              );
            }).toList(),
            decoration: InputDecoration(
              labelText:
                  'APPLICATION_MOBILE_CAPTAINVET_24'.tr('Choisissez un animal'),
            ),
          ),

          const SizedBox(height: 25.0),

          /// pick a rdv type filtered by species
          Visibility(
            visible: animalValue != null,
            child: DropdownButtonFormField<String>(
              value: rdvTypeValue.isNotEmpty ? rdvTypeValue : null,
              icon: const Icon(Icons.add),
              iconSize: 18,
              elevation: 16,
              style: const TextStyle(color: Colors.black),
              onChanged: animalValue != null
                  ? (String? newValue) {
                      if (newValue != null) {
                        inheritedState.addData('rdv-types', newValue);
                        setState(() {
                          rdvTypeValue = newValue;
                          vetValue = '';
                        });
                      }
                    }
                  : null,
              validator: (val) {
                return val != null
                    ? null
                    : 'APPLICATION_MOBILE_CAPTAINVET_25'
                        .tr('Choisissez un motif');
              },
              items: animalValue != null
                  ? inheritedState.rdvTypesByVetRdvTypeAndAnimalType
                      .map((value) {
                      return DropdownMenuItem<String>(
                        value: value.id,
                        child: Text(
                          value.attributes['name'] as String? ??
                              'APPLICATION_MOBILE_FIELD_LABEL_OTHER_CHOICE'
                                  .tr('Autre'),
                        ),
                      );
                    }).toList()
                  : <DropdownMenuItem<String>>[],
              decoration: InputDecoration(
                labelText: 'APPLICATION_MOBILE_CAPTAINVET_27'
                    .tr('Choisissez un motif'),
              ),
            ),
          ),

          const SizedBox(height: 25.0),

          /// pick a vet
          Visibility(
            visible: animalValue != null && rdvTypeValue.isNotEmpty,
            child: DropdownButtonFormField<String>(
              value: vetValue.isNotEmpty ? vetValue : null,
              icon: const Icon(Icons.add),
              iconSize: 18,
              elevation: 16,
              style: const TextStyle(color: Colors.black),
              onChanged: animalValue != null && rdvTypeValue.isNotEmpty
                  ? (String? newValue) {
                      if (newValue != null) {
                        inheritedState.addData('vets', newValue);
                        setState(() {
                          vetValue = newValue;
                        });
                      }
                    }
                  : null,
              validator: (val) {
                return val != null
                    ? null
                    : 'APPLICATION_MOBILE_CAPTAINVET_28'
                        .tr('Choisissez un Vétérinaire');
              },
              items: animalValue != null && rdvTypeValue.isNotEmpty
                  ? inheritedState.vetsByAnimalTypeAndRdvType.map((value) {
                      return DropdownMenuItem<String>(
                        value: value.id,
                        child: Text(
                          '${value.attributes['firstName']} ${value.attributes['lastName']}',
                        ),
                      );
                    }).toList()
                  : <DropdownMenuItem<String>>[],
              decoration: InputDecoration(
                labelText: 'APPLICATION_MOBILE_CAPTAINVET_29'
                    .tr('Choisissez un Vétérinaire'),
              ),
            ),
          ),

          const SizedBox(height: 25.0),

          /// validation to second step
          Visibility(
            visible: animalValue != null &&
                rdvTypeValue.isNotEmpty &&
                vetValue.isNotEmpty,
            child: ElevatedButton(
              onPressed: () {
                FormState? form = formKey.currentState;
                if (form != null && form.validate()) {
                  form.save();

                  /// move to second step
                  inheritedState.next();
                }
              },
              child: Text('APPLICATION_MOBILE_CAPTAINVET_30'.tr('Continuer')),
            ),
          ),
        ],
      ),
    );
  }
}
