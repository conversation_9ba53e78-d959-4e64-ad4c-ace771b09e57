import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Api/captainvet_api.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/captainvet/cv_appointment.dart';

import 'cv_login_form.dart';

class CVPortal extends StatefulWidget {
  final MBService? clinic;

  const CVPortal({Key? key, this.clinic}) : super(key: key);

  @override
  _CVPortalState createState() => _CVPortalState();
}

class _CVPortalState extends State<CVPortal> {
  late bool containsCaptainVetToken;
  List<MBService> captainvetServices = <MBService>[];
  late MBService? selectedCaptainvetService;

  @override
  void initState() {
    super.initState();
    containsCaptainVetToken = Data().get().owner.captainvetToken != null;
    if (widget.clinic == null) {
      captainvetServices = Data()
          .get()
          .services
          .where((service) => service.captainvetId != null)
          .toList();
      if (captainvetServices.length == 1) {
        selectedCaptainvetService = captainvetServices.first;
      }
    } else {
      selectedCaptainvetService = widget.clinic;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: MBAppBar(
          title:
              Text('APPLICATION_MOBILE_CAPTAINVET_00'.tr('Portail CaptainVet')),
        ),
        body: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Image.asset('assets/images/captainvet_black.png'),
              ),
              selectedCaptainvetService == null
                  ? Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 25.0),
                          child: Text(
                              'APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_SERVICE'
                                  .tr()),
                        ),
                        ListView.builder(
                          itemCount: captainvetServices.length,
                          shrinkWrap: true,
                          itemBuilder: (BuildContext context, int index) {
                            MBService captainvetService =
                                captainvetServices[index];
                            return ListTile(
                              leading: SizedBox(
                                width: MediaQuery.of(context).size.width / 6,
                                child: CachedNetworkImage(
                                  imageUrl: captainvetService.mainImageUrl,
                                  fit: BoxFit.cover,
                                  errorWidget: (context, url, error) =>
                                      const Icon(Icons.error),
                                ),
                              ),
                              title: Text(captainvetService.name!),
                              onTap: () {
                                setState(() {
                                  selectedCaptainvetService = captainvetService;
                                });
                              },
                            );
                          },
                        ),
                      ],
                    )
                  : const SizedBox.shrink(),

              /// take appointment
              Visibility(
                visible: containsCaptainVetToken &&
                    selectedCaptainvetService != null,
                child: ElevatedButton(
                  onPressed: () {
                    Tools().navigatorPush(
                        CVAppointment(clinic: selectedCaptainvetService!));
                  },
                  child: Text('APPLICATION_MOBILE_CAPTAINVET_01'
                      .tr('Prendre un Rendez-vous')),
                ),
              ),
              Visibility(
                visible: !containsCaptainVetToken &&
                    selectedCaptainvetService != null,
                child: Column(
                  children: [
                    Text('APPLICATION_MOBILE_CAPTAINVET_02'
                        .tr('Première connexion')),
                    const SizedBox(height: 15.0),
                    Builder(builder: (ctx) {
                      return ElevatedButton(
                        onPressed: () {
                          _createCaptainVetUser(ctx);
                        },
                        child: Text('APPLICATION_MOBILE_CAPTAINVET_03'
                            .tr('Je crée mon compte')),
                      );
                    }),
                    ElevatedButton(
                      onPressed: () async {
                        bool result = await _dialogAction(context);
                        if (result) {
                          setState(() {
                            containsCaptainVetToken =
                                Data().get().owner.captainvetToken != null;
                          });
                        } else {
                          Tools().common.showMessage(
                                context,
                                'APPLICATION_MOBILE_CAPTAINVET_04'.tr(
                                    'la connexion avec captainvet a échouée'),
                              );
                        }
                      },
                      child: Text('APPLICATION_MOBILE_CAPTAINVET_05'
                          .tr("J'ai déjà un compte")),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool> _dialogAction(BuildContext context) async {
    bool? result = await showDialog<bool>(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext ctx) {
        return AlertDialog(
          title: Text(
            'APPLICATION_MOBILE_CAPTAINVET_06'
                .tr('vos identifiants captainvet'),
          ),
          content: const CVLoginForm(),
        );
      },
    );

    return result ?? false;
  }

  Future<void> _createCaptainVetUser(BuildContext context) async {
    return await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext ctx) {
        return AlertDialog(
          title: Text(
            'APPLICATION_MOBILE_CAPTAINVET_07'
                .tr('Nous créons pour vous un utilisateur de captain vet'),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                'APPLICATION_MOBILE_CAPTAINVET_08'
                    .tr('à partir des données myBuddy.'),
              ),
              Text(
                'APPLICATION_MOBILE_CAPTAINVET_09'.tr('Confirmez la création.'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () async {
                bool? result = await CaptainVetApi()
                    .createCaptainVetUser(ctx)
                    .catchError((error) {
                  Tools().message.showMessage(context, error.toString());
                });
                if (result != null) {
                  await Tools().navigatorPush(CVAppointment(
                    clinic: selectedCaptainvetService!,
                  ));
                } else {
                  Tools().navigatorPop();
                }
              },
              child: Text('APPLICATION_MOBILE_CAPTAINVET_11'.tr('Créer')),
            ),
            TextButton(
              onPressed: () async {
                Tools().navigatorPop();
              },
              child: Text('APPLICATION_MOBILE_CAPTAINVET_12'.tr('Annuler')),
            ),
          ],
        );
      },
    );
  }
}
