import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/services_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/templates/service/service_page.dart';

class WidgetServiceList extends StatelessWidget {
  const WidgetServiceList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<ServicesBloc>(context);
    return StreamBuilder<List<MBService>>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        List<MBService> services;
        if (!snapshot.hasData) return Container();
        services = snapshot.data!;
        return ListView.separated(
          itemBuilder: (context, position) {
            return GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  Tools().navigatorPush(
                    ServicePage(clinic: services[position]),
                  );
                },
                child: _serviceItem(services[position]));
          },
          separatorBuilder: (BuildContext context, int index) => const Divider(),
          itemCount: services.length,
        );
      },
    );
  }

  Widget _serviceItem(MBService service) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 12.0),
      child: Row(
        children: <Widget>[
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(right: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  _serviceName(service),
                  Text(
                    service.getFullAddress(),
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
          Visibility(
            visible: service.official || service.parentServiceId != null,
            child: const Icon(
              FontAwesomeIcons.userCheck,
              color: Colors.blue,
            ),
          )
        ],
      ),
    );
  }

  Widget _serviceName(MBService service) {
    bool isFavorite = (service.official == true && Data().getFavoriteClinic() != null)
        ? service.id == Data().getFavoriteClinic()?.id
        : false;
    return Row(
      children: <Widget>[
        Flexible(
          child: Text(
            service.name ?? '',
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 15.0,
              fontWeight: FontWeight.bold,
              color: isFavorite ? Colors.green : Colors.blue,
            ),
          ),
        ),
        isFavorite ? const Icon(Icons.star, color: Colors.green) : Container()
      ],
    );
  }
}
