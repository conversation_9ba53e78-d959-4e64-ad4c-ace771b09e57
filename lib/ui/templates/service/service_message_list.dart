import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/messages_bloc.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_refresh_indicator.dart';
import 'package:mybuddy/ui/templates/message/widget_message_list.dart';

class ServiceMessageList extends StatelessWidget{
  final MBService service;

  const ServiceMessageList({Key? key, required this.service}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(actions: [
        if (service.allowMessage())
          IconButton(
            onPressed: () => Tools().message.addNewMessage(context, service: service),
            icon: const Icon(
              FontAwesomeIcons.plus,
              size: 16.0,
            ),
          ),
      ],),
        body: MBRefreshIndicator(
      child: BlocProvider<MessagesBloc>(
        bloc: MessagesBloc(service: service),
        child: const WidgetMessageList(),
      ),),
    );
  }

}