import 'dart:async';

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_api_service.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/service_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/geolocation.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/emergency.dart';
import 'package:mybuddy/models/opening.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_dynamic_image_widget.dart';
import 'package:mybuddy/ui/components/mb_list_tile.dart';
import 'package:mybuddy/ui/components/mb_speed_dial_child.dart';
import 'package:mybuddy/ui/templates/service/add_edit_service.dart';
import 'package:mybuddy/ui/templates/service/service_message_list.dart';
import 'package:mybuddy/ui/templates/service/services_page.dart';
import 'package:mybuddy/ui/templates/staff/staff_list_page.dart';
import 'package:share_plus/share_plus.dart';

class WidgetServicePage extends StatelessWidget {
  const WidgetServicePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<ServiceBloc>(context);
    return StreamBuilder<MBService?>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        return snapshot.hasData
            ? serviceWidget(context, snapshot.data!)
            : const SizedBox();
      },
    );
  }

  Widget serviceWidget(BuildContext context, MBService service) {
    List<MBSpeedDialChild> floatingActionButtons =
        _floatingActionButtons(context, service);
    return Scaffold(
      floatingActionButton: MBSpeedDial(
        heroTag: 'service-speed-dial',
        children: floatingActionButtons,
      ),
      body: CustomScrollView(
        slivers: <Widget>[
          ///First sliver is the App Bar
          SliverAppBar(
            ///Properties of app bar
            backgroundColor: Colors.blue,
            leading: BackButton(
              color: service.getImageId() != null
                  ? Colors.grey[600]
                  : Colors.white,
              onPressed: () => Tools().navigatorPop(),
            ),
            floating: false,
            pinned: true,
            expandedHeight: service.getImageId() == null ? 0 : 295,

            ///Properties of the App Bar when it is expanded
            flexibleSpace: FlexibleSpaceBar(
              centerTitle: true,
              title: SizedBox(
                width: MediaQuery.of(context).size.width * 0.66,
                child: Text(
                  service.name ?? '',
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13.0,
                    fontWeight: FontWeight.bold,
                    shadows: <Shadow>[
                      Shadow(
                        offset: Offset(.1, 0.1),
                        blurRadius: 2.0,
                        color: Color.fromARGB(120, 0, 0, 0),
                      ),
                    ],
                  ),
                ),
              ),
              background: SizedBox(
                height: 295,
                width: MediaQuery.of(context).size.width,
                child: DynamicImageWidget(service.getAllMBImages()),
              ),
            ),
          ),
          SliverList(
            ///Lazy building of list
            delegate: SliverChildListDelegate(
              [
                MBListTile(
                  iconData: null,
                  text: service.description,
                  color: Colors.black,
                ),
                MBListTile(
                  iconData: FontAwesomeIcons.phone,
                  text: service.phone,
                  color: Colors.blue,
                  onPressed: () async {
                    String url =
                        'tel:' + (service.phone?.replaceAll(' ', '') ?? '');
                    Tools().common.launchURL(url, context);
                  },
                ),
                MBListTile(
                  iconData: FontAwesomeIcons.phone,
                  text: service.phoneEmergency,
                  color: Colors.blue,
                  iconColor: Colors.red,
                  onPressed: () async {
                    String url = 'tel:' +
                        (service.phoneEmergency?.replaceAll(' ', '') ?? '');
                    Tools().common.launchURL(url, context);
                  },
                ),
                MBListTile(
                  iconData: FontAwesomeIcons.fax,
                  text: service.fax,
                  color: Colors.blue,
                ),
                MBListTile(
                  iconData: FontAwesomeIcons.envelope,
                  text: service.email,
                  color: Colors.blue,
                  onPressed: service.email != null
                      ? () async {
                          String url = 'mailto:' + service.email!.trim();
                          Tools().common.launchURL(url, context);
                        }
                      : null,
                ),
                MBListTile(
                  iconData: FontAwesomeIcons.mapMarkerAlt, //Icons.location_on,
                  text: service.getFullAddress(),
                  color: Colors.blue,
                  onPressed: () async {
                    String url =
                        GeoLocation.getMapLink(service.getFullAddress()) ?? '';
                    Tools().common.launchURL(url, context);
                  },
                ),
                MBListTile(
                  iconData: FontAwesomeIcons.externalLinkAlt,
                  text: service.website,
                  color: Colors.blue,
                  onPressed: () async {
                    Tools().common.launchURL(service.website ?? '', context);
                  },
                ),
                MBListTile(
                  visible: service.googlePlaceId.trim() != '',
                  iconData: FontAwesomeIcons.starHalfAlt,
                  text: 'APPLICATION_MOBILE_LABEL_GOOGLE_RANK'.tr(),
                  color: Colors.blue,
                  onPressed: () async {
                    String url =
                        GeoLocation.getGooglePlaceLink(service.googlePlaceId) ??
                            '';
                    Tools().common.launchURL(url, context);
                  },
                ),
                MBListTile(
                  visible: service.staffs.isNotEmpty,
                  iconData: FontAwesomeIcons.users,
                  text: 'APPLICATION_MOBILE_LABEL_SERVICE_STAFF'.tr(),
                  color: Colors.blue,
                  onPressed: () {
                    Tools().navigatorPush(
                      StaffListPage(
                        clinic: service,
                      ),
                    );
                  },
                ),
                MBListTile(
                  visible:
                      service.services != null && service.services!.isNotEmpty,
                  iconData: FontAwesomeIcons.networkWired,
                  text: 'partners'.tr(),
                  color: Colors.blue,
                  onPressed: () {
                    Tools().navigatorPush(
                      ServicesPage(
                        serviceParent: service,
                      ),
                    );
                  },
                ),
                _timetableWidget(context, service)
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _deleteAction(BuildContext context, MBService service) async {
    MBResponse response =
        await MbApiService().removeClinicRequest(context, service);

    if (response.success) {
      Tools().navigatorPop();
    }
  }

  List<MBSpeedDialChild> _floatingActionButtons(
      BuildContext context, MBService service) {
    List<MBSpeedDialChild> floatingActionButtons = [];

    if (service.serviceTypeId == 1 || !service.official) {
      floatingActionButtons.add(
        MBSpeedDialChild(
          iconData: Icons.delete,
          backgroundColor: Theme.of(context).errorColor,
          onTap: () => _deleteAction(context, service),
        ),
      );
    }

    if (!service.official) {
      floatingActionButtons.add(
        MBSpeedDialChild(
          iconData: Icons.edit,
          backgroundColor: Colors.orangeAccent,
          onTap: () {
            Tools().navigatorPush(
              AddEditServiceForm(
                service: service,
              ),
            );
          },
        ),
      );
    }
    floatingActionButtons.add(
      MBSpeedDialChild(
        iconData: Icons.share,
        backgroundColor: Colors.orangeAccent,
        onTap: () {
          List<String> shareMessage = [];
          if (service.name != null && service.name != '') {
            shareMessage.add(service.name!);
          }
          if (service.getFullAddress() != '') {
            shareMessage.add(service.getFullAddress());
          }
          if (service.phone != null && service.phone != '') {
            shareMessage.add(service.phone!);
          }
          if (service.website != null && service.website != '') {
            shareMessage.add(service.website!);
          }
          Share.share(shareMessage.join(' - '));
        },
      ),
    );

    if (service.canMakeAppointment()) {
      floatingActionButtons.add(
        MBSpeedDialChild(
          iconData: FontAwesomeIcons.calendarAlt,
          backgroundColor: Theme.of(context).colorScheme.secondary,
          onTap: () => Tools()
              .appointment
              .appointmentAction(service: service, context: context),
        ),
      );
    }

    if (service.canMakeOrder()) {
      floatingActionButtons.add(
        MBSpeedDialChild(
          iconData: FontAwesomeIcons.shoppingCart,
          backgroundColor: Theme.of(context).colorScheme.secondary,
          onTap: () =>
              Tools().order.orderAction(service: service, context: context),
        ),
      );
    }

    if (service.official &&
        (service.allowMessage() ||
            Data().get().getMessagesGrouped(service: service).isNotEmpty)) {
      floatingActionButtons.add(
        MBSpeedDialChild(
          iconData: FontAwesomeIcons.envelope,
          backgroundColor: Theme.of(context).colorScheme.secondary,
          onTap: () {
            Tools().navigatorPush(ServiceMessageList(service: service));
          },
        ),
      );
    }

    Owner owner = Data().get().owner;
    if (owner.favoriteClinicId != service.id) {
      floatingActionButtons.add(
        MBSpeedDialChild(
            iconData: FontAwesomeIcons.solidStar,
            backgroundColor: Colors.green,
            onTap: () async {
              if (owner.favoriteClinicId == null ||
                  owner.favoriteClinicId != service.id) {
                owner.favoriteClinicId = service.id;
                MBResponse response =
                    await MbApiOwner().updateUserRequest(context);
                if (response.success) {
                  CommonTools().showMessage(
                    context,
                    'APPLICATION_MOBILE_MESSAGE_SERVICE_UPDATE_FAVORITE'.tr(
                      '%name% is now your favorite service',
                      {'%name%': service.name?.truncateWithEllipsis() ?? 'This'},
                    ),
                  );
                  ///to refresh news
                  unawaited(MbApiOwner().refreshLoginRequest());
                }
              }
            }),
      );
    }
    return floatingActionButtons;
  }

  Widget _timetableWidget(BuildContext context, MBService service) {
    List<Widget> timeTable = [];

    if (service.openings != null && service.openings!.isNotEmpty) {
      for (Opening o in service.openings!) {
        timeTable.add(
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 1, 90, 1),
            child: Row(
              children: <Widget>[
                Text(
                  o.getDayCode().tr(),
                ),
                Expanded(
                  child: Text(
                    o.toFormattedTimeString(context),
                    textAlign: TextAlign.end,
                  ),
                )
              ],
            ),
          ),
        );
      }
    }
    if (service.emergency != null) {
      Emergency emergency = service.emergency!;
      List<String> strList = [];
      if (emergency.nights) {
        strList.add('APPLICATION_MOBILE_LABEL_EMERGENCY_NIGHTS'.tr());
      }
      if (emergency.sundays) {
        strList.add('APPLICATION_MOBILE_LABEL_EMERGENCY_SUNDAYS'.tr());
      }
      if (emergency.holidays) {
        strList.add('APPLICATION_MOBILE_LABEL_EMERGENCY_HOLIDAYS'.tr());
      }
      if (strList.isNotEmpty) {
        timeTable.add(const Divider());
        timeTable.add(
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 1, 90, 1),
            child: Text(
                '${'APPLICATION_MOBILE_LABEL_EMERGENCY_SERVICE'.tr()}: ${strList.join(', ')}'),
          ),
        );
      }
    }

    if (timeTable.isEmpty) {
      return Container();
    }

    return Container(
      color: Colors.grey[300],
      child: Column(
        children: <Widget>[
          MBListTile(
            iconData: FontAwesomeIcons.clock,
            text: 'APPLICATION_MOBILE_LABEL_SERVICE_OPENING_HOURS'.tr(),
            color: Colors.black,
            bgColor: Colors.grey[300],
          ),
          Column(
            children: timeTable,
          ),
          Container(height: 10)
        ],
      ),
    );
  }
}
