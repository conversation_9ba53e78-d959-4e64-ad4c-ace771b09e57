import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/service_bloc.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/templates/service/widget_service.dart';

class ServicePage extends StatelessWidget {
  final MBService clinic;

  const ServicePage({Key? key, required this.clinic}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ServiceBloc>(
      bloc: ServiceBloc(serviceId: clinic.id),
      child: const WidgetServicePage(),
    );
  }
}
