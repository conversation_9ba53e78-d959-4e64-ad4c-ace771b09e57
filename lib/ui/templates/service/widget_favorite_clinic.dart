import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/fav_service_bloc.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_clinic_card_widget.dart';
import 'package:mybuddy/ui/templates/service/service_page.dart';

class WidgetFavoriteClinic extends StatelessWidget {
  const WidgetFavoriteClinic({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<FavServiceBloc>(context);
    return StreamBuilder<MBService?>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        return snapshot.hasData
            ? _clinicWidget(context, snapshot.data!)
            : Container();
      },
    );
  }

  Widget clinicImageWidget(BuildContext context, MBService service) {
    if (service.hasImage()) {
      return ClipRRect(
        borderRadius:
            const BorderRadius.vertical(bottom: Radius.circular(10.0)),
        child: Image(
          image:
              CachedNetworkImageProvider(service.getFavoriteImageUrl() ?? ''),
          errorBuilder: (BuildContext context, Object exception,
                  StackTrace? stackTrace) =>
              const Icon(Icons.error),
        ),
      );
    } else if (service.official) {
      // if official clinic, can't add photo
      return const SizedBox(height: 50);
    } else {
      // if not an official clinic, can add photo
      return Container(
        height: 100,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            // Where the linear gradient begins and ends
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            // Add one stop for each color. Stops should increase from 0 to 1
            stops: [0.1, 0.3, 0.7, 0.9],
            colors: [
              // Colors are easy thanks to Flutter's Colors class.
              Color.fromARGB(240, 50, 96, 152),
              Color.fromARGB(255, 50, 96, 152),
              Color.fromARGB(255, 0, 0, 152),
              Color.fromARGB(240, 50, 96, 152),
            ],
          ),
//                              color: Color.fromARGB(255, 50, 96, 152),
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(10.0),
          ),
        ),
        child: IconButton(
          icon: const Icon(Icons.add_a_photo),
          color: Colors.grey,
          onPressed: () {},
        ),
      );
    }
  }

  Widget _clinicWidget(BuildContext context, MBService service) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Tools().navigatorPush(
          ServicePage(clinic: service),
        );
      },
      child: Wrap(
        children: <Widget>[
          Stack(
            children: <Widget>[
              Container(height: 130),
              Wrap(
                children: <Widget>[
                  Column(mainAxisSize: MainAxisSize.max, children: <Widget>[
                    clinicImageWidget(context, service),
                    Container(height: 70)
                  ])
                ],
              ),
              Positioned(
                bottom: 45,
                right: 30,
                left: 30,
                child: MBClinicCardWidget(service: service),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class ClinicImage extends StatelessWidget {
  const ClinicImage({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<FavServiceBloc>(context);

    return StreamBuilder<MBService?>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.expand();
        }

        MBService service = snapshot.data!;

        if (service.hasImage()) {
          return Image(
            fit: BoxFit.cover,
            image: CachedNetworkImageProvider(service.getFavoriteImageUrl()!),
            errorBuilder: (BuildContext context, Object exception,
                    StackTrace? stackTrace) =>
                const Icon(Icons.error),
          );
        } else if (service.official) {
          // if official clinic, can't add photo
          return const SizedBox(height: 50);
        } else {
          // if not an official clinic, can add photo
          return Container(
            height: 100,
            width: MediaQuery.of(context).size.width,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                // Where the linear gradient begins and ends
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                // Add one stop for each color. Stops should increase from 0 to 1
                stops: [0.1, 0.3, 0.7, 0.9],
                colors: [
                  // Colors are easy thanks to Flutter's Colors class.
                  Color.fromARGB(240, 50, 96, 152),
                  Color.fromARGB(255, 50, 96, 152),
                  Color.fromARGB(255, 0, 0, 152),
                  Color.fromARGB(240, 50, 96, 152),
                ],
              ),
//                              color: Color.fromARGB(255, 50, 96, 152),
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(10.0),
              ),
            ),
            child: IconButton(
              icon: const Icon(Icons.add_a_photo),
              color: Colors.grey,
              onPressed: () {},
            ),
          );
        }
      },
    );
  }
}

class ClinicCard extends StatelessWidget {
  const ClinicCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<FavServiceBloc>(context);

    return StreamBuilder<MBService?>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox.expand();

        MBService service = snapshot.data!;

        return GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            Tools().navigatorPush(
              ServicePage(clinic: service),
            );
          },
          child: MBClinicCardWidget(service: service),
        );
      },
    );
  }
}
