import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/Api/mb_api_service.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';

class AddEditServiceForm extends StatefulWidget {
  final String? title;
  final MBService? service;
  final bool isClinic;

  const AddEditServiceForm({Key? key, this.service, this.title, this.isClinic = true}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _AddEditServiceFormState();
}

class _AddEditServiceFormState extends State<AddEditServiceForm> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool init = false;
  late MBService _service;
  late Country? _country;
  late List<Country> countries;
  late bool isNew;

  @override
  Widget build(BuildContext context) {
    String _title = widget.title ?? _service.name ?? 'rien';
    return Scaffold(
      appBar: MBAppBar(
        title: Text(_title, maxLines: 2),
      ),
      body: SafeArea(
        top: false,
        bottom: false,
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.disabled,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              children: <Widget>[
                const SizedBox(height: 15.0,),
                TextFormField(
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_SERVICE_NAME'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_SERVICE_NAME'.tr() + '*',
                  ),
                  initialValue: _service.name,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  validator: (val) => val == null || val.isEmpty
                      ? 'APPLICATION_MOBILE_LABEL_FIELDS_REQUIRED'.tr() : null,
                  onSaved: (val) => _service.name = val,
                ),
                const SizedBox(height: 15.0,),
                TextFormField(
                  maxLines: 4,
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_DESCRIPTION'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_DESCRIPTION'.tr() + '*',
                  ),
                  initialValue: _service.description,
                  inputFormatters: [LengthLimitingTextInputFormatter(4096)],
                  validator: (val) => val == null || val.isEmpty
                      ? 'APPLICATION_MOBILE_LABEL_FIELDS_REQUIRED'.tr() : null,
                  onSaved: (val) => _service.description = val,
                ),
                const SizedBox(height: 15.0,),
                TextFormField(
                  keyboardType: TextInputType.phone,
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_PHONE_NUMBER'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_PHONE_NUMBER'.tr() + '*',
                  ),
                  initialValue: _service.phone,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  validator: (val) => val == null || val.isEmpty
                      ? 'APPLICATION_MOBILE_LABEL_FIELDS_REQUIRED'.tr() : null,
                  onSaved: (val) => _service.phone = val,
                ),
                const SizedBox(height: 15.0,),
                TextFormField(
                  keyboardType: TextInputType.streetAddress,
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_ADDRESS'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_ADDRESS'.tr() + '*',
                  ),
                  initialValue: _service.address,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  validator: (val) =>  val == null || val.isEmpty
                      ? 'APPLICATION_MOBILE_LABEL_FIELDS_REQUIRED'.tr() : null,
                  onSaved: (val) => _service.address = val,
                ),
                const SizedBox(height: 15.0,),
                TextFormField(
                  keyboardType: TextInputType.streetAddress,
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_ZIPCODE'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_ZIPCODE'.tr() + '*',
                  ),
                  initialValue: _service.zipCode,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  validator: (val) => val == null || val.isValidPostalCode(_service.countryId) ? null : 'APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_ZIPCODE'.tr() ,
                  onSaved: (val) => _service.zipCode = val,
                ),
                const SizedBox(height: 15.0,),
                TextFormField(
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_CITY'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_CITY'.tr() + '*',
                  ),
                  initialValue: _service.city,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  validator: (val) =>  val == null || val.isEmpty
                      ? 'APPLICATION_MOBILE_LABEL_FIELDS_REQUIRED'.tr() : null,
                  onSaved: (val) => _service.city = val,
                ),
                const SizedBox(height: 15.0,),
                DropdownButtonFormField<Country>(
                  value: _country,
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_COUNTRY'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_COUNTRY'.tr() + '*',
                  ),
                  onChanged: (Country? newValue) {
                    setState(() {
                      if (newValue != null && newValue != _country) {
                        _country = newValue;
                        _service.countryId = _country!.id;
                      }
                    });
                  },
                  validator: (val) {
                    return val != null ? null : 'APPLICATION_MOBILE_LABEL_FIELDS_REQUIRED'.tr();
                  },
                  items: countries.map((Country country) {
                    return DropdownMenuItem<Country>(
                      value: country,
                      child: Text(country.name),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 15.0,),
                TextFormField(
                  keyboardType: TextInputType.phone,
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_EMERGENCY_PHONE_NUMBER'.tr(),
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_EMERGENCY_PHONE_NUMBER'.tr(),
                  ),
                  initialValue: _service.phoneEmergency,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  onSaved: (val) => _service.phoneEmergency = val,
                ),
                const SizedBox(height: 15.0,),
                TextFormField(
                  keyboardType: TextInputType.phone,
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_FAX'.tr(),
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_FAX'.tr(),
                  ),
                  initialValue: _service.fax,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  onSaved: (val) => _service.fax = val,
                ),
                const SizedBox(height: 15.0,),
                TextFormField(
                  keyboardType: TextInputType.emailAddress,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'.tr(),
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'.tr(),
                  ),
                  initialValue: _service.email,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  onSaved: (val) => _service.email = val,
                ),
                const SizedBox(height: 15.0,),
                TextFormField(
                  keyboardType: TextInputType.url,
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_EMAIL_WEBSITE'.tr(),
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_EMAIL_WEBSITE'.tr(),
                  ),
                  initialValue: _service.website,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  onSaved: (val) => _service.website = val,
                ),
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(vertical: 25.0),
                  child: Builder(
                    builder: (context) => ElevatedButton(
                      onPressed: () {
                        _submitForm(context);
                      },
                      child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr()),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    countries = Ref().getActiveCountries();
    if (widget.service != null) {
      isNew = false;
      _service = widget.service!.copy(); //need to copy especially if we cancel
      _country = Ref().get().getCountry(widget.service!.countryId);
    } else {
      isNew = true;
      _service = MBService();
      _country = Ref().get().getCountry(Data().get().owner.countryId);
      if (widget.isClinic) {
        _service.serviceTypeId = 1;
      }
    }
  }

  Future<void> _submitForm(BuildContext context) async {
    final FormState? form = _formKey.currentState;

    if (form == null || !form.validate()) {
      Tools().common.showValidationErrorMessage(context);
      return;
    }
    form.save();

    MBResponse response = isNew
        ? await MbApiService().addPrivateClinicRequest(context, _service)
        : await MbApiService().editPrivateClinicRequest(context, _service);

    if (response.success) {
      Tools().navigatorPop();
    }
  }
}
