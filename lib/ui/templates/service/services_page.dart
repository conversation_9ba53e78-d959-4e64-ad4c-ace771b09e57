import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/services_bloc.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/service/add_edit_service.dart';
import 'package:mybuddy/ui/templates/service/widget_service_list.dart';

class ServicesPage extends StatelessWidget {
  final bool isClinic;
  final MBService? serviceParent;

  const ServicesPage({Key? key, this.isClinic = true, this.serviceParent}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String titleCode;
    if (serviceParent != null) {
      titleCode = 'APPLICATION_MOBILE_LABEL_SERVICE';
    } else if (isClinic) {
      titleCode = 'APPLICATION_MOBILE_LABEL_MY_VETS';
    } else {
      titleCode = 'APPLICATION_MOBILE_LABEL_MY_SERVICES';
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: MBAppBar(
        title: Text(
          titleCode.tr(),
          maxLines: 2,
        ),
      ),
      body: BlocProvider<ServicesBloc>(
        bloc: ServicesBloc(isClinic, serviceParent),
        child: const WidgetServiceList(),
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: 'add-clinic',
        onPressed: () {
          if (isClinic) {
            Tools().clinic.addClinicAction(context);
          } else {
            Tools().navigatorPush(AddEditServiceForm(
              title: 'APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_SERVICE'.tr(),
              isClinic: false,
            ));
          }
        },
        child: const Icon(FontAwesomeIcons.plus),
      ),
    );
  }
}
