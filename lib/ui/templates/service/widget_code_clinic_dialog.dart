import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_service.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class WidgetCodeClinicDialog extends StatefulWidget {
  final Function? onSubmit;

  const WidgetCodeClinicDialog({Key? key, this.onSubmit}): super(key: key);

  @override
  State<StatefulWidget> createState() => _WidgetCodeClinicDialogState();
}

class _WidgetCodeClinicDialogState extends State<WidgetCodeClinicDialog> {
  final TextEditingController _textFieldController = TextEditingController();
  String? clinicName, clinicCode;
  late bool codeError;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        PinCodeTextField(
          autoFocus: true,
          appContext: context,
          pinTheme: PinTheme(
            activeFillColor: Colors.blue,
            selectedFillColor: Colors.transparent,
            inactiveFillColor: Colors.transparent,
            activeColor: Colors.transparent,
            selectedColor: Colors.blue,
            inactiveColor: Colors.red,
            shape: PinCodeFieldShape.box,
            fieldHeight: 45,
            fieldWidth: 32,
          ),
          length: 6,
          obscureText: false,
          animationType: AnimationType.fade,
          animationDuration: const Duration(milliseconds: 300),
          enableActiveFill: true,
          textStyle: const TextStyle(
            fontSize: 20,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          controller: _textFieldController,
          onCompleted: (code) async {
            MBResponse response = await MbApiService().getClinicWithCodeRequest(null, code);
            if (response.success) {
              setState(() {
                clinicName = response.body['name'];
                clinicCode = code;
              });
            } else {
              setState(() {
                codeError = true;
              });
            }
          },
          onChanged: (code) {
            if (code.length < 6) {
              setState(() {
                codeError = false;
                clinicName = null;
              });
            }
          },
        ),
        clinicName != null && clinicCode != null
            ? ElevatedButton(
                onPressed: () async {
                  //TODO MIDDLE Can't snack api message , no scaffold
                  MBResponse response = await MbApiService().addAffiliateClinicRequest(null, clinicCode!);
                  if (response.success) {
                    Tools().navigatorPop();
                  }
                },
                child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_CONNECT_TO'.tr() + clinicName!),
              )
            : Container(),
        Visibility(
          visible: codeError,
          child: Text('APPLICATION_MOBILE_LABEL_ERROR_CLINIC_CODE'.tr(),
              style: const TextStyle(color: Colors.red)),
        ),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    codeError = false;
  }
  @override
  void dispose() {
    _textFieldController.dispose();
    super.dispose();
  }
}
