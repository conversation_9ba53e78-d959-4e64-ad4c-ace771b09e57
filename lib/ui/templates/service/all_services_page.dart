
import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/clinics_and_services_bloc.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_base_home_screen.dart';
import 'widgets/list_widgets.dart';


class ClinicsAndServicesPage extends StatelessWidget {
  final bool isClinic;
  final MBService? serviceParent;

  const ClinicsAndServicesPage({Key? key, this.isClinic = true, this.serviceParent})
      : super(key: key);

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      body: HomeSliverScreen(
        automaticallyImplyLeading: true,
        title: 'APPLICATION_MOBILE_LABEL_MY_SERVICES'.tr(),
        child: BlocProvider<ClinicsAndServicesBloc>(
          bloc: ClinicsAndServicesBloc(),
          child: const ClinicsAndServicesWidget(),
        ),
      ),
    );
  }
}

class ClinicsAndServicesWidget extends StatelessWidget {
  const ClinicsAndServicesWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<ClinicsAndServicesBloc>(context);

    return StreamBuilder<List<MBService>>(
      stream: bloc.stream,
      builder: (BuildContext context, AsyncSnapshot<List<MBService>> snapshot) {
        if (!snapshot.hasData) return Container();

        List<MBService> clinics = snapshot.data!.where((c) => c.serviceTypeId == 1).toList();
        List<MBService> others = snapshot.data!.where((c) => c.serviceTypeId == 2).toList();

        return SingleChildScrollView(
          child: Column(
            children: [
              ServiceListHeaderWidget(serviceType: 1, isEmpty: clinics.isEmpty),
              ServiceListContentWidget(list: clinics, serviceType: 1),
              const Divider(indent: 5.0, endIndent: 5.0),
              ServiceListHeaderWidget(serviceType: 2, isEmpty: others.isEmpty),
              ServiceListContentWidget(list: others, serviceType: 2),
            ],
          ),
        );
      },
    );
  }
}
