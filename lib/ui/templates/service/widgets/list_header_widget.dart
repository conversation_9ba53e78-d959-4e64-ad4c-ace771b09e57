import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

import '../add_edit_service.dart';

class ServiceListHeaderWidget extends StatelessWidget {
  final int? serviceType;
  final bool isEmpty;

  const ServiceListHeaderWidget({Key? key, this.serviceType, this.isEmpty = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {

    return Container(
      height: 50.0,
      alignment: Alignment.bottomLeft,
      padding: const EdgeInsets.symmetric(horizontal: 5.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            serviceType == 1
                ? 'APPLICATION_MOBILE_LABEL_MY_VETS'.tr()
                : 'APPLICATION_MOBILE_LABEL_SERVICE'.tr(),
            style: Theme.of(context).textTheme.bodyText1,
          ),
          if (!isEmpty)
            TextButton(
              onPressed: () async {
                if (serviceType == 1) {
                  await Tools().clinic.addClinicAction(context);
                } else {
                  await Tools().navigatorPush(AddEditServiceForm(
                    title: 'APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_SERVICE'.tr(),
                    isClinic: false,
                  ));
                }
              },
              child: Text('APPLICATION_MOBILE_BUTTON_LABEL_ADD'.tr()),
            ),

          ///endif
        ],
      ),
    );
  }
}
