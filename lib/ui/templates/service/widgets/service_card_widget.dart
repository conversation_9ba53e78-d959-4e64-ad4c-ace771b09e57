import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/templates/service/widgets/list_empty_widget.dart';

import '../service_page.dart';

class ServiceCardWidget extends StatelessWidget {
  final MBService? service;
  final bool bookmarked;

  /// service card widget.
  const ServiceCardWidget({Key? key, this.service, this.bookmarked = true})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        if (service != null)
          Padding(
            padding: const EdgeInsets.fromLTRB( 10.0,0,10,5),
            child: Row(
              children: [
                MBAvatar(
                  imageUrl: service!.imageUrl,
                  backgroundColor: Theme.of(context).backgroundColor,
                  stackedChild: (service!.official ||
                              service!.parentServiceId != null) &&
                          bookmarked
                      ? Icon(
                          Icons.bookmark,
                          color:  Theme.of(context).colorScheme.secondary,
                          size: 27.0,
                        )
                      : null,
                ),
                const SizedBox(width: 10.0),
                Expanded(child: _TitleWidget(service: service!)),
              ],
            ),
          ),
        service == null
            ? const ServiceListEmptyWidget()
            : _ContentWidget(service: service!),

        ///endif
      ],
    );
  }
}

class _TitleWidget extends StatelessWidget {
  final MBService service;

  const _TitleWidget({Key? key, required this.service}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => Tools().navigatorPush(ServicePage(clinic: service)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            service.name ?? '',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.left,
            style: Theme.of(context).textTheme.bodyText1,
          ),
          Text(
            service.getFullAddress(),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.left,
            style: Theme.of(context).textTheme.subtitle1,
          ),
        ],
      ),
    );
  }
}

class _ContentWidget extends StatelessWidget {
  final MBService service;

  const _ContentWidget({Key? key, required this.service}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    /// set base size of item
    double itemSize = MediaQuery.of(context).size.width * 0.22;
    List<ServiceInfoItem> items = [
      ServiceInfoItem(
        'APPLICATION_MOBILE_ICON_CALL_TITLE'.tr('Call'),
        Icons.phone,
        () {
          if (service.phone != null) {
            String url = 'tel:' + service.phone!.replaceAll(' ', '');
            Tools().common.launchURL(url, context);
          }
        },
      ),
      if (service.canMakeAppointment())
        ServiceInfoItem(
          'APPLICATION_MOBILE_ICON_APT_TITLE'.tr('Appointment'),
          Icons.calendar_today_outlined,
          () => Tools()
              .appointment
              .appointmentAction(service: service, context: context),
        ),

      /// endif
      if (service.canMakeOrder())
        ServiceInfoItem(
          'APPLICATION_MOBILE_ICON_ORDER_TITLE'.tr('Order'),
          Icons.shopping_cart,
          () => Tools().order.orderAction(service: service, context: context),
        ),

      /// endif
      ServiceInfoItem(
        'APPLICATION_MOBILE_ICON_EMAIL_TITLE'.tr('Email'),
        Icons.email,
        () async {
          String url = 'mailto:' + service.email!.trim();
          Tools().common.launchURL(url, context);
        },
      ),
      if (service.phoneEmergency != null &&
          service.phoneEmergency!.trim() != '')
        ServiceInfoItem(
          'APPLICATION_MOBILE_ICON_EMERGENCY_TITLE'.tr('Emergency'),
          Icons.local_phone,
          () async {
            String url =
                'tel:' + service.phoneEmergency!.replaceAll(' ', '').trim();
            Tools().common.launchURL(url, context);
          },
        ),
      if (service.acceptMessage)
        ServiceInfoItem(
          'APPLICATION_MOBILE_ICON_MESSAGE_TITLE'.tr('Message'),
          Icons.message,
          () => Tools().message.addNewMessage(context, service: service),
        ),

      /// endif
    ];

    return SizedBox(
      width: double.infinity,
      height: itemSize,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        padding: const EdgeInsets.fromLTRB(20.0, 0.0, 20.0, 0.0),
        itemCount: items.length,
        itemBuilder: (context, index) {
          ServiceInfoItem item = items[index];
          return SizedBox(
            width: itemSize,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                InkWell(
                  onTap: item.onPressed,
                  child: CircleAvatar(
                    backgroundColor: Theme.of(context).backgroundColor,
                    radius: itemSize * 0.38,
                    child: Icon(
                      item.icon,
                      color: index == 4
                          ? Colors.red
                          : Theme.of(context).primaryColor,
                      size: itemSize * 0.33,
                    ),
                  ),
                ),
                Text(
                  item.label!,
                  // softWrap: true,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontSize: itemSize / 9,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class ServiceInfoItem {
  final String? label;
  final IconData icon;
  final VoidCallback onPressed;

  ServiceInfoItem(this.label, this.icon, this.onPressed);
}
