import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';

import '../service_page.dart';
import 'list_empty_widget.dart';
import 'service_card_widget.dart';

class ServiceListContentWidget extends StatelessWidget {
  final List<MBService> list;
  final int serviceType;

  const ServiceListContentWidget({Key? key, required this.list, required this.serviceType})
      : assert(serviceType == 1 || serviceType == 2), super(key: key);

  @override
  Widget build(BuildContext context) {
    if(list.isEmpty) {
      return ServiceListEmptyWidget(serviceType: serviceType);
    }
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: list.length,
      itemBuilder: (BuildContext context, int index) {
        MBService item = list[index];
        bool isFavorite = Data().getFavoriteClinic() != null
            ? item.id == Data().getFavoriteClinic()!.id
            : false;

        if (isFavorite) {
          return ServiceCardWidget(service: item);
        } else {
          return ListTile(
            leading: MBAvatar(
              imageUrl: item.imageUrl,
              size: 40.0,
              backgroundColor: Theme.of(context).colorScheme.surface,
              stackedChild: item.official || item.parentServiceId != null ? Icon(
                Icons.bookmark,
                color: Theme.of(context).colorScheme.secondary,
                size: 18.0,
              ) : null,
            ),
            title: Text(item.name ?? ''),
            subtitle: Text(item.getFullAddress()),
            onTap: () => Tools().navigatorPush(ServicePage(clinic: item)),
          );
        }
      },
      padding: const EdgeInsets.symmetric(vertical: 10.0),
    );
  }
}
