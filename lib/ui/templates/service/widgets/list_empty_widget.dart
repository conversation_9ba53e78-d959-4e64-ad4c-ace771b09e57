import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/ui/components/mb_hexa_badge_widget.dart';

import 'package:mybuddy/extensions/string_extensions.dart';

import '../add_edit_service.dart';

class ServiceListEmptyWidget extends StatelessWidget {
  final int serviceType;

  const ServiceListEmptyWidget({Key? key, this.serviceType = 1})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    String title = serviceType == 1
        ? 'APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_CLINIC'.tr()
        : 'APPLICATION_MOBILE_TEXT_SERVICE_NO_SERVICE'.tr();

    return Container(
      margin: const EdgeInsets.all(10.0),
      padding: const EdgeInsets.all(5.0),
      decoration: BoxDecoration(
        color: Theme.of(context).backgroundColor,
        borderRadius: const BorderRadius.all(Radius.circular(15.0)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ListTile(
            contentPadding: const EdgeInsets.all(10.0),
            leading: MBHexaBadge(
              size: 50.0,
              icon: FontAwesomeIcons.briefcaseMedical,
              color: Theme.of(context).colorScheme.secondary.withOpacity(0.25),
            ),
            title: Text(
              title,
              style: Theme.of(context).textTheme.bodyText1,
            ),
            subtitle: serviceType == 1
                ? Text(
                    'APPLICATION_MOBILE_LABEL_NO_CLINIC_SENTENCE'.tr(),
                    maxLines: 2,
                    style: Theme.of(context).textTheme.subtitle1,
                  )
                : null,
          ),
          Container(
            margin: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 5.0),
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () async {
                if (serviceType == 1) {
                  await Tools().clinic.addClinicAction(context);
                } else {
                  await Tools().navigatorPush(AddEditServiceForm(
                    title: 'APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_SERVICE'.tr(),
                    isClinic: false,
                  ));
                }
              },
              child: Text('APPLICATION_MOBILE_BUTTON_LABEL_ADD'.tr()),
            ),
          ),
        ],
      ),
    );
  }
}
