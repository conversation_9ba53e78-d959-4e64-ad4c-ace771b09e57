
export 'community_screen.dart';
export 'message_screen.dart';
export 'pet_screen.dart';
export 'read_screen.dart';
export 'workout_screen.dart';
export 'challenge_screen.dart';



// class HomeScreen extends StatelessWidget {
//
//   const HomeScreen({Key key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     final bloc = BlocProvider.of<GenericBloc>(context);
//
//     return StreamBuilder<LoginData>(
//       stream: bloc.stream,
//       builder: (context, snapshot) {
//         if (!snapshot.hasData) return Container();
//         LoginData thisData = snapshot.data;
//         MBService favoriteClinic = Data().get().getService(thisData.owner.favoriteClinicId);
//
//         return HomeSliverScreen(
//           actions: [
//             if(AppConfig.of(context).isWoofTrax)
//               IconButton(
//                 onPressed: () async {
//                   await Tools().navigatorPush(MapBoxGlPage());
//                 },
//                 icon: Icon(
//                   FontAwesomeIcons.walking,
//                   size: 16.0,
//                 ),
//               ),
//
//             /// endif
//           ],
//           expandedHeight: 0.33,
//           // background: Image.network(favoriteClinic.imageUrl, fit: BoxFit.cover,),
//           background: ClipRRect(
//             borderRadius: BorderRadius.vertical(bottom: Radius.circular(10.0)),
//             child: Image(
//                 image: CachedNetworkImageProvider(favoriteClinic.getFavoriteImageUrl()),
//                 errorBuilder: (BuildContext context, Object exception, StackTrace stackTrace) {
//                   return Icon(Icons.error);
//                 }
//                 ),
//           ),
//           child: SingleChildScrollView(
//             child: Column(
//               children: [
//                 BlocProvider(
//                   bloc: FavServiceBloc(),
//                   child: ClinicHomeWidget(),
//                 ),
//                 ReminderHomeWidget(),
//                 BlocProvider<AnnouncementsBloc>(
//                   bloc: AnnouncementsBloc(),
//                   child: AnnouncementHomeWidget(),
//                 ),
//               ],
//             ),
//           ),
//         );
//       }
//     );
//   }
// }

