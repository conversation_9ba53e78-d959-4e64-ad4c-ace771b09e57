import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:signal_strength_indicator/signal_strength_indicator.dart';
import '../../../../controllers/gps_indication_controller.dart';

class GPSIndicator extends StatelessWidget {
  GPSIndicator({Key? key}) : super(key: key);
  final GpsIndicationController controller = GpsIndicationController.of();

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 5,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(14.0)),
      child: Padding(
        padding:
        const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        child: Row(
          children: [
            Obx(
                  () => SignalStrengthIndicator.bars(
                value: controller.gpsSignalValue.value,
                size: 20,
                barCount: 4,
                spacing: 0.1,
                radius: const Radius.circular(4.0),
                inactiveColor: controller.gpsSignalColor.value,
                activeColor: controller.gpsSignalColor.value,
              ),
            ),
            const SizedBox(
              width: 8,
            ),
            Obx(
                  () => RichText(
                  text: TextSpan(
                      text: "GPS: ",
                      style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black),
                      children: [
                        TextSpan(
                            text: controller.gpsSignalName.value,
                            style: TextStyle(
                                color:
                                controller.gpsSignalColor.value))
                      ])),
            )
          ],
        ),
      ),
    );
  }
}
