import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/generic_bloc.dart';
import 'package:mybuddy/controllers/weekly_stats_controller.dart';
import 'package:mybuddy/models/login_data.dart';
import 'package:mybuddy/ui/components/mb_base_home_screen.dart';
import 'package:mybuddy/ui/templates/home/<USER>/empty_weekly_goals.dart';
import 'package:mybuddy/ui/templates/home/<USER>/home_summary_widget.dart';
import 'package:mybuddy/ui/templates/home/<USER>/weekly_stats_widget.dart';

import '../../../../controllers/home_summary_controller.dart';
import '../../../../controllers/teams_controller.dart';
import '../widgets/offline_walk_sync_widget.dart';
import '../widgets/start_walk_card_widget.dart';

class FeedScreen extends StatelessWidget {
  FeedScreen({Key? key}) : super(key: key);

  final TeamsController teamController = Get.put(TeamsController());
  final WeeklyStatsController statsController = WeeklyStatsController.of();

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<GenericBloc>(context);

    return HomeSliverScreen(
      expandedHeight: 0.0,
      centerWidget: Align(
        alignment: Alignment.bottomCenter,
        child: Padding(
          padding: const EdgeInsets.only(bottom: 10.0),
          child: Image.asset(
            "assets/images/wooftrax_color.png",
            height: 40,
          ),
        ),
      ),
      backgroundColor: const Color(0xfffbfbfb),
      child: RefreshIndicator(
        displacement: 100.0,
        onRefresh: () async {
          if (await Tools().common.isOnline(alertContext: context)) {
            statsController.refreshHomeStats();
            HomeSummaryController.of().fetchHomeSummary();
            await MbApiOwner().refreshLoginRequest();
          }
        },
        child: MediaQuery.removePadding(
          context: context,
          removeTop: true,
          child: StreamBuilder(
            stream: bloc.stream,
            builder: (BuildContext context, AsyncSnapshot<LoginData> snapshot) {
              LoginData? data = snapshot.data;
              statsController.checkIfGoalIsSet();

              return ListView(
                shrinkWrap: true,
                children: [
                  OfflineWalkSyncWidget(),
                  HomeSummaryWidget(),
                  const SizedBox(height: 5),
                  StartWalkCard(owner: data?.owner, stats: data?.stats),
                  const SizedBox(height: 5),
                  Obx(() {
                    if (statsController.isGoalSet) {
                      return WeeklyStats(stats: data?.stats);
                    } else {
                      return const EmptyWeeklyGoals();
                    }
                  }),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
