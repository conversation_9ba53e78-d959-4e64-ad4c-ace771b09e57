import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:sticky_headers/sticky_headers/widget.dart';
import '../../../../../controllers/charity_point_controller.dart';
import '../../../../components/pagination_widget.dart';
import 'charity_points_week_header.dart';

class CharityPointWeekList extends StatelessWidget {
  CharityPointWeekList({Key? key}) : super(key: key);

  CharityPointController controller = Get.find();

  void openGoalAchievedMessage(index, innerIndex) {
    if (controller.detailMessageIndex.value == index &&
        controller.detailMessageInnerIndex.value == innerIndex) {
      controller.detailMessageIndex.value = -1;
      controller.detailMessageInnerIndex.value = -1;
      return;
    }
    controller.detailMessageIndex.value = index;
    controller.detailMessageInnerIndex.value = innerIndex;
  }

  @override
  Widget build(BuildContext context) {
    return PaginationWidget(
      child: (context, index) {
        return _weekList(index);
      },
      controller: controller,
    );
  }

  Widget _weekList(int index) {
    return StickyHeader(
      header: CharityPointsWeekHeader(pointData: controller.dataList[index]),
      content: ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: controller.dataList[index].days.length,
          itemBuilder: (context, innerIndex) {
            return _dayRowWidget(index, innerIndex);
          }),
    );
  }

  Widget _dayRowWidget(int index, int innerIndex) {
    return Obx(
      () => Column(
        children: [
          Container(
            padding: const EdgeInsets.all(15),
            color: innerIndex % 2 != 0 ? const Color(0xffF4F4F4) : null,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    flex: 2,
                    child: Text(
                      controller.dataList[index].days[innerIndex].day,
                      style: const TextStyle(
                          fontSize: 14, fontWeight: FontWeight.w400),
                    )),
                Expanded(
                    flex: 2,
                    child: RichText(
                        text: TextSpan(
                            text:
                                "${controller.dataList[index].days[innerIndex].walks}",
                            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Color(0xff282828)),
                            children: const [
                          TextSpan(
                              text: " Walks",
                              style: TextStyle(fontWeight: FontWeight.w400))
                        ]))),
                Expanded(
                    flex: 3,
                    child: RichText(
                        text: TextSpan(
                            text: double.parse(
                                    "${controller.dataList[index].days[innerIndex].distance}")
                                .toStringAsFixed(2),
                            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Color(0xff282828)),
                            children: const [
                          TextSpan(
                              text: " Miles",
                              style: TextStyle(fontWeight: FontWeight.w400))
                        ]))),
                Expanded(
                    flex: 4,
                    child: RichText(
                        text: TextSpan(
                            text: double.parse(
                                    "${controller.dataList[index].days[innerIndex].charityPoints}")
                                .toInt()
                                .toString(),
                            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Color(0xff282828)),
                            children: const [
                          TextSpan(
                              text: " Charity Points",
                              style: TextStyle(fontWeight: FontWeight.w400))
                        ]))),
                if (controller.dataList[index].days[innerIndex].goalAcheived)
                  GestureDetector(
                      onTap: () {
                        openGoalAchievedMessage(index, innerIndex);
                      },
                      child: Image.asset(
                        "assets/icon/goal_badge.png",
                        width: 22,
                      ))
                else
                  const SizedBox(
                    width: 22,
                  ),
              ],
            ),
          ),
          _goalAchievedMessage(index, innerIndex)
        ],
      ),
    );
  }

  Widget _goalAchievedMessage(int index, int innerIndex) {
    if (controller.detailMessageIndex.value == index &&
        controller.detailMessageInnerIndex.value == innerIndex) {
      return Container(
        color: const Color(0xffDEF9ED),
        height: 45,
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 15),
        alignment: Alignment.centerLeft,
        child: const Text(
          "100% charity points achieved.",
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
        ),
      );
    }

    return const SizedBox.shrink();
  }
}
