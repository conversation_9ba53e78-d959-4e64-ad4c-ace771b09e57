import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/controllers/charity_point_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/home/<USER>/charityPoints/charity_points_weeks_list.dart';
import '../../../../components/mb_no_record_found.dart';

class CharityPointScreen extends StatefulWidget {
  const CharityPointScreen({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _CharityPointScreenState();
}

class _CharityPointScreenState extends State<CharityPointScreen> {
  CharityPointController controller = Get.put(CharityPointController());

  @override
  Widget build(BuildContext ctx) {
    return Scaffold(
      backgroundColor:
          controller.dataList.isEmpty ? null : const Color(0xfffbfbfb),
      appBar: MBAppBar(
        elevation: 1,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(15.0),
          ),
        ),
        title: Text(
          'APPLICATION_MOBILE_TITLE_CHARITY_POINTS'.tr(),
          style: Theme.of(ctx).textTheme.headlineSmall,
        ),
      ),
      body: Obx(
        () {
          if (controller.dataList.isEmpty) {
            if (controller.loading) {
              return _loadingWidget(context);
            } else if (controller.error) {
              return _errorWidget(context);
            }
            return _recordNotFoundWidget();
          }

          return Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  topCard(
                    "assets/icon/walk.png",
                    "${controller.history.value.totalWalks}",
                    "Walks",
                    const Color.fromRGBO(77, 159, 109, 1),
                  ),
                  topCard(
                    "assets/icon/miles_purple_icon.png",
                    "${controller.history.value.totalDistance}",
                    "Miles",
                    const Color.fromRGBO(88, 69, 112, 1),
                  ),
                  topCard(
                    "assets/icon/yellow_paw.png",
                    controller.history.value.totalPoints.toPointsLong(),
                    "Charity Points",
                    const Color.fromRGBO(243, 174, 86, 1),
                  ),
                ],
              ),
              Expanded(
                child: CharityPointWeekList(),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _recordNotFoundWidget() {
    return Center(
      child: MBNoRecordFound(
        title: 'APPLICATION_MOBILE_TEXT_NO_CHARITY_POINTS_FOUND'.tr(),
        otherWidget: Text(
          'APPLICATION_MOBILE_TEXT_NO_CHARITY_POINTS_FOUND_DESC'.tr(),
          style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: Color(0xffA2B2C6)),
        ),
      ),
    );
  }

  Widget topCard(String icon, String title, String subTitle, Color textColor) {
    return Card(
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: const BorderSide(color: Color(0xffE3E3E3), width: 1)),
        margin: const EdgeInsets.symmetric(vertical: 15),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                icon,
                height: 25,
              ),
              const SizedBox(
                width: 15,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: textColor,
                        ),
                  ),
                  Text(
                    subTitle,
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: const Color.fromRGBO(128, 128, 128, 1),
                        ),
                  )
                ],
              ),
            ],
          ),
        ));
  }

  Widget _errorWidget(BuildContext context) {
    return Center(
      child: TextButton(
        onPressed: () {
          controller.loading = true;
          controller.error = false;
          controller.pageFetch();
        },
        child: Text(
          'Error while loading data, tap to try again',
          style: Theme.of(context).textTheme.headlineSmall,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _loadingWidget(BuildContext context) {
    return Center(
      child: Text(
        'Loading...',
        style: Theme.of(context).textTheme.headlineSmall,
      ),
    );
  }
}
