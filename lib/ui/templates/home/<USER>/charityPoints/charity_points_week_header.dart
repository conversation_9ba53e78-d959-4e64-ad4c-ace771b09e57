import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/datetime_extensions.dart';

import '../../../../../models/charity_point_history.dart';

class CharityPointsWeekHeader extends StatelessWidget {
  const CharityPointsWeekHeader({Key? key, required this.pointData})
      : super(key: key);
  final PointsData pointData;

  @override
  Widget build(BuildContext context) {
    if (pointData.title.isNotEmpty) {
      return _titleHeader();
    }
    return _dateRangeHeader();
  }

  Widget _titleHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      color: const Color(0xff6E568C),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
              flex: 2,
              child: Text(
                pointData.title,
                style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white),
              )),
          Flexible(
              flex: 3,
              child: RichText(
                  text: TextSpan(
                      text: double.parse("${pointData.weekPoints}")
                          .toInt()
                          .toString(),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      children: const [
                    TextSpan(
                        text: " Charity Points",
                        style: TextStyle(fontWeight: FontWeight.w400))
                  ]))),
        ],
      ),
    );
  }

  Widget _dateRangeHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 16),
      color: const Color(0xff6E568C),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
              flex: 4,
              child: RichText(
                  text: TextSpan(children: [
                TextSpan(
                    text:
                        DateTime.parse(pointData.from).toDateFormatActivities(),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    )),
                TextSpan(
                    text:
                        " - ${DateTime.parse(pointData.to).toDateFormatActivities()}",
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ))
              ]))),
          Flexible(
              flex: 3,
              child: RichText(
                  text: TextSpan(
                      text: double.parse("${pointData.weekPoints}")
                          .toInt()
                          .toString(),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      children: const [
                    TextSpan(
                        text: " Charity Points",
                        style: TextStyle(fontWeight: FontWeight.w400))
                  ]))),
        ],
      ),
    );
  }
}
