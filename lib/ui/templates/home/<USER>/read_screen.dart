import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/mb_base_home_screen.dart';
import 'package:mybuddy/ui/templates/announcement/widget_announcement_list.dart';

import '../../../components/internet_widget_switcher.dart';
import '../../../components/mb_appbar.dart';

class ReadScreen extends StatelessWidget {
  const ReadScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: MBAppBar(
          title: Text(
            'APPLICATION_MOBILE_SECTION_READ_TITLE'.tr('Read'),
            style: const TextStyle(fontWeight: FontWeight.w700, fontSize: 22),
          ),
        ),
        body: WidgetAnnouncementList());
  }
}
