import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:url_launcher/url_launcher.dart';

class RulesScreen extends StatelessWidget {
  const RulesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    TextStyle linkStyle = const TextStyle(color: Colors.blue);
    return Scaffold(
      backgroundColor: const Color(0xFFFBFBFB),
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_TITLE_CHARITY_POINTS_RULES'.tr(),
          style: Theme.of(context).textTheme.headline6,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(30.0),
        child: RichText(
          text: TextSpan(
            style: Theme.of(context).textTheme.subtitle1!.copyWith(height: 1.5),
            children: <TextSpan>[
              const TextSpan(
                  text:
                      'The amount of Charity Points you and others walking for your animal charity earn determines the donations your animal charity will receive from WoofTrax. For full details '),
              TextSpan(
                  text: 'click here.',
                  style: linkStyle,
                  recognizer: TapGestureRecognizer()
                    ..onTap = () async {
                      await launch("http://wooftrax.com/charitypoints");
                    }),
            ],
          ),
        ),
      ),
    );
  }
}
