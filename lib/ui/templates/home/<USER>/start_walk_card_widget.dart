import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/walk_provider.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/stats.dart';
import 'package:mybuddy/ui/templates/workout/workout_pet_selection.dart';

class StartWalkCard extends StatelessWidget {
  final Owner? owner;
  final Stats? stats;

  const StartWalkCard({Key? key, required this.owner, required this.stats})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final WalkProvider walkProvider = WalkProvider.of(context);

    return Card(
      child: ListView(
        padding: const EdgeInsets.symmetric(vertical: 15.0),
        primary: false,
        shrinkWrap: true,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15.0),
            child: SizedBox(
                width: double.infinity,
                child: Obx(() => RichText(
                      text: TextSpan(
                        style: Theme.of(context).textTheme.bodyMedium,
                        children: <TextSpan>[
                          TextSpan(
                              text: "Charity: ",
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                      color: Colors.grey[900],
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      height: 1.5)),
                          TextSpan(
                              text: walkProvider.shelter.name,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                      color: Colors.grey[900],
                                      fontSize: 14,
                                      fontWeight: FontWeight.w700,
                                      height: 1.5)),
                        ],
                      ),
                    ))),
          ),
          Container(
            margin: const EdgeInsets.only(top: 15.0, bottom: 10.0),
            height: 130.0,
            child: WorkoutPetSelectedWidget(size: 89,),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  await Tools().location.openWorkoutPage(context);
                },
                style: ElevatedButton.styleFrom(
                  elevation: 0.0,
                ),
                child: Obx(
                  () => Text(
                    walkProvider.getStartWalkCardTitle(),
                    style: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
