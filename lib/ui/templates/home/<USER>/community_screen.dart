import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/mb_base_home_screen.dart';
import 'package:mybuddy/ui/templates/community/community_groups.dart';
import 'package:mybuddy/ui/templates/community/community_home.dart';
import 'package:mybuddy/ui/templates/community/community_teams_page.dart';

import '../../../../controllers/community_controller.dart';

class CommunityScreen extends StatefulWidget {
  const CommunityScreen({Key? key}) : super(key: key);

  @override
  _CommunityScreenState createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen>
    with SingleTickerProviderStateMixin {
  CommunityController controller = CommunityController.of();
  late TabController tabController;

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: controller.tabs.length, vsync: this);
    tabController.addListener(() {
      if (tabController.indexIsChanging) {
        controller.switchCommunityTab(CommunityTab.values[tabController.index]);
      }
    });
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFFBFBFB),
      child: HomeSliverScreen(
        backgroundColor: const Color(0xFFFBFBFB),
        title: 'APPLICATION_MOBILE_SECTION_COMMUNITY_TITLE'.tr('Community'),
        expandedHeight: 0.0,
        tabs: controller.tabs,
        tabController: tabController,
        tabFontSize: 15,
        tabFontWeight: FontWeight.w700,
        tabWidth: controller.tabWidth,
        tabHeight: controller.tabHeight,
        child: TabBarView(
          controller: tabController,
          physics: const NeverScrollableScrollPhysics(),
          children: const [
            CommunityGroupsPage(),
            CommunityHomePage(),
          ],
        ),
      ),
    );
  }
}
