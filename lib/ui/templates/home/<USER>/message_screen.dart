import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/messages_bloc.dart';
import 'package:mybuddy/blocs/notifications_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/ui/components/mb_base_home_screen.dart';
import 'package:mybuddy/ui/components/mb_refresh_indicator.dart';
import 'package:mybuddy/ui/templates/message/widget_message_list.dart';
import 'package:mybuddy/ui/templates/notification/widget_notification_list.dart';

class MessageScreen extends StatefulWidget {
  final String? title;
  final bool isPushed;

  const MessageScreen({Key? key, this.title, this.isPushed = false})
      : super(key: key);

  @override
  _MessageScreenState createState() => _MessageScreenState();
}

class _MessageScreenState extends State<MessageScreen>
    with SingleTickerProviderStateMixin {
  TabController? tabController;

  late bool hasMessagingModule;

  List<String>? tabs;

  @override
  void initState() {
    super.initState();
    hasMessagingModule = Data().getClinicsForMessage().isNotEmpty ||
        Data().get().messages.isNotEmpty;
    if (hasMessagingModule) {
      tabs = <String>['MESSAGES', 'NOTIFICATIONS'];
      tabController = TabController(length: tabs!.length, vsync: this);
    }
  }

  @override
  void dispose() {
    if (tabController != null) {
      tabController!.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return HomeSliverScreen(
      title: widget.title,
      automaticallyImplyLeading: widget.isPushed,
      actions: [
        if (hasMessagingModule && Data().getClinicsForMessage().isNotEmpty)
          IconButton(
            onPressed: () => Tools().message.addNewMessage(context),
            icon: const Icon(
              FontAwesomeIcons.plus,
              size: 16.0,
            ),
          ),

        /// endif
      ],
      tabs: tabs,
      tabController: tabController,
      child: hasMessagingModule
          ? TabBarView(
              controller: tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _messages(context:context),
                _notifications(context: context),
              ],
            )
          : _notifications(context: context),
    );
  }

  Widget _messages({BuildContext? context}) => MBRefreshIndicator(
    child: BlocProvider<MessagesBloc>(
      bloc: MessagesBloc(),
      child: const WidgetMessageList(),
    ),
  );

  Widget _notifications({BuildContext? context}) => MBRefreshIndicator(
        child: BlocProvider<NotificationsBloc>(
          bloc: NotificationsBloc(),
          child: const WidgetNotificationList(),
        ),
      );

}
