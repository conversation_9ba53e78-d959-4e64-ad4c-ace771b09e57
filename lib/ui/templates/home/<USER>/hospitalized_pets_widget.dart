import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/ui/components/mb_home_card.dart';
import 'package:mybuddy/ui/templates/pet/diary/pet_diary_page.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class HospitalizedPetsCard extends StatelessWidget {
  final List<Pet>? hospitalizedPets;
  const HospitalizedPetsCard({Key? key, this.hospitalizedPets}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (hospitalizedPets == null || hospitalizedPets!.isEmpty) {
      return const SizedBox.shrink();
    }

    return ListView(
      padding: const EdgeInsets.all(10.0),
      primary: false,
      shrinkWrap: true,
      children: [
        Text(
          ('APPLICATION_MOBILE_LABEL_HOSPITALIZATION_NEWS_ANIMAL'.tr())
              .capitalize(),
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        const SizedBox(height: 10.0),
        ...hospitalizedPets!.map((p) {
          return Padding(
            padding: const EdgeInsets.all(1.0),
            child: MBDenseHomeCard(
              title: p.name,
              icon: FontAwesomeIcons.hospitalSymbol,
              onTap: () {
                Tools().navigatorPush(
                  PetDiaryPage(pet: p, index: 1, filter: 7),
                );
              },
              color: Colors.white,
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }),
      ],
    );
  }
}
