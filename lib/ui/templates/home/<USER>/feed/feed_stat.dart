import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/generic_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/models/stats.dart';
import 'package:mybuddy/ui/templates/workout/workout_summary_widget.dart';

class FeedStat extends StatelessWidget {
  const FeedStat({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Padding(
        padding: const EdgeInsets.all(30),
        child: SizedBox(
          width: width - 2 * 30,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              const OwnerWorkoutSummary(),
              const SizedBox(height: 6.0),
              BlocProvider<GenericBloc>(
                bloc: GenericBloc(),
                child: Builder(
                  builder: (BuildContext context) {
                    final bloc = BlocProvider.of<GenericBloc>(context);
                    return StreamBuilder(
                      stream: bloc.stream,
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          return const TotalStatRow();
                        }
                        return const SizedBox.shrink();
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ));
  }
}

class TotalStatRow extends StatelessWidget {
  const TotalStatRow({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Stats stats = Data().get().stats;

    TextStyle textStyle = TextStyle(
      fontSize: 12.0,
      color: Theme.of(context).primaryColor,
    );

    return Row(
      children: <Widget>[
        const Icon(
          FontAwesomeIcons.globeAmericas,
          size: 10.0,
        ),
        const SizedBox(width: 3.0),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'People have walked ${stats.workoutCount.toUs()} times',
              style: textStyle,
            ),
            Text(
              'for ${stats.workoutDistance.toUs()} miles',
              style: textStyle,
            ),
          ],
        ),

        ///endif
      ],
    );
  }
}
