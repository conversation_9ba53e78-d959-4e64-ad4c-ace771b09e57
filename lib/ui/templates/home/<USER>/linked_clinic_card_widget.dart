import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_card_header_widget.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/ui/templates/service/all_services_page.dart';
import 'package:mybuddy/ui/templates/service/widgets/service_card_widget.dart';

class LinkedClinicCard extends StatelessWidget {
  final MBService? service;
  const LinkedClinicCard({Key? key, this.service}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (service == null) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 10.0, right: 10),
          child: CardHeaderWidget(
            title:
            ('APPLICATION_MOBILE_LABEL_SERVICE_CLINIC'.tr()).capitalize(),
            onPressed: () {
              Tools().navigatorPush(const ClinicsAndServicesPage());
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(bottom: 10.0),
          child: ServiceCardWidget(service: service, bookmarked: false),
        ),
      ],
    );
  }
}
