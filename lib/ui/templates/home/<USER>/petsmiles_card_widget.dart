import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/login_data.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/stats.dart';
import 'package:mybuddy/models/workout.dart';
import 'package:mybuddy/ui/components/earning_points_footer_component.dart';
import 'package:mybuddy/ui/components/mb_progress_bar_widget.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';

class PetSmilesCard extends StatelessWidget {
  final List<Workout>? workouts;

  const PetSmilesCard({Key? key, this.workouts}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    LoginData data = Data().get();
    Owner owner = data.owner;
    Stats stats = data.stats;

    List<Workout> ownerWorkouts = workouts ?? <Workout>[];
    int ownerPoints = owner.points;
    Map<String, dynamic> buildLevel = ownerPoints.toLevel();
    double totalDistance = 0.0;
    for (var w in ownerWorkouts) {
      totalDistance += w.distance;
    }

    TextStyle textStyle = TextStyle(
      fontSize: 13.0,
      color: Theme.of(context).primaryColor,
    );
    SizedBox horizontalSeparation = const SizedBox(width: 4.0);
    Icon icon(IconData? iconData) {
      return Icon(
        iconData,
        size: 12.0,
      );
    }

    return ListView(
      padding: const EdgeInsets.all(10.0),
      primary: false,
      shrinkWrap: true,
      children: [
        Text(
          AppConfig.of(context).appDisplayName,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        const SizedBox(height: 10.0),

        /// Owner total points
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0),
          child: Row(
            children: [
              EarnedPointsWidget(
                size: 18.0,
                totalPoints: ownerPoints,
                reversed: true,
              ),
              Text(
                '/',
                style: TextStyle(
                  color: Theme.of(context).disabledColor,
                  fontSize: 18.0,
                ),
              ),
              Text(
                'APPLICATION_MOBILE_FEED_TITLE_OWNER_LEVEL_GOAL'.tr(
                  '[g] points',
                  {'[g]': buildLevel['goal'].toString()},
                ),
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const Spacer(),
              Text(
                'APPLICATION_MOBILE_FEED_TITLE_OWNER_LEVEL'.tr(
                  'Level [l]',
                  {'[l]': buildLevel['level'].toString()},
                ),
                style: Theme.of(context).textTheme.titleSmall,
              ),
            ],
          ),
        ),

        /// PetSmiles progress bar
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 5.0,
            horizontal: 10.0,
          ),
          child: MBProgressBar(value: buildLevel['progress']),
        ),

        /// Shelter info
        if (owner.shelter != null)
          Padding(
            padding: const EdgeInsets.fromLTRB(10.0, 3.0, 10.0, 0.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                icon(FontAwesomeIcons.handHoldingHeart),
                horizontalSeparation,
                Expanded(
                  child: Text(
                    'APPLICATION_MOBILE_FEED_TITLE_OWNER_SHELTER'.tr(
                      'You are walking for "[name]", [city] [state]',
                      {
                        '[name]': owner.shelter!.name,
                        '[city]': owner.shelter!.city,
                        '[state]': owner.shelter!.state,
                      },
                    ),
                    style: textStyle,
                  ),
                ),
              ],
            ),
          ),

        ///endif
        /// Personal walking info
        Padding(
          padding: const EdgeInsets.fromLTRB(10.0, 3.0, 10.0, 0.0),
          child: Row(
            children: <Widget>[
              icon(FontAwesomeIcons.paw),
              horizontalSeparation,
              Text(
                'APPLICATION_MOBILE_FEED_TITLE_OWNER_WORKOUTS_TOTAL_COUNT'.tr(
                  '[count] walks',
                  {'[count]': ownerWorkouts.length.toString()},
                ),
                style: textStyle,
              ),
              horizontalSeparation,
              icon(FontAwesomeIcons.shoePrints),
              horizontalSeparation,
              Text(
                'APPLICATION_MOBILE_FEED_TITLE_OWNER_WORKOUTS_TOTAL_DISTANCE'.tr(
                  '[distance] [unit] walked',
                  {
                    '[distance]': totalDistance.toUserLength().toLocalStringAsFixed(1),
                    '[unit]': owner.units.toStringDistanceLabel
                  },
                ),
                style: textStyle,
              ),
              // horizontalSeparation,
              // const EarningPointsWidget(size: 12.0),
            ],
          ),
        ),

        /// Global walking info
        Padding(
          padding: const EdgeInsets.fromLTRB(10.0, 3.0, 10.0, 0.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              icon(FontAwesomeIcons.globeAmericas),
              horizontalSeparation,
              Expanded(
                child: Text(
                  'APPLICATION_MOBILE_FEED_TITLE_COMMUNITY_WORKOUTS'.tr(
                    'The [app_name] community has walked [count] times for [distance] [unit]',
                    {
                      '[app_name]': AppConfig.of(context).appDisplayName,
                      '[count]': stats.workoutCount.toUs(),
                      '[distance]': stats.workoutDistance.toUserLength().toUs(),
                      '[unit]': owner.units.toStringDistanceLabel
                    },
                  ),
                  style: textStyle,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
