import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
// import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/fav_service_bloc.dart';
import 'package:mybuddy/blocs/generic_bloc.dart';
import 'package:mybuddy/blocs/pets_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/login_data.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/ui/components/mb_base_home_screen.dart';
import 'package:mybuddy/ui/components/mb_clinic_card_widget.dart';
import 'package:mybuddy/ui/components/mb_home_card.dart';
import 'package:mybuddy/ui/templates/announcement/widget_announcement_list.dart';
import 'package:mybuddy/ui/templates/apt/appointment_page.dart';
// import 'package:mybuddy/ui/templates/home/<USER>/feed/feed_stat.dart';
import 'package:mybuddy/ui/templates/pet/diary/pet_diary_page.dart';
import 'package:mybuddy/ui/templates/pet/diary/petactivity_page.dart';
import 'package:mybuddy/ui/templates/pet/pet_pageview.dart';
import 'package:mybuddy/ui/templates/pet/widget_home_pet_carousel.dart';
import 'package:mybuddy/ui/templates/service/widget_favorite_clinic.dart';

class WidgetHomeHome extends StatelessWidget {
  final Function()? onPetWidgetTap;

  const WidgetHomeHome({Key? key, this.onPetWidgetTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<GenericBloc>(context);
    return StreamBuilder<LoginData>(
      stream: bloc.stream,
      builder: (context, AsyncSnapshot<LoginData> snapshot) {
        if (!snapshot.hasData) return Container();
        LoginData thisData = snapshot.data!;

        bool isServiceImage = !SettingsDelegate().get().forcePetView &&
            Data().getFavoriteClinic() != null &&
            Data().getFavoriteClinic()!.imageId != null;

        List<Widget> widgets = [];

        widgets.add(const ClinicHomeWidget());

        //TODO MIDDLE add end of health plan end of insurance
        //also in local notifications

        if (thisData.pets.isEmpty) {
          widgets.add(MBHomeCard(
            'APPLICATION_MOBILE_BUTTON_LABEL_DOG_ADD'.tr(),
            subtitleText: 'APPLICATION_MOBILE_LABEL_NO_PET_SENTENCE'.tr(),
            icon: FontAwesomeIcons.paw,
            onTap: () async {
              await Tools().pet.addPetAction(context, null);
              bloc.update();
            },
          ));
        } else {
          for (var pet in thisData.pets) {
            if (pet.isBirthdayToday() && pet.deceased == false) {
              widgets.add(MBHomeCard(
                pet.name,
                color: const Color.fromRGBO(180, 77, 18, 1.0),
                backgroundColor: const Color.fromRGBO(253, 198, 53, 1.0),
                subtitleText: 'APPLICATION_MOBILE_HAPPY_BIRTHDAY'.tr(),
                icon: FontAwesomeIcons.birthdayCake,
                onTap: () {
                  Tools().navigatorPush(PetPageView(pet: pet));
                },
              ));
            }
          }
        }

        // if (AppConfig.of(context).isWoofTrax){
        //   widgets.add(const FeedStat());
        // }


        bool isAppBarExpanded = isServiceImage;

        Data().getPets().forEach(
          (p) {
            if (!p.deceased) {
              if (p.imageId != null) {
                isAppBarExpanded = true;
              }
              if (p.hospitalized) {
                widgets.add(
                  MBHomeCard(
                    p.name,
                    subtitleText:
                        'APPLICATION_MOBILE_LABEL_HOSPITALIZATION_NEWS_ANIMAL'
                            .tr(),
                    icon: FontAwesomeIcons.hospitalSymbol,
                    onTap: () {
                      Tools().navigatorPush(
                        PetDiaryPage(pet: p, index: 1, filter: 7),
                      );
                    },
                    color: Colors.white,
                    backgroundColor: Theme.of(context).errorColor,
                  ),
                );
              }
            }
          },
        );

        /// comings part
        List<PetActivity> incomings = Data().getFilteredPetActivities(
            chronoFilter: ChronoFilter.future, livingState: LivingState.alive);
        int displayedIncomingCount = 0;
        for (PetActivity incoming in incomings) {
          if (displayedIncomingCount >= 3) break;
          Color color;
          DateTime dateTime;
          AppointmentRequest? apt;
          String title = '';
          switch (incoming.activityId) {
            case 1:
              if (incoming.dateStart == null) {
                continue; // TODO NS is it better than convert [dateTime] nullable ???
              }
              dateTime = incoming.dateStart!;
              break;
            case 2:
              if (incoming.nextOccurrence == null) {
                continue; // TODO NS is it better than convert [dateTime] nullable ???
              }
              title = '';
              dateTime = incoming.nextOccurrence!;
              break;
            default:
              if (incoming.dateStart == null) {
                continue; // TODO NS is it better than convert [dateTime] nullable ???
              }
              dateTime = incoming.dateStart!;
          }

          if (dateTime.isBefore(DateTime.now())) {
            color = Colors.orangeAccent;
          } else if (dateTime
              .isBefore(DateTime.now().add(const Duration(days: 14)))) {
            color = Colors.greenAccent;
          } else {
            break;

            /// out from foreach
          }

          switch (incoming.activityId) {
            case 1:
              apt = Data().getAppointmentWithPetActivity(incoming.id);
              if (apt == null) {
                break;
              }
              title = thisData.getService(apt.serviceId)?.name ?? '';
              widgets.add(
                MBHomeCard(
                  title,
                  subtitleText: 'APPLICATION_MOBILE_TITLE_APPOINTMENT'.tr() +
                      ' ' +
                      Data().dateTimeToUserDateStr(dateTime),
                  icon: FontAwesomeIcons.calendarAlt,
                  onTap: () {
                    Tools().navigatorPush(AppointmentPage(appointment: apt!));
                  },
                  color: color,
                ),
              );
              break;
            case 2:
              widgets.add(
                MBHomeCard(
                  incoming.title,
                  subtitleText: 'APPLICATION_MOBILE_LABEL_REMINDER'.tr() +
                      ' ' +
                      Data().dateTimeToUserDateStr(dateTime),
                  icon: FontAwesomeIcons.clock,
                  onTap: () {
                    Tools()
                        .navigatorPush(PetActivityPage(petActivity: incoming));
                  },
                  color: color,
                ),
              );
              break;
            default:
              widgets.add(
                MBHomeCard(
                  incoming.title ?? incoming.comment,
                  subtitleText: Data().dateTimeToUserDateStr(dateTime),
                  icon: FontAwesomeIcons.exclamation,
                  onTap: () {
                    Tools()
                        .navigatorPush(PetActivityPage(petActivity: incoming));
                  },
                  color: color,
                ),
              );
          }
          displayedIncomingCount++;
        }

        return BlocProvider<FavServiceBloc>(
          bloc: FavServiceBloc(),
          child: HomeSliverScreen(
            // actions: [
            //   if (AppConfig.of(context).isWoofTrax)
            //     RawMaterialButton(
            //       onPressed: () async {
            //         await Tools().location.openWorkoutPage(context);
            //       },
            //       elevation: 0.0,
            //       fillColor: Colors.white54,
            //       shape: const CircleBorder(),
            //       constraints:
            //           const BoxConstraints(minWidth: 16.0, minHeight: 16.0),
            //       padding: const EdgeInsets.all(8.0),
            //       child: const Icon(
            //         FontAwesomeIcons.walking,
            //         size: 16.0,
            //       ),
            //     ),
            //
            //   /// endif
            // ],
            expandedHeight: isAppBarExpanded
                ? MediaQuery.of(context).size.height * 0.34
                : 0,
            background: isServiceImage
                ? const ClinicImage()
                : GestureDetector(
                    onTap: () {},
                    child: BlocProvider<PetsBloc>(
                      bloc: PetsBloc(),
                      child: const WidgetHomePetCarousel(),
                    ),
                  ),
            child: RefreshIndicator(
              onRefresh: () async {
                if (await Tools().common.isOnline(alertContext: context)) {
                  await MbApiOwner().refreshLoginRequest();
                  // await Tools().localNotification.setNotifications();
                }
              },
              child: MediaQuery.removePadding(
                context: context,
                removeTop: true,
                child: ListView(
                  shrinkWrap: true,
                  children: widgets,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
