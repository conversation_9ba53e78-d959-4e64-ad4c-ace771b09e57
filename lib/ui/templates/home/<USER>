import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_location.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/generic_bloc.dart';
import 'package:mybuddy/blocs/walk_provider.dart';
import 'package:mybuddy/controllers/challenges_controller.dart';
import 'package:mybuddy/controllers/home_tab_controller.dart';
import 'package:mybuddy/controllers/weekly_stats_controller.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/pages/banner_widget.dart';
import 'package:mybuddy/ui/components/mb_hamburger.dart';
import 'package:mybuddy/ui/templates/home/<USER>/feed_screen.dart';

import '../../../class/location_manager.dart';
import '../../../controllers/workout_sync_controller.dart';
import '../workout/workout_history_widget.dart';
import 'screens/home_screens.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with WidgetsBindingObserver {
  BoxConstraints? constraints;

  final WorkoutSyncController syncController = WorkoutSyncController.of();
  final WeeklyStatsController statsController = WeeklyStatsController.of();
  final HomeTabController tabController = Get.put(HomeTabController());
  final ChallengesController challengesController =
      Get.put(ChallengesController());

  List<MBView> views({required BuildContext context}) => [
        /// First View
        MBView(
          bottomNavigationBarItem: const BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          screen: BlocProvider<GenericBloc>(
            bloc: GenericBloc(),
            child: FeedScreen(),
          ),
        ),

        /// Second View
        MBView(
          bottomNavigationBarItem: const BottomNavigationBarItem(
            icon: Icon(Icons.group),
            label: 'Community',
          ),
          screen: const CommunityScreen(),
        ),

        /// Third View
        MBView(
          bottomNavigationBarItem: const BottomNavigationBarItem(
            icon: Icon(
              FontAwesomeIcons.walking,
              size: 40,
            ),
            // label: 'Walk',
          ),
          screen: const WorkoutScreen(),
        ),

        /// Fourth View
        MBView(
          bottomNavigationBarItem: BottomNavigationBarItem(
            icon: Obx(
              () => Stack(
                children: [
                  const Icon(Icons.emoji_events_outlined),
                  if (challengesController
                      .shouldShowAvailableChallengeIndicator)
                    const Padding(
                      padding: EdgeInsets.only(
                        left: 15,
                      ),
                      child: Icon(
                        Icons.circle_rounded,
                        color: Colors.red,
                        size: 8,
                      ),
                    )
                ],
              ),
            ),
            label: 'Challenges',
          ),
          screen: const ChallengeScreen(),
        ),

        /// Fifth View
        MBView(
          bottomNavigationBarItem: BottomNavigationBarItem(
            icon: Image.asset(
              'assets/icon/activity_history.png',
              height: 16,
            ),
            activeIcon: Image.asset(
              'assets/icon/activity_history.png',
              height: 16,
              color: Theme.of(context).indicatorColor,
            ),
            label: 'APPLICATION_MOBILE_LABEL_TAB_BUDDYCREDITS_HISTORY'.tr(),
          ),
          screen: WorkoutsHistoryPage(),
        ),
      ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WalkProvider.of(context).initializeWalk();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
    syncController.startSyncProcess();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.inactive ||
        state == AppLifecycleState.detached) return;

    final isBackground = state == AppLifecycleState.paused;

    if (isBackground) {
      //No need to stop location updates if the walk is ongoing
      if (!WalkProvider.of().walkOngoing.value) {
        LocationManager().stopLocationUpdatesAppWide();
      }
    } else {
      //If the walk is ongoing, location will already be granted
      if (!WalkProvider.of().walkOngoing.value) {
        LocationTools().getLocationPermissions();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Tools().setContext(context, 'home');

    return WillPopScope(
      onWillPop: () => tabController.onWillPop(context).then((value) => value),
      child: Scaffold(
        primary: false,
        body: Column(
          children: [
            Expanded(
              child: Stack(
                children: <Widget>[
                  PageView(
                    controller: tabController.pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    children:
                        views(context: context).map((v) => v.screen).toList(),
                  ),
                  Positioned(
                    left: 10.0,
                    top: MediaQuery.of(context).padding.top,

                    /// status bar height
                    child: Container(
                      constraints: const BoxConstraints(
                        minWidth: 48.0,
                        minHeight: 56.0,
                      ),
                      alignment: Alignment.center,
                      child: Obx(() {
                        return !tabController.isWalkTab
                            ? const MBHamburger()
                            : const SizedBox.shrink();
                      }),
                    ),
                  ),

                  /// endif
                ],
              ),
            ),
            Obx(() {
              return !tabController.isWalkTab
                  ? const BannerAdWidget()
                  : const SizedBox.shrink();
            }),

            /// endif
          ],
        ),
        resizeToAvoidBottomInset: true,
        bottomNavigationBar: Obx(() {
          return CupertinoTabBar(
            iconSize: 20.0,
            activeColor: Theme.of(context).indicatorColor,
            // selectedFontSize: 11.0,
            // unselectedFontSize: 10.0,
            items: views(context: context)
                .map((v) => v.bottomNavigationBarItem)
                .toList(),
            currentIndex: tabController.selectedIndex,
            onTap: (index) => tabController.tabSwitcher(HomeTab.values[index]),
          );
        }),
      ),
    );
  }
}

class MBView {
  final BottomNavigationBarItem bottomNavigationBarItem;
  final Widget screen;

  MBView({
    required this.bottomNavigationBarItem,
    required this.screen,
  });
}
