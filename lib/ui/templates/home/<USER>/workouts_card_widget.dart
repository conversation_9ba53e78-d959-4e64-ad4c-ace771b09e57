import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/models/workout.dart';
import 'package:mybuddy/ui/components/mb_card_header_widget.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/templates/workout/widgets/workout_info_tile.dart';
import 'package:mybuddy/ui/templates/workout/workout_details.dart';
import 'package:mybuddy/ui/templates/workout/workout_history_widget.dart';

import '../../../../models/walk_model.dart';

class WorkoutsCard extends StatelessWidget {
  final List<Workout>? workouts;

  const WorkoutsCard({Key? key, this.workouts}) : super(key: key);

  List<Workout> get _workouts {
    List<Workout> results = workouts ?? <Workout>[];
    if (results.isNotEmpty) {
      results.sort((a, b) {
        if (a.createdAt == null) {
          return 1;
        } else if (b.createdAt == null) {
          return -1;
        } else {
          return b.createdAt!.compareTo(a.createdAt!);
        }
      });
      int wLength = results.length;
      if (wLength > 10) {
        results = results.sublist(0, 10);
      }
    }

    return results;
  }

  @override
  Widget build(BuildContext context) {
    if (_workouts.isEmpty) {
      return const SizedBox.shrink();
    }
    final Size screenSize = MediaQuery.of(context).size;

    return ListView(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      primary: false,
      shrinkWrap: true,
      children: [
        ///card header
        Padding(
          padding: const EdgeInsets.only(left: 15.0),
          child: CardHeaderWidget(
            title: 'History'.tr(),
            onPressed: () => Tools().navigatorPush(
              WorkoutsHistoryPage(),
            ),
          ),
        ),

        ///horizontal list of last workouts
        SizedBox(
          width: screenSize.width,
          height: 250,
          child: ListView(
            padding: const EdgeInsets.only(right: 15.0),
            scrollDirection: Axis.horizontal,
            shrinkWrap: true,
            children: _workouts.map((workout) {
              ///return one element
              return GestureDetector(
                onTap: () => Tools().navigatorPush(WorkoutDetails(workout)),
                child: Card(
                  margin: const EdgeInsets.only(left: 15.0),
                  child: Container(
                    padding: const EdgeInsets.all(15.0),
                    width: screenSize.width - 60,
                    height: 250,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Text(
                              "${DateFormat.yMMMd('en_US').format(workout.createdAt!)} | ${DateFormat.jm('en_US').format(workout.createdAt!)}",
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                      color: const Color(0xff808080),
                                      fontWeight: FontWeight.w500,
                                      fontSize: 13),
                            ),
                          ],
                        ),
                        Divider(color: Colors.grey[100]),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [_petAvatar(context, workout)],
                        ),
                        Divider(color: Colors.grey[100]),
                        IntrinsicHeight(
                          child: Row(
                            children: [
                              Expanded(
                                  child: WorkoutInfoTile(
                                "${workout.distance.toUserLength().toLocalStringAsFixed(2)} ${Data().get().owner.units.toStringDistanceLabel}",
                                'APPLICATION_MOBILE_TITLE_SHARE_DISTANCE'
                                    .tr("Distance"),
                                MoodType.values
                                    .elementAt(workout.mood != null &&
                                            workout.mood != 0
                                        ? workout.mood! - 1
                                        : 0)
                                    .toIcon,
                                background: true,
                              )),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8.0),
                                child: VerticalDivider(
                                    width: 2, color: Colors.grey[200]),
                              ),
                              Expanded(
                                  child: WorkoutInfoTile(
                                workout.duration.toENTimeString(),
                                "APPLICATION_MOBILE_TITLE_SHARE_DURATION"
                                    .tr("Duration"),
                                "assets/icon/clock.png",
                                background: true,
                              )),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          color: Colors.grey[100],
                          child: Row(
                            children: [
                              Image.asset(
                                "assets/icon/handshake.png",
                                height: 20,
                                width: 20,
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: Text(
                                  workout.shelter?.name ??
                                      "-".useCorrectEllipsis(),
                                  overflow: TextOverflow.ellipsis,
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineSmall
                                      ?.copyWith(
                                          color: const Color(0xff162941),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500),
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _petAvatar(BuildContext context, Workout workout) {
    return workout.pets.isNotEmpty
        ? Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
              workout.getFilledPets().length > 4
                  ? 4
                  : workout.getFilledPets().length, (index) {
            var data = workout.getFilledPets()[index];
            if (index == 3) {
              return Container(
                  margin: EdgeInsets.only(left: index == 0 ? 0 : 10),
                  width: 60,
                  child: Column(
                    children: [
                      const MBDefaultImage(size: 50),
                      const SizedBox(height: 10),
                      Text(
                        "${workout.getFilledPets().length - 3} more",
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context)
                            .textTheme
                            .headlineSmall
                            ?.copyWith(
                                color: const Color(0xff162941),
                                fontSize: 11,
                                fontWeight: FontWeight.w400),
                      ),
                    ],
                  ));
            }
            return Container(
                margin: EdgeInsets.only(left: index == 0 ? 0 : 10),
                width: 60,
                child: Column(
                  children: [
                    MBAvatar(
                      imageUrl: data.avatarUrl,
                      size: 50,
                    ),
                    const SizedBox(height: 10),
                    Text(
                      data.name,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context)
                          .textTheme
                          .headlineSmall
                          ?.copyWith(
                              color: const Color(0xff162941),
                              fontSize: 11,
                              fontWeight: FontWeight.w400),
                    ),
                  ],
                ));
          }),
        )
        : Column(
            children: [
              const MBDefaultDogImage(size: 50),
              const SizedBox(height: 10),
              Text(
                'APPLICATION_MOBILE_LABEL_PET_LEXI'.tr('Lexi'),
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: const Color(0xff162941),
                    fontSize: 11,
                    fontWeight: FontWeight.w400),
              ),
            ],
          );
  }
}

class ParameterColumn extends StatelessWidget {
  final String label;
  final String? value;

  const ParameterColumn({Key? key, required this.label, this.value})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        Text(
          value ?? 'Unknown',
          style: Theme.of(context).textTheme.titleSmall,
        ),
      ],
    );
  }
}
