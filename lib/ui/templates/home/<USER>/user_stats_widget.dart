import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/home_stats_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/stats.dart';

import 'circular_percent_indicator.dart';

class HomeStats extends StatelessWidget {
  final Stats? stats;
  final HomeStatsController statsController = HomeStatsController.of();

  HomeStats({Key? key, required this.stats}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListView(
        primary: true,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 15.0),
                child: Text(
                  "Daily Goals",
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    builder: (_) => SizedBox(
                      height: 216,
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: makeBottomSheetItems(
                            context,
                            statsController,
                          ),
                        ),
                      ),
                    ),
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                  );
                },
                child: Row(
                  children: [
                    Obx(
                      () => Text(
                        statsController.selectedDate,
                        style: TextStyle(
                          fontSize: 15.0,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: 5.0),
                    Icon(
                      FontAwesomeIcons.angleDown,
                      size: 16.0,
                      color: Theme.of(context).disabledColor,
                    )
                  ],
                ),
              ),
            ],
          ),
          Obx(
            () => Stack(
              children: [
                Visibility(
                  visible: statsController.loading,
                  child: LinearProgressIndicator(
                    color: Theme.of(context).indicatorColor,
                    backgroundColor: Theme.of(context).disabledColor,
                  ),
                ),
                Column(
                  children: [
                    const SizedBox(height: 10.0),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        StatsCard(
                          completed: statsController.homeStats.userStats.walks
                              .toDouble(),
                          goal: statsController
                              .homeStats.dailyUserGoal.dailyWalkGoal
                              .toDouble(),
                          showGoal: statsController.showGoals,
                          color: const Color.fromRGBO(77, 177, 118, 1),
                          backgroundColor:
                              const Color.fromRGBO(128, 210, 161, 1),
                          icon: "assets/icon/walk.png",
                          label: "APPLICATION_MOBILE_HOME_STATS_WALKS_FINISHED"
                              .tr(),
                          rounded: true,
                        ),
                        StatsCard(
                          completed: statsController
                              .homeStats.userStats.coveredDistance,
                          goal: statsController
                              .homeStats.dailyUserGoal.dailyDistanceGoal
                              .toDouble(),
                          showGoal: statsController.showGoals,
                          color: const Color.fromRGBO(110, 86, 140, 1),
                          backgroundColor:
                              const Color.fromRGBO(192, 172, 219, 1),
                          icon: "assets/icon/challenge_distance.png",
                          label:
                              "APPLICATION_MOBILE_HOME_STATS_MILES_WALKED".tr(),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 15),
        ],
      ),
    );
  }
}

List<Widget> makeBottomSheetItems(
  BuildContext context,
  HomeStatsController statsController,
) {
  List<Widget> items = [];
  statsController.bottomSheetItems.forEachIndexed(
    (i, item) {
      items.add(
        TextButton(
          onPressed: () async {
            if (item == 'Custom') {
              DateTime? result = await Tools().common.showDateDialog(
                    context: context,
                    initialDate: statsController.lastQueriedDate,
                    firstDate: DateTime(1900),
                    lastDate: DateTime.now(),
                  );
              if (result != null) {
                Tools().navigatorPop();
                statsController.setDate(result, item);
              }
            } else {
              Tools().navigatorPop();
              statsController.setDateByItem(item);
            }
          },
          child: Text(
            item,
            style: TextStyle(
              fontSize: 14.0,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      );
      if (i < statsController.bottomSheetItems.length - 1) {
        items.add(const Divider(thickness: 1));
      }
    },
  );
  return items;
}

class StatsCard extends StatelessWidget {
  final double completed;
  final double goal;
  final Color color;
  final Color backgroundColor;
  final String icon;
  final String label;
  final bool showGoal;
  final bool rounded;
  const StatsCard({
    Key? key,
    required this.completed,
    required this.goal,
    required this.color,
    required this.backgroundColor,
    required this.icon,
    required this.label,
    required this.showGoal,
    this.rounded = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double calculatePercent() {
      if (!showGoal) {
        // fill whole bar if completed > 0 and no goal to be shown
        return completed > 0 ? 1 : 0;
      }
      double percent = completed / goal;
      // ensure ceiling of percent is 1
      return percent > 1 ? 1 : percent;
    }

    String makeLabel() {
      // round numerator, otherwise still ensure 0.0 is displayed as 0.
      String numerator = rounded
          ? completed.toStringAsFixed(0)
          : completed <= 0
              ? '0'
              : completed.toStringAsFixed(1);
      // return with denominator if goal is to be shown, otherwise just numerator
      return showGoal ? "$numerator / ${goal.toStringAsFixed(0)}" : numerator;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CircularPercentIndicator(
          backgroundColor: backgroundColor,
          radius: 62.0,
          lineWidth: 10.0,
          percent: calculatePercent(),
          circularStrokeCap: CircularStrokeCap.round,
          backgroundWidth: 7.0,
          center: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                icon,
                width: 20.0,
                height: 20.0,
              ),
              const SizedBox(height: 4.0),
              Text(
                makeLabel(),
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16.0,
                  color: color,
                ),
              ),
              const SizedBox(height: 2.0),
              Text(
                label,
                style: Theme.of(context)
                    .textTheme
                    .labelMedium!
                    .copyWith(fontSize: 13.0),
              ),
            ],
          ),
          animation: true,
          progressColor: color,
        ),
        const SizedBox(height: 10.0),
      ],
    );
  }
}
