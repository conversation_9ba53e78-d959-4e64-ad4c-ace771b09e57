import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/account/owner_set_weekly_goals.dart';

class EmptyWeeklyGoals extends StatelessWidget {
  const EmptyWeeklyGoals({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
        child: Row(
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width / 2 - 25,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  OutlinedButton.icon(
                    onPressed: () {
                      Tools().navigatorPush(SetWeeklyGoalsScreen());
                    },
                    icon: SvgPicture.asset(
                      'assets/icon/paws.svg',
                      width: 17,
                      height: 17,
                    ),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        vertical: 5,
                        horizontal: 15,
                      ),
                      foregroundColor: Theme.of(context).colorScheme.secondary,
                      side: BorderSide(
                        color: Theme.of(context).colorScheme.secondary,
                        width: 1,
                      ),
                    ),
                    label: Text(
                      "APPLICATION_MOBILE_BUTTON_LABEL_SET_WEEKLY_GOALS".tr(),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    'APPLICATION_MOBILE_TEXT_SET_WEEKLY_GOALS'.tr(),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      height: 1.42,
                    ),
                  ),
                  const SizedBox(height: 5),
                ],
              ),
            ),
            const SizedBox(width: 25),
            Expanded(
              child: SvgPicture.asset(
                'assets/images/weekly_goals_illustration.svg',
              ),
            )
          ],
        ),
      ),
    );
  }
}
