import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

import '../../../../controllers/offline_workout_sync_controller.dart';

class OfflineWalkSyncWidget extends StatelessWidget {
  OfflineWalkSyncWidget({Key? key}) : super(key: key);

  OfflineWorkoutSyncController controller = OfflineWorkoutSyncController.of();

  @override
  Widget build(BuildContext context) {
    return Obx((){
      if(!controller.isUnSyncWalksAvailable) {
        return const SizedBox.shrink();
      }
      return Card(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                getText(),
                style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w500),
              ),
              getEndingWidget()
            ],
          ),
        ),
      );
    });
  }

  String getText() {
    return !controller.isSyncingInProgress
        ? "Walks to upload"
        : "Your walks are uploading";
  }

  Widget getEndingWidget() {
    return !controller.isSyncingInProgress
        ? ElevatedButton(
            style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xffF3AE56),
                foregroundColor: Colors.white,
                fixedSize: const Size(125, 36),
                padding: EdgeInsets.zero),
            onPressed: () {
              controller.saveLocalOfflineWalksToServer();
            },
            child: const Text("Upload Now",
                style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600)))
        : const CircularProgressIndicator(color: Color(0xffF3AE56));
  }
}
