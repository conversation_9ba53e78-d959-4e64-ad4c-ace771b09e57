import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/controllers/challenges_controller.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/ui/components/mb_base_home_screen.dart';
import '../../challenge/challenge_home.dart';

class ChallengeScreen extends StatefulWidget {
  const ChallengeScreen({Key? key}) : super(key: key);

  @override
  _ChallengeScreenState createState() => _ChallengeScreenState();
}

class _ChallengeScreenState extends State<ChallengeScreen>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  ChallengesController controller = Get.put(ChallengesController());
  late TabController tabController;

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: controller.tabs.length, vsync: this);
    tabController.addListener(() {
      if (tabController.indexIsChanging) {
        controller
            .switchChallengeList(ChallengeTab.values[tabController.index]);
      }
    });
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFFBFBFB),
      child: HomeSliverScreen(
          expandedHeight: 0.0,
          primaryColor: const Color(0xff162941),
          title: 'APPLICATION_MOBILE_TITLE_CHALLENGES'.tr(),
          tabs: controller.tabs,
          tabController: tabController,
          tabFontSize: 15,
          tabFontWeight: FontWeight.w700,
          child: Obx(() {
            return controller.loading
                ? const Center(
                    child: CircularProgressIndicator.adaptive(),
                  )
                : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: ChallengeHomePage(),
                  );
          })),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
