import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/pets_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/ui/components/internet_widget_switcher.dart';
import 'package:mybuddy/ui/components/mb_base_home_screen.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/pet/widget_pets_list.dart';

import '../../../../controllers/internet_controller.dart';
import '../../../../pages/banner_widget.dart';

class PetScreen extends StatefulWidget {
  final bool implyLeading;

  const PetScreen({Key? key, this.implyLeading = false}) : super(key: key);

  @override
  _PetScreenState createState() => _PetScreenState();
}

class _PetScreenState extends State<PetScreen> {

  @override
  Widget build(BuildContext context) {
    return HomeSliverScreen(
        automaticallyImplyLeading: widget.implyLeading,
        title: 'APPLICATION_MOBILE_LABEL_MY_DOGS'.tr(),
        child: InternetWidgetSwitcher(
          onlineWidget: Column(
            children: [
              Expanded(
                child: RefreshIndicator(
                onRefresh: () async => await MbApiOwner().refreshLoginRequest(),
                  child: BlocProvider<PetsBloc>(
                    bloc: PetsBloc(livingState: LivingState.both),
                    child: const WidgetPetList(),
                  ),
                ),
              ),
              const BannerAdWidget(),
              const SizedBox(height: 20.0),
            ],
          ),
          retryMethod: () async => await MbApiOwner().refreshLoginRequest(),
        )
    );
  }
}
