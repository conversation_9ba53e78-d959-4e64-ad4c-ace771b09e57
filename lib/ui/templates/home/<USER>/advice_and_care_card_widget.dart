import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/petcarepacks_bloc.dart';
import 'package:mybuddy/models/petcarepacks.dart';
import 'package:mybuddy/ui/components/mb_card_header_widget.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/templates/petcarepack/petcarepack_folder_page.dart';
import 'package:mybuddy/ui/templates/petcarepack/petcarepack_item_page.dart';

class AdviceAndCareCard extends StatelessWidget {
  /// shows a card of advice and care
  /// It needs to be nested into a bloc [BlocProvider] of [PetCarePacksBloc]
  const AdviceAndCareCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<PetCarePacksBloc>(context);
    final Size screenSize = MediaQuery.of(context).size;

    return StreamBuilder<List<PetCarePack>>(
      stream: bloc.stream,
      builder: (BuildContext context,
          AsyncSnapshot<List<PetCarePack>> snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }
        // reversed homeCare list in order to avoid anal glands card first
        List<PetCarePack> petCarePacks = snapshot.data!.reversed.toList();

        return ListView(
          padding: const EdgeInsets.symmetric(vertical: 10.0),
          primary: false,
          shrinkWrap: true,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10.0),
              child: CardHeaderWidget(
                title:
                ('APPLICATION_MOBILE_SECTION_READ_ADVICE_TITLE'.tr()).capitalize(),
              ),
            ),
            const SizedBox(height: 10.0),
            SizedBox(
              width: screenSize.width,
              height: 200.0,
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 15.0),
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                children: petCarePacks.map((pcp) {
                  return GestureDetector(
                    onTap: (){
                      //todo factorize
                      if (pcp.items.length == 1) {
                        PetCarePackItem item = pcp.items.first;
                        if (item.items.isEmpty) {
                          Tools().navigatorPush(
                            PetCarePackItemPage(item: item, petCarePack: pcp),
                          );
                        } else {
                          Tools().navigatorPush(
                            PetCarePackPageFolder(items: item.items, title: item.title, petCarePack: pcp),
                          );
                        }
                      } else if (pcp.items.isNotEmpty) {
                        Tools().navigatorPush(
                          PetCarePackPageFolder(items: pcp.items, title: pcp.title, petCarePack: pcp),
                        );
                      }
                    },
                    child: Container(
                      margin: const EdgeInsets.all(5.0),
                      width: 155.0,
                      child: Column(
                        children: [
                          MBCachedImage(
                            imageUrl: pcp.imageUrl,
                            size: 155.0,
                            radius: 8.0,
                          ),
                          const SizedBox(height: 5.0),
                          Text(
                            pcp.title,
                            textAlign: TextAlign.center,
                            style: Theme.of(context)
                                .textTheme
                                .bodyText1
                                ?.copyWith(fontSize: 12.0),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        );
      },
    );
  }
}
