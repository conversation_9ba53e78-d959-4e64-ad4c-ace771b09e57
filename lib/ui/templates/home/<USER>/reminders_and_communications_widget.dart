import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/login_data.dart';
import 'package:mybuddy/models/message.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/ui/components/mb_home_card.dart';
import 'package:mybuddy/ui/templates/apt/appointment_page.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/message/message_page.dart';
import 'package:mybuddy/ui/templates/pet/diary/petactivity_page.dart';

class RemindersAndCommunicationsCard extends StatelessWidget {
  final LoginData? loginData;

  const RemindersAndCommunicationsCard({Key? key, this.loginData})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (loginData == null ||
        (loginData!.petActivities.isEmpty && loginData!.messages.isEmpty)) {
      return const SizedBox.shrink();
    }
    return ListView(
      padding: const EdgeInsets.all(10.0),
      primary: false,
      shrinkWrap: true,
      children: [
        Text(
          'Reminders and Communication',
          style: Theme.of(context).textTheme.bodyText1,
        ),
        const SizedBox(height: 10.0),
        _RemindersCard(petActivities: loginData!.petActivities),
        _CommunicationsCard(messages: loginData!.messages),
      ],
    );
  }
}

class _RemindersCard extends StatelessWidget {
  final List<PetActivity>? petActivities;

  const _RemindersCard({Key? key, this.petActivities}) : super(key: key);

  List<PetActivity> get _petActivities => petActivities ?? <PetActivity>[];

  @override
  Widget build(BuildContext context) {
    if (_petActivities.isEmpty) {
      return const SizedBox.shrink();
    }

    return ListView(
      primary: false,
      shrinkWrap: true,
      children: getFilteredPetActivities(context),
    );
  }
}

class _CommunicationsCard extends StatelessWidget {
  final List<MBMessage>? messages;

  const _CommunicationsCard({Key? key, this.messages}) : super(key: key);

  List<MBMessage> get _messages {
    List<MBMessage>? results = <MBMessage>[];
    if (messages != null) {
      for (var message in messages!) {
        if (message.items.isNotEmpty &&
            message.items.last.timestamp
                .isAfter(DateTime.now().subtract(const Duration(days: 3)))) {
          results.add(message);
        }
      }
    }
    return results;
  }

  @override
  Widget build(BuildContext context) {
    if (_messages.isEmpty) {
      return const SizedBox.shrink();
    }

    return ListView(
      primary: false,
      shrinkWrap: true,
      children: _messages.map((message) {
        return MBDenseHomeCard(
          icon: Icons.message,
          color: Theme.of(context).disabledColor,
          title: message.title,
          subtitle:
              Data().dateTimeToUserDateTimeStr(message.items.last.timestamp),
          onTap: () => Tools().navigatorPush(MessagePage(message: message)),
        );
      }).toList(),
    );
  }
}

List<Widget> getFilteredPetActivities(BuildContext context) {
  LoginData thisData = Data().get();
  List<Widget> widgets = [];

  /// comings part
  List<PetActivity> incomings = Data().getFilteredPetActivities(
      chronoFilter: ChronoFilter.future, livingState: LivingState.alive);
  int displayedIncomingCount = 0;
  for (PetActivity incoming in incomings) {
    if (displayedIncomingCount >= 3) break;
    Color color;
    DateTime dateTime;
    AppointmentRequest? apt;
    String title = '';
    switch (incoming.activityId) {
      case 1:
        if (incoming.dateStart == null) {
          continue; // TODO NS is it better than convert [dateTime] nullable ???
        }
        dateTime = incoming.dateStart!;
        break;
      case 2:
        if (incoming.nextOccurrence == null) {
          continue; // TODO NS is it better than convert [dateTime] nullable ???
        }
        title = '';
        dateTime = incoming.nextOccurrence!;
        break;
      default:
        if (incoming.dateStart == null) {
          continue; // TODO NS is it better than convert [dateTime] nullable ???
        }
        dateTime = incoming.dateStart!;
    }

    if (dateTime.isBefore(DateTime.now())) {
      color = Colors.orangeAccent;
    } else if (dateTime
        .isBefore(DateTime.now().add(const Duration(days: 14)))) {
      color = Colors.greenAccent;
    } else {
      break;

      /// out from foreach
    }

    switch (incoming.activityId) {
      case 1:
        apt = Data().getAppointmentWithPetActivity(incoming.id);
        if (apt == null) {
          break;
        }
        title = thisData.getService(apt.serviceId)?.name ?? '';
        widgets.add(MBDenseHomeCard(
          icon: FontAwesomeIcons.calendarAlt,
          title: title,
          subtitle: 'APPLICATION_MOBILE_TITLE_APPOINTMENT'.tr() +
              ' ' +
              Data().dateTimeToUserDateStr(dateTime),
          onTap: () {
            Tools().navigatorPush(AppointmentPage(appointment: apt!));
          },
          color: color,
        ));
        break;
      case 2:
        widgets.add(MBDenseHomeCard(
          icon: FontAwesomeIcons.clock,
          title: incoming.title ?? '',
          subtitle: 'APPLICATION_MOBILE_LABEL_REMINDER'.tr() +
              ' ' +
              Data().dateTimeToUserDateStr(dateTime),
          onTap: () {
            Tools().navigatorPush(PetActivityPage(petActivity: incoming));
          },
          color: color,
        ));
        break;
      default:
        widgets.add(MBDenseHomeCard(
          icon: FontAwesomeIcons.exclamation,
          title: incoming.title ?? incoming.comment ?? '',
          subtitle: Data().dateTimeToUserDateStr(dateTime),
          onTap: () {
            Tools().navigatorPush(PetActivityPage(petActivity: incoming));
          },
          color: color,
        ));
    }
    displayedIncomingCount++;
  }

  return widgets;
}
