import 'package:flutter/material.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/controllers/home_summary_controller.dart';
import 'package:mybuddy/controllers/home_tab_controller.dart';

class HomeSummaryWidget extends StatelessWidget {
  HomeSummaryWidget({Key? key}) : super(key: key);

  HomeSummaryController controller = HomeSummaryController.of();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.find<HomeTabController>().tabSwitcher(HomeTab.history);
      },
      child: Card(
        child: Column(
          children: [
            Obx(
              () => Visibility(
                visible: controller.loading,
                child: LinearProgressIndicator(
                  color: Theme.of(context).indicatorColor,
                  backgroundColor: Theme.of(context).disabledColor,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(15),
              child: ListView(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text("Summary",
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).primaryColor,
                          )),
                      const Icon(Icons.arrow_forward_ios_rounded, size: 15),
                    ],
                  ),
                  const SizedBox(height: 15),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Obx(() => subDataTile(context, "assets/icon/walk.png",
                          "Walks", "${controller.homeSummary.totalWalks}")),
                      Obx(
                        () => subDataTile(
                            context,
                            "assets/icon/miles_purple_icon.png",
                            "Miles",
                            controller.homeSummary.totalDistanceWithOneDecimal),
                      ),
                      Obx(
                        () => subDataTile(
                            context,
                            "assets/icon/yellow_paw.png",
                            "Charity Points",
                            controller.homeSummary.totalPointsInUSFormat),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Column(
                    children: [
                      Obx(
                        () => NeumorphicProgress(
                          percent: controller.percentToCompleteNextLevel,
                          height: 16,
                          style: const ProgressStyle(
                            variant: Color(0xffFFC67E),
                            accent: Color(0xffFF9C1F),
                            borderRadius: BorderRadius.all(Radius.circular(25)),
                            depth: -3,
                          ),
                        ),
                      ),
                      const SizedBox(height: 5),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Obx(
                            () => Text("Level ${controller.userLevel}",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context).primaryColor,
                                )),
                          ),
                          Row(
                            children: [
                              Obx(
                                () => Text(
                                    "${controller.userNextLevelRequiredPoints} points to",
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      color: Color(0xff939393),
                                    )),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              Obx(
                                () => Text("Level ${controller.userNextLevel}",
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Theme.of(context).primaryColor,
                                    )),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget subDataTile(
      BuildContext context, String icon, String title, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              icon,
              width: 16.0,
              height: 16.0,
            ),
            const SizedBox(width: 5.0),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyText2?.copyWith(
                    color: const Color(0xFF939393),
                    fontSize: 12.0,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 5.0),
        Text(
          value,
          style: Theme.of(context).textTheme.subtitle1?.copyWith(
                fontSize: 16.0,
                fontWeight: FontWeight.w600,
                color: const Color(0xff474747),
              ),
        ),
      ],
    );
  }
}
