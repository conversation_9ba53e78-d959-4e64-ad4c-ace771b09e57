import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/announcement.dart';
import 'package:mybuddy/ui/components/mb_text_shadow_widget.dart';
import 'package:mybuddy/ui/templates/announcement/announcement_page.dart';

class AnnouncementsCarousel extends StatefulWidget {
  final List<Announcement>? announcements;
  const AnnouncementsCarousel({Key? key, this.announcements}) : super(key: key);

  @override
  State<AnnouncementsCarousel> createState() => _AnnouncementsCarouselState();
}

class _AnnouncementsCarouselState extends State<AnnouncementsCarousel> {
  int _current = 0;
  late List<Announcement> _announcements;

  void _initList() {
    _announcements = widget.announcements ?? <Announcement>[];
    _announcements.sort();
  }

  @override
  void initState() {
    super.initState();
    _initList();
  }
  @override
  void didUpdateWidget(covariant AnnouncementsCarousel oldWidget) {
    super.didUpdateWidget(oldWidget);
    _initList();
  }
  @override
  Widget build(BuildContext context) {
    if (_announcements.isEmpty) {
      return const SizedBox(height: 250.0);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Stack(
        children: [
          CarouselSlider(
            items: _announcements.map(buildItem).toList(),
            options: CarouselOptions(
              autoPlay: true,
              height: MediaQuery.of(context).size.width,
              viewportFraction: 1.0,
              autoPlayInterval: const Duration(seconds: 6),
              enableInfiniteScroll: _announcements.length > 1,
              scrollDirection: Axis.horizontal,
              onPageChanged: (index, reason) {
                setState(() => _current = index);
              },
            ),
          ),
          Positioned(
            bottom: 40.0,
            left: 23.0,
            child: Row(
              children: _announcements.asMap().entries.map((entry) {
                return Container(
                  width: 8.0,
                  height: 8.0,
                  margin: const EdgeInsets.symmetric(horizontal: 2.0),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _current == entry.key
                        ? Theme.of(context).colorScheme.secondary
                        : Theme.of(context).backgroundColor,
                  ),
                );
              }).toList(),
            ),
          )
        ],
      ),
    );
  }

  Widget buildItem(Announcement announcement) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Tools().navigatorPush(
          AnnouncementPage(announcement: announcement),
        );
      },
      child: Stack(
        fit: StackFit.expand,
        children: [
          CachedNetworkImage(
            imageUrl: announcement.mainImageUrl,
            fit: BoxFit.cover,
            errorWidget: (context, url, error) => const Icon(Icons.error),
          ),
          Positioned(
            bottom: 60.0,
            left: 0.0,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const MBTextShadow(
                  label: 'Read',
                  textAlign: TextAlign.left,
                  fontSize: 16.0,
                ),
                const SizedBox(height: 15.0),
                MBTextShadow(
                  label: announcement.title.clean(),
                  textAlign: TextAlign.left,
                  fontSize: 14.0,
                  width: MediaQuery.of(context).size.width - 15.0,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
