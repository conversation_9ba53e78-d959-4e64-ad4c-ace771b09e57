import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_dynamic_links.dart';
import 'package:mybuddy/ui/templates/account/process_finished_screens.dart';
import 'package:mybuddy/ui/templates/account/reset_password.dart';

/// This class is responsible for managing the deeplinks, as this
/// is the landing screen of every dynamic link
class DynamicLinkLandingPage extends StatefulWidget {
  final String linkType;
  final PendingDynamicLinkData data;

  const DynamicLinkLandingPage(
      {Key? key, required this.linkType, required this.data})
      : super(key: key);

  @override
  State<DynamicLinkLandingPage> createState() => _DynamicLinkLandingPageState();
}

class _DynamicLinkLandingPageState extends State<DynamicLinkLandingPage> {
  late bool isLoading;
  late bool linkExpired;
  late bool emailConfirmed; // This keeps the track of email confirmation

  @override
  void initState() {
    super.initState();
    // Verify if the link is valid and proceed to next step
    isLoading = false;
    linkExpired = false;
    emailConfirmed = false;
    _kickOffFlow();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _renderBody(),
    );
  }

  Widget _renderBody() {
    if (isLoading) {
      return SizedBox(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: const Center(
          heightFactor: 2.0,
          child: CircularProgressIndicator.adaptive(),
        ),
      );
    }

    if (linkExpired) {
      if (widget.linkType == LINK_TYPE_FORGOT_PASSWORD) {
        return ResetPasswordLinkExpired(
          data: widget.data,
        );
      }
      return EmailVerificationLinkExpired(
        data: widget.data,
      );
    }

    if (emailConfirmed) {
      return EmailVerified(type: widget.linkType,);
    }

    return Container();
  }

  void _kickOffFlow() async {
    setState(() {
      isLoading = true;
    });

    bool valid = false;
    String linkType = widget.linkType;

    // Verify email change link is done explicitly due to it being a tokenized request
    if (linkType == LINK_TYPE_VERIFY_CHANGE_EMAIL) {
      valid = await _checkIfChangeEmailLinkIsValid();
    } else {
      valid = await _checkIfLinkIsValid();
    }

    if (valid) {
      // We go on to confirm the user email
      switch (linkType) {
        case LINK_TYPE_VERIFY_EMAIL:
          _confirmEmailFlow();
          break;
        case LINK_TYPE_FORGOT_PASSWORD:
          _forgotPasswordFlow();
          break;
        case LINK_TYPE_VERIFY_CHANGE_EMAIL:
          _verifyChangeEmailFlow();
          break;
      }
    } else {
      // Show link expired view
      setState(() {
        isLoading = false;
        linkExpired = true;
      });
    }
  }

  /// This checks if the link has expired or not by sending the code received
  /// in the dynamic link
  Future<bool> _checkIfLinkIsValid() async {
    String linkType = widget.linkType;
    Uri uri = widget.data.link;
    String code = uri.queryParameters['code'] ?? "";
    String email = uri.queryParameters['email'] ?? "";

    MBResponse response =
        await MbApiOwner().verifyLinkCodeExpiration(null, code, email, linkType);

    return response.success;
  }

  /// This checks if the link has expired or not by sending the code received
  /// in the dynamic link
  Future<bool> _checkIfChangeEmailLinkIsValid() async {
    Uri uri = widget.data.link;
    String code = uri.queryParameters['code'] ?? "";
    String email = uri.queryParameters['old_email'] ?? "";

    MBResponse response = await MbApiOwner()
        .verifyChangeEmailLinkCodeExpiration(null, code, email);

    return response.success;
  }

  /// Confirms the email that is received in the dynamic link
  void _confirmEmailFlow() async {
    Uri uri = widget.data.link;
    String code = uri.queryParameters['code'] ?? "";
    String email = uri.queryParameters['email'] ?? "";

    MBResponse response =
        await MbApiOwner().userEmailVerification(context, code, email);

    setState(() {
      isLoading = false;
      emailConfirmed = response.success;
    });
  }

  /// Change email verification that is received in the dynamic link
  void _verifyChangeEmailFlow() async {
    Uri uri = widget.data.link;
    String code = uri.queryParameters['code'] ?? "";
    String oldEmail = uri.queryParameters['old_email'] ?? "";
    String newEmail = uri.queryParameters['new_email'] ?? "";

    MBResponse response = await MbApiOwner()
        .userChangedEmailVerification(context, code, oldEmail, newEmail);

    setState(() {
      isLoading = false;
      emailConfirmed = response.success;
    });
  }

  /// Forgot password flow will be handled in its own screen
  void _forgotPasswordFlow() {
    Uri uri = widget.data.link;
    String code = uri.queryParameters['code'] ?? "";
    String email = uri.queryParameters['email'] ?? "";
    Tools().navigatorPop();
    Tools().navigatorPush(ResetPassword(
      email: email,
      code: code,
    ));
  }
}
