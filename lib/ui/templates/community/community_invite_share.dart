import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';

import '../../../Api/mb_api_community.dart';
import '../../../models/community.dart';

class InviteShare extends StatefulWidget {
  final Team team;

  const InviteShare({required this.team, Key? key}) : super(key: key);

  @override
  State<InviteShare> createState() => _InviteShareState();
}

class _InviteShareState extends State<InviteShare> {
  late String link;
  late bool isLoading;

  @override
  void initState() {
    super.initState();
    link = "";
    isLoading = false;
    getLink();
  }

  Future<void> getLink() async {
    setState(() {
      isLoading = true;
    });
    String localLink = await MbApiCommunity().getPublicLink(null, widget.team);
    setState(() {
      link = localLink;
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? const Center(
            heightFactor: 4.0,
            child: CircularProgressIndicator.adaptive(),
          )
        : SingleChildScrollView(
            child: Column(
              children: [
                const SizedBox(height: 20),
                _linkCopyCard(),
                const SizedBox(height: 20),
                _linkShareCard(),
                const SizedBox(height: 20),
                _shareQRCard(),
                const SizedBox(height: 20),
              ],
            ),
          );
  }

  Widget _linkCopyCard() {
    return Card(
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 15),
        child: Column(
          children: [
            _headerTile(
              icon: "assets/icon/copy_icon.png",
              title: 'APPLICATION_MOBILE_COMMUNITY_INVITATION_COPY'
                  .tr("Copy Link"),
              subTitle: 'APPLICATION_MOBILE_COMMUNITY_INVITATION_COPY_DESC'.tr(
                  "You can use this public link to invite your friends to this group"),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 15.0),
              child: TextField(
                decoration: InputDecoration(
                    fillColor: const Color(0xffF2F2F2),
                    filled: true,
                    hintText: link,
                    hintStyle: Theme.of(context)
                        .textTheme
                        .bodySmall
                        ?.copyWith(color: Colors.black, fontSize: 12),
                    border: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xff9C9C9C)),
                        borderRadius: BorderRadius.circular(8))),
                keyboardType: TextInputType.emailAddress,
                enableSuggestions: false,
                autocorrect: false,
                readOnly: true,
                textInputAction: TextInputAction.done,
              ),
            ),
            _customButton(
                title: 'APPLICATION_MOBILE_COMMUNITY_INVITATION_COPY_BUTTON'
                    .tr("Copy"),
                onPressed: () async {
                  Clipboard.setData(ClipboardData(text: link));
                  Tools().common.showMessage(
                      context,
                      'APPLICATION_MOBILE_COMMUNITY_INVITATION_LINK_COPIED'
                          .tr());
                })
          ],
        ),
      ),
    );
  }

  Widget _linkShareCard() {
    return Card(
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 15),
        child: Column(
          children: [
            _headerTile(
              icon: "assets/icon/link.png",
              title: 'APPLICATION_MOBILE_COMMUNITY_INVITATION_SHARE_LINK'
                  .tr("Share Link"),
              subTitle: 'APPLICATION_MOBILE_COMMUNITY_INVITATION_SHARE_DESC'
                  .tr("Send Invitation via SMS, email or through another app"),
            ),
            const SizedBox(
              height: 20,
            ),
            _customButton(
                title:
                    'APPLICATION_MOBILE_COMMUNITY_INVITATION_SHARE'.tr("Share"),
                onPressed: () async {
                  await Share.share("Join My Group on WoofTrax\n" + link,
                      subject: "Join My Group on WoofTrax");
                })
          ],
        ),
      ),
    );
  }

  Widget _shareQRCard() {
    return Card(
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.only(left: 18, right: 18, top: 15, bottom: 5),
        child: Column(
          children: [
            _headerTile(
              icon: "assets/icon/qr.png",
              title: 'APPLICATION_MOBILE_COMMUNITY_INVITATION_SHARE_QR'.tr(),
              subTitle:
                  'APPLICATION_MOBILE_COMMUNITY_INVITATION_SHARE_QR_DESC'.tr(),
            ),
            const SizedBox(height: 5),
            QrImage(
              data: link,
              version: QrVersions.auto,
              size: 100,
            )
          ],
        ),
      ),
    );
  }

  Widget _headerTile(
      {required String icon, required String title, required String subTitle}) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.secondary,
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Image.asset(icon),
        ),
      ),
      title: Text(
        title,
        style: Theme.of(context)
            .textTheme
            .titleLarge
            ?.copyWith(color: const Color(0xff192943), fontSize: 15),
      ),
      subtitle: Padding(
        padding: const EdgeInsets.only(top: 5.0),
        child: Text(
          subTitle,
          style: Theme.of(context)
              .textTheme
              .titleMedium
              ?.copyWith(color: const Color(0xff070707), fontSize: 12),
        ),
      ),
    );
  }

  Widget _customButton({required String title, required Function onPressed}) {
    return Row(
      children: [
        Expanded(
          child: RawMaterialButton(
            constraints: const BoxConstraints(minWidth: 60.0, minHeight: 50.0),
            onPressed: () {
              onPressed();
            },
            shape: const StadiumBorder(
              side: BorderSide(color: Color(0xff9C9C9C)),
            ),
            child: Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
            ),
          ),
        ),
      ],
    );
  }
}
