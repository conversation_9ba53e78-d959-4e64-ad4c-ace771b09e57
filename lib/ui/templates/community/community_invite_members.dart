import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/controllers/teammates_controller.dart';
import 'package:mybuddy/controllers/teams_controller.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/ui/templates/community/community_invite_email.dart';

import '../../../class/data.dart';
import '../../../models/community.dart';
import '../../components/mb_base_home_screen.dart';
import 'community_invite_contact_members.dart';
import 'community_invite_share.dart';

class CommunityInviteMembers extends StatefulWidget {
  final Team team;

  const CommunityInviteMembers({
    required this.team,
    Key? key,
  }) : super(key: key);

  @override
  State<CommunityInviteMembers> createState() => _CommunityInviteMembersState();
}

class _CommunityInviteMembersState extends State<CommunityInviteMembers>
    with SingleTickerProviderStateMixin {
  late TabController tabController;
  late List<InvitedUser> invitedUsers;
  late bool isLoading, isInternetIssue;

  @override
  void initState() {
    super.initState();
    invitedUsers = [];
    isLoading = true;
    isInternetIssue = false;
    _initializeTabController();
    _fetchSentInvitations();
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Container(
        color: const Color(0xFFFBFBFB),
        child: HomeSliverScreen(
          expandedHeight: 0.0,
          automaticallyImplyLeading: true,
          primaryColor: const Color(0xff162941),
          title: 'APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE'.tr('Invite'),
          tabs: [
            'APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_CONTACTS'.tr('Contacts'),
            'APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_EMAIL'.tr('Email'),
            'APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_SHARE'.tr('Share')
          ],
          tabController: tabController,
          tabFontSize: 15,
          tabFontWeight: FontWeight.w700,
          child: isLoading
              ? const Center(
                  heightFactor: 4.0,
                  child: CircularProgressIndicator.adaptive(),
                )
              : isInternetIssue
                  ? _internetIssueWidget(context)
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: TabBarView(
                        controller: tabController,
                        physics: const NeverScrollableScrollPhysics(),
                        children: [
                          InviteContactMembers(
                            team: widget.team,
                            invitedUsers: invitedUsers,
                            inviteFunction: _inviteMember,
                          ),
                          InviteEmail(
                            team: widget.team,
                            invitedUsers: invitedUsers,
                            inviteFunction: _inviteMember,
                          ),
                          InviteShare(
                            team: widget.team,
                          )
                        ],
                      ),
                    ),
        ),
      ),
    );
  }

  void _initializeTabController() {
    tabController = TabController(length: 3, vsync: this);
  }

  void _fetchSentInvitations() async {
    setState(() {
      isLoading = true;
    });

    MBResponse response =
        await MbApiCommunity().getInvitedUsers(null, widget.team.id!);
    if (response.success) {
      log(response.body.toString());
      List<InvitedUser> users = response.entity as List<InvitedUser>;
      setState(() {
        isLoading = false;
        isInternetIssue = false;
        invitedUsers = users;
      });
    } else if (response.errorMessage == "NOT ONLINE") {
      setState(() {
        isLoading = false;
        isInternetIssue = true;
      });
    }
  }

  Widget _internetIssueWidget(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          "assets/images/no_activity.png",
          width: MediaQuery.of(context).size.width * 0.45,
        ),
        const SizedBox(
          height: 35,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal:15),
          child: Text(
            'APPLICATION_MOBILE_MESSAGE_NETWORK_REQUIRED'.tr(),
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.headline2?.copyWith(
                fontSize: 16, fontWeight: FontWeight.w700, color: Colors.black),
          ),
        ),
        const SizedBox(
          height: 25,
        ),
        RawMaterialButton(
          onPressed: () async {
            _fetchSentInvitations();
          },
          fillColor: Theme.of(context).colorScheme.secondary,
          padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 15),
          constraints: const BoxConstraints(minWidth: 242, minHeight: 57),
          shape: const StadiumBorder(),
          child: Text(
            'WOOFTRAX__BUTTON_INTERNET_RETRY_LABEL'.tr(),
            style: Theme.of(context).textTheme.headline6?.copyWith(
                fontWeight: FontWeight.w500, color: Colors.white, fontSize: 14),
          ),
        )
      ],
    );
  }

  Future<InvitationStatus> _inviteMember(
      String? firstName, String? lastName, String email) async {
    Friend friend = Friend(ownerEmail: email);
    friend.ownerFirstName = firstName;
    friend.ownerLastName = lastName;

    MBResponse response = await MbApiCommunity()
        .inviteSingleFriendByMail(context, widget.team.id!, friend);
    String message = "";

    if (response.success) {
      String inviteStatus = response.body['inviteStatus'];
      if (inviteStatus == 'INVITE_SENT_SUCCESSFULLY') {
        message = 'APPLICATION_MOBILE_COMMUNITY_INVITATION_SENT'.tr();
        List<InvitedUser> newUserList = [...invitedUsers];
        newUserList.removeWhere((element) => element.email == email);
        newUserList.add(InvitedUser(
            email: email,
            firstName: firstName,
            lastName: lastName,
            lastInvite:
                LastInvite(date: DateTime.now().toUtc().toIso8601String())));
        setState(() {
          invitedUsers = newUserList;
        });
      } else if (inviteStatus == 'USER_ADDED_TO_GROUP') {
        message =
            'APPLICATION_MOBILE_COMMUNITY_INVITATION_USER_ADDED_TO_PACK'.tr();
        Get.find<TeamsController>().increaseTeamMemberCount(widget.team.id!);
      } else if (inviteStatus == 'ALREADY_MEMBER_OF_THE_GROUP') {
        message =
            'APPLICATION_MOBILE_COMMUNITY_INVITATION_USER_ALREADY_IN_PACK'.tr();
      } else if (inviteStatus == 'INVITE_ALREADY_SENT_SUCCESSFULLY') {
        message =
            'APPLICATION_MOBILE_COMMUNITY_INVITATION_USER_ALREADY_SENT'.tr();
      }
    }

    return InvitationStatus(response.success, message);
  }
}
