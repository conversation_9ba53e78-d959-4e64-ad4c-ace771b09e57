import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:mybuddy/class/community_validations.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/ui/components/loading_overlay.dart';

import '../../../Api/mb_api_community.dart';
import '../../../Api/mb_response.dart';
import '../../../Tools/tools.dart';
import '../../../app_theme.dart';
import '../../../class/data.dart';
import '../../../controllers/teams_controller.dart';
import '../../../models/community.dart';
import '../../components/mb_stackable_avatar.dart';

class ContactsList extends StatefulWidget {
  ContactsList(
      {required this.persons,
      required this.desc,
      required this.team,
      this.invitedUsers,
      this.inviteFunction,
      Key? key})
      : super(key: key);

  final List<Friend> persons;
  late Team team;
  final String desc;
  final List<InvitedUser>? invitedUsers;
  final Future<InvitationStatus> Function(String?, String?, String)?
      inviteFunction;

  @override
  State<ContactsList> createState() => _ContactsListState();
}

class _ContactsListState extends State<ContactsList> {
  late bool isInviting;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    isInviting = false;
  }

  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: isInviting,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 15.0),
            child: Text(
              widget.desc,
              style: Theme.of(context)
                  .textTheme
                  .bodySmall
                  ?.copyWith(color: const Color(0xffA2B2C6), fontSize: 13),
            ),
          ),
          Expanded(
            child: (widget.persons.isEmpty)
                ? Center(
                    child: Text('APPLICATION_MOBILE_COMMUNITY_NO_CONTACT'
                        .tr('No contacts')))
                : ListView.separated(
                    shrinkWrap: true,
                    padding: const EdgeInsets.fromLTRB(10.0, 5.0, 10.0, 80.0),
                    itemCount: widget.persons.length,
                    itemBuilder: (BuildContext context, int index) {
                      Friend person = widget.persons.elementAt(index);
                      Map<String, dynamic> buildLevel =
                          person.ownerTotalPoints.toLevel();

                      return contactTile(person, buildLevel, context);
                    },
                    separatorBuilder: (_, int index) =>
                        const Divider(height: 0.0),
                  ),
          ),
        ],
      ),
    );
  }

  Widget contactTile(Friend person, Map buildLevel, context) {
    bool? added = person.isTeamMember;
    List<InvitedUser> filteredInvitation = widget.invitedUsers
            ?.where((element) => element.email == person.ownerEmail)
            .toList() ??
        [];

    bool buttonCondition() {
      return added == true ||
          (filteredInvitation.isNotEmpty &&
              !CommunityValidations.checkResendValidity(filteredInvitation.first.lastInvite));
    }

    _onButtonTap(Friend person) async {
      MBResponse response;

      setState(() {
        isInviting = true;
      });

      if (person.ownerId == null && widget.inviteFunction != null) {
        if (filteredInvitation.isNotEmpty &&
            !CommunityValidations.checkResendValidity(filteredInvitation.first.lastInvite)) {
          Tools().common.showMessage(
              context,
              CommunityValidations.resendMessage(filteredInvitation.first.lastInvite),
              Colors.white,
              Colors.black);
        } else {
          InvitationStatus status = await widget.inviteFunction!(
              person.ownerFirstName, person.ownerLastName, person.ownerEmail!);

          if (status.success) {
            Tools().common.showMessage(
                  context,
                  status.message,
                );
          }
        }
      } else {
        response = await MbApiCommunity().addWooftraxUser(
          context,
          widget.team.id,
          person,
        );

        if (response.success) {
          person.isTeamMember = true;
          added = true;
          Tools().common.showMessage(
                context,
                'APPLICATION_MOBILE_COMMUNITY_MEMBER_ADDED_TOAST'
                    .tr('Member Added Successfully !'),
              );
        }
      }

      setState(() {
        isInviting = false;
      });
    }

    return ListTile(
      dense: true,
      contentPadding: EdgeInsets.zero,
      minLeadingWidth: 0,
      // selected: CreateCommunityPack.of(context, rebuild: false).peopleOnWooftrax.contains(person),
      selectedTileColor: Theme.of(context).backgroundColor,
      // onTap: () => CreateCommunityPack.of(context).handlePeople(person),
      leading: MBAvatar(
        imageUrl: person.ownerImageUrl,
        size: 40,
        foregroundColor: AppTheme.woofTraxTheme.backgroundColor,
        backgroundColor: AppTheme.woofTraxTheme.primaryColorLight,
        noImageUrl: Image.asset("assets/images/user_placeholder.jpg",fit: BoxFit.contain,),
      ),
      title: Text(
        person.fullName,
        overflow: TextOverflow.ellipsis,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: Colors.black, fontSize: 13, fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        person.ownerEmail!,
        style: Theme.of(context).textTheme.subtitle1?.copyWith(
              color: const Color(0xff070707),
              fontSize: 12,
            ),
      ),
      trailing: RawMaterialButton(
        elevation: buttonCondition() ? 0 : 2,
        constraints: const BoxConstraints(minWidth: 60.0, minHeight: 36.0),
        onPressed: added == true
            ? null
            : () {
                _onButtonTap(person);
              },
        fillColor: buttonCondition()
            ? const Color(0xffE8ECF0)
            : Theme.of(context).colorScheme.secondary,
        shape: const StadiumBorder(
          side: BorderSide.none,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Text(
          person.ownerId != null
              ? added == true
                  ? 'APPLICATION_MOBILE_BUTTON_LABEL_ADDED'.tr('Added')
                  : 'APPLICATION_MOBILE_BUTTON_LABEL_ADD'.tr('Add')
              : filteredInvitation.isEmpty
                  ? 'APPLICATION_MOBILE_BUTTON_LABEL_INVITE'.tr('Invite')
                  : CommunityValidations.checkResendValidity(filteredInvitation.first.lastInvite)
                      ? 'APPLICATION_MOBILE_BUTTON_LABEL_RESEND'.tr()
                      : 'APPLICATION_MOBILE_BUTTON_LABEL_INVITE_SENT'
                          .tr('Invite Sent'),
          style: Theme.of(context).textTheme.headline6?.copyWith(
                fontWeight:
                    buttonCondition() ? FontWeight.w700 : FontWeight.w500,
                fontSize: 13,
                color:
                    buttonCondition() ? const Color(0xffA2B2C6) : Colors.white,
              ),
        ),
      ),
    );
  }
}
