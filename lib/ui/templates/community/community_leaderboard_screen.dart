import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/leaderboard_user_detail_controller.dart';
import 'package:mybuddy/ui/components/pagination_widget.dart';
import 'package:mybuddy/ui/templates/community/widgets/leaderboard_stats_widget.dart';

import '../../../app_theme.dart';
import '../../../controllers/group_leaderboard_controller.dart';
import '../../../models/community.dart';
import '../../components/mb_stackable_avatar.dart';
import 'details/leaderboard_user_details_screen.dart';
import 'widgets/leaderboard_filter_widget.dart';

class TeamLeaderboardScreen extends StatefulWidget {
  const TeamLeaderboardScreen({Key? key, required this.team}) : super(key: key);
  final Team team;

  @override
  State<TeamLeaderboardScreen> createState() => _TeamLeaderboardScreenState();
}

class _TeamLeaderboardScreenState extends State<TeamLeaderboardScreen> {
  late GroupLeaderboardController controller;

  @override
  void initState() {
    super.initState();
    controller = GroupLeaderboardController.of(
        widget.team.id ?? 0, widget.team.createdAt ?? DateTime.now());
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<GroupLeaderboardController>(
      builder: (GetxController cont) {
        return Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [_currentUserPosition(), _filterButton()],
            ),
            LeaderboardStatsWidget(controller),
            Expanded(
              child: Card(
                margin: const EdgeInsets.only(bottom: 10),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10.0),
                  child: PaginationWidget(
                      controller: controller,
                      emptyWidget: const Center(child: Text("No data found")),
                      separator: true,
                      child: (context, index) {
                        return _userRankTile(index);
                      }),
                ),
              ),
            )
          ],
        );
      },
    );
  }

  /// This method is used to show the current user rank in the leaderboard
  Widget _currentUserPosition() {
    return Obx(
      () => RichText(
        text: TextSpan(
            text: "Your Position: ",
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF7F7F7F),
            ),
            children: [
              TextSpan(
                  text: "${controller.userPosition}",
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).colorScheme.secondary,
                  )),
            ]),
      ),
    );
  }

  /// This method is used to show the filter button by which user can select the filter from the bottom sheet
  Widget _filterButton() {
    return TextButton(
      style: TextButton.styleFrom(
        padding: EdgeInsets.zero,
      ),
      onPressed: () async {
        showModalBottomSheet(
          context: context,
          builder: (_) => LeaderboardFilterWidget(controller, widget.team),
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
        );
        // showMonthPicker(context, onSelected: (month, year){});
      },
      child: Row(
        children: [
          Text(
            controller.getSelectedMonthAndYear(),
            style: const TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: Color(0xff474747),
            ),
          ),
          const SizedBox(width: 5),
          const Icon(
            Icons.keyboard_arrow_down_rounded,
            color: Color(0xff474747),
          ),
        ],
      ),
    );
  }

  /// This method is used to show user rank tile in the list view
  Widget _userRankTile(int index) {
    // Check if the current user is in the list
    bool isMe = controller.isMyRank(index);

    // Set color based on the current user
    Color color = isMe
        ? Theme.of(context).colorScheme.secondary
        : const Color(0xff474747);

    return GestureDetector(
      onTap: () {
        Tools()
            .navigatorPush(LeaderboardUserDetailsScreen(
              user: controller.dataList[index],
              team: widget.team,
            ))
            .then((value) => Get.delete<LeaderboardUserDetailController>());
      },
      child: Container(
        color: Colors.transparent,
        child: Row(
          children: [
            _rankWidget(index, color),
            const SizedBox(width: 5),
            _userWidget(index, color),
            const SizedBox(width: 15),
            _walksDetailWidget(isMe, index, color),
          ],
        ),
      ),
    );
  }

  /// This method is used to show rank widget in the list view
  /// For the first three records, it will show badge image
  /// otherwise it will show rank number
  Widget _rankWidget(int index, Color color) {
    if (index < 3) {
      return Image.asset(
        "assets/icon/badge-${index + 1}.png",
        width: 24,
        height: 24,
      );
    }
    return SizedBox(
      width: 25,
      child: Center(
        child: Text(
          "${index + 1}.",
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w700,
            fontSize: 15,
          ),
        ),
      ),
    );
  }

  /// This method is used to show user first name and image in the list view
  Widget _userWidget(int index, Color color) {
    return Expanded(
      child: Row(
        children: [
          MBAvatar(
            imageUrl: controller.dataList[index].userImage,
            noImageUrl: Image.asset(
              "assets/images/user_placeholder.jpg",
              fit: BoxFit.contain,
            ),
            size: 38,
            foregroundColor: AppTheme.woofTraxTheme.colorScheme.surface,
            backgroundColor: AppTheme.woofTraxTheme.primaryColorLight,
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              controller.dataList[index].fullName,
              style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  overflow: TextOverflow.ellipsis),
            ),
          ),
        ],
      ),
    );
  }

  /// This method is used to show walks and distance details of a user in the list view
  Widget _walksDetailWidget(bool isMe, int index, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        RichText(
            text: TextSpan(
          text: controller.dataList[index].commaSeperatedDistanceInt,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
          children: [
            TextSpan(
              text: " mi",
              style: TextStyle(
                color: isMe ? color : const Color(0xff595959),
                fontWeight: FontWeight.w500,
              ),
            )
          ],
        )),
        Text(
          "${controller.dataList[index].workouts ?? 0} Walks",
          style: TextStyle(
            color: isMe ? color : const Color(0xff939393),
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
        )
      ],
    );
  }
}
