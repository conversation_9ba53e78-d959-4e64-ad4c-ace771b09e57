import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/models/workout.dart';
import 'package:mybuddy/ui/components/no_internet_widget.dart';
import 'package:mybuddy/ui/templates/community/community_view_members.dart';

import '../../../controllers/internet_controller.dart';
import '../../../pages/banner_widget.dart';
import '../../components/mb_base_home_screen.dart';
import 'community_leaderboard_screen.dart';
import 'create_community_pack.dart';
import 'details/team_details_screens.dart';

class InheritedTeam extends InheritedWidget {
  final TeamScreenState data;

  const InheritedTeam({
    Key? key,
    required this.data,
    required Widget child,
  }) : super(key: key, child: child);

  @override
  bool updateShouldNotify(InheritedTeam oldWidget) => true; //old.data != data;
}

class TeamScreen extends StatefulWidget {
  final Team team;
  final String routeName;

  TeamScreen({Key? key, required this.team})
      : routeName = 'MessagePage-${team.messageId}',
        super(key: key);

  @override
  TeamScreenState createState() => TeamScreenState();

  static TeamScreenState of(BuildContext context, {bool rebuild = true}) {
    return rebuild
        ? context.dependOnInheritedWidgetOfExactType<InheritedTeam>()!.data
        : context.findAncestorWidgetOfExactType<InheritedTeam>()!.data;
  }
}

class TeamScreenState extends State<TeamScreen>
    with SingleTickerProviderStateMixin {
  late TabController controller;

  List<String> tabs = <String>[];

  late Team _team;

  Team get teamPack => _team;

  final int _defaultPerPageCount = 10;
  final int nextPageThreshold = 2;
  int _pageNumber = 1;

  bool hasMore = true;
  bool error = false;
  bool loading = true;

  late List<Workout> workouts;

  @override
  void initState() {
    super.initState();
    tabs.add('APPLICATION_MOBILE_TITLE_LEADERBOARD'.tr('Leaderboard'));
    tabs.add('APPLICATION_MOBILE_COMMUNITY_ACTIVITIES'.tr("Activities"));
    tabs.add('APPLICATION_MOBILE_COMMUNITY_MEMBERS'.tr("Members"));
    controller = TabController(length: tabs.length, vsync: this);
    _team = widget.team;
    workouts = <Workout>[];
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: HomeSliverScreen(
        expandedHeight: 0.0,
        automaticallyImplyLeading: true,
        primaryColor: const Color(0xff162941),
        title: _team.name,
        tabs: tabs.map((e) => e).toList(),
        tabController: controller,
        tabFontSize: 15,
        tabFontWeight: FontWeight.w700,
        backgroundColor: const Color.fromRGBO(251, 251, 251, 1),
        actions: [
          IconButton(
            onPressed: () {
              menuBottomSheet();
            },
            icon: const Icon(
              Icons.more_vert_rounded,
              color: Color(0xff162941),
            ),
          ),
        ],
        child: bodyWidget(),
      ),
    );
  }

  Widget bodyWidget() {
    return Obx(() {
      if (!InternetController.of().isConnected) {
        return const NoInternetWidget();
      }
      return Column(
        children: [
          Expanded(
            child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: InheritedTeam(
                  data: this,
                  child: TabBarView(
                    physics: const NeverScrollableScrollPhysics(),
                    controller: controller,
                    children: [
                      TeamLeaderboardScreen(
                        team: _team,
                      ),
                      const TeamWalksScreen(),
                      CommunityMembersPage(team: _team),
                    ],
                  ),
                )),
          ),
          const BannerAdWidget(),
          const SizedBox(height: 20.0),
        ],
      );
    });
  }

  Future<void> fetchTeamWalks({bool reload = false}) async {
    try {
      if (reload) {
        _pageNumber = 1;
      }

      var response = await MbApiCommunity().getTeamWalks(
        _team.id,
        page: _pageNumber,
        perPageCount: _defaultPerPageCount,
      );

      if (response.success) {
        List<Workout> fetchedWorkouts =
            Workout.parseWorkouts(response.body['workouts']);

        if (mounted) {
          setState(() {
            hasMore = fetchedWorkouts.length == _defaultPerPageCount;
            loading = false;
            _pageNumber = _pageNumber + 1;
            workouts.addAll(fetchedWorkouts);
          });
        }
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      setState(() {
        loading = false;
        error = true;
      });
    }
  }

  void retry() {
    setState(() {
      loading = true;
      error = false;
    });
    fetchTeamWalks();
  }

  bool filterSettings(PackSettings packSetting) {
    late List<PackSettings> permissions;
    if (_team.isAdmin) {
      permissions = [
        PackSettings.edit,
        PackSettings.delete,
      ];
    } else {
      permissions = [
        PackSettings.leave,
      ];
    }
    return permissions.contains(packSetting);
  }

  void menuBottomSheet() {
    showModalBottomSheet(
        context: context,
        builder: (BuildContext bc) {
          List<PackSettings> options =
              PackSettings.values.where(filterSettings).toList();
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                height: 10,
              ),
              ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (ctx, index) {
                    var text = options[index].toText()!;
                    return TextButton(
                        onPressed: () async {
                          Tools().navigatorPop();
                          bool? success = await options[index]
                              .toPush(context, _team, updatedTeam: (team) {
                            setState(() {
                              _team = team;
                            });
                          });
                          if (success != null && success) {
                            Team? t = Data().get().getTeam(_team.id);
                            if (t != null) {
                              setState(() {
                                _team = t;
                              });
                            }
                          }
                        },
                        child: Center(
                            child: Text(
                          text,
                          style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: text == "Delete Group"
                                  ? const Color(0xffE14646)
                                  : Theme.of(context).colorScheme.secondary),
                        )));
                  },
                  separatorBuilder: (ctx, index) {
                    return const Divider();
                  },
                  itemCount: options.length),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        });
  }
}

enum PackSettings { edit, delete, leave }

extension MapPackSettings on PackSettings {
  String? toText() {
    switch (this) {
      case PackSettings.edit:
        return 'APPLICATION_MOBILE_COMMUNITY_EDIT'.tr('Edit');
      case PackSettings.delete:
        return 'APPLICATION_MOBILE_COMMUNITY_DELETE_PACK'.tr('Delete Group');
      case PackSettings.leave:
        return 'APPLICATION_MOBILE_COMMUNITY_LEAVE_PACK'.tr();
      default:
        return '';
    }
  }

  Future<bool?> toPush(BuildContext context, Team team,
      {Function(Team)? updatedTeam}) async {
    switch (this) {
      case PackSettings.edit:
        Tools().navigatorPush(CreateCommunityPack(
          team: team,
          updatedTeam: (t) {
            if (updatedTeam != null) {
              updatedTeam(t);
            }
          },
        ));
        return true;
      case PackSettings.leave:
        return await Tools().common.showWarningDialog(
          context,
          validTitle: 'APPLICATION_MOBILE_COMMUNITY_PACK_LEAVE'.tr("Leave"),
          rejectTitle: 'APPLICATION_MOBILE_LABEL_CANCEL'.tr("Cancel"),
          bgColor: const Color(0xffF2F0E4),
          textColor: Theme.of(context).colorScheme.secondary,
          title: 'APPLICATION_MOBILE_COMMUNITY_WARNING'.tr("Warning"),
          content: RichText(
              text: TextSpan(
                  text: 'APPLICATION_MOBILE_COMMUNITY_PACK_LEAVE_MEMBER_DESC1'
                      .tr("Are you sure you want to leave "),
                  style:
                      TextStyle(color: Theme.of(context).colorScheme.secondary),
                  children: [
                TextSpan(
                    text: team.name,
                    style: const TextStyle(fontWeight: FontWeight.bold)),
                TextSpan(
                    text: 'APPLICATION_MOBILE_COMMUNITY_PACK_LEAVE_MEMBER_DESC2'
                        .tr(" ?\n\nLeaving group will erase all your recorded data permanently from this group."))
              ])),
          onValid: () async {
            MBResponse response =
                await MbApiCommunity().removeTeam(context, team);

            Tools().navigatorPop(value: response.success);

            if (response.success) {
              Tools().common.showMessage(
                  context,
                  'APPLICATION_MOBILE_COMMUNITY_PACK_DELETE_MESSAGE'
                      .tr("Group left successfully"));
            }
          },
        );
      case PackSettings.delete:
        return await Tools().common.showWarningDialog(
          context,
          validTitle: 'APPLICATION_MOBILE_COMMUNITY_PACK_DELETE'.tr("Delete"),
          rejectTitle: 'APPLICATION_MOBILE_LABEL_CANCEL'.tr("Cancel"),
          bgColor: const Color(0xffF2F0E4),
          textColor: Theme.of(context).colorScheme.secondary,
          title: 'APPLICATION_MOBILE_COMMUNITY_WARNING'.tr("Warning"),
          content: RichText(
              text: TextSpan(
                  text: 'APPLICATION_MOBILE_COMMUNITY_PACK_DELETE_DESC1'
                      .tr("Are you sure you want to delete "),
                  style:
                      TextStyle(color: Theme.of(context).colorScheme.secondary),
                  children: [
                TextSpan(
                    text: team.name,
                    style: const TextStyle(fontWeight: FontWeight.bold)),
                TextSpan(
                    text: 'APPLICATION_MOBILE_COMMUNITY_PACK_DELETE_DESC2'.tr(
                        " ?\n\nDeleting this group will remove it from your My Groups list and also delete this group's walking history. All members' individual walking history is retained."))
              ])),
          onValid: () async {
            MBResponse response =
                await MbApiCommunity().removeTeam(context, team);

            Tools().navigatorPop(value: response.success);

            if (response.success) {
              Tools().common.showMessage(
                  context,
                  'APPLICATION_MOBILE_COMMUNITY_PACK_DELETE_MESSAGE'
                      .tr("Group has been deleted"));
            }
          },
        );
      default:
        return false;
    }
  }
}
