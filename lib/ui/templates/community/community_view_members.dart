import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/app_theme.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/teammates_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/models/login_data.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/components/popup_menu_button.dart';
import 'package:mybuddy/ui/templates/community/community_invite_members.dart';

import '../../../controllers/teams_controller.dart';
import '../../components/pagination_widget.dart';

class CommunityMembersPage extends StatelessWidget {
  CommunityMembersPage({Key? key, required this.team}) : super(key: key) {
    teammatesController.initTeamId(team.id);
  }

  final Team team;
  final TeammatesController teammatesController = Get.put(
    TeammatesController(),
  );
  final Owner _owner = Data().get().owner.copy();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (team.canInvite || team.isAdmin) _addMemberTile(),
        _searchField(context, teammatesController),
        _teamList(context, teammatesController),
      ],
    );
  }

  Widget _addMemberTile() {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        backgroundColor: const Color(0xff0B5F5D),
        child: Padding(
          padding: const EdgeInsets.all(15.0),
          child: Image.asset("assets/icon/add_member.png"),
        ),
        radius: 27,
      ),
      title: Text(
        'APPLICATION_MOBILE_COMMUNITY_MEMBER_ADD'.tr("Add Members"),
        style: const TextStyle(color: Color(0xff162941), fontSize: 17),
      ),
      trailing: const Icon(
        Icons.keyboard_arrow_right_rounded,
        size: 17,
      ),
      onTap: () async {
        Tools()
            .navigatorPush(
              CommunityInviteMembers(
                team: team,
              ),
            )
            .then((value) => teammatesController.initialFetch());
      },
    );
  }

  Widget _searchField(BuildContext context, TeammatesController controller) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 15.0),
      child: TextField(
        decoration: InputDecoration(
            prefixIcon: const Icon(
              Icons.search,
              color: Color(0xff25323B),
            ),
            hintText: 'APPLICATION_MOBILE_COMMUNITY_MEMBER_SEARCH'
                .tr("Search Members"),
            hintStyle: Theme.of(context)
                .textTheme
                .bodySmall
                ?.copyWith(color: const Color(0xff25323B), fontSize: 13),
            focusedBorder: const OutlineInputBorder(
                borderSide: BorderSide(color: Color(0xff25323B))),
            border: const OutlineInputBorder(
                borderSide: BorderSide(color: Color(0xff25323B)))),
        onChanged: controller.setQuery,
        keyboardType: TextInputType.emailAddress,
        enableSuggestions: false,
        autocorrect: false,
        textInputAction: TextInputAction.done,
      ),
    );
  }

  bool isTeammateExists(List<Friend> teammates, int id) {
    for (Friend teammate in teammates) {
      if (teammate.ownerId == id && teammate.isAdmin) {
        return true;
      }
    }
    return false;
  }

  Widget _teamList(BuildContext context, TeammatesController controller) {
    // _teammates
    //     .sort((a, b) => b.ownerFirstName?.compareTo(a.ownerFirstName!) ?? 0);
    // _teammates.sort((a, b) => a.isAdmin ? 0 : 1);

    return Expanded(
      child: PaginationWidget(
        child: (context, index) => _teamMemberTile(
            context,
            controller.dataList[index],
            isTeammateExists(controller.dataList, _owner.id)),
        controller: controller,
        separator: true,
      ),
    );
  }

  Widget _teamMemberTile(BuildContext context, Friend _teammate, bool isAdmin) {
    List<PopupItemMenu> itemList = [
      const PopupItemMenu(
          item: PopupItem.removeUser, displayText: 'Remove user'),
      const PopupItemMenu(item: PopupItem.makeAdmin, displayText: 'Make Admin'),
    ];
    return GestureDetector(
        child: Stack(
      children: [
        Row(
          children: [
            _userAvatar(_teammate, context),
            const SizedBox(
              width: 15,
            ),
            Flexible(
              child: Text(
                _teammate.fullName,
                style:
                    const TextStyle(fontSize: 13, fontWeight: FontWeight.w600),
              ),
            ),
            const SizedBox(
              width: 15,
            ),
            if (_teammate.isAdmin)
              Container(
                decoration: BoxDecoration(
                  color:
                      AppTheme.woofTraxTheme.primaryColorLight.withAlpha(150),
                  borderRadius: const BorderRadius.all(Radius.circular(50.0)),
                ),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                  child: Text(
                    'APPLICATION_MOBILE_COMMUNITY_GROUP_ADMIN'.tr('Admin'),
                    style: const TextStyle(
                      fontSize: 12.0,
                    ),
                  ),
                ),
              ),
          ],
        ),
        if (isAdmin && !_teammate.isAdmin)
          Positioned(
              right: -5,
              child: PopupButtonMenu(
                items: itemList,
                onItemSelected: (item) async {
                  switch (item) {
                    case PopupItem.removeUser:
                      _onTapConfirmation(
                        _teammate,
                        context,
                        'APPLICATION_MOBILE_COMMUNITY_REMOVE'
                            .tr("Remove Confirmation"),
                        'APPLICATION_MOBILE_COMMUNITY_PACK_DELETE_MEMBER_DESC1'
                            .tr("Are you sure you want to remove "),
                        'APPLICATION_MOBILE_COMMUNITY_PACK_DELETE_MEMBER_DESC2'.tr(
                            " ?\n\nRemoving this member will erase all their activity data from this group permanently."),
                        '',
                        () async {
                          MBResponse response = await MbApiCommunity()
                              .removeTeammateFromTeam(context, team, _teammate);
                          if (response.success) {
                            Tools()
                                .common
                                .showMessage(context, "Removed Successfully!");
                          }
                        },
                      );
                      break;
                    case PopupItem.makeAdmin:
                      _onTapConfirmation(
                        _teammate,
                        context,
                        'APPLICATION_MOBILE_COMMUNITY_REMOVE'
                            .tr("Admin Confirmation"),
                        'APPLICATION_MOBILE_COMMUNITY_PACK_DELETE_MEMBER_DESC1'
                            .tr("Are you sure you want to assign "),
                        'APPLICATION_MOBILE_COMMUNITY_PACK_DELETE_MEMBER_DESC2'.tr(
                            " ?\n\nTransferring your admin rights to another member is irreversible."),
                        ' as the Admin',
                        () async {
                          MBResponse response = await MbApiCommunity()
                              .makeTeammateAdmin(context, team, _teammate);
                          if (response.success) {
                            Tools().common.showMessage(
                                context, "Make Admin Successfully!");
                          }
                        },
                      );

                      break;
                  }
                },
              )),
      ],
    ));
  }

  Widget _userAvatar(Friend _teammate, BuildContext context) {
    return Stack(
      children: [
        MBAvatar(
          imageUrl: _teammate.ownerImageUrl,
          noImageUrl: Image.asset(
            "assets/images/user_placeholder.jpg",
            fit: BoxFit.contain,
          ),
          size: 40,
          foregroundColor: AppTheme.woofTraxTheme.backgroundColor,
          backgroundColor: AppTheme.woofTraxTheme.primaryColorLight,
        ),
        if (_teammate.isActiveWalker)
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.secondary,
                size: 15,
              ),
            ),
          ),
      ],
    );
  }

  void _onTapConfirmation(
      Friend teammate,
      BuildContext context,
      String title,
      String content,
      String desc,
      String? extraText,
      Future<void> Function() onValid) {
    if (!teammate.isAdmin && team.isAdmin) {
      Tools().common.showWarningDialog(context,
          validTitle: 'APPLICATION_MOBILE_LABEL_YES'.tr("Yes"),
          rejectTitle: 'APPLICATION_MOBILE_LABEL_NO'.tr("No"),
          bgColor: const Color(0xffF2F0E4),
          textColor: Theme.of(context).colorScheme.secondary,
          title: title,
          content: RichText(
              text: TextSpan(
                  text: content,
                  style:
                      TextStyle(color: Theme.of(context).colorScheme.secondary),
                  children: [
                TextSpan(
                    text: teammate.fullName,
                    style: const TextStyle(fontWeight: FontWeight.bold)),
                const TextSpan(text: " from "),
                TextSpan(
                    text: team.name,
                    style: const TextStyle(fontWeight: FontWeight.bold)),
                TextSpan(text: extraText),
                TextSpan(text: desc)
              ])),
          onValid: onValid);
    }
  }
}
