import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/app_theme.dart';
import 'package:mybuddy/controllers/leaderboard_user_detail_controller.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';

class LeaderboardUserDetailsHeader extends StatelessWidget {
  LeaderboardUserDetailsHeader(this.controller, {Key? key});

  LeaderboardUserDetailController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        MBAvatar(
          imageUrl: controller.user.userImage,
          noImageUrl: Image.asset(
            "assets/images/user_placeholder.jpg",
            fit: BoxFit.contain,
          ),
          size: 80,
          foregroundColor: AppTheme.woofTraxTheme.backgroundColor,
          backgroundColor: const Color(0xffF3AE56),
        ),
        const SizedBox(height: 10),
        Obx(
          () => Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                controller.user.fullName,
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
              if (!controller.loading) ...[
                const SizedBox(width: 10),
                Padding(
                  padding: const EdgeInsets.only(bottom: 3),
                  child: Text(
                    controller.user.isAdmin ? "Admin" : "Member",
                    style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w400,
                        color: Color(0xff939393)),
                  ),
                ),
              ]
            ],
          ),
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Obx(
              () => _statsWidget(
                  "Miles ", controller.distance, const Color(0xff6E568C)),
            ),
            const SizedBox(width: 20),
            Obx(
              () => _statsWidget("Walks ", controller.walks,
                  Theme.of(context).colorScheme.secondary),
            ),
          ],
        ),
        const Divider(
          thickness: 1,
          height: 25,
          color: Color(0xffDAE2EB),
        ),
      ],
    );
  }

  Widget _statsWidget(String title, String value, [Color? color]) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          title,
          style: const TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: Color(0xff939393)),
        ),
        Text(
          value,
          style: TextStyle(
              fontSize: 15, fontWeight: FontWeight.w700, color: color),
        ),
      ],
    );
  }
}
