import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

import '../create_community_pack.dart';

class TeamDescriptionField extends StatefulWidget {
  const TeamDescriptionField({Key? key}) : super(key: key);

  @override
  _TeamDescriptionFieldState createState() => _TeamDescriptionFieldState();
}

class _TeamDescriptionFieldState extends State<TeamDescriptionField> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: TextFormField(
        initialValue: CreateCommunityPack.of(context).description,
        style: const TextStyle(color: Color(0xff162941)),
        textAlignVertical: TextAlignVertical.top,
        decoration: InputDecoration(
            alignLabelWithHint: true,
            hintText: 'APPLICATION_MOBILE_FIELD_HINT_TEAM_DESCRIPTION'
                .tr('Describe your team in a few words'),
            labelText: 'APPLICATION_MOBILE_FIELD_LABEL_TEAM_DESCRIPTION'
                .tr('Description'),
            labelStyle: const TextStyle(
              color: Color(0xff162941),
            ),
            hintStyle: const TextStyle(color: Color(0xff162941)),
            focusedBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Color(0xff162941)),
                borderRadius: BorderRadius.circular(8))),
        inputFormatters: [LengthLimitingTextInputFormatter(500)],
        maxLines: 6,
        maxLength: 500,
        onSaved:
            CreateCommunityPack.of(context, rebuild: false).setTeamDescription,
      ),
    );
  }
}
