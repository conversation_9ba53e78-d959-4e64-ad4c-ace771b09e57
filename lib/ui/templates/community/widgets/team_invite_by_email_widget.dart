import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/email_verification_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/ui/components/email_textfield_with_validation.dart';

class TeamInviteByEmail extends StatefulWidget {
  final Team team;
  final Future<InvitationStatus> Function(String?, String?, String)
      inviteFunction;

  const TeamInviteByEmail(
      {Key? key, required this.team, required this.inviteFunction})
      : super(key: key);

  @override
  _TeamInviteByEmailState createState() => _TeamInviteByEmailState();
}

class _TeamInviteByEmailState extends State<TeamInviteByEmail> {
  EmailVerificationController controller = EmailVerificationController.of();
  late bool isLoading;

  @override
  void initState() {
    super.initState();
    isLoading = false;
    controller.init();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'APPLICATION_MOBILE_COMMUNITY_INVITE_BODY'.tr(),
                style: Theme.of(context).textTheme.bodyText1,
              ),
              Padding(
                padding: const EdgeInsets.only(top: 15.0, bottom: 15.0),
                child: EmailTextFieldWithValidation(controller),
              ),
              _showButtonOrLoader(),
            ]),
      ),
    );
  }

  Widget _showButtonOrLoader() {
    if (isLoading) {
      return const Center(
        heightFactor: 2.0,
        child: CircularProgressIndicator.adaptive(),
      );
    }

    return SizedBox(
      width: double.infinity,
      child: Builder(
        builder: (context) => Obx(
          () => RawMaterialButton(
            constraints: const BoxConstraints(minHeight: 50.0),
            onPressed: controller.isButtonDisabled ? null : _invitePeople,
            shape: const StadiumBorder(
              side: BorderSide(color: Color(0xff9C9C9C)),
            ),
            child: Text(
              'APPLICATION_MOBILE_COMMUNITY_SEND_INVITE'.tr(),
              style: Theme.of(context).textTheme.headline6?.copyWith(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
            ),
          ),
        ),
      ),
    );
  }

  void _invitePeople() async {
    if (!controller.emailValidate()) {
      Tools().common.showMessage(
          context, 'APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_MAIL'.tr());
      return;
    }

    setState(() {
      isLoading = true;
    });
    InvitationStatus status = await widget.inviteFunction(
        null, null, controller.emailController.value.text);

    if (status.success) {
      controller.init();

      Tools().common.showMessage(
            context,
            status.message,
          );
    }

    setState(() {
      isLoading = false;
    });
  }
}
