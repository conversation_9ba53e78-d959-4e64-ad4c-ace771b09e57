import 'dart:io';

import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/templates/community/create_community_pack.dart';

class TeamAvatarField extends StatelessWidget {
  const TeamAvatarField({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final CreateCommunityPackState packState = CreateCommunityPack.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 25.0),
      child: MBAvatar(
        radius: 20,
        size: 69.0,
        imageUrl: packState.teamAvatarUrl,
        backgroundColor: const Color(0xffF1F1E4),
        foregroundColor: const Color(0xffF8F7F3),
        noImageUrl: packState.teamAvatarUrl == null && packState.avatar != null
            ? Image.file(packState.avatar!,)
            : const Icon(
                Icons.image,
                color: Color(0xff162941),
              ),
        stackedChild: const Icon(
          Icons.photo_camera_outlined,
          size: 18.0,
          color: Color(0xff162941),
        ),
        onTap: () async {
          File? result = await Tools().image.pickAndCropAvatar(context);
          if (result != null) {
            packState.setAvatar(result);
          }
        },
      ),
    );
  }
}
