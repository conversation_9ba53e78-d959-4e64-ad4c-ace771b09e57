import 'package:flutter/material.dart';

import '../create_community_pack.dart';

import 'package:mybuddy/extensions/string_extensions.dart';

class TeamEveryOneCanInviteField extends StatelessWidget {
  const TeamEveryOneCanInviteField({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'APPLICATION_MOBILE_FIELD_LABEL_PERMISSIONS'.tr('Permissions'),
            style: Theme.of(context)
                .textTheme
                .bodyText1
                ?.copyWith(color: const Color(0xff162941)),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'APPLICATION_MOBILE_FIELD_LABEL_TEAM_EVERYONE_CAN_INVITE'
                    .tr('All members can send group invitation'),
                style: Theme.of(context).textTheme.bodyText2,
              ),
              Switch(
                value: CreateCommunityPack.of(context, rebuild: false)
                    .everyOneCanInvite,
                activeColor: Theme.of(context).colorScheme.secondary,
                onChanged:
                    CreateCommunityPack.of(context).switchEveryOneCanInvite,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
