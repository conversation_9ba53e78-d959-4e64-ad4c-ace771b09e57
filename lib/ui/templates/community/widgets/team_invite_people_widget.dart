import 'dart:async';

import 'package:contacts_service/contacts_service.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';

import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/community/create_community_pack.dart';

class TeamInvitePeople extends StatefulWidget {
  const TeamInvitePeople({Key? key}) : super(key: key);

  @override
  _TeamInvitePeopleState createState() => _TeamInvitePeopleState();
}

class _TeamInvitePeopleState extends State<TeamInvitePeople> {
  late List<Friend> people;
  int registeredCount = 0;
  bool isLoading = false;

  Future<void> fetchContacts() async {
    setState(() {
      isLoading = true;
    });

    /// Load contacts from phone without thumbnails.
    Iterable<Contact> contacts =
        await ContactsService.getContacts(withThumbnails: false);

    ///get all contacts with emails
    List<Contact> contactsWithMails =
        contacts.where((c) => c.emails!.isNotEmpty).toList();

    /// check myBuddy/wooftrax accounts
    List jsonList = await (MbApiCommunity()
        .checkMWOwners(contactsToMapList(contactsWithMails))) ?? [];

    /// map results and count registered accounts
    people = jsonList.map((json) {
      Friend f = Friend.fromJson(json);
      if (f.ownerId != null) {
        registeredCount++;
      }

      return f;
    }).toList();

    if(mounted) {// widget may be destroyed at this point
      setState(() {
        isLoading = false;
      });
    }
  }


  @override
  void initState() {
    super.initState();
    fetchContacts();
  }
  @override
  Widget build(BuildContext context) {
    return isLoading ? const Center(
        heightFactor: 4.0,
        child: CircularProgressIndicator(),
    ) : ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.fromLTRB(10.0, 5.0, 10.0, 80.0),
      itemCount: people.length + 1,
      itemBuilder: (BuildContext context, int index) {
        if (index == 0) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 10.0),
                child: Text(
                  'APPLICATION_MOBILE_FIELD_LABEL_TEAM_INVITE_USER'
                      .tr('Add people to the group') +
                      ' ($registeredCount)',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ),
              if (people.isEmpty)
                const Flexible(child: Center(child: Text('no contacts'))),
              ///endif
            ],
          );
        }
        Friend person = people.elementAt(index - 1);
        Map<String, dynamic> buildLevel = person.ownerTotalPoints.toLevel();

        return ListTile(
          dense: true,
          selected: CreateCommunityPack.of(context, rebuild: false).people.contains(person),
          selectedTileColor: Theme.of(context).colorScheme.surface,
          onTap: () => CreateCommunityPack.of(context).handlePeople(person),
          leading: MBAvatar(
            imageUrl: person.ownerImageUrl,
            backgroundColor: Theme.of(context).colorScheme.surface,
            size: 36,
            noImageUrl: Text(person.initials),
          ),
          title: Text(person.fullName),
          subtitle: SizedBox(
            height: 33.0,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(person.ownerEmail!,),
                if(person.ownerId != null)
                  Text('Level ${buildLevel['level']}',),
                ///endif
              ],
            ),
          ),
          trailing: person.ownerId != null ? const Icon(
            Icons.pets_outlined,
            size: 20,
          ) : null,
        );
      },
      separatorBuilder: (_, int index) => index == 0
          ? const SizedBox.shrink()
          : const Divider(height: 0.0),
    );
  }
}

List<Map<String, dynamic>> contactsToMapList(List<Contact> contacts) {
  List<Map<String, dynamic>> list = <Map<String, dynamic>>[];

  for (Contact contact in contacts) {
    if(contact.emails == null) {
      continue;
    }
    for (Item e in contact.emails!) {
      Map<String, dynamic> data = <String, dynamic>{};
      data['givenName'] = contact.givenName;
      data['familyName'] = contact.familyName;
      data['email'] = e.value;
      list.add(data);
    }
  }

  return list;
}
