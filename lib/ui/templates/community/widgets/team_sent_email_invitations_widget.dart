import 'package:contacts_service/contacts_service.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/community_validations.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/ui/components/loading_overlay.dart';

import '../../../../class/permissions_handling.dart';

class _FriendContact {
  InvitedUser friend;
  bool isContact;
  bool enableResend;

  _FriendContact(this.friend, this.isContact, this.enableResend);
}

class TeamSentEmailInvitations extends StatefulWidget {
  final Team team;
  final List<InvitedUser> invitedUsers;

  const TeamSentEmailInvitations(
      {Key? key, required this.team, required this.invitedUsers})
      : super(key: key);

  @override
  _TeamSentEmailInvitationsState createState() =>
      _TeamSentEmailInvitationsState();
}

class _TeamSentEmailInvitationsState extends State<TeamSentEmailInvitations> {
  late List<_FriendContact> people;
  late List<_FriendContact> searchPeople;
  late bool isLoading;

  @override
  void initState() {
    super.initState();
    isLoading = false;
    people = [];
    searchPeople = [];
    _fetchSentInvitations();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant TeamSentEmailInvitations oldWidget) {
    super.didUpdateWidget(oldWidget);
    _fetchSentInvitations();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.invitedUsers.isEmpty) {
      return const SizedBox();
    } else {
      return LoadingOverlay(
        isLoading: isLoading,
        child: Container(
          padding: const EdgeInsets.only(top: 20.0),
          child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'APPLICATION_MOBILE_COMMUNITY_SENT_INVITATIONS'.tr(),
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 15.0, bottom: 15.0),
                  child: TextField(
                    decoration: InputDecoration(
                      hintText:
                          'APPLICATION_MOBILE_FIELD_LABEL_SEARCH_EMAIL_ADDRESS'
                              .tr(),
                      prefixIcon:
                          const Icon(Icons.search, color: Color(0xff25323B)),
                      hintStyle: Theme.of(context)
                          .textTheme
                          .bodySmall
                          ?.copyWith(
                              color: const Color(0xff25323B), fontSize: 13),
                    ),
                    onChanged: _searchEmail,
                    keyboardType: TextInputType.emailAddress,
                    enableSuggestions: false,
                    autocorrect: false,
                    textInputAction: TextInputAction.search,
                  ),
                ),
                emailListView(),
              ]),
        ),
      );
    }
  }

  Widget emailListView() {
    return Flexible(
        child: Card(
      margin: const EdgeInsets.only(bottom: 30),
      child: ListView.separated(
        shrinkWrap: true,
        padding: const EdgeInsets.all(0),
        itemCount: searchPeople.length,
        itemBuilder: (BuildContext context, int index) {
          _FriendContact person = searchPeople.elementAt(index);
          return contactTile(person, context);
        },
        separatorBuilder: (_, int index) => const Divider(height: 0.0),
      ),
    ));
  }

  Widget contactTile(_FriendContact person, BuildContext context) {
    return ListTile(
      dense: true,
      contentPadding: const EdgeInsets.all(10.0),
      minLeadingWidth: 0,
      // selected: CreateCommunityPack.of(context, rebuild: false).peopleOnWooftrax.contains(person),
      selectedTileColor: Theme.of(context).colorScheme.surface,
      // onTap: () => CreateCommunityPack.of(context).handlePeople(person),
      title: Row(
        children: [
          Text(
            person.friend.email!,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.black, fontSize: 13, fontWeight: FontWeight.w400),
          ),
          person.isContact
              ? Padding(
                  padding: const EdgeInsets.only(left: 5.0),
                  child: Image.asset(
                    "assets/images/ic_contact.png",
                    width: 18,
                    height: 18,
                  ),
                )
              : const SizedBox(),
        ],
      ),
      trailing: RawMaterialButton(
        constraints: const BoxConstraints(minWidth: 80.0, minHeight: 36.0),
        elevation: !person.enableResend ? 0 : 2,
        onPressed: () async {
          _resendInvite(person.friend);
        },
        fillColor: person.enableResend
            ? Theme.of(context).colorScheme.secondary
            : const Color(0xffE8ECF0),
        shape: const StadiumBorder(
          side: BorderSide.none,
        ),
        child: Text(
          'APPLICATION_MOBILE_BUTTON_LABEL_RESEND'.tr(),
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: 14,
                color: person.enableResend
                    ? Colors.white
                    : const Color(0xffA2B2C6),
              ),
        ),
      ),
    );
  }

  void _searchEmail(String val) {
    searchPeople = people
        .where((element) =>
            element.friend.email!.toLowerCase().contains(val.toLowerCase()))
        .toList();

    setState(() {});
  }

  void _resendInvite(InvitedUser friend) async {
    if (!CommunityValidations.checkResendValidity(friend.lastInvite)) {
      Tools().common.showMessage(
          context,
          CommunityValidations.resendMessage(friend.lastInvite),
          Colors.white,
          Colors.black);

      return;
    }

    setState(() {
      isLoading = true;
    });

    Friend teamFriend = Friend(ownerEmail: friend.email);

    MBResponse response = await MbApiCommunity().inviteSingleFriendByMail(
      context,
      widget.team.id,
      teamFriend,
    );

    if (response.success) {
      Tools().common.showMessage(
            context,
            'APPLICATION_MOBILE_COMMUNITY_INVITATION_SENT'.tr(),
          );
    }

    setState(() {
      isLoading = false;
    });
  }

  void _fetchSentInvitations() async {
    people = await _matchFriendsWithContacts(widget.invitedUsers);

    setState(() {
      searchPeople = [...people];
    });
  }

  Future<List<_FriendContact>> _matchFriendsWithContacts(
      List<InvitedUser> friends) async {

    List<String> emails = <String>[];

    ///if permission is granted
    if(await checkContactPermission()) {
      /// Load contacts from phone without thumbnails.
      Iterable<Contact> contacts =
      await ContactsService.getContacts(withThumbnails: false);

      ///get all contacts with emails
      for (Contact contact in contacts) {
        if (contact.emails == null) continue;
        for (Item item in contact.emails!) {
          if (item.value != null) {
            emails.add(item.value!);
          }
        }
      }
    }

    List<_FriendContact> friendContacts = friends
        .map((e) => _FriendContact(e, emails.contains(e.email),
        CommunityValidations.checkResendValidity(e.lastInvite)))
        .toList();

    return friendContacts;
  }

}
