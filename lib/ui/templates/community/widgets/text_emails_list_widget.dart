import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/community.dart';

class TextEmailListWidget extends StatefulWidget {
  final List<Friend> children;
  final void Function(Friend)? onAdd;
  final void Function(Friend)? onRemoveChip;

  const TextEmailListWidget(
      {Key? key, this.onAdd, required this.children, this.onRemoveChip})
      : super(key: key);

  @override
  _TextEmailListWidgetState createState() => _TextEmailListWidgetState();
}

class _TextEmailListWidgetState extends State<TextEmailListWidget> {
  late TextEditingController editingController;
  bool isValid = false;

  void validator() {
    if (editingController.text.isEmpty ||
        !editingController.text.isEmail() ||
        widget.children.any((p) => p.ownerEmail == editingController.text)) {
      setState(() {
        isValid = false;
      });
    } else {
      setState(() {
        isValid = true;
      });
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    editingController = TextEditingController();
    editingController.addListener(validator);
  }

  @override
  void dispose() {
    editingController.removeListener(validator);
    editingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              'Invite a friend by their email address',
              style: Theme.of(context).textTheme.bodyText1,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: TextField(
              controller: editingController,
              decoration: InputDecoration(
                suffixIcon: IconButton(
                  onPressed: isValid
                      ? () {
                          widget.onAdd!(
                              Friend(ownerEmail: editingController.text));
                          setState(() {
                            editingController.text = '';
                          });
                        }
                      : null,
                  icon: const Icon(Icons.add),
                ),
              ),
              keyboardType: TextInputType.emailAddress,
              enableSuggestions: false,
              autocorrect: false,
              textInputAction: TextInputAction.done,
            ),
          ),
          AnimatedSize(
            duration: const Duration(milliseconds: 250),
            curve: Curves.bounceIn,
            child: Wrap(
              spacing: 5.0,
              children: widget.children.map((p) {
                return InputChip(
                  label: Text(
                    p.ownerEmail!,
                    style: const TextStyle(fontSize: 8.0),
                  ),
                  onDeleted: () => widget.onRemoveChip!(p),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
