import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/settings_delegate.dart';

import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/community.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'text_emails_list_widget.dart';

class TeamInviteByMail extends StatefulWidget {
  final Team team;

  const TeamInviteByMail({Key? key, required this.team}) : super(key: key);

  @override
  _TeamInviteByMailState createState() => _TeamInviteByMailState();
}

class _TeamInviteByMailState extends State<TeamInviteByMail> {
  final SharedPreferences sharedPreferences = SettingsDelegate().prefs;
  late List<String> _invitedFriends;
  bool alreadyInvited = false;
  List<Friend> people = <Friend>[];

  void handlePeople(Friend friend) {
    if (_invitedFriends.contains(friend.ownerEmail)) {
      setState(() => alreadyInvited = true);
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() => alreadyInvited = false);
        }
      });
    } else {
      setState(() {
        if (people.contains(friend)) {
          people.remove(friend);
        } else {
          people.add(friend);
        }
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _invitedFriends = sharedPreferences.getStringList('invited_friends') ?? [];
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(
        15.0,
        15.0,
        15.0,
        MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              widget.team.name,
              style: Theme.of(context).textTheme.headline5,
            ),
          ),
          TextEmailListWidget(
            children: people,
            onAdd: handlePeople,
            onRemoveChip: handlePeople,
          ),
          AnimatedOpacity(
            opacity: alreadyInvited ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 250),
            child: Text(
              'APPLICATION_MOBILE_ERROR_INVITATION_SENT'.tr(),
              style: const TextStyle(color: Colors.red, fontSize: 10.0),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  Tools().navigatorPop(removeLast: false, value: false);
                },
                child: Text('APPLICATION_MOBILE_BUTTON_LABEL_CLOSE'.tr()),
              ),
              TextButton(
                onPressed: people.isNotEmpty
                    ? () async {
                        FocusScope.of(context).unfocus();
                        for (var person in people) {
                          if (person.ownerEmail != null &&
                              !_invitedFriends.contains(person.ownerEmail)) {
                            _invitedFriends.add(person.ownerEmail!);
                          }
                        }
                        sharedPreferences.setStringList(
                            'invited_friends', _invitedFriends);

                        MBResponse response =
                            await MbApiCommunity().inviteFriendByMail(
                          context,
                          widget.team.id,
                          people,
                        );
                        Tools().navigatorPop(value: response.success);

                        if (response.success) {
                          Tools().common.showMessage(
                                context,
                                'APPLICATION_MOBILE_COMMUNITY_INVITATION_SENT'
                                    .tr(),
                              );
                        }
                      }
                    : null,
                child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SEND'.tr()),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
