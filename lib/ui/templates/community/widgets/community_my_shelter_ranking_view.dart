import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/owner_bloc.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/shelter.dart';
import 'package:mybuddy/ui/templates/community/community_screens.dart';

class CommunityMyShelterRankingView extends StatelessWidget {
  const CommunityMyShelterRankingView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<OwnerBloc>(
      bloc: OwnerBloc(),
      child: const _StreamBuilderMyShelterRanking(),
    );
  }
}

class _StreamBuilderMyShelterRanking extends StatelessWidget {
  const _StreamBuilderMyShelterRanking({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<OwnerBloc>(context);
    return StreamBuilder<Owner>(
      stream: bloc.stream,
      builder: (BuildContext context, AsyncSnapshot<Owner> snapshot) {
        return FriendsListScreen(
          shelter: snapshot.data?.shelter ?? Shelter.none(),
        );
      },
    );
  }
}
