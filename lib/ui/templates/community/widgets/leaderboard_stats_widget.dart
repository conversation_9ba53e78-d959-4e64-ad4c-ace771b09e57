import 'package:flutter/material.dart';
// import 'package:flutter_neumorphic/flutter_neumorphic.dart'; // Temporarily disabled due to compatibility issues
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/controllers/group_leaderboard_controller.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/ui/components/stats_data_tile.dart';

class LeaderboardStatsWidget extends StatelessWidget {
  const LeaderboardStatsWidget(this.controller, {Key? key}) : super(key: key);

  final GroupLeaderboardController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Obx(
              () => StatsDataTile(
                icon: "assets/icon/walk.png",
                title: "Walks",
                value: controller.stats.totalWalks.toString(),
                tooltip:
                    'APPLICATION_MOBILE_LABEL_LEADERBOARD_MILES_TOOLTIP'.tr(),
              ),
            ),
            const SizedBox(width: 10),
            Obx(
              () => StatsDataTile(
                  icon: "assets/icon/miles_purple_icon.png",
                  title: "Miles",
                  value: controller.stats.totalDistance?.toStringAsFixed(2) ??
                      "0.0"),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            Obx(
              () => StatsDataTile(
                  icon: "assets/icon/clock_blue.png",
                  title: "Time",
                  value: "${controller.stats.totalTime?.toENTimeString()}"),
            ),
            const SizedBox(width: 10),
            Obx(
              () => StatsDataTile(
                  icon: "assets/icon/members.png",
                  title: "Members",
                  value: controller.stats.totalMembers.toString()),
            ),
          ],
        ),
        const SizedBox(height: 10),
      ],
    );
  }
}
