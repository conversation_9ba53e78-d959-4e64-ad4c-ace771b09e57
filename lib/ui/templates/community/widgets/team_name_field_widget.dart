import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

import '../create_community_pack.dart';

class TeamNameField extends StatefulWidget {
  const TeamNameField({Key? key}) : super(key: key);

  @override
  _TeamNameFieldState createState() => _TeamNameFieldState();
}

class _TeamNameFieldState extends State<TeamNameField> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: TextFormField(
        initialValue: CreateCommunityPack.of(context).name,
        style: const TextStyle(color: Color(0xff162941)),
        decoration: InputDecoration(
            labelText: 'APPLICATION_MOBILE_SENTENCE_LABEL_TEAM_NAME'
                .tr('What\'s your group\'s name ?'),
            labelStyle: const TextStyle(color: Color(0xff162941)),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xff162941)),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Color(0xff162941)),
                borderRadius: BorderRadius.circular(8))),
        inputFormatters: [LengthLimitingTextInputFormatter(50)],
        validator: (val) => val!.isEmpty || val.trim() == ''
            ? 'APPLICATION_MOBILE_SENTENCE_HINT_TEAM_NAME'
                .tr('Please set the group\'s name')
            : !RegExp(r'^[a-zA-Z0-9 ]+$').hasMatch(val)
                ? 'APPLICATION_MOBILE_SENTENCE_HINT_TEAM_NAME'
                    .tr('You may only use the characters A to Z and 0 to 9')
                : null,
        onSaved: CreateCommunityPack.of(context, rebuild: false).setTeamName,
      ),
    );
  }
}
