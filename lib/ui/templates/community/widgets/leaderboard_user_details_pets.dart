import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:mybuddy/app_theme.dart';
import 'package:mybuddy/controllers/leaderboard_user_detail_controller.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';

class LeaderboardUserDetailsPets extends StatelessWidget {
  LeaderboardUserDetailsPets(this.controller, {Key? key}) : super(key: key);

  LeaderboardUserDetailController controller;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: controller.user.pets.isNotEmpty,
      replacement: _lexiPetWidget(),
      child: Flexible(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: StaggeredGrid.count(
            crossAxisCount: 3,
            mainAxisSpacing: 10,
            children: List.generate(controller.user.pets.length,
                (index) => _petWidget(controller.user.pets[index])),
          ),
        ),
      ),
    );
  }

  Widget _petWidget(Pet pet) {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(100),
          child: MBAvatar(
            imageUrl: pet.imageUrl,
            noImageUrl: Image.asset(
              "assets/images/noConnexionPicture.png",
              fit: BoxFit.contain,
            ),
            size: 95,
            foregroundColor: AppTheme.woofTraxTheme.colorScheme.surface,
          ),
        ),
        const SizedBox(height: 10),
        Text(
          pet.name,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400),
        ),
      ],
    );
  }

  Widget _lexiPetWidget() {
    return Center(
      child: Column(
        children: [
          MBAvatar(
            noImageUrl: Image.asset(
              "assets/images/lexi_wooftrax.png",
              fit: BoxFit.contain,
            ),
            size: 97,
          ),
          Text(
            "Lexi",
            style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w400,
                color: Theme.of(Get.context!).colorScheme.secondary),
          ),
          const SizedBox(height: 20),
          Text(
            "${controller.user.firstName} is walking with WoofTrax’s virtual dog Lexi.",
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xff7f7f7f),
            ),
          )
        ],
      ),
    );
  }
}
