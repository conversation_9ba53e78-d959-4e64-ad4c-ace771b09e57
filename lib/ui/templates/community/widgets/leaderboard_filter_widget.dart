import 'package:flutter/material.dart';
import 'package:flutter_custom_month_picker/flutter_custom_month_picker.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/group_leaderboard_controller.dart';
import 'package:mybuddy/models/community.dart';

class LeaderboardFilterWidget extends StatelessWidget {
  const LeaderboardFilterWidget(this.controller, this.team, {Key? key})
      : super(key: key);

  final GroupLeaderboardController controller;
  final Team team;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 230,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return TextButton(
                onPressed: () => _applyFilter(index, context),
                child: Text(
                  controller.filterSheetItems[LeaderBoardFilter.values[index]],
                  style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) {
              return const Divider(
                thickness: 1,
              );
            },
            itemCount: LeaderBoardFilter.values.length),
      ),
    );
  }

  void _applyFilter(int index, BuildContext context) async {
    Tools().navigatorPop();

    if (index == LeaderBoardFilter.month.index) {
      showMonthPicker(context, onSelected: (month, year) {
        controller.fetchSelectedMonth(month, year);
      },
          firstYear: team.createdAt?.year ?? DateTime.now().year,
          lastYear: DateTime.now().year,
          firstEnabledMonth: team.createdAt?.month ?? DateTime.now().month,
          lastEnabledMonth: DateTime.now().month,
          initialSelectedMonth: controller.startDate.month,
          initialSelectedYear: controller.startDate.year,
          highlightColor: Theme.of(context).colorScheme.secondary);
    } else if (index == LeaderBoardFilter.custom.index) {
      DateTimeRange? range = await Tools().common.showDateRangeDialog(
            context: context,
            helpText: "Leaderboard Date Filter",
            firstDate: team.createdAt,
            initialDateRange: controller.initialDateRange,
          );
      if (range != null) controller.fetchCustomRange(range);
    } else {
      // 0 is denoting all time records
      controller.fetchAllTime();
    }
  }
}
