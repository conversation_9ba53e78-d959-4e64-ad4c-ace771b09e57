import 'package:flutter/material.dart';
import 'package:mybuddy/ui/components/internet_widget_switcher.dart';
import 'package:mybuddy/ui/templates/community/widgets/community_my_shelter_ranking_view.dart';

import 'community_screens.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class CommunityHomePage extends StatefulWidget {
  const CommunityHomePage({Key? key}) : super(key: key);

  @override
  _CommunityHomePageState createState() => _CommunityHomePageState();
}

class _CommunityHomePageState extends State<CommunityHomePage>
    with SingleTickerProviderStateMixin {
  late TabController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InternetWidgetSwitcher(
        onlineWidget: Column(
          children: [
            // Container(
            //   constraints: BoxConstraints(minHeight: 50.0, maxHeight: 70),
            //   alignment: Alignment.center,
            // child: ListView.builder(
            //   scrollDirection: Axis.horizontal,
            //   itemCount: 10,
            //   itemBuilder: (context, index) => Container(
            //     margin: EdgeInsets.all(10.0),
            //     width: 50,
            //     height: 50,
            //     color: Color.fromRGBO(0, (index * 20), 0, 1.0),
            //   ),
            // ),
            // ),
            TabBar(
              controller: _controller,
              tabs: <Tab>[
                Tab(
                  child: Text(
                    'APPLICATION_MOBILE_LABEL_MY_SHELTER_RANKING'
                        .tr('My Shelter'),
                  ),
                ),
                Tab(
                  child: Text(
                    'APPLICATION_MOBILE_LABEL_GLOBAL_RANKING'.tr('Global'),
                  ),
                ),
              ],
            ),
            Expanded(
              child: TabBarView(
                controller: _controller,
                // physics: const NeverScrollableScrollPhysics(),
                children: const <Widget>[
                  CommunityMyShelterRankingView(),
                  FriendsListScreen(),
                ],
              ),
            ),
          ],
        ),
        retryMethod: () {
          _controller.animateTo(0);
        });
  }
}
