import 'package:flutter/material.dart';
import 'package:mybuddy/controllers/teams_controller.dart';
import 'package:mybuddy/ui/components/internet_widget_switcher.dart';
import 'package:mybuddy/ui/templates/community/community_teams_page.dart';

import 'community_screens.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class CommunityGroupsPage extends StatefulWidget {
  const CommunityGroupsPage({Key? key}) : super(key: key);

  @override
  _CommunityGroupsPageState createState() => _CommunityGroupsPageState();
}

class _CommunityGroupsPageState extends State<CommunityGroupsPage> {
  final TeamsController _controller = TeamsController.of();

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: InternetWidgetSwitcher(
          onlineWidget: Column(
            children: [
              TabBar(
                tabs: <Tab>[
                  Tab(
                    child: Text(
                      'APPLICATION_MOBILE_LABEL_MY_SHELTER_RANKING'
                          .tr('My Groups'),
                    ),
                  ),
                  Tab(
                    child: Text(
                      'APPLICATION_MOBILE_LABEL_GLOBAL_RANKING'
                          .tr('Public Groups'),
                    ),
                  ),
                ],
                onTap: (index) {
                  _controller.currentTab = index;
                },
              ),
              const Expanded(
                child: TeamListPage(),
              ),
            ],
          ),
          retryMethod: () {
            _controller.initialFetch();
          }),
    );
  }
}
