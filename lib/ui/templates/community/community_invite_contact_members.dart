import 'package:contacts_service/contacts_service.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';

import '../../../Api/mb_api_community.dart';
import '../../../class/data.dart';
import '../../../class/permissions_handling.dart';
import '../../../models/community.dart';
import 'community_contact_users.dart';
import 'widgets/team_invite_people_widget.dart';

enum InviteStatus { add, invite, resend, inviteSent }

class InviteContactMembers extends StatefulWidget {
  final Team team;
  final List<InvitedUser> invitedUsers;
  final Future<InvitationStatus> Function(String?, String?, String)
      inviteFunction;

  const InviteContactMembers(
      {required this.team,
      required this.invitedUsers,
      required this.inviteFunction,
      Key? key})
      : super(key: key);

  @override
  State<InviteContactMembers> createState() => _InviteContactMembersState();
}

class _InviteContactMembersState extends State<InviteContactMembers>
    with SingleTickerProviderStateMixin {
  List<Friend> people = [];
  List<Friend> peopleOnWooftrax = [];
  List<Friend> peopleNotWooftrax = [];
  bool isLoading = false, isContactPermissionGranted = true;

  late TabController controller;
  List<String> tabs = <String>[];

  Future<void> fetchContacts() async {
    setState(() {
      isLoading = true;
    });

    /// Load contacts from phone without thumbnails.
    Iterable<Contact> contacts =
        await ContactsService.getContacts(withThumbnails: false);

    ///get all contacts with emails
    List<Contact> contactsWithMails =
        contacts.where((c) => c.emails!.isNotEmpty).toList();

    /// check myBuddy/wooftrax accounts
    List jsonList = await (MbApiCommunity().checkMWOwners(
            contactsToMapList(contactsWithMails),
            teamId: widget.team.id!)) ??
        [];

    /// map results and count registered accounts
    people = jsonList.map((json) {
      Friend f = Friend.fromJson(json);
      if (f.ownerEmail != Data().data?.owner.email) {
        if (f.ownerId != null) {
          peopleOnWooftrax.add(f);
        } else {
          peopleNotWooftrax.add(f);
        }
      }

      return f;
    }).toList();

    if (mounted) {
      // widget may be destroyed at this point
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> checkContactPermission() async {
    isContactPermissionGranted = await askContactPermissions();
    setState(() {});
    if (isContactPermissionGranted) {
      fetchContacts();
    }
  }

  @override
  void initState() {
    super.initState();
    tabs.add('APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_WOOFTRAX_CONTACTS'
        .tr('On WoofTrax'));
    tabs.add('APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_NOT_WOOFTRAX_CONTACTS'
        .tr('Not on WoofTrax'));
    controller = TabController(length: tabs.length, vsync: this);
    checkContactPermission();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? const Center(
            heightFactor: 4.0,
            child: CircularProgressIndicator.adaptive(),
          )
        : Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 15.0),
                child: TextField(
                  decoration: InputDecoration(
                    prefixIcon: const Icon(
                      Icons.search,
                      color: Color(0xff25323B),
                    ),
                    hintText: 'APPLICATION_MOBILE_COMMUNITY_SEARCH_EMAIL_NAME'
                        .tr("Search by Email or Name"),
                    hintStyle: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: const Color(0xff25323B), fontSize: 13),
                  ),
                  onChanged: searchUser,
                  keyboardType: TextInputType.emailAddress,
                  enableSuggestions: false,
                  autocorrect: false,
                  textInputAction: TextInputAction.done,
                ),
              ),
              TabBar(
                controller: controller,
                tabs: tabs.map((e) => Text(e)).toList(),
                labelPadding: const EdgeInsets.all(10),
              ),
              Expanded(
                child: TabBarView(
                  controller: controller,
                  children: isContactPermissionGranted
                      ? [
                          ContactsList(
                            persons: peopleOnWooftrax,
                            desc: 'APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_DESC'
                                .tr("Add your contacts directly to your group"),
                            team: widget.team,
                          ),
                          ContactsList(
                            persons: peopleNotWooftrax,
                            desc:
                                'APPLICATION_MOBILE_TITLE_COMMUNITY_INVITE_DESC_2'
                                    .tr(
                              "Invite your contacts on WoofTrax",
                            ),
                            team: widget.team,
                            inviteFunction: widget.inviteFunction,
                            invitedUsers: widget.invitedUsers,
                          )
                        ]
                      : List.generate(
                          tabs.length,
                          (index) => Center(
                                  child: Text(
                                'WOOFTRAX_ALERT_CONTACT_PERMISSION_DENIED_TITLE'
                                    .tr(),
                                textAlign: TextAlign.center,
                              ))),
                ),
              ),
            ],
          );
  }

  void searchUser(String val) {
    if (val.isEmpty) {
      if (people.isNotEmpty) {
        peopleOnWooftrax =
            people.where((element) => element.ownerId != null).toList();
        peopleNotWooftrax =
            people.where((element) => element.ownerId == null).toList();
      }
    } else {
      if (controller.index == 0) {
        peopleOnWooftrax =
            people.where((element) => element.ownerId != null).toList();
        peopleOnWooftrax = peopleOnWooftrax
            .where((element) =>
                element.ownerEmail!.toLowerCase().contains(val.toLowerCase()) ||
                element.fullName.toLowerCase().contains(val.toLowerCase()))
            .toList();
      } else {
        peopleNotWooftrax =
            people.where((element) => element.ownerId == null).toList();
        peopleNotWooftrax = peopleNotWooftrax
            .where((element) =>
                element.ownerEmail!.toLowerCase().contains(val.toLowerCase()) ||
                element.fullName.toLowerCase().contains(val.toLowerCase()))
            .toList();
      }
    }
    setState(() {});
  }
}
