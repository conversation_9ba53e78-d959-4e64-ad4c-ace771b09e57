import 'package:flutter/material.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/ui/templates/community/widgets/team_invite_by_email_widget.dart';
import 'package:mybuddy/ui/templates/community/widgets/team_sent_email_invitations_widget.dart';

class InviteEmail extends StatefulWidget {
  final Team team;
  final List<InvitedUser> invitedUsers;
  final Future<InvitationStatus> Function(String?, String?, String)
      inviteFunction;

  const InviteEmail(
      {Key? key,
      required this.team,
      required this.invitedUsers,
      required this.inviteFunction})
      : super(key: key);

  @override
  _InviteEmailState createState() => _InviteEmailState();
}

class _InviteEmailState extends State<InviteEmail> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TeamInviteByEmail(
            team: widget.team, inviteFunction: widget.inviteFunction),
        Flexible(
          child: TeamSentEmailInvitations(
              team: widget.team, invitedUsers: widget.invitedUsers),
        )
      ],
    );
  }
}
