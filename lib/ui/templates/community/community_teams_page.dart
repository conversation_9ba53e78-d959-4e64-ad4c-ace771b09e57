import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get/instance_manager.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/teams_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/components/pagination_widget.dart';
import 'package:mybuddy/ui/components/widget_add_new_button.dart';

import '../../components/internet_widget_switcher.dart';
import 'community_team_screen.dart';
import 'create_community_pack.dart';

class TeamListPage extends StatefulWidget {
  const TeamListPage({Key? key}) : super(key: key);

  @override
  State<TeamListPage> createState() => _TeamListPageState();
}

class _TeamListPageState extends State<TeamListPage> {
  final TeamsController teamsController = Get.put(TeamsController());

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    if (teamsController.error) {
      teamsController.pageFetch();
    }
  }

  @override
  Widget build(BuildContext context) {
    return InternetWidgetSwitcher(
      onlineWidget: PaginationWidget(
        child: (context, index) => Obx(
          () => TeamCard(
              team: teamsController.dataList[index],
              isExpanded: index == teamsController.expandedIndex,
              onExpanded: () => teamsController.expandedIndex =
                  index == teamsController.expandedIndex ? -1 : index,
              currentTab: teamsController.currentTab),
        ),
        leadingWidget: teamsController.currentTab == 0
            ? AddNewButton(
                label: "APPLICATION_MOBILE_COMMUNITY_CREATE_NEW_GROUP".tr(),
                onPressed: () =>
                    {Tools().navigatorPush(const CreateCommunityPack())},
                svgAssetPath: 'assets/icon/create_group.svg')
            : _searchGroupField(context, teamsController),
        emptyWidget: _emptyGroupsWidget(),
        controller: teamsController,
      ),
      retryMethod: () {
        teamsController.initialFetch();
      },
    );
  }

  Widget _emptyGroupsWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 15),
          child: AddNewButton(
              label: "APPLICATION_MOBILE_COMMUNITY_CREATE_NEW_GROUP".tr(),
              onPressed: () =>
                  {Tools().navigatorPush(const CreateCommunityPack())},
              svgAssetPath: 'assets/icon/create_group.svg'),
        ),
        Column(
          children: [
            Text(
              "APPLICATION_MOBILE_LABEL_EMPTY_GROUP_LINE1".tr(),
              style: const TextStyle(
                color: Colors.black,
                fontSize: 21,
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              "APPLICATION_MOBILE_LABEL_EMPTY_GROUP_LINE2".tr(),
              style: const TextStyle(
                color: Color(0xff4D4D4D),
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              "APPLICATION_MOBILE_LABEL_EMPTY_GROUP_LINE3".tr(),
              style: const TextStyle(
                color: Color(0xff474747),
                fontSize: 15,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox.shrink(),
      ],
    );
  }

  Widget _searchGroupField(BuildContext context, TeamsController controller) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0, horizontal: 15.0),
      child: TextField(
        decoration: InputDecoration(
            prefixIcon: const Icon(
              Icons.search,
              color: Color(0xff419563),
            ),
            hintText: 'APPLICATION_MOBILE_COMMUNITY_GROUP_SEARCH'
                .tr("Search a group..."),
            hintStyle: Theme.of(context)
                .textTheme
                .bodySmall
                ?.copyWith(color: const Color(0xff419563), fontSize: 13),
            focusedBorder: const OutlineInputBorder(
                borderSide: BorderSide(color: Color(0xff419563)),
                borderRadius: BorderRadius.all(Radius.circular(100.0))),
            border: const OutlineInputBorder(
                borderSide: BorderSide(color: Color(0xff419563)),
                borderRadius: BorderRadius.all(Radius.circular(100.0)))),
        onChanged: controller.setQuery,
        keyboardType: TextInputType.emailAddress,
        enableSuggestions: false,
        autocorrect: false,
        textInputAction: TextInputAction.done,
      ),
    );
  }
}

class TeamCard extends StatelessWidget {
  final Team team;
  final bool isExpanded;
  final VoidCallback onExpanded;
  final int currentTab;

  const TeamCard({
    required this.team,
    required this.isExpanded,
    required this.onExpanded,
    required this.currentTab,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (currentTab == 0) {
          Tools().navigatorPush(TeamScreen(team: team));
        } else {
          Tools().common.showCustomDialog(
                title: "Would you like to join the ${team.name}?",
                description:
                    "Join fellow dog lovers in the ${team.name}! Click Join to connect and share.",
                buttonColor: const Color(0xff419563),
                validTitle: 'APPLICATION_MOBILE_BUTTON_LABEL_JOIN'.tr("Join"),
                rejectTitle:
                    'APPLICATION_MOBILE_BUTTON_LABEL_CANCEL'.tr("Cancel"),
                onValid: () async {
                  MBResponse response =
                      await MbApiCommunity().addMeToTeam(context, team);
                  if (response.success) {
                    Tools().navigatorPop();
                    Tools().navigatorPush(TeamScreen(team: team));
                  }
                },
              );
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        decoration: _tileDecoration(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 3),
            Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MBAvatar(
                  size: 45,
                  imageUrl: team.imageUrl,
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  noImageUrl: const Icon(Icons.image, size: 20.0),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 5),
                      Text(
                        team.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 15,
                          color: Color(0xff474747),
                        ),
                      ),
                      const SizedBox(height: 3),
                      Text(
                        team.description!,
                        style: const TextStyle(
                          fontWeight: FontWeight.w300,
                          fontSize: 13,
                          color: Color(0xff5A5A5A),
                        ),
                        maxLines: isExpanded ? 50 : 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: onExpanded,
                  icon: Icon(
                    isExpanded
                        ? FontAwesomeIcons.chevronUp
                        : FontAwesomeIcons.chevronDown,
                  ),
                  iconSize: 12,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  color: const Color(0xff474747),
                  alignment: Alignment.topRight,
                ),
              ],
            ),
            const SizedBox(
              height: 10,
            ),
            const Text(
              'Totals',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
                color: Color(0xff218359),
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 5),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 7),
              decoration: _tileDecoration(),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  groupDetailTile(
                    icon: "assets/icon/walk.png",
                    title: "Walks",
                    value: team.totalWalks.toString(),
                  ),
                  groupDetailTile(
                    icon: "assets/icon/miles_purple_icon.png",
                    title: "Miles",
                    value: team.totalMiles!.toStringAsFixed(1),
                  ),
                  groupDetailTile(
                    icon: "assets/icon/members.png",
                    title: "Members",
                    value: team.teamMembersCount.toString(),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  BoxDecoration _tileDecoration() {
    return BoxDecoration(
      border: Border.all(
        color: const Color(0xffEEEEEE),
        width: 1,
      ),
      color: Colors.white,
      borderRadius: BorderRadius.circular(5),
    );
  }

  Column groupDetailTile(
      {required String icon, required String title, required String value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Image.asset(
              icon,
              width: 16,
              height: 16,
            ),
            const SizedBox(width: 2),
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 12,
                color: Color(0xff939393),
              ),
            ),
          ],
        ),
        const SizedBox(height: 5),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 13,
            color: Color(0xff474747),
          ),
        ),
      ],
    );
  }
}
