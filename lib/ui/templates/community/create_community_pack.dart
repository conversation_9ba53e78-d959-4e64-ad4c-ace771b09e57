import 'dart:io';

import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'community_invite_members.dart';
import 'widgets/team_widgets.dart';

class _InheritedCommunityPack extends InheritedWidget {
  final CreateCommunityPackState data;

  const _InheritedCommunityPack({
    Key? key,
    required this.data,
    required Widget child,
  }) : super(key: key, child: child);

  @override
  bool updateShouldNotify(covariant InheritedWidget oldWidget) => true;
}

class CreateCommunityPack extends StatefulWidget {
  final Team? team;
  final Function(Team)? updatedTeam;

  const CreateCommunityPack({Key? key, this.team, this.updatedTeam})
      : super(key: key);

  @override
  CreateCommunityPackState createState() => CreateCommunityPackState();

  static CreateCommunityPackState of(BuildContext context,
      {bool rebuild = true}) {
    return rebuild
        ? context
            .dependOnInheritedWidgetOfExactType<_InheritedCommunityPack>()!
            .data
        : context
            .findAncestorWidgetOfExactType<_InheritedCommunityPack>()!
            .data;
  }
}

class CreateCommunityPackState extends State<CreateCommunityPack> {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final SharedPreferences sharedPreferences = SettingsDelegate().prefs;
  late List<String> _invitedFriends;
  bool alreadyInvited = false;

  bool everyOneCanInvite = false;
  List<Friend> people = <Friend>[];
  File? avatar;

  late Team _team;

  late bool isCreating;

  late bool shouldPopulate;

  late bool isLoading;

  String? get teamAvatarUrl => _team.imageUrl;

  String get name => _team.name;

  String get description => _team.description ?? '';

  void setTeamName(String? value) {
    _team.name = value!.trim();
  }

  void setTeamDescription(String? value) {
    _team.description = value;
  }

  void setAvatar(File file) {
    if (isCreating) {
      setState(() {
        avatar = file;
      });
    } else {
      MbApiCommunity().handleTeam(null, _team, avatar: file).then((response) {
        if (mounted) {
          setState(() {
            _team = Team.fromJson(response.body['team']);
          });
        }
      });
    }
  }

  void switchEveryOneCanInvite(bool value) {
    setState(() {
      everyOneCanInvite = value;
    });
  }

  void handlePeople(Friend friend) {
    if (_invitedFriends.contains(friend.ownerEmail)) {
      setState(() => alreadyInvited = true);
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() => alreadyInvited = false);
        }
      });
    } else {
      setState(() {
        if (people.contains(friend)) {
          people.remove(friend);
        } else {
          people.add(friend);
        }
      });
    }
  }

  void completeTeam() {
    _team.everyoneCanInvite = everyOneCanInvite;
    for (var person in people) {
      if (person.ownerEmail != null &&
          !_invitedFriends.contains(person.ownerEmail)) {
        _invitedFriends.add(person.ownerEmail!);
      }
    }
    sharedPreferences.setStringList('invited_friends', _invitedFriends);
  }

  @override
  void initState() {
    super.initState();
    _team = widget.team ?? Team();
    isCreating = widget.team == null;
    shouldPopulate = isCreating;
    if (!isCreating) {
      everyOneCanInvite = widget.team!.everyoneCanInvite;
    }
    _invitedFriends = sharedPreferences.getStringList('invited_friends') ?? [];
    isLoading = false;
  }

  @override
  Widget build(BuildContext context) {
    return _InheritedCommunityPack(
      data: this,
      child: Scaffold(
        backgroundColor: const Color(0xfffbfbfb),
        persistentFooterButtons: [
          _createButton(),
        ],
        appBar: MBAppBar(
          iconTheme:
              IconTheme.of(context).copyWith(color: const Color(0xff162941)),
          title: Text(
            isCreating
                ? 'APPLICATION_MOBILE_BUTTON_COMMUNITY_PACK_CREATE'
                    .tr('Create a Group')
                : 'Edit ${_team.name}',
            style: Theme.of(context).textTheme.headline6,
          ),
        ),
        body: GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: SizedBox(
            height: MediaQuery.of(context).size.height,
            child: SingleChildScrollView(
              child: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: const [
                    TeamAvatarField(),
                    TeamNameField(),
                    TeamDescriptionField(),
                    Divider(indent: 10.0, endIndent: 10.0),
                    TeamEveryOneCanInviteField(),
                    Divider(indent: 10.0, endIndent: 10.0),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _createButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: isLoading
            ? null
            : () {
                FormState? formState = formKey.currentState;
                if (formState != null && formState.validate()) {
                  setState(() {
                    isLoading = true;
                  });
                  formState.save();
                  completeTeam();
                  MbApiCommunity()
                      .handleTeam(context, _team, avatar: avatar)
                      .then((response) {
                    setState(() {
                      isLoading = false;
                    });
                    if (response.success) {
                      Tools().common.showMessage(
                            context,
                            isCreating
                                ? 'APPLICATION_MOBILE_COMMUNITY_CREATED'
                                    .tr('Group Created Successfully !')
                                : 'APPLICATION_MOBILE_COMMUNITY_EDITED'
                                    .tr('Group Edited Successfully !'),
                            Theme.of(context).colorScheme.secondary,
                          );
                      Team team = Team.fromJson(response.body["team"]);
                      if (isCreating) {
                        Tools().navigatorReplacement(CommunityInviteMembers(
                          team: team,
                        ));
                      } else {
                        if (widget.updatedTeam != null) {
                          widget.updatedTeam!(team);
                          Tools().navigatorPop(value: response.success);
                        }
                      }
                    }
                  });
                }
              },
        child: Text(
          isCreating
              ? 'APPLICATION_MOBILE_BUTTON_LABEL_TEAM_CREATE'.tr('Create Group')
              : 'APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr(),
        ),
      ),
    );
  }
}
