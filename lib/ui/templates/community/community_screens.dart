import 'dart:async';

import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/shelter.dart';
import 'package:mybuddy/ui/components/earning_points_footer_component.dart';

class FriendsListScreen extends StatefulWidget {
  final Shelter? shelter;

  const FriendsListScreen({Key? key, this.shelter})
      : super(key: key);

  @override
  _FriendsListScreenState createState() => _FriendsListScreenState();
}

class _FriendsListScreenState extends State<FriendsListScreen>
    with AutomaticKeepAliveClientMixin {
  final int _defaultPerPageCount = 50;
  final int _nextPageThreshold = 10;
  late int _pageNumber;
  int _totalItemsCount = 0;
  int _position = 0;
  String shelterName = '';

  bool _hasMore = true;
  bool _error = false;
  bool _loading = true;
  late bool _isYours;

  late Owner _owner;
  late List<Friend> _friends;

  // TextEditingController _editingController;

  // Timer _debounce;

  @override
  void didUpdateWidget(oldWidget) {
    super.didUpdateWidget(oldWidget);
    Owner updateOwner = Data().get().owner;
    if (!_loading &&  (
        (_isYours && oldWidget.shelter?.name != widget.shelter?.name) ||
        _owner.points != updateOwner.points ||
        _owner.publicAvatarId != updateOwner.publicAvatarId
    )) {
      fetch(onInitState: true);
    }
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _isYours = widget.shelter != null;
    _owner = Data().get().owner.copy();
    _friends = <Friend>[];
    // _editingController = TextEditingController();

    fetch(onInitState: true);
  }

  @override
  void dispose() {
    // _debounce?.cancel();
    // _editingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 5.0),
          child: Text(
            _isYours
                ? shelterName
                : '${AppConfig.of(context).appDisplayName.replaceAll("®", "")} $shelterName',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ),
        Text(
          'Your Position: $_position' +
              (_isYours ?' | Active Walkers: ${_totalItemsCount.toString()}' : ''),
          style: Theme.of(context).textTheme.titleMedium,
        ),
        // Padding(
        //   padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 5.0),
        //   child: TextField(
        //     controller: _editingController,
        //     decoration: InputDecoration(
        //       hintText: 'User name',
        //       prefixIcon: Icon(Icons.search),
        //     ),
        //     autocorrect: false,
        //     toolbarOptions: ToolbarOptions(),
        //     keyboardType: TextInputType.name,
        //     onChanged: _onSearchChanged,
        //   ),
        // ),
        Expanded(
          child: _getList(context),
        ),
      ],
    );
  }

  Future<void> retrieveCount() async {
    try {
      var response = await MbApiCommunity()
          .countActiveWalkers(slug: _isYours ? 'package' : 'all');

      if (response.success) {
        Tools.debugPrint('je veux le nom ${response.body['packageName']}');
        if (mounted) {
          _position = response.body['position'] ?? 0;
          setState(() {
            shelterName = widget.shelter?.name ?? "Ranking";
            _totalItemsCount = response.body['totalActiveWalkers'];
          });
        }
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
    }
  }

  Future<void> fetch({bool onInitState = false}) async {
    if (onInitState) {
      _pageNumber = 1;
      _loading = true;
      _friends.clear();
      await retrieveCount();
    }
    try {
      var response = await MbApiCommunity().getCommunity(
        slug: _isYours ? 'package' : 'all',
        page: _pageNumber,
        perPageCount: _defaultPerPageCount,
        // query: _editingController.text
      );

      if (response.success) {
        List<Friend> fetchedFriends =
            Friend.parseFriends(response.body['results']);

        _hasMore = fetchedFriends.length == _defaultPerPageCount;
        _pageNumber += 1;
        if (onInitState) {
          _friends = fetchedFriends;
          _owner = Data().get().owner.copy();
        } else {
          _friends.addAll(fetchedFriends);
        }

        if (mounted) {
          setState(() {
            _loading = false;
          });
        }
      }else{
        if (mounted) {
          setState(() {
            _loading = false;
            _error = true;
          });
        }
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      if (mounted) {
        setState(() {
          _loading = false;
          _error = true;
        });
      }
    }
  }

  // void _onSearchChanged(String query) {
  //   if (_debounce != null && _debounce.isActive) _debounce.cancel();
  //   _debounce = Timer(const Duration(milliseconds: 1000), () {
  //     setState(() {
  //       _loading = true;
  //       _pageNumber = 1;
  //       _friends.clear();
  //     });
  //     fetch();
  //   });
  // }

  Widget _getList(BuildContext context) {
    if (_friends.isEmpty) {
      if (_loading) {
        return _loadingWidget(context);
      } else if (_error) {
        return _errorWidget(context);
      }
      return Center(child: Text('APPLICATION_MOBILE_TEXT_EMPTY_DATA'.tr()));
    } else {
      return RefreshIndicator(
        onRefresh: () async {
          fetch(onInitState: true);
        },
        child: ListView.separated(
          primary: false,
          shrinkWrap: true,
          separatorBuilder: (context, index) => const Divider(height: 0.0),
          itemCount: _friends.length + (_hasMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (_hasMore && index == _friends.length - _nextPageThreshold) {
              fetch();
            }
            if (index == _friends.length) {
              if (_error) {
                return _errorWidget(context);
              } else {
                return _loadingWidget(context);
              }
            }
            final Friend friend = _friends[index];
            final Map<String, dynamic> level =
                friend.ownerTotalPoints.toLevel();

            return ListTile(
              dense: true,
              contentPadding: const EdgeInsets.fromLTRB(5.0, 3.0, 10.0, 3.0),
              tileColor: Colors.white,
              title: Text(
                friend.ownerFirstName ?? '',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              leading: Container(
                width: 45,
                alignment: Alignment.center,
                child: Text(
                  friend.position?.toString() ?? '',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  EarnedPointsWidget(totalPoints: friend.ownerTotalPoints),
                  Text(
                    'Level ${level['level']}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            );
          },
        ),
      );
    }
  }

  Widget _errorWidget(BuildContext context) {
    return Center(
      child: TextButton(
        onPressed: () {
          setState(() {
            _loading = true;
            _error = false;
            fetch();
          });
        },
        child: Text(
          'Error while loading data, tap to try again',
          style: Theme.of(context).textTheme.headlineSmall,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _loadingWidget(BuildContext context) {
    return Center(
      child: Text(
        'Loading...',
        style: Theme.of(context).textTheme.headlineSmall,
      ),
    );
  }
}
