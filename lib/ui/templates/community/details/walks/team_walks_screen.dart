import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/community/community_team_screen.dart';
import 'package:mybuddy/ui/templates/workout/workout_card_widget.dart';

import '../../../../../Tools/tools.dart';
import '../../../../../blocs/walk_provider.dart';
import '../../../../components/mb_no_record_found.dart';

class TeamWalksScreen extends StatefulWidget {
  const TeamWalksScreen({Key? key}) : super(key: key);

  @override
  _TeamWalksScreenState createState() => _TeamWalksScreenState();
}

class _TeamWalksScreenState extends State<TeamWalksScreen> {
  bool init = false;

  // @override
  // void didChangeDependencies() {
  //   super.didChangeDependencies();
  //   TeamScreen.of(context).fetchTeamWalks();
  // }

  @override
  Widget build(BuildContext context) {
    if (!init) {
      TeamScreen.of(context).fetchTeamWalks();
      init = true;
    }
    final TeamScreenState state = TeamScreen.of(context);

    if (state.workouts.isEmpty) {
      if (state.loading) {
        return _loadingWidget(context);
      } else if (state.error) {
        return _errorWidget(context);
      }
      return _emptyWidget(context);
    } else {
      return ListView.separated(
          padding: const EdgeInsets.fromLTRB(0.0, 20.0, 0.0, 15.0),
          separatorBuilder: (context, index) => const Divider(),
          itemCount: state.workouts.length + (state.hasMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (state.hasMore &&
                index == state.workouts.length - state.nextPageThreshold) {
              state.fetchTeamWalks();
            }
            if (index == state.workouts.length) {
              if (state.error) {
                return _errorWidget(context);
              } else {
                return _loadingWidget(context);
              }
            }

            return WorkoutTileWidget(
              workout: state.workouts[index],
            );
          });
    }
  }

  Widget _errorWidget(BuildContext context) {
    return Center(
      child: TextButton(
        onPressed: () => TeamScreen.of(context).retry(),
        child: Text(
          'APPLICATION_MOBILE_COMMUNITY_DATA_ERROR'
              .tr('Error while loading data, tap to try again'),
          style: Theme.of(context).textTheme.headline6,
        ),
      ),
    );
  }

  Widget _emptyWidget(BuildContext context) {
    final WalkProvider walkProvider = WalkProvider.of(context);

    return MBNoRecordFound(
      title: 'APPLICATION_MOBILE_TEXT_NO_ACTIVITY'.tr("No Activities Found"),
      otherWidget: SizedBox(
          width: 240,
          child: ElevatedButton(
            onPressed: () async => await Tools()
                .location
                .openWorkoutPage(context)
                .then((value) =>
                    TeamScreen.of(context).fetchTeamWalks(reload: true)),
            child: Obx(
              () => Text(
                walkProvider.getStartWalkCardTitle(),
                style: const TextStyle(fontSize: 14),
              ),
            ),
          )),
    );
  }

  Widget _loadingWidget(BuildContext context) {
    return Center(
      child: Text(
        'APPLICATION_MOBILE_COMMUNITY_LOADING'.tr('Loading...'),
        style: Theme.of(context).textTheme.headline6,
      ),
    );
  }
}
