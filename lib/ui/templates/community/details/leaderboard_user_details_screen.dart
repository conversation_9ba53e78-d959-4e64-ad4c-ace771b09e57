import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mybuddy/controllers/leaderboard_user_detail_controller.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/models/group_leaderboard.dart';
import 'package:mybuddy/ui/components/mb_base_home_screen.dart';

import '../widgets/leaderboard_user_details_header.dart';
import '../widgets/leaderboard_user_details_pets.dart';

class LeaderboardUserDetailsScreen extends StatelessWidget {
  LeaderboardUserDetailsScreen(
      {Key? key, required GroupLeaderboardMember user, required Team team}) {
    controller = Get.put(LeaderboardUserDetailController(user.obs, team));
  }

  late LeaderboardUserDetailController controller;

  @override
  Widget build(BuildContext context) {
    return Material(
      child: HomeSliverScreen(
        expandedHeight: 0.0,
        automaticallyImplyLeading: true,
        primaryColor: const Color(0xff162941),
        backgroundColor: const Color.fromRGBO(251, 251, 251, 1),
        title: controller.team.name,
        child: Card(
          margin: const EdgeInsets.fromLTRB(15, 20, 15, 35),
          child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
              child: SizedBox(
                width: double.infinity,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    LeaderboardUserDetailsHeader(controller),
                    Obx(() => controller.loading
                        ? SizedBox(
                            height: 100,
                            child: Center(
                              child: Text(
                                'Loading...',
                                style: Theme.of(context).textTheme.headline6,
                              ),
                            ),
                          )
                        : Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _charityWidget(),
                                const SizedBox(height: 20),
                                LeaderboardUserDetailsPets(controller),
                              ],
                            ),
                          )),
                  ],
                ),
              )),
        ),
      ),
    );
  }

  Widget _charityWidget() {
    return Visibility(
      visible: controller.user.shelter.isNotEmpty,
      child: Align(
        alignment: Alignment.centerLeft,
        child: RichText(
            text: TextSpan(
                text: "Charity: ",
                style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    color: Color(0xff474747)),
                children: [
              TextSpan(
                text: controller.user.shelter,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ])),
      ),
    );
  }
}
