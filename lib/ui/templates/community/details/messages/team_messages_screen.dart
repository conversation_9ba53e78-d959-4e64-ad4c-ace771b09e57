import 'dart:async';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:linkwell/linkwell.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_api_com.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/message_bloc.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/extensions/datetime_extensions.dart';
import 'package:mybuddy/models/message.dart';
import 'package:mybuddy/ui/components/empty_inbox_widget.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/components/widget_simple_image_dialog.dart';


class TeamMessagesScreen extends StatefulWidget {
  final bool canUpdate;

  const TeamMessagesScreen({Key? key, required this.canUpdate}) : super(key: key);

  @override
  _TeamMessagesScreenState createState() => _TeamMessagesScreenState();
}

class _TeamMessagesScreenState extends State<TeamMessagesScreen> {
  late TextEditingController textEditingController;
  late ScrollController scrollController;

  MBMessage? message;
  List<MessageItem> items = <MessageItem>[];

  bool show = false;

  Future<void> refreshMessage() async {
    if(message != null) {
      await MbApiCom().setMessageReadAt(message!.id);
    }
  }

  Future<void> scrollListener() async {
    setState(() {
      show = true;
    });
    await Future.delayed(const Duration(milliseconds: 2000), () {
      if(mounted) {
        setState(() {
          show = false;
        });
      }
    });
  }

  @override
  void initState() {
    super.initState();
    textEditingController = TextEditingController();
    scrollController = ScrollController();

    scrollController.addListener(scrollListener);

    SchedulerBinding.instance!.addPostFrameCallback((_) {
      if(scrollController.hasClients) {
        scrollController.jumpTo(scrollController.position.maxScrollExtent);
      }
    });
  }
  @override
  void dispose() {
    super.dispose();
    refreshMessage();
    textEditingController.dispose();
    scrollController.removeListener(scrollListener);
    scrollController.dispose();
  }
  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<MessageBloc>(context);

    return Column(
      children: <Widget>[
        Expanded(
          child: Scrollbar(
            child: StreamBuilder<MBMessage?>(
                stream: bloc.stream,
                builder: (BuildContext context, AsyncSnapshot<MBMessage?> snapshot) {
                  if (snapshot.connectionState == ConnectionState.none) {
                    return Center(
                      child: Text(
                        'APPLICATION_MOBILE_MESSAGE_ERROR_NO_NETWORK'.tr(),
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    );
                  }
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }
                  if(snapshot.hasData) {
                    message = snapshot.data;
                    items = message!.items;
                    if(items.isEmpty) {
                      return const EmptyInboxWidget(type: InboxType.messageItems);
                    }
                  }
                  return RefreshIndicator(
                    onRefresh: () async {
                      await refreshMessage();
                      bloc.update();
                    },
                    child: GroupedListView<MessageItem, int>(
                      controller: scrollController,
                      padding: EdgeInsets.zero,
                      reverse: true,
                      order: GroupedListOrder.DESC,
                      itemComparator: (MessageItem item1, MessageItem item2){
                        return item1.timestamp.compareTo(item2.timestamp);
                      },
                      itemBuilder: (BuildContext ctx, MessageItem item) {
                        return buildLine(ctx, item);
                      },
                      elements: items,
                      useStickyGroupSeparators: show,
                      floatingHeader: true,
                      groupBy: (MessageItem item) {
                        return item.timestamp.year * 10000 +
                            item.timestamp.month * 100 +
                            item.timestamp.day;
                      },
                      groupHeaderBuilder: (MessageItem item) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              width: 90.0,
                              height: 30.0,
                              margin: const EdgeInsets.only(top: 5.0),
                              decoration: BoxDecoration(
                                borderRadius: const BorderRadius.all(Radius.circular(15.0)),
                                color: Theme.of(context).primaryColorLight,
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                item.timestamp.toDayFormat()!,
                                style: Theme.of(context).textTheme.titleMedium!.copyWith(
                                  fontSize: 10.0
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  );
                }),
          ),
        ),
        SizedBox(
          width: MediaQuery.of(context).size.width,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: <Widget>[
                IconButton(
                  icon: const Icon(FontAwesomeIcons.camera),
                  color: Theme.of(context).colorScheme.secondary,
                  onPressed: widget.canUpdate ? () async {
                    File? image = await Tools()
                        .image
                        .pickImage(context, myBuddyGallery: true);
                    if (image == null) {
                      return;
                    }
                    if (await validImage(context, image)) {
                      await MbApiCom().addMessageItemRequest(
                        context,
                        message,
                        image: image,
                      );
                      bloc.update();
                    }
                  } : null,
                ),
                Expanded(
                  child: TextField(
                    maxLines: 2,
                    controller: textEditingController,
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(7.0),
                      border: const OutlineInputBorder(
                        borderRadius: BorderRadius.all(
                          Radius.circular(10.0),
                        ),
                      ),
                      enabled: widget.canUpdate,
                      filled: true,
                      fillColor: Colors.white70,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(FontAwesomeIcons.solidPaperPlane),
                  onPressed: widget.canUpdate ? () async {
                    String value = textEditingController.text;
                    value = value.trim();
                    if (value != '') {
                      FocusScope.of(context).unfocus();
                      MBResponse response = await MbApiCom().addMessageItemRequest(
                        null,
                        message,
                        content: value,
                      );
                      bloc.update();
                      if (response.success) {
                        textEditingController.text = '';
                      }
                    }
                  } : null,
                  color: Theme.of(context).colorScheme.secondary,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget buildLine(BuildContext context, MessageItem item) {
    if(message == null) {
      return Container();
    }
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: message!.isFromOwner(item.userId)
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!message!.isFromOwner(item.userId))
            Padding(
              padding: const EdgeInsets.only(right: 6.0),
              child: MBAvatar(
                imageUrl: message!.getItemSender(item.userId)!.userImageUrl,
                size: 30.0,
                backgroundColor: Theme.of(context).primaryColorLight,
                noImageUrl: Text(
                  message!.getItemSender(item.userId)!.initials,
                  style: TextStyle(
                      fontSize: 10.0,
                      color: Theme.of(context).primaryColor),
                ),
              ),
            ),

          ///endif
          _buildContent(item),
          Visibility(
            visible: message!.isFromOwner(item.userId) &&
                message!.messageUsers
                    .any((mu) => mu.messageReadAt != null && mu.messageReadAt!.isAfter(item.timestamp)),
            child: const Icon(
              FontAwesomeIcons.check,
              color: Colors.green,
              size: 12.0,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(MessageItem item) {
    return Builder(
      builder: (context) {
        if(item.file != null) {
          return SizedBox(
            height: 70,
            child: IconButton(
              icon: const Icon(FontAwesomeIcons.file),
              onPressed: () {
                Tools()
                    .common
                    .launchURL(MbBaseApi().getFileUrl(item.file!), context);
              },
            ),
          );
        }
        return Stack(
          children: [
            Container(
              padding: const EdgeInsets.fromLTRB(10.0, 10.0, 10.0, 25.0),
              constraints: BoxConstraints(
                minWidth: 75.0,
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(15.0)),
                color: message!.isFromOwner(item.userId)
                    ? Theme.of(context).colorScheme.secondary
                    : Theme.of(context).colorScheme.surface,
              ),
              child: item.imageId == null ? LinkWell(
                item.content,
                textWidthBasis: TextWidthBasis.longestLine,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: message!.isFromOwner(item.userId)
                    ? Colors.white
                    : Colors.black87,
                ),
                linkStyle: message!.isFromOwner(item.userId) ? const TextStyle(
                  color: Colors.white,
                  decoration: TextDecoration.underline,
                ) : null,
              ) :  SizedBox(
                height: MediaQuery.of(context).size.height * 0.33,
                child: GestureDetector(
                  onTap: () async {
                    await showDialog(
                      barrierColor: Colors.transparent,
                      context: context,
                      builder: (_) => SimpleImageDialog(
                        imageUrl: item.imageUrl ?? item.imageId?.toImageUrl(),
                        deleteIcon: false,
                      ),
                    );
                  },
                  child: CachedNetworkImage(
                    imageUrl: item.imageUrl ?? item.imageId?.toImageUrl() ?? '',
                    fit: BoxFit.contain,
                    errorWidget: (context, url, error) => const Icon(Icons.error),
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 5.0,
              right: 10.0,
              child: Text(
                item.timestamp.toTimeFormat(),
                style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 9.0),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<bool> validImage(BuildContext context, File image) async {
    List<Widget> list = <Widget>[
      Image(
        image: FileImage(image),
      ),
      TextButton(
        onPressed: () {
          Tools().navigatorPop(removeLast: false, value: true);
        },
        child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SEND'.tr()),
      ),
      const Divider(),
      TextButton(
        onPressed: () {
          Tools().navigatorPop(removeLast: false, value: false);
        },
        child: Text('APPLICATION_MOBILE_BUTTON_LABEL_CANCEL'.tr()),
      )
    ];

    bool? confirmed = await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext ctx) {
        return AlertDialog(
          content: SingleChildScrollView(
            child: Column(
              children: list,
            ),
          ),
        );
      },
    );

    return confirmed ?? false;
  }
}
