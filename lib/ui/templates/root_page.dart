import 'dart:async';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/root_controller.dart';
import 'package:mybuddy/ui/components/waiting_screen_widget.dart';
import 'package:mybuddy/ui/templates/home/<USER>';
import 'onboarding/discover_wooftrax_page.dart';
import 'onboarding/landing_page.dart';

class RootPage extends StatelessWidget {
  RootController rootController = RootController.of;

  @override
  Widget build(BuildContext context) {
    Tools().setContext(context, 'root');
    return Obx(() {
      switch (rootController.authStatus.value) {
        case AuthStatus.onBoarding:
          return const DiscoverWoofTraxPage();

        case AuthStatus.notSignedIn:
          return LandingPage();

        case AuthStatus.signedIn:
          return const HomePage();

        case AuthStatus.notDetermined:
        default:
          return const WaitingScreenWidget();
      }
    });
  }
}
