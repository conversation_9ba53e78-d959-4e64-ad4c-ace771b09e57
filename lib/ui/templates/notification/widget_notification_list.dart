import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/notifications_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/notification.dart';
import 'package:mybuddy/ui/components/empty_inbox_widget.dart';

class WidgetNotificationList extends StatelessWidget {
  const WidgetNotificationList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<NotificationsBloc>(context);
    return StreamBuilder<List<MBNotification>>(
        stream: bloc.stream,
        builder: (context, snapshot) {
          List<MBNotification> notifications;
          if (!snapshot.hasData) return Container();
          notifications = snapshot.data!;
          if (notifications.isEmpty) {
            return const EmptyInboxWidget(type: InboxType.notifications);
          }
          return Scrollbar(
            child: ListView.separated(
              itemBuilder: (context, position) {
                MBNotification notification = notifications[position];
                return GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () async {
                      await Tools().pushNotification.handleNotification(notifications[position]);
                      bloc.update();
                    },
                    child: oneNotification(notification));
              },
              separatorBuilder: (BuildContext context, int index) => const Divider(),
              itemCount: notifications.length,
            ),
          );
        });
  }

  Widget oneNotification(MBNotification notification) => Column(
        children: <Widget>[
          Row(
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 3.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.message,
                              style:
                                  TextStyle(fontWeight: notification.read ? null : FontWeight.bold),
                            ),
                          ),
                          Builder(builder: (context) {
                            return SizedBox(
                              height: 1,
                              width: MediaQuery.of(context).size.width * 0.10,
                            );
                          })
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(12.0, 3.0, 12.0, 12.0),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: Text(
                              _typeToText(notification.type),
                              style: const TextStyle(fontSize: 11.0, color: Colors.blue),
                            ),
                          ),
                          Text(
                            Data().dateTimeToUserDateTimeStr(notification.timestamp),
                            style: const TextStyle(fontSize: 11.0, color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ],
      );

  String _typeToText(int type) {
    switch (type) {
      case 1:
        return 'APPLICATION_MOBILE_TITLE_APPOINTMENT_REQUEST'.tr();
      case 2:
        return 'APPLICATION_MOBILE_TITLE_APPOINTMENT'.tr();
      case 3:
        return 'APPLICATION_MOBILE_TITLE_ANNOUNCEMENT'.tr();
      case 4:
        return 'APPLICATION_MOBILE_TITLE_ORDER'.tr();
      case 5:
        return 'APPLICATION_MOBILE_TITLE_HOSPITALIZATION'.tr();
      case 6:
        return 'APPLICATION_MOBILE_TITLE_REMINDER'.tr();
//      case 9:
//        return 'APPLICATION_MOBILE_TITLE_APPOINTMENT'.tr();
      default:
        return 'APPLICATION_MOBILE_TITLE_MISC'.tr();
    }
  }
}
