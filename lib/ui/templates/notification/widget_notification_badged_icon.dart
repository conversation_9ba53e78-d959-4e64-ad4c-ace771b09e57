import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/unread_notifications_bloc.dart';
import 'package:mybuddy/ui/components/widget_badged_icon.dart';

class WidgetNotificationsIcon extends StatelessWidget {
  /// need to be wrapped in [BlocProvider] of [UnreadNotificationsBloc]
  const WidgetNotificationsIcon({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<UnreadNotificationsBloc>(context);
    return StreamBuilder<int>(
      stream: bloc.stream,
      builder: (BuildContext context, AsyncSnapshot<int> snapshot) {
        if (snapshot.hasData) {
          return BadgedIcon(iconData: FontAwesomeIcons.bell, badgeValue: snapshot.data,);
        }
        return const Icon(FontAwesomeIcons.bell);
      },
    );
  }
}
