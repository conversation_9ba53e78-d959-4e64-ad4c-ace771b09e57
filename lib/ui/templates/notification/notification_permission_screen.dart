import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:mybuddy/class/constants.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

import '../../../Tools/tools.dart';

class NotificationPermissionScreen extends StatelessWidget {
  NotificationPermissionScreen({Key? key}) : super(key: key) {
    //initialize the points to show to the user to guide him to enable notifications
    points = [
      "APPLICATION_MOBILE_NOTIFICATION_PERMISSION_POINT2".tr(),
      "APPLICATION_MOBILE_NOTIFICATION_PERMISSION_POINT3".tr(),
      "APPLICATION_MOBILE_NOTIFICATION_PERMISSION_POINT4".tr()
    ];
  }

  List<String> points = [];

  void switchToHomeScreen() async {
    setNotificationPermissionScreenStatus();
    Tools().navigatorPop();
  }

  // Function to show dialog when user denies notification permission to guide user to enable it using settings
  Future<void> showNotificationPermissionDeniedDialog() async {
    await showDialog(
      context: Get.context!,
      builder: (BuildContext ctx) {
        return AlertDialog(
          title: Text(
            'WOOFTRAX_NOTIFICATION_PERMISSION_DENIED_TITLE'.tr(
                'Allow WoofTrax to send you notifications about new challenges and other ways to earn more for your charity.'),
          ),
          content: Text('WOOFTRAX_ALERT_LOCATION_PERMISSION_DENIED_MESSAGE'.tr(
              'Go to Settings, WoofTrax, and set Allow Notifications to ON.')),
          actions: <Widget>[
            TextButton(
              onPressed: () async {
                await AppSettings.openAppSettings();
                Tools().navigatorPop(removeLast: false, value: false);
                switchToHomeScreen();
              },
              child: Text(
                  'WOOFTRAX_ALERT_LOCATION_PERMISSION_DENIED_OPEN_SETTINGS'
                      .tr('Go to Settings')),
            ),
            TextButton(
              onPressed: () {
                Tools().navigatorPop(removeLast: false, value: false);
                switchToHomeScreen();
              },
              child:
                  Text('APPLICATION_MOBILE_BUTTON_LABEL_CANCEL'.tr('Cancel')),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext ctx) {
    return WillPopScope(
      onWillPop: () {
        return Future.value(false);
      },
      child: Scaffold(
          backgroundColor: const Color(0xFFFBFBFB),
          appBar: AppBar(
            elevation: 1,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(15.0),
              ),
            ),
            title: Text(
              'APPLICATION_MOBILE_LABEL_NOTIFICATIONS'.tr(),
              style: Theme.of(ctx).textTheme.headline6,
            ),
            leading: const SizedBox.shrink(),
          ),
          body: Container(
            padding: const EdgeInsets.symmetric(horizontal: 30),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "APPLICATION_MOBILE_NOTIFICATION_PERMISSION_TITLE".tr(),
                  style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.w600,
                      color: Color(0xff3B3C3F)),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  "APPLICATION_MOBILE_NOTIFICATION_PERMISSION_SUB_TITLE".tr(),
                  style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      color: Color(0xff5A5B5D)),
                ),
                const SizedBox(
                  height: 20,
                ),
                ListView.separated(
                  itemBuilder: (context, index) {
                    return RichText(
                        text: TextSpan(children: [
                      const TextSpan(
                        text: "• ",
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Color(0xff979797)),
                      ),
                      TextSpan(
                        text: points[index],
                        style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            color: Color(0xff5A5B5D)),
                      ),
                    ]));
                  },
                  separatorBuilder: (context, index) {
                    return const SizedBox(
                      height: 20,
                    );
                  },
                  itemCount: points.length,
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                ),
                const SizedBox(
                  height: 100,
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.zero,
                      minimumSize: const Size(double.infinity, 40),
                      maximumSize: const Size(double.infinity, 40)),
                  onPressed: () {
                    Tools().askNotificationsPermissionAndRegisterDevice(
                        onGranted: switchToHomeScreen,
                        onDenied: () async {
                          setNotificationPermissionScreenStatus();
                          await showNotificationPermissionDeniedDialog();
                        });
                  },
                  child: Text(
                    "APPLICATION_MOBILE_LABEL_NEXT".tr().toUpperCase(),
                    style: const TextStyle(
                        fontWeight: FontWeight.w500, fontSize: 16),
                  ),
                ),
              ],
            ),
          )),
    );
  }

  void setNotificationPermissionScreenStatus() {
    SettingsDelegate().prefs.setBool(notificationPermissionAsked, true);
  }
}
