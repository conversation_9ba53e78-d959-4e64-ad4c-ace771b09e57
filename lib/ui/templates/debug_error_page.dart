import 'package:flutter/material.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_html_widget.dart';

class DebugErrorPage extends StatelessWidget {
  final String contentHtml;
  final String? contentTxt;

  const DebugErrorPage({Key? key, this.contentHtml = '', this.contentTxt}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: MBAppBar(
        title: const Text(
          'DEBUG',
          maxLines: 2,
        ),
      ),
      body: SingleChildScrollView(
        child: getDebugWidget(),
      ),
    );
  }

  Widget getDebugWidget() {
    if (contentHtml.trim() != '') {
      return MBHtmlWidget(
        htmlSrc: contentHtml,
      );
    } else if (contentTxt != null &&
        contentTxt!.trim() != '' &&
        contentTxt!.contains('</pre>')) {
      String content = contentTxt!.trim().replaceAll('', '');
      content = content.replaceAll('\\n', '<br>');

      return MBHtmlWidget(
        htmlSrc: content,
      );
    } else if (contentTxt != null && contentTxt!.trim() != '') {
      return Text(contentTxt!.trim());
    } else {
      return const Text('nothing to debug');
    }
  }
}
