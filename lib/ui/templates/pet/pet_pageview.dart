import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/pet_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/pet_weight_controller.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/ui/templates/pet/widget_pet_profil.dart';

import '../../../controllers/pet_workout_controller.dart';

class PetPageView extends StatefulWidget {
  final Pet pet;

  const PetPageView(
      {Key? key, required this.pet})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _PetPageState();
}

class _PetPageState extends State<PetPageView> {
  late List<Pet> pets;
  late int _selectedIndex;
  late PageController pageController;
  late PetWorkoutController workoutController;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView.builder(
        controller: pageController,
        //way to prevent infinite scroll on only one pet
        itemCount: pets.length == 1 ? 1 : null,
        onPageChanged: (newPage) {
          setState(() {
            _selectedIndex = newPage;
          });
          workoutController.updatePetId(pets[newPage % pets.length].id);
        },
        itemBuilder: (BuildContext context, int index) {
          return BlocProvider<PetBloc>(
            bloc: PetBloc(petId: pets[index % pets.length].id),
            child: WidgetPetProfile(),
          );
        },
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    pets = Data().getPets(livingState: LivingState.both);
    _selectedIndex = pets.lastIndexOf(widget.pet);
    pageController = PageController(
      /// move to allow two direction swipe even for the first pet
      initialPage: _selectedIndex + 100 * pets.length,
    );
    workoutController =
        Get.put(PetWorkoutController(petId: pets[_selectedIndex].id));
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }
}
