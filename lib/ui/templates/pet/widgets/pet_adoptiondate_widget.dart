import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/dog_form_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/custom_cupertino_date_picker.dart';
import 'package:mybuddy/ui/templates/pet/widgets/date_picker_widget.dart';

class PetAdoptionDateWidget extends StatelessWidget {
  PetAdoptionDateWidget(this.controller, {Key? key}) : super(key: key);

  final DogFormController controller;

  @override
  Widget build(BuildContext context) {
    return DatePickerWidget(
      label: "Select adoption date or date dog joined family",
      selectedDate: controller.adoptionDate,
      onSelectedItemChanged: (date) {
        controller.adoptionDate = date;
      },
      onSave: () {
        controller.savedAdoptionDate();
      },
    );
  }
}
