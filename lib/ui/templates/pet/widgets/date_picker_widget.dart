import 'package:flutter/material.dart';
import 'package:mybuddy/controllers/dog_form_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/custom_cupertino_date_picker.dart';

class DatePickerWidget extends StatelessWidget {
  DatePickerWidget(
      {Key? key,
      required this.label,
      required this.selectedDate,
      required this.onSelectedItemChanged,
      required this.onSave})
      : super(key: key);

  final String label;
  final DateTime selectedDate;
  final void Function(DateTime) onSelectedItemChanged;
  final VoidCallback onSave;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: const TextStyle(color: Color(0xff939393), fontSize: 13),
          ),
        ),
        Expanded(
            child: Center(
          child: CustomCupertinoDatePicker(
            itemExtent: 50,
            minDate: DateTime.now().subtract(const Duration(days: 31 * 365)),
            maxDate: DateTime.now(),
            selectedDate: selectedDate,
            selectionOverlay: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: double.infinity,
                  height: 35,
                  decoration: const BoxDecoration(
                    border: Border.symmetric(
                      horizontal:
                          BorderSide(color: Color(0xFFC4C4C4), width: 1),
                    ),
                  ),
                ),
              ],
            ),
            selectedStyle: Theme.of(context).textTheme.headline6!.copyWith(
                  color: const Color(0xff419563),
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
            unselectedStyle: Theme.of(context).textTheme.headline6!.copyWith(
                  color: const Color(0xff192943),
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
            disabledStyle: Theme.of(context).textTheme.headline6!.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
            onSelectedItemChanged: onSelectedItemChanged,
          ),
        )),
        _saveButton(context, onSave),
      ],
    );
  }

  Widget _saveButton(context, void Function() onPressed) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(5, 5, 5, 10),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 10)),
        onPressed: onPressed,
        child: Container(
          width: double.infinity,
          alignment: Alignment.center,
          child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SAVE'.tr()),
        ),
      ),
    );
  }
}
