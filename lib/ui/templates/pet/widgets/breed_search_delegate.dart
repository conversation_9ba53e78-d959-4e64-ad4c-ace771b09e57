
import 'package:flutter/material.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/models/refs_data.dart';

import 'package:mybuddy/extensions/string_extensions.dart';

class BreedSearchDelegate extends SearchDelegate<Breed?> {
  final int? speciesId;
  final int? breedId;

  BreedSearchDelegate(this.speciesId,{this.breedId});

  late List<Breed> _breeds;

  @override
  void showResults(BuildContext context) => super.showResults(context);

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
          showSuggestions(context);
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) => BackButton(
    onPressed: () => close(context, null),
  );

  @override
  Widget buildResults(BuildContext context) {
    _breeds = Ref().get().getFilteredBreeds(speciesId);
    _breeds.removeWhere((element) => element.id==breedId);

    if (_breeds.isEmpty) {
      return Center(
        child: Text(
          'APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_SPECIES'.tr(),
        ),
      );
    }

    String q = query.toLowerCase();

    List<String> _filteredBreeds = [];
    List<Breed> list = _breeds.where((Breed b) {
      String name = b.name.toLowerCase();
      //remove duplicate
      if (name.contains(q) && !_filteredBreeds.contains(name)) {
        _filteredBreeds.add(name);
        return true;
      }
      return false;
    }).toList();

    return ListView.separated(
      itemBuilder: (context, index) {
        Breed breed = list[index];
        return ListTile(
          title: Text(breed.name),
          onTap: () => close(context, breed),
        );
      },
      separatorBuilder: (_, __) => const Divider(
        height: 1.0,
        color: Colors.black54,
      ),
      itemCount: list.length,
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) => buildResults(context);

  @override
  String? get searchFieldLabel =>
      'APPLICATION_MOBILE_BUTTON_LABEL_SEARCH_FOR_A_BREED'.tr();

  @override
  TextStyle get searchFieldStyle => const TextStyle(
    fontSize: 18.0,
    fontWeight: FontWeight.normal,
  );
}
