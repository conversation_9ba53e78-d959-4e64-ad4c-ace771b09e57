import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/shelter.dart';

class CharityConfirmationDialog extends StatelessWidget {
  const CharityConfirmationDialog(this.shelter, {this.onConfirm, Key? key})
      : super(key: key);

  final Shelter shelter;
  final VoidCallback? onConfirm;

  @override
  Widget build(BuildContext context) {
    Color greenColor = Theme.of(context).colorScheme.secondary;

    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "APPLICATION_MOBILE_MESSAGE_PET_ADOPTED_FROM".tr(),
            textAlign: TextAlign.center,
            style: const TextStyle(
                fontWeight: FontWeight.w700,
                color: Color(0xff474747),
                fontSize: 14),
          ),
          const SizedBox(
            height: 15,
          ),
          const Divider(
            height: 0,
          ),
          ListTile(
            leading: Padding(
              padding: const EdgeInsets.only(top: 5),
              child: Image.asset(
                "assets/icon/handshake.png",
                width: 35,
              ),
            ),
            horizontalTitleGap: 10,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8.0)),
            ),
            contentPadding: const EdgeInsets.all(0),
            minVerticalPadding: 8.0,
            title: Text(
              shelter.name,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.bodyText1?.copyWith(
                  color: const Color(0xff474747),
                  fontWeight: FontWeight.w700,
                  fontSize: 15),
            ),
            subtitle: Text(
              '${shelter.city}, ${shelter.state}, ${shelter.zipCode ?? ""}',
              style: Theme.of(context).textTheme.subtitle2?.copyWith(
                  color: const Color(0xff5A5A5A),
                  fontWeight: FontWeight.w400,
                  fontSize: 12),
            ),
          ),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.zero, elevation: 0),
                  onPressed: () {
                    Tools().navigatorPop();
                    onConfirm?.call();
                  },
                  child: Text(
                    "APPLICATION_MOBILE_BUTTON_LABEL_CONFIRM".tr(),
                    style: const TextStyle(
                        fontWeight: FontWeight.w500, fontSize: 13),
                  ),
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Expanded(
                child: OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.zero,
                    side: BorderSide(
                      color: greenColor,
                    ),
                  ),
                  onPressed: () {
                    Tools().navigatorPop();
                  },
                  child: Text(
                    'APPLICATION_MOBILE_BUTTON_LABEL_CANCEL'.tr(),
                    style: TextStyle(
                        color: greenColor,
                        fontWeight: FontWeight.w500,
                        fontSize: 13),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
