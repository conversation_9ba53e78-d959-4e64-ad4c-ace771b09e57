import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/state.dart';

class StateSearchDelegate extends SearchDelegate<CountryState?> {
  StateSearchDelegate({required this.states});

  final List<CountryState> states;

  @override
  void showResults(BuildContext context) => super.showResults(context);

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
          showSuggestions(context);
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) => BackButton(
        onPressed: () => close(context, null),
      );

  @override
  Widget buildResults(BuildContext context) {
    if (states.isEmpty) {
      return Center(
        child: Text(
          'APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_STATES'.tr(),
        ),
      );
    }

    String q = query.toLowerCase();

    List<String> _filteredStates = [];
    List<CountryState> list = states.where((CountryState b) {
      String name = b.name!.toLowerCase();
      //remove duplicate
      if (name.contains(q) && !_filteredStates.contains(name)) {
        _filteredStates.add(name);
        return true;
      }
      return false;
    }).toList();

    return ListView.separated(
      itemBuilder: (context, index) {
        CountryState state = list[index];
        return ListTile(
          title: Text(state.name!),
          onTap: () => close(context, state),
        );
      },
      separatorBuilder: (_, __) => const Divider(
        height: 1.0,
        color: Colors.black54,
      ),
      itemCount: list.length,
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) => buildResults(context);

  @override
  String? get searchFieldLabel =>
      'APPLICATION_MOBILE_BUTTON_LABEL_SEARCH_FOR_A_STATE'.tr();

  @override
  TextStyle get searchFieldStyle => const TextStyle(
        fontSize: 18.0,
        fontWeight: FontWeight.normal,
      );
}
