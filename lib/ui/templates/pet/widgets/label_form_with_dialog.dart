import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';

class LabelFormWithDialog extends StatelessWidget {
  final String label;
  final String? dialogTitle;
  final String? dialogContent;
  final String? confirmButtonTitle;

  const LabelFormWithDialog(
      {Key? key,
      required this.label,
      this.dialogTitle,
      this.dialogContent,
      this.confirmButtonTitle = "Okay"})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Flexible(
            child: Text(
              label,
              style: Theme.of(context)
                  .textTheme
                  .labelMedium
                  ?.copyWith(fontSize: 14, fontWeight: FontWeight.w600),
            ),
          ),
          const SizedBox(width: 10),
          GestureDetector(
            onTap: () {
              Tools().common.showCustomDialog(
                    title: dialogTitle,
                    description: dialogContent,
                    validTitle: confirmButtonTitle,
                  );
            },
            child: Icon(
              FontAwesomeIcons.infoCircle,
              size: 18.0,
              color: Theme.of(context).colorScheme.secondary,
            ),
          ),
        ],
      ),
    );
  }
}
