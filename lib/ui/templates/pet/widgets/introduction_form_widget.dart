

import 'package:flutter/material.dart';

class IntroductionFormWidget extends StatelessWidget {
  const IntroductionFormWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                Icons.pets,
                size: 20.0,
                color: Theme.of(context).disabledColor,
              ),
              const SizedBox(width: 8.0),
              Text(
                'Animal Info',
                style: Theme.of(context).textTheme.headline6,
              ),
            ],
          ),
          Text(
            'Please provide your pet\'s information to be able to walk with him',
            style: Theme.of(context).textTheme.subtitle2,
          ),
        ],
      ),
    );
  }
}
