import 'package:flutter/material.dart';
import 'package:mybuddy/controllers/dog_form_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/custom_cupertino_date_picker.dart';
import 'package:mybuddy/ui/templates/pet/widgets/date_picker_widget.dart';

class PetBirthdateTabWidget extends StatelessWidget {
  PetBirthdateTabWidget(this.controller, {Key? key}) : super(key: key);

  final DogFormController controller;

  @override
  Widget build(BuildContext context) {
    return DatePickerWidget(
      label:
          "'Age' to estimate or 'Birthdate' to enter your pet's exact birthday",
      selectedDate: controller.birthDate,
      onSelectedItemChanged: (date) {
        controller.birthDate = date;
        controller.updateAgeUsingDate(date);
      },
      onSave: () {
        controller.saveBirthDate(BirthDateType.date);
      },
    );
  }

  Widget _saveButton(context, void Function() onPressed) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(5, 5, 5, 10),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 10)),
        onPressed: onPressed,
        child: Container(
          width: double.infinity,
          alignment: Alignment.center,
          child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SAVE'.tr()),
        ),
      ),
    );
  }
}
