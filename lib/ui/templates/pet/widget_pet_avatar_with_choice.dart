import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_pet.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/pet_bloc.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';

class PetAvatarWithChoice extends StatefulWidget {
  /// need to be wrapped by [BlocProvider] of [PetBloc]
  PetAvatarWithChoice({Key? key, this.returnAvatar}) : super(key: key);
  Function(File)? returnAvatar;

  @override
  State<PetAvatarWithChoice> createState() => _PetAvatarWithChoiceState();
}

class _PetAvatarWithChoiceState extends State<PetAvatarWithChoice> {
  File? avatar;

  @override
  Widget build(BuildContext context) {
    final PetBloc bloc = BlocProvider.of<PetBloc>(context);

    return StreamBuilder<Pet?>(
      stream: bloc.stream,
      builder: (BuildContext context, AsyncSnapshot<Pet?> snapshot) {
        Pet? pet;
        if (snapshot.hasData) {
          pet = snapshot.data!;
        }

        return Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Container(
              height: 118,
              width: 118,
              decoration: BoxDecoration(
                color: const Color(0xffC4C4C4).withAlpha(100),
                borderRadius: BorderRadius.circular(100),
                border: Border.all(
                  color: const Color(0xffB6B7B9),
                  width: 1.5
                ),
              ),
              child: MBAvatar(
                size: 118.0,
                backgroundColor: const Color(0xffC4C4C4).withAlpha(100),
                imageUrl: pet?.avatarUrl,
                noImageUrl: avatar == null ? Padding(
                  padding: const EdgeInsets.all(15.0),
                  child: Image.asset("assets/icon/add_dog_placeholder.png"),
                ) : Image.file(avatar!),
                onTap: () async {
                  File? result = await Tools().image.pickAndCropAvatar(
                    context,
                    entity: pet,
                    petGallery: pet != null,
                  );
                  if (result != null) {
                    if (snapshot.hasData) {
                      unawaited(MbApiPet()
                          .editPetRequest(context, pet!, avatar: result));
                    } else {
                      setState(() {
                        avatar = result;
                      });
                      widget.returnAvatar!(result);
                    }
                  }
                  bloc.update();
                },
              ),
            ),
            GestureDetector(
              onTap: () async {
                File? result = await Tools().image.pickAndCropAvatar(
                      context,
                      entity: pet,
                      petGallery: pet != null,
                    );
                if (result != null) {
                  if (snapshot.hasData) {
                    unawaited(MbApiPet()
                        .editPetRequest(context, pet!, avatar: result));
                  } else {
                    setState(() {
                      avatar = result;
                    });
                    widget.returnAvatar!(result);
                  }
                }
                bloc.update();
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: const Color(0xff474747),width: 0.2)),
                child: Text(
                  "+ Add Photo",
                  style: TextStyle(
                      color: Theme.of(context).colorScheme.secondary,
                      fontSize: 11,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
