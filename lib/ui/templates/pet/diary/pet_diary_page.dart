import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_petactivity_item.dart';
import 'package:mybuddy/ui/templates/pet/diary/add_note.dart';
import 'package:mybuddy/ui/templates/reminder/add_edit_reminder.dart';

class PetDiaryPage extends StatefulWidget {
  final Pet pet;
  final int index;
  final int filter;

  const PetDiaryPage(
      {Key? key, this.index = 0, this.filter = 0, required this.pet})
      : super(key: key);

  @override
  _PetDiaryPageState createState() => _PetDiaryPageState();
}

class _PetDiaryPageState extends State<PetDiaryPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late Map<int, String> _filtersUpcoming;
  late Map<int, String> _filtersPast;
  late List<DropdownMenuItem<int>> _dropDownMenuItemsUpcoming;
  late List<DropdownMenuItem<int>> _dropDownMenuItemsPast;
  late int? _currentFilterUpcoming;
  late int? _currentFilterPast;
  late String floatingActionButtonText;
  late List<PetActivity> _pastPetActivities;
  late List<PetActivity> _comingPetActivities;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    _tabController.index = widget.index;
    _tabController.addListener(_handleTabSelection);
    _handleTabSelection();

    /// init dropdowns
    _filtersUpcoming = {
      0: 'APPLICATION_MOBILE_LABEL_FILTER_ALL'.tr(),
      999: 'APPLICATION_MOBILE_LABEL_FILTER_ONLY_VET'.tr(),
    };
    _filtersPast = {
      0: 'APPLICATION_MOBILE_LABEL_FILTER_ALL'.tr(),
      999: 'APPLICATION_MOBILE_LABEL_FILTER_ONLY_VET'.tr(),
      1: 'APPLICATION_MOBILE_TITLE_APPOINTMENT'.tr(),
      2: 'APPLICATION_MOBILE_TITLE_REMINDER'.tr(),
//      3: 'assurance'.tr(),
//      4: 'perte'.tr(),
//      5: 'image'.tr(),
      6: 'APPLICATION_MOBILE_FIELD_LABEL_NOTE'.tr(),
      7: 'APPLICATION_MOBILE_TITLE_HOSPITALIZATION'.tr(),
      8: 'APPLICATION_MOBILE_TITLE_NOTE'.tr(),
      9: 'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_PREVENTION'.tr(),
    };
    _dropDownMenuItemsUpcoming = getDropDownMenuItems(_filtersUpcoming);
    _dropDownMenuItemsPast = getDropDownMenuItems(_filtersPast);
    _currentFilterUpcoming = widget.index == 0 ? widget.filter : 0;
    _currentFilterPast = widget.index == 1 ? widget.filter : 0;

    refresh(all: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(
          widget.pet.name +
              ' - ' +
              'APPLICATION_MOBILE_LABEL_PET_PET_LIFE'.tr(),
          maxLines: 2,
        ),
        bottom: TabBar(
          tabs: [
            Tab(text: 'APPLICATION_MOBILE_TEXT_PETSLIFE_INCOMING'.tr()),
            Tab(text: 'APPLICATION_MOBILE_TEXT_PETSLIFE_PASSED'.tr()),
          ],
          controller: _tabController,
          onTap: (_) => refresh(),
        ),
        // actions: <Widget>[
        //   MBHamburger(),
        // ],
      ),
      // drawer: MBDrawer(),
      body: TabBarView(
        controller: _tabController,
        children: [
          Column(
            children: <Widget>[
              DiaryDropDownWidget(
                value: _currentFilterUpcoming,
                items: _dropDownMenuItemsUpcoming,
                onChanged: changedDropDownItem,
              ),
              Expanded(
                child: Scrollbar(
                  child: _comingPetActivities.isEmpty
                      ? Center(
                          child: Text(
                            'APPLICATION_MOBILE_TEXT_PETSLIFE_NO_INCOMING'.tr(),
                          ),
                        )
                      : ListView.separated(
                          separatorBuilder: (context, index) =>
                              const SizedBox(height: 10.0),
                          itemCount: _comingPetActivities.length,
                          itemBuilder: (context, index) => MBPetActivityItem(
                            _comingPetActivities[index],
                            onChanged: refresh,
                          ),
                        ),
                ),
              )
            ],
          ),
          Column(
            children: <Widget>[
              DiaryDropDownWidget(
                value: _currentFilterPast,
                items: _dropDownMenuItemsPast,
                onChanged: changedDropDownItem,
              ),
              Expanded(
                child: Scrollbar(
                  child: _pastPetActivities.isEmpty
                      ? Center(
                          child: Text(
                            'APPLICATION_MOBILE_TEXT_PETSLIFE_NO_PASSED'.tr(),
                          ),
                        )
                      : ListView.separated(
                          separatorBuilder: (context, index) =>
                              const SizedBox(height: 10.0),
                          itemCount: _pastPetActivities.length,
                          itemBuilder: (context, index) => MBPetActivityItem(
                            _pastPetActivities[index],
                            onChanged: refresh,
                            showType: _currentFilterPast == 0,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: 'add-note-reminder',
        onPressed: () async {
          bool needRefresh = (await Tools().navigatorPush(
              _tabController.index == 0 ? AddReminderForm(
                title: floatingActionButtonText,
                pet: widget.pet,
              ) : AddNoteForm(
                title: floatingActionButtonText,
                pet: widget.pet,
              ))) as bool;
          if (needRefresh) {
            refresh();
          }
        },
        icon: const Icon(FontAwesomeIcons.plus),
        label: Text(floatingActionButtonText),
      ),
    );
  }

  void changedDropDownItem(int? selectedFilter) {
    if (_tabController.index == 0) {
      _currentFilterUpcoming = selectedFilter;
    } else {
      _currentFilterPast = selectedFilter;
    }
    refresh();
    setState(() {});
  }

  List<DropdownMenuItem<int>> getDropDownMenuItems(Map<int, String> filters) {
    List<DropdownMenuItem<int>> items = [];
    filters.forEach((key, value) {
      items.add(DropdownMenuItem(
        value: key,
        child: Text(value),
      ));
    });
    return items;
  }

  void refresh({all = false}) {
    if (all || _tabController.index == 0) {
      _comingPetActivities = Data().getFilteredPetActivities(
        petId: widget.pet.id,
        typeFilter: _currentFilterUpcoming,
        chronoFilter: ChronoFilter.future,
      );
    }
    if (all || _tabController.index == 1) {
      _pastPetActivities = Data().getFilteredPetActivities(
        petId: widget.pet.id,
        typeFilter: _currentFilterPast,
        chronoFilter: ChronoFilter.past,
      );
    }
  }

  void _handleTabSelection() {
    setState(() {
      if (_tabController.index == 0) {
        floatingActionButtonText =
            'APPLICATION_MOBILE_BUTTON_LABEL_ACTIVITY_REMINDER_ADD'.tr();
      } else {
        floatingActionButtonText =
            'APPLICATION_MOBILE_BUTTON_LABEL_ACTIVITY_NOTE_ADD'.tr();
      }
    });
  }
}

class DiaryDropDownWidget extends StatelessWidget {
  final int? value;
  final List<DropdownMenuItem<int>>? items;
  final ValueChanged<int?>? onChanged;

  const DiaryDropDownWidget({Key? key, this.value, this.items, this.onChanged})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: Theme.of(context).backgroundColor,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).colorScheme.secondary),
        ),
      ),
      child: Center(
        child: DropdownButtonHideUnderline(
          child: DropdownButton<int>(
            style: TextStyle(
              fontSize: 18.0,
              color: Theme.of(context).colorScheme.secondary,
            ),
            value: value,
            items: items,
            onChanged: onChanged,
          ),
        ),
      ),
    );
  }
}
