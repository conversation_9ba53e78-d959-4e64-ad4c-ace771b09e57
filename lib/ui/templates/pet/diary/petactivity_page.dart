import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/pet_activity_bloc.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/ui/templates/pet/diary/widget_pet_activity.dart';

class PetActivityPage extends StatelessWidget {
  final int? petActivityIndex;
  final PetActivity? petActivity;
  final bool fromHealthBook;

  const PetActivityPage({Key? key, this.petActivityIndex, this.petActivity, this.fromHealthBook = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if(petActivityIndex!=null){
      return WidgetPetActivity(fromHealthBook: fromHealthBook, petActivityIndex: petActivityIndex,);
    }
    return BlocProvider<PetActivityBloc>(
      bloc: PetActivityBloc(petActivityId: petActivity!.id!),
      child: WidgetPetActivity(fromHealthBook: fromHealthBook),
    );
  }
}
