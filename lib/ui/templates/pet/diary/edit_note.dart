import 'package:datetime_picker_formfield/datetime_picker_formfield.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';

import 'package:mybuddy/Api/mb_api_pet_activity.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/controllers/pet_weight_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_simple_text_widget.dart';

import 'widgets/note_form_widgets.dart';


class EditNoteForm extends StatefulWidget {
  final String title;
  final PetActivity note;

  const EditNoteForm({Key? key, required this.note, required this.title}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _EditNoteFormState();
}

class _EditNoteFormState extends State<EditNoteForm> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  late PetActivity _note;
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();

    _note = widget.note.copy();
    _controller = TextEditingController();
    _controller.text = Data().dateTimeToUserDateTimeStr(_note.dateStart);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(widget.title, maxLines: 2),
        leading: BackButton(
          // do not delete
          onPressed: () => Tools().navigatorPop(value: false),
        ),
      ),
      body: SafeArea(
        top: false,
        bottom: false,
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.disabled,
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              children: <Widget>[
                MBSimpleTextWidget(
                  text: Tools().pet.getPetListStr(widget.note.pets),
                  icon: FontAwesomeIcons.paw,
                ),
                MBSimpleTextWidget(
                  text: Ref().get().getNoteType(_note.typeId)?.label,
                  icon: FontAwesomeIcons.stickyNote,
                ),
                const Divider(),
                WeightTextFormField(note: _note),
                DurationTextFormField(note: _note),
                DistanceTextFormField(note: _note, edited: true),
                FoodTextFormField(note: _note),
                const SizedBox(height: 20.0),
                DescriptionTextFormField(note: _note),
                const SizedBox(height: 20.0),
                TextFormField(
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_DATE'.tr(),
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_DATE'.tr(),
                  ),
                  controller: _controller,
                  validator: (val) =>
                      _note.dateStart != null || _note.dateStart!.isBefore(DateTime.now())
                          ? null
                          : 'APPLICATION_MOBILE_MESSAGE_INVALID_DATE'.tr(),
                  onTap: () => _chooseDateTime(context),
                ),
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(vertical: 25.0),
                  child: Builder(
                    builder: (context) => ElevatedButton(
                      onPressed: () {
                        _submitForm(context);
                      },
                      child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr()),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _chooseDateTime(BuildContext context) async {
    DateTime now = DateTime.now();
    DateTime initialDate = _note.dateStart ?? now;
    if (initialDate.isAfter(now)) initialDate = now;

    DateTime? date = await Tools().common.showDateDialog(
          context: context,
          initialDate: initialDate,
          firstDate: DateTime(now.year - 15, 1, 1),
          lastDate: now,
        );

    DateTime result;
    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(initialDate),
      );
      result = DateTimeField.combine(date, time);
    } else {
      result = initialDate;
    }
    if (result.isAfter(now)) result = now;

    setState(() {
      _note.dateStart = result;
      _controller.text = Data().dateTimeToUserDateTimeStr(_note.dateStart);
    });
  }

  Future<void> _submitForm(BuildContext context) async {
    final FormState? form = _formKey.currentState;

    if (form == null || !form.validate()) {
      Tools().common.showValidationErrorMessage(context);
      return;
    }
    form.save();

    MBResponse response = await MbApiPetActivity().editPetActivityRequest(context, _note);

    if (response.success) {
      PetWeightController controller = Get.find();
      controller.updateActivity(PetActivity.fromJson(response.body["petActivity"]));
      Tools().navigatorPop(value: true);
    }
  }
}
