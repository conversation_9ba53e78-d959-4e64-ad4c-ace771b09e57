import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_api_pet_activity.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/pet_activity_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/controllers/pet_weight_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/file.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/models/staff.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_simple_text_widget.dart';
import 'package:mybuddy/ui/components/mb_speed_dial_child.dart';
import 'package:mybuddy/ui/components/widget_image_clickable.dart';
import 'package:mybuddy/ui/templates/contract/add_edit_contract.dart';
import 'package:mybuddy/ui/templates/pet/diary/edit_note.dart';
import 'package:mybuddy/ui/templates/pet/healthbook/add_treatment.dart';
import 'package:mybuddy/ui/templates/reminder/add_edit_reminder.dart';

class WidgetPetActivity extends StatelessWidget {
  final bool fromHealthBook;
  final int? petActivityIndex;

  WidgetPetActivity({Key? key, this.fromHealthBook = false,this.petActivityIndex})
      : super(key: key);

  PetWeightController controller = Get.find();

  @override
  Widget build(BuildContext context) {

    if(petActivityIndex!=null){
      return Obx(() {
          return _petActivityWidget(context, controller.dataList[petActivityIndex!]);
        }
      );
    }

    final bloc = BlocProvider.of<PetActivityBloc>(context);
    return StreamBuilder<PetActivity?>(
      stream: bloc.stream,
      builder: (BuildContext context, AsyncSnapshot<PetActivity?> snapshot) {
        if (snapshot.hasData) {
          return _petActivityWidget(context, snapshot.data!);
        } else if (snapshot.connectionState == ConnectionState.active) {
          return _notFoundWidget();
        }
        return Container();
      },
    );
  }

  Widget _clinicWidget(PetActivity petActivity) {
    if (petActivity.serviceId == null) return Container();
    try {
      MBService? service = Data().get().getService(petActivity.serviceId);
      return MBSimpleTextWidget(
          text: service?.name, icon: FontAwesomeIcons.clinicMedical);
    } catch (e) {
      return MBSimpleTextWidget(
          text: 'APPLICATION_MOBILE_MESSAGE_ERROR_NOT_RIGHT_CLINIC'.tr(),
          icon: FontAwesomeIcons.clinicMedical);
      //todo MIDDLE what to do in such case;
    }
  }

  Widget _commentWidget(PetActivity petActivity) {
    return MBSimpleTextWidget(
      text: petActivity.comment,
      icon: FontAwesomeIcons.comment,
//        color: Colors.black,
//        bgColor: Colors.grey[300]
    );
  }

  Widget _companyWidget(PetActivity petActivity) {
    return MBSimpleTextWidget(
      text: petActivity.company,
      icon: FontAwesomeIcons.industry,
//      color: Colors.black,
//      bgColor: Colors.grey[300],
    );
  }

//  todo MIDDLE add calendar with correct date of reminder //ios pb
//  Widget _calendarWidget() {
//    if (petActivity == null) return Container();
//    return GestureDetector(
//    behavior: HitTestBehavior.translucent,
//        child: Icon(FontAwesomeIcons.calendarPlus, color: Colors.blue),
//        onTap: () {
//          final Event event = Event(
//            title:
//            'APPLICATION_MOBILE_TITLE_APPOINTMENT'.tr() + ' ' + service.name,
//            description: 'APPLICATION_MOBILE_FIELD_LABEL_WHICH_PET'.tr() +
//                ' ' +
//                            petActivity.getPetListString(),
//            location: service.getFullAddress(),
//            startDate: petActivity.dateStart,
//            endDate: petActivity.dateStart.add(Duration(minutes: 30)),
//          );
//          Add2Calendar.addEvent2Cal(event);
//        });
//    return Container();
//  }

  Widget _contractNumberWidget(PetActivity petActivity) {
    return MBSimpleTextWidget(
      text: petActivity.contractNumber,
      icon: FontAwesomeIcons.barcode,
//      color: Colors.black,
//      bgColor: Colors.grey[300],
    );
  }

  Widget _dateWidget(PetActivity petActivity) {
    //todo HIGH fuseau horaire check everywhere
    return Center(
      child: Text(
        Data().dateTimeToUserDateTimeStr(petActivity.dateStart),
        style: const TextStyle(color: Colors.green),
      ),
    );
  }

  List<MBSpeedDialChild> _floatingActionButtons(
      BuildContext context, PetActivity petActivity) {
    List<MBSpeedDialChild> floatingActionButtons = [];
    if (petActivity.serviceId == null) {
      floatingActionButtons.add(
        MBSpeedDialChild(
          iconData: Icons.delete,
          backgroundColor: Theme.of(context).errorColor,
          label: 'APPLICATION_MOBILE_BUTTON_LABEL_DELETE'.tr(),
          onTap: () async {
            bool? result = await Tools().common.showValidDialog(
              context,
              title: 'APPLICATION_MOBILE_BUTTON_LABEL_DELETE'.tr(),
              text: 'APPLICATION_MOBILE_MESSAGE_CONFIRMATION'.tr(),
            );
            if(result == true) {
              MBResponse response = await MbApiPetActivity()
                  .removePetActivityRequest(context, petActivity);
              if (response.success) {
                controller.deleteActivity(petActivity.id!);
                Tools().navigatorPop(value: true);
              }
            }
          },
        ),
      );
      floatingActionButtons.add(
        MBSpeedDialChild(
          iconData: Icons.edit,
          backgroundColor: Theme.of(context).colorScheme.secondary,
          label: 'APPLICATION_MOBILE_BUTTON_LABEL_EDIT'.tr(),
          onTap: () {
            Widget w;
            switch (petActivity.activityId) {
              case 2:
                w = AddReminderForm(
                  title: 'APPLICATION_MOBILE_BUTTON_LABEL_EDIT'.tr(),
                  reminder: petActivity,
                );
                break;
              case 3:
                w = AddEditContract(
                  title: 'APPLICATION_MOBILE_BUTTON_LABEL_EDIT'.tr(),
                  contract: petActivity,
                );
                break;
              case 9:
                w = AddEditTreatment(petActivity: petActivity);
                break;
              default:
                w = EditNoteForm(
                  title: 'APPLICATION_MOBILE_BUTTON_LABEL_EDIT'.tr(),
                  note: petActivity,
                );
            }

            Tools().navigatorPush(w);
          },
        ),
      );
    }

    if (petActivity.nextOccurrence != null) {
      floatingActionButtons.add(
        MBSpeedDialChild(
          iconData: FontAwesomeIcons.check,
          backgroundColor: Colors.green[200],
          label: 'APPLICATION_MOBILE_LABEL_MARK_AS_DONE'.tr(),
          onTap: () =>
              _markDone(context, petActivity.nextOccurrence, petActivity),
        ),
      );
    }

    if (petActivity.nextOccurrence != null &&
        petActivity.getRemainingOccurrenceCount() > 1 &&
        (petActivity.endRecurrence == null ||
            (petActivity.endRecurrence != null &&
                petActivity.endRecurrence!.isAfter(DateTime.now())))) {
      floatingActionButtons.add(
        MBSpeedDialChild(
          iconData: FontAwesomeIcons.checkDouble,
          backgroundColor: Colors.green,
          label: 'APPLICATION_MOBILE_LABEL_MARK_AS_DONE_TODAY'.tr(),
          onTap: () => _markDone(context, DateTime.now(), petActivity),
        ),
      );
    }

    if (petActivity.nextOccurrence != null &&
        petActivity.getRemainingOccurrenceCount() > 1) {
      floatingActionButtons.add(
        MBSpeedDialChild(
          iconData: FontAwesomeIcons.checkDouble,
          backgroundColor: Colors.green,
          label: 'APPLICATION_MOBILE_LABEL_MARK_AS_DONE_ALL'.tr(),
          onTap: () =>
              _markDone(context, petActivity.endRecurrence, petActivity),
        ),
      );
    }

    if (petActivity.activityId == 9 && fromHealthBook == false) {
      floatingActionButtons.add(
        MBSpeedDialChild(
          iconData: FontAwesomeIcons.bookMedical,
          backgroundColor: Colors.green,
          label: 'APPLICATION_MOBILE_TITLE_HEALTHBOOK2'.tr(),
          onTap: () {
            Tools().common.showNotImplemented(context);
          },
        ),
      );
    }

    return floatingActionButtons;
  }

  Widget _healthNoteWidget(PetActivity petActivity) {
    List<Widget> healthNotesWidget = [];
    healthNotesWidget.add(_parameterItemWidget(
      'APPLICATION_MOBILE_LABEL_CAUSE',
      petActivity.cause,
    ));
//    if(petActivity.generalCondition != '' ){
//      healthNotesWidget.add(_healthNoteItemWidget('APPLICATION_MOBILE_LABEL_GENERAL_CONDITION', petActivity.cause));
//    }
    healthNotesWidget.add(_parameterItemWidget(
      'APPLICATION_MOBILE_FIELD_LABEL_FOOD',
      petActivity.foodName,
    ));
    if (petActivity.weight != null) {
      healthNotesWidget.add(_parameterItemWidget(
        'APPLICATION_MOBILE_FIELD_LABEL_WEIGHT',
        Data().convertWeightToUserUnitStr(petActivity.weight!),
      ));
    }
    if (petActivity.duration != null) {
      healthNotesWidget.add(_parameterItemWidget(
        'APPLICATION_MOBILE_FIELD_LABEL_DURATION',
        petActivity.duration.toString() + ' min',
      ));
    }
    if (petActivity.distance != null) {
      healthNotesWidget.add(_parameterItemWidget(
        'APPLICATION_MOBILE_FIELD_LABEL_DURATION',
        Data().convertLengthToUserUnitStr(petActivity.distance!),
      ));
    }
    healthNotesWidget.add(_parameterItemWidget(
      'APPLICATION_MOBILE_LABEL_TEST_PERFORMED',
      petActivity.testPerformed,
    ));
    healthNotesWidget.add(_parameterItemWidget(
      'APPLICATION_MOBILE_LABEL_ANOMALY_REPORTED',
      petActivity.anomalyReported,
    ));
    healthNotesWidget.add(_parameterItemWidget(
      'APPLICATION_MOBILE_LABEL_DIAGNOSTIC',
      petActivity.diagnostic,
    ));
    healthNotesWidget.add(_parameterItemWidget(
      'APPLICATION_MOBILE_LABEL_MEDICAL_TREATMENT',
      petActivity.medicalTreatment,
    ));
    healthNotesWidget.add(_parameterItemWidget(
      'APPLICATION_MOBILE_LABEL_SURGERY_TREATMENT',
      petActivity.surgeryTreatment,
    ));
//    if(petActivity.vaccination.trim() != '' ){
//      healthNotesWidget.add(_healthNoteItemWidget('vaccination-TR', petActivity.vaccination));
//    }
//      healthNotesWidget.add(_healthNoteItemWidget('APPLICATION_MOBILE_LABEL_SUGGESTIONS', petActivity.externPestControl));
//      healthNotesWidget.add(_healthNoteItemWidget('iAPPLICATION_MOBILE_LABEL_DIAGNOSTIC', petActivity.internPestControl));
    healthNotesWidget.add(_parameterItemWidget(
      'APPLICATION_MOBILE_LABEL_SUGGESTIONS',
      petActivity.suggestions,
    ));

    return _simpleItemColumn(healthNotesWidget);
  }

  Widget _imagesWidget(BuildContext context, PetActivity petActivity) {
    if (petActivity.images.isEmpty) {
      return Container();
    }
    List<Widget> rowImageList = [];
    rowImageList.add(const Icon(Icons.photo, color: Colors.grey));
    for (var i in petActivity.images) {
      rowImageList.add(
        Padding(
          padding: const EdgeInsets.only(left: 7),
          child: SizedBox(
            width: MediaQuery.of(context).size.width / 3,
            height: MediaQuery.of(context).size.width / 3,
            child: CachedImageClickable(image: i, title: ''),
          ),
        ),
      );
    }
    return Padding(
      padding: const EdgeInsets.only(left: 20, top: 10),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(children: rowImageList),
      ),
    );
  }

  Future<void> _markDone(
      BuildContext context, DateTime? to, PetActivity petActivity) async {
    petActivity.dateEnd = to;
    await MbApiPetActivity().editPetActivityRequest(context, petActivity);
  }

  Widget _nextOccurrenceWidget(PetActivity petActivity) {
    if (petActivity.nextOccurrence == null) return Container();
    String text = '${'APPLICATION_MOBILE_LABEL_NEXT'.tr()} ';
    text += '${Data().dateTimeToUserDateStr(petActivity.nextOccurrence)}\n';
    int remaining = petActivity.getRemainingOccurrenceCount();
    if (remaining > 1) {
      text +=
          '($remaining ${'APPLICATION_MOBILE_FIELD_LABEL_RECURRENCE_REMAINING'.tr()})';
    } else {
      text += '(${'APPLICATION_MOBILE_FIELD_LABEL_RECURRENCE_LAST'.tr()})';
    }
    return MBSimpleTextWidget(
      text: text,
      icon: FontAwesomeIcons.history,
      color: Colors.red,
      strColor: Colors.red,
      // bgColor: Colors.grey[300],
      onTap: () {},
    );
  }

  Widget _nextTreatmentWidget(PetActivity petActivity) {
    if (petActivity.activityId != 9) return Container();
    return MBSimpleTextWidget(
      text: Data().dateTimeToUserDateStr(petActivity.dateEnd),
      icon: Icons.autorenew,
      onTap: () {},
    );
  }

  Widget _parameterItemWidget(String title, String? value,
      {color = Colors.blue}) {
    if (value == null || value.trim() == '') return Container();
    return Padding(
      padding: const EdgeInsets.fromLTRB(10, 2, 10, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            title.tr(),
            style: const TextStyle(color: Colors.grey),
          ),
          Text(
            value,
            style: TextStyle(color: color),
          ),
        ],
      ),
    );
  }

  Widget _notFoundWidget() {
    return Scaffold(
      appBar: MBAppBar(
        leading: BackButton(
          onPressed: () => Tools().navigatorPop(value: true),
        ),
      ),
      body: Center(
        child: Text('APPLICATION_MOBILE_TEXT_EMPTY_DATA'.tr()),
      ),
    );
  }

  Widget _petActivityWidget(BuildContext context, PetActivity petActivity) {
    return Scaffold(
      appBar: MBAppBar(
        leading: BackButton(
          onPressed: () => Tools().navigatorPop(value: true),
        ),
      ),
      floatingActionButton: Builder(
        builder: (context) {
          return MBSpeedDial(
            heroTag: 'pet-activity-speed-dial',
            children: _floatingActionButtons(context, petActivity),
          );
        },
      ),
      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            _typeNameWidget(petActivity),
            _dateWidget(petActivity),
            const Divider(),
            _reminderTypeWidget(petActivity),
            _petWidget(petActivity),
            _clinicWidget(petActivity),
            _staffWidget(petActivity),
            const Divider(),
            _titleWidget(petActivity),
            _companyWidget(petActivity),
            _contractNumberWidget(petActivity),
            _responseWidget(petActivity),
            _imagesWidget(context, petActivity),
            _commentWidget(petActivity),
            _healthNoteWidget(petActivity),
            _treatmentWidget(petActivity),
            _recurrenceWidget(petActivity),
            _nextOccurrenceWidget(petActivity),
            _nextTreatmentWidget(petActivity)
//          _treatmentWidget()
//          _debugRecurrenceWidget()
          ],
        ),
      ),
    );
  }

  Widget _petWidget(PetActivity petActivity) {
    return MBSimpleTextWidget(
      text: Tools().pet.getPetListStr(petActivity.pets),
      icon: FontAwesomeIcons.paw,
    );
  }

  Widget _recurrenceWidget(PetActivity petActivity) {
    String? _recurrenceDetails = petActivity.getFullRecurrenceDetails();
    if (_recurrenceDetails == null) return Container();
    return MBSimpleTextWidget(
      text: _recurrenceDetails,
      icon: Icons.autorenew,
      onTap: () {},
    );
  }

  Widget _reminderTypeWidget(PetActivity petActivity) {
    if (petActivity.activityId != 2) return Container();
    ReminderType? reminderType =
        Ref().get().getReminderType(petActivity.typeId);
    String label = reminderType != null ? reminderType.label : '';
    return MBSimpleTextWidget(
      text: label,
      icon: FontAwesomeIcons.folderOpen,
    );
  }

  Widget _responseWidget(PetActivity petActivity) {
    return MBSimpleTextWidget(
      text: petActivity.sentence,
      icon: FontAwesomeIcons.stethoscope,
      color: Colors.black,
      bgColor: Colors.grey[300],
    );
  }

  Widget _simpleItemColumn(List<Widget> list) {
    return Row(
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.only(left: 10, right: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: list,
          ),
        ),
      ],
    );
  }

  Widget _staffWidget(PetActivity petActivity) {
    if (petActivity.serviceId == null || petActivity.staffId == null) {
      return Container();
    }
    MBService? service = Data().get().getService(petActivity.serviceId);
    if (service == null) return Container();
    Staff? staff = service.findStaff(petActivity.staffId);
    if (staff == null) return Container();
    //todo MIDDLE staff can be existant but not visible in the application.
    return Padding(
      padding: const EdgeInsets.only(left: 20, top: 20, bottom: 15),
      child: Row(
        children: <Widget>[
          const Icon(FontAwesomeIcons.userMd, color: Colors.blue),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 7),
              child: Text(
                staff.getFullName(),
                style: const TextStyle(color: Colors.blue),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _titleWidget(PetActivity petActivity) {
    return MBSimpleTextWidget(
      text: petActivity.title,
      icon: FontAwesomeIcons.tag,
//      color: Colors.black,
//      bgColor: Colors.grey[300],
    );
  }

  Widget _treatmentWidget(PetActivity petActivity) {
    if (petActivity.lotNumber == null || petActivity.lotNumber!.trim() == '') {
      return Container();
    }

    List<Widget> treatmentWidgets = [];
    treatmentWidgets.add(_parameterItemWidget(
      'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_LOT_NUMBER',
      petActivity.lotNumber,
    ));
    if (petActivity.treatments != null) {
      Tools.debugPrint('length ${petActivity.treatments!.length}');
      for (Treatment treatment in petActivity.treatments!) {
        Treatment? fullTreatment = Ref().get().getTreatment(treatment.id);
        if (fullTreatment != null) {
          treatmentWidgets.add(
            Padding(
              padding: const EdgeInsets.only(left: 10, right: 10, top: 5),
              child: Text(
                '${fullTreatment.valence} (${fullTreatment.acronym})',
              ),
            ),
          );
        }
      }
    }
    return _simpleItemColumn(treatmentWidgets);
  }

  /// activity 1 & 4 not sent here
  Widget _typeNameWidget(PetActivity petActivity) {
    String? text;
    switch (petActivity.activityId) {
      case 2:
        text = petActivity.title;
        break;
      case 3:
        text = petActivity.typeId == 1
            ? 'APPLICATION_MOBILE_LABEL_WELLNESS'.tr()
            : 'APPLICATION_MOBILE_LABEL_INSURANCES'.tr();
        break;
      case 5:
        text = 'Image';
        break;
      case 6:
        text = 'APPLICATION_MOBILE_FIELD_LABEL_NOTE'.tr();
        NoteType? noteType = Ref().get().getNoteType(petActivity.typeId);
        if (noteType != null) {
          text += ' (${noteType.label})';
        }
        break;
      case 7:
        text = 'APPLICATION_MOBILE_TITLE_HOSPITALIZATION'.tr();
        break;
      case 8:
        text = 'APPLICATION_MOBILE_TITLE_NOTE'.tr();
        break;
      case 9:
        text = petActivity.treatmentName;
        break;
      default:
        text = 'MISSING'.tr();
    }
    text = text ?? '';
    return Center(
      child: Text(
        text,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

//  Widget _debugRecurrenceWidget() {
//    String debugText = 'dateStart: ${petActivity.dateStart}\n';
//    debugText += 'dateEnd: ${petActivity.dateEnd}\n';
//    debugText += 'endRecurrence: ${petActivity.endRecurrence}\n';
//    debugText += 'unit: ${petActivity.intervalRecurrenceUnit}\n';
//    debugText += 'value: ${petActivity.intervalRecurrenceValue}';
//    return MBSimpleTextWidget(
//        text: debugText,
//        icon: FontAwesomeIcons.handsHelping,
//        color: Colors.blue,
//        strColor: Colors.red,
//        bgColor: Colors.grey[300],
//        onTap: () {});
//  }
}
