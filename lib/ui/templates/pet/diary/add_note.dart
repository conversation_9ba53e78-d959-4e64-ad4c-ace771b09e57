import 'dart:io';

import 'package:datetime_picker_formfield/datetime_picker_formfield.dart';
import 'package:flutter/material.dart';

import 'package:mybuddy/Api/mb_api_pet_activity.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_images_form_field.dart';
import 'package:mybuddy/ui/components/mb_pet_multi_form_field.dart';

import 'widgets/note_form_widgets.dart';

class AddNoteForm extends StatefulWidget {
  final String title;
  final Pet? pet;
  final MBService? service;

  const AddNoteForm({Key? key, this.pet, this.service, required this.title}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _AddNoteFormState();
}

class _AddNoteFormState extends State<AddNoteForm> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  late PetActivity _note;
  late List<Pet> pets;
  late List<NoteType> noteTypes;
  late TextEditingController _controller;
  late List<File> _images;
  late List<MBService> services;
  MBService? _service;

  late bool _sendToVet;
  bool loading = false;

  @override
  void initState() {
    super.initState();
    /// get select arrays
    pets = Data().getPets();
    noteTypes = Ref().getNoteTypes();
    services = Data().getClinicsForMessage();

    _images = <File>[];

    /// deactivated
//    _sendToVet = widget.service != null;
    _sendToVet = false;

    ///
    _note = PetActivity();
    _note.activityId = 6;
    _controller = TextEditingController();
    _note.dateStart = DateTime.now();
    _controller.text = Data().dateTimeToUserDateTimeStr(_note.dateStart);
    if (widget.pet != null) {
      _note.pets.add(widget.pet!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(widget.title, maxLines: 2),
        leading: BackButton(
          onPressed: () => Tools().navigatorPop(value: false),
        ),
      ),
      body: SafeArea(
        top: false,
        bottom: false,
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.disabled,
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              children: <Widget>[
                const SizedBox(height: 20.0),
                MBPetMultiFormField(
                  validator: (val) {
                    if (_note.pets.isEmpty) {
                      return 'APPLICATION_MOBILE_HB_ERROR_NO_PET'.tr();
                    }
                    return null;
                  },
                  initialValue: _note.pets,
                  onSaved: (value) {
                    if (value == null) return;
                    setState(() {
                      _note.pets = List<Pet>.from(value);
                    });
                  },
                ),
                const SizedBox(height: 20.0),
                DropdownButtonFormField<NoteType>(
                  value: Ref().get().getNoteType(_note.typeId),
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_TYPE'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_TYPE'.tr(),
                  ),
                  onChanged: (NoteType? newValue) {
                    setState(() {
                      if (newValue != null) {
                        _note.typeId = newValue.id;
                      }
                    });
                  },
                  validator: (val) {
                    return val != null
                        ? null
                        : 'APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_TYPE'.tr();
                  },
                  items: noteTypes.map((NoteType noteType) {
                    return DropdownMenuItem<NoteType>(
                      value: noteType,
                      child: Text(noteType.label),
                    );
                  }).toList(),
                ),
                Visibility(
                  visible: false,
                  child: Column(
                    children: [
                      const SizedBox(height: 20.0),
                      SwitchListTile(
                        title: Text(
                          'APPLICATION_MOBILE_FIELD_LABEL_SEND_TO'.tr().removeAllHtmlTags(),
                        ),
                        value: _sendToVet,
                        onChanged: (bool value) {
                          setState(() {
                            _sendToVet = value;
                          });
                        },
                      ),
                    ],
                  ),
                ),
                Visibility(
                  visible: _sendToVet,
                  child: Column(
                    children: [
                      const SizedBox(height: 20.0),
                      DropdownButtonFormField<MBService>(
                        value: _service,
                        decoration: InputDecoration(
                          hintText: 'APPLICATION_MOBILE_LABEL_SERVICE_CLINIC'.tr() + '*',
                          labelText: 'APPLICATION_MOBILE_LABEL_SERVICE_CLINIC'.tr(),
                        ),
                        onChanged: (MBService? newValue) {
                          setState(() {
                            if (newValue != _service) {
                              _service = newValue;
                            }
                          });
                        },
                        validator: (val) {
                          return val != null
                              ? null
                              : 'APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_SERVICE'.tr();
                        },
                        items: services.map((MBService service) {
                          return DropdownMenuItem<MBService>(
                            value: service,
                            child: Text(service.name ?? ''),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
                WeightTextFormField(note: _note),
                DurationTextFormField(note: _note),
                DistanceTextFormField(note: _note),
                FoodTextFormField(note: _note),
                const SizedBox(height: 20.0),
                DescriptionTextFormField(note: _note),
                const SizedBox(height: 20.0),
                ImagesFormField( // top padding included
                  height: MediaQuery.of(context).size.width * 0.30,
                  context: context,
                  initialValue: _images,
                  onSaved: (val) {
                    if (val != null) {
                      _images = val;
                    }
                  },
                  numberItems: 5,
                  text: 'APPLICATION_MOBILE_BUTTON_LABEL_NB_PHOTO_NOTE'.tr(),
                ),
                const SizedBox(height: 20.0),
                TextFormField(
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_DATE'.tr(),
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_DATE'.tr(),
                  ),
                  controller: _controller,
                  validator: (val) =>
                      _note.dateStart != null || _note.dateStart!.isBefore(DateTime.now())
                          ? null
                          : 'APPLICATION_MOBILE_MESSAGE_INVALID_DATE'.tr(),
                  onTap: () => _chooseDateTime(context),
                ),
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(vertical: 25.0),
                  child: Builder(
                    builder: (context) => ElevatedButton(
                      onPressed: loading?null:() {
                        setState(() {
                          loading=true;
                        });
                        _submitForm(context);
                      },
                      child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr()),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _chooseDateTime(BuildContext context) async {
    DateTime now = DateTime.now();
    DateTime initialDate = _note.dateStart ?? now;
    if (initialDate.isAfter(now)) initialDate = now;

    DateTime? date = await Tools().common.showDateDialog(
          context: context,
          initialDate: initialDate,
          firstDate: DateTime(now.year - 15, 1, 1),
          lastDate: now,
        );

    DateTime result;
    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(initialDate),
      );
      result = DateTimeField.combine(date, time);
    } else {
      result = initialDate;
    }
    if (result.isAfter(now)) result = now;

    setState(() {
      _note.dateStart = result;
      _controller.text = Data().dateTimeToUserDateTimeStr(_note.dateStart);
    });
  }

  Future<void> _submitForm(BuildContext context) async {
    final FormState? form = _formKey.currentState;

    if (form == null || !form.validate()) {
      Tools().common.showValidationErrorMessage(context);
      return;
    }
    form.save();

    MBResponse response = await MbApiPetActivity().addPetActivityRequest(context, _note, images: _images);

    if (response.success) {
      Tools().navigatorPop(value: true);
    }
  }
}
