import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class DistanceTextFormField extends StatelessWidget {
  final PetActivity note;
  final bool edited;

  const DistanceTextFormField({Key? key, required this.note, this.edited = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String? initialValue;
    if(note.distance != null) {
      initialValue = edited
          ? Data().convertLengthToUserUnitStr(note.distance!, withUnit: false)
          : note.distance!.toString();
    }
    return Visibility(
      visible: note.typeId == 4,
      child: Column(
        children: [
          const SizedBox(height: 20.0),
          TextFormField(
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: 'APPLICATION_MOBILE_FIELD_LABEL_DISTANCE'.tr(),
              labelText: 'APPLICATION_MOBILE_FIELD_LABEL_DISTANCE'.tr(),
              suffixText: Data().getUnitLength().shortName,
            ),
            initialValue: initialValue,
            inputFormatters: [LengthLimitingTextInputFormatter(10)],
            validator: (val) => note.typeId == 4 && (val == null || val.isEmpty)
                ? 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_WALK_DISTANCE'.tr()
                : null,
            onSaved: (val) {
              if (val != null) {
                note.distance = Data().convertLengthFromUserUnitStr(val);
              }
            },
          ),
        ],
      ),
    );
  }
}
