import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class DescriptionTextFormField extends StatelessWidget {
  final PetActivity note;
  final bool edited;

  const DescriptionTextFormField({Key? key, required this.note, this.edited = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      maxLines: 4,
      keyboardType: TextInputType.multiline,
      decoration: InputDecoration(
        hintText: 'APPLICATION_MOBILE_FIELD_LABEL_DESCRIPTION'.tr(),
        labelText: 'APPLICATION_MOBILE_FIELD_LABEL_DESCRIPTION'.tr(),
      ),
      initialValue: note.comment,
      inputFormatters: [LengthLimitingTextInputFormatter(256)],
      validator: (val) => ((val == null || val.isEmpty || val.trim() == '') &&
          note.typeId != 2 &&
          note.typeId != 3 &&
          note.typeId != 4 &&
          note.typeId != 7)
          ? 'APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_DESCRIPTION'.tr()
          : null,
      onSaved: (val) => note.comment = val?.trim(),
    );
  }
}
