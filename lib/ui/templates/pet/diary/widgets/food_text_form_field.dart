import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class FoodTextFormField extends StatelessWidget {
  final PetActivity note;

  const FoodTextFormField({Key? key, required this.note}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: note.typeId == 3,
      child: Column(
        children: [
          const SizedBox(height: 20.0),
          TextFormField(
            decoration: InputDecoration(
              hintText: 'APPLICATION_MOBILE_FIELD_LABEL_FOOD'.tr(),
              labelText: 'APPLICATION_MOBILE_FIELD_LABEL_FOOD'.tr(),
            ),
            initialValue: note.foodName,
            inputFormatters: [LengthLimitingTextInputFormatter(256)],
            validator: (val) => note.typeId == 3 && (val == null || val.isEmpty)
                ? 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_FOOD_NAME'.tr()
                : null,
            onSaved: (val) => note.foodName = val,
          ),
        ],
      ),
    );
  }
}
