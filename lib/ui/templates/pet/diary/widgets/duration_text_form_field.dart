import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class DurationTextFormField extends StatelessWidget {
  final PetActivity note;

  const DurationTextFormField({Key? key, required this.note}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: note.typeId == 4 || note.typeId == 7,
      child: Column(
        children: [
          const SizedBox(height: 20.0),
          TextFormField(
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: 'APPLICATION_MOBILE_FIELD_LABEL_DURATION'.tr(),
              labelText: 'APPLICATION_MOBILE_FIELD_LABEL_DURATION'.tr(),
              suffixText: 'min',
            ),
            initialValue: note.duration?.toString(),
            inputFormatters: [LengthLimitingTextInputFormatter(10)],
            validator: (val) => (note.typeId == 4 || note.typeId == 7) &&
                    (val == null || val.isEmpty)
                ? 'APPLICATION_MOBILE_MESSAGE_ERROR_MISSING_FIELD'.tr()
                : null,
            onSaved: (val) {
              if (val != null) {
                note.duration = int.parse(val);
              }
            },
          ),
        ],
      ),
    );
  }
}
