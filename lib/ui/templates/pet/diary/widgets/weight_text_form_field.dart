import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';

class WeightTextFormField extends StatelessWidget {
  final PetActivity note;

  const WeightTextFormField({Key? key, required this.note}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: note.typeId == 2 || note.typeId == 3,
      child: Column(
        children: [
          const SizedBox(height: 20.0),
          TextFormField(
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            decoration: InputDecoration(
              hintText: 'APPLICATION_MOBILE_FIELD_LABEL_UNIT_WEIGHT'.tr(),
              labelText: 'APPLICATION_MOBILE_FIELD_LABEL_UNIT_WEIGHT'.tr(),
              suffixText: Data().getUnitWeight().shortName,
            ),
            initialValue: note.weight?.toLocalizedString(),
            inputFormatters: [LengthLimitingTextInputFormatter(10)],
            validator: (val) => (note.typeId == 2 || note.typeId == 3) &&
                    (val == null || val.isEmpty)
                ? 'APPLICATION_MOBILE_MESSAGE_ERROR_MISSING_FIELD'.tr()
                : null,
            onSaved: (val) {
              if (val != null) {
                note.weight = Data().convertWeightFromUserUnitStr(val);
              }
            },
          ),
        ],
      ),
    );
  }
}
