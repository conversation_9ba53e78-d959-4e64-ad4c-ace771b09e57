import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/class/constants.dart'
    show vaccinesInformationSharedPrefsKey;
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:shared_preferences/shared_preferences.dart';

class VaccinesInformationWidget extends StatefulWidget {
  const VaccinesInformationWidget(
      {Key? key})
      : super(key: key);

  @override
  _VaccinesInformationWidgetState createState() =>
      _VaccinesInformationWidgetState();
}

class _VaccinesInformationWidgetState extends State<VaccinesInformationWidget> {
  final SharedPreferences sharedPreferences = SettingsDelegate().prefs;
  late bool _informationDisplayed;

  @override
  void initState() {
    super.initState();
    _informationDisplayed =
        sharedPreferences.getBool(vaccinesInformationSharedPrefsKey) ?? true;
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 100),
      switchInCurve: Curves.easeIn,
      switchOutCurve: Curves.easeOut,
      transitionBuilder: (Widget child, Animation<double> animation) {
        return SizeTransition(
          sizeFactor: animation,
          axis: Axis.vertical,
          // axisAlignment: -1,
          child: child,
        );
      },
      child: _informationDisplayed
          ? Card(
              color: Theme.of(context).colorScheme.surface,
              child: ListTile(
                contentPadding:
                    const EdgeInsets.fromLTRB(15.0, 5.0, 15.0, 15.0),
                leading: Icon(
                  FontAwesomeIcons.infoCircle,
                  color: Theme.of(context).primaryColor,
                ),
                minLeadingWidth: 20.0,
                title: Align(
                  alignment: Alignment.topRight,
                  child: GestureDetector(
                    onTap: () {
                      setState(() => _informationDisplayed = false);
                      if (sharedPreferences
                              .containsKey(vaccinesInformationSharedPrefsKey) &&
                          sharedPreferences
                              .getBool(vaccinesInformationSharedPrefsKey)!) {
                        sharedPreferences.setBool(
                            vaccinesInformationSharedPrefsKey, false);
                      }
                    },
                    child: const Icon(
                      Icons.close,
                      size: 20.0,
                    ),
                  ),
                ),
                subtitle: Text(
                  'Vaccination helps protect your pet. '
                  'It is not compulsory but it is strongly recommended. '
                  'The diseases concerned are particularly painful, some are fatal or transmissible to humans.',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
            )
          : Align(
              alignment: Alignment.centerRight,
              child: IconButton(
                icon: Icon(
                  FontAwesomeIcons.infoCircle,
                  color: Theme.of(context).primaryColor,
                ),
                onPressed: () {
                  setState(() => _informationDisplayed = true);
                },
              ),
            ),
    );
  }
}
