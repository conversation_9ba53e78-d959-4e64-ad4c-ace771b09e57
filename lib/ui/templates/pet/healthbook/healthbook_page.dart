import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/healthbook.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/treatment_type.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/pet/diary/petactivity_page.dart';
import 'package:mybuddy/ui/templates/pet/healthbook/add_treatment.dart';

import 'widgets/vaccines_information.dart';

class HealthBookPage extends StatefulWidget {
  final Pet pet;

  const HealthBookPage({Key? key, required this.pet}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _HealthBookPageState();
}

//TODO MIDDLE AJOUTER LIEN PETCAREPACK  ++refs Type prevention

class _HealthBookPageState extends State<HealthBookPage> {
  HealthBook? healthBook;
  List<TreatmentActivity> vaccines = <TreatmentActivity>[];
  List<TreatmentActivity> internals = <TreatmentActivity>[];
  List<TreatmentActivity> externals = <TreatmentActivity>[];
  List<TreatmentActivity> sterilization = <TreatmentActivity>[];

  void getHealthBook() {
    if(healthBook != null) {
      healthBook = null;
      vaccines.clear();
      internals.clear();
      externals.clear();
      sterilization.clear();
    }
    healthBook = Tools().pet.getHealthBookData(widget.pet);

    for (var treatment in healthBook!.treatments) {
      switch (treatment.treatment.type) {
        case 0:
          vaccines.add(treatment);
          break;
        case 1:
          internals.add(treatment);
          break;
        case 2:
          externals.add(treatment);
          break;
        case 3:
          sterilization.add(treatment);
          break;
        default:
          break;
      }
    }
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    getHealthBook();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(
          '${widget.pet.name} - ${'APPLICATION_MOBILE_TITLE_HEALTHBOOK2'.tr()}',
          maxLines: 2,
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(10.0),
        children: [
          const VaccinesInformationWidget(),
          _buildTreatmentType(context, treatments: vaccines),
          _buildTreatmentType(context, treatments: internals),
          _buildTreatmentType(context, treatments: externals),
          _buildTreatmentType(context, treatments: sterilization),
        ],
      ),
    );
  }

  Future showChoiceDialog(TreatmentActivity treatmentActivity) async {
    const TextStyle textStyle =
        TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold);
    List<Widget> list = <Widget>[
      TextButton.icon(
        label: Expanded(
          child: Text(
            'APPLICATION_MOBILE_BUTTON_LABEL_ADD'.tr(),
            style: textStyle,
          ),
        ),
        onPressed: () async {
          Tools().navigatorPop(removeLast: false);
          await Tools().navigatorPush(
            AddEditTreatment(
              pet: widget.pet,
              treatment: treatmentActivity.treatment,
            ),
          );
          getHealthBook();
        },
        icon: const Icon(FontAwesomeIcons.plus),
      ),
    ];
    if (treatmentActivity.petActivity != null) {
      list.add(const Divider());
      list.add(
        TextButton.icon(
          icon: const Icon(FontAwesomeIcons.eye),
          label: Expanded(
            child: Text(
              'APPLICATION_MOBILE_BUTTON_LABEL_SEE'.tr(),
              style: textStyle,
            ),
          ),
          onPressed: () async {
            Tools().navigatorPop(removeLast: false);
            await Tools().navigatorPush(
              PetActivityPage(
                petActivity: treatmentActivity.petActivity!,
                fromHealthBook: true,
              ),
            );
            getHealthBook();
          },
        ),
      );
    }

    return await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: SingleChildScrollView(
            child: Column(
              children: list,
            ),
          ),
        );
      },
    );
  }

  Widget _buildGroupSeparator(dynamic type) {
//    Widget _child = Container(); //TODO MIDDLE add link to homecare
    TreatmentType? trType = Ref().get().getTreatmentType(type);
    return Container(
      padding: const EdgeInsets.fromLTRB(5.0, 15.0, 0.0, 0.0),
      child: Text(
        trType != null ? trType.code.tr() : '',
        style: Theme.of(context).textTheme.headline6,
      ),
    );
  }

/*  Widget _treatmentWidget(TreatmentActivity trA) {
    Widget _statusWidget;
    if (trA.petActivity == null) {
      _statusWidget = const Icon(FontAwesomeIcons.exclamationTriangle, color: Colors.red);
    } else if (trA.treatment.type == 3) {
      _statusWidget = Text(
        'APPLICATION_MOBILE_BUTTON_LABEL_DONE'.tr(),
        style: const TextStyle(color: Colors.green),
      );
    } else {
      _statusWidget = Text(
        Data().dateTimeToUserDateStr(trA.petActivity?.dateEnd),
        style: TextStyle(
            color: trA.petActivity?.dateEnd != null && trA.petActivity!.dateEnd!.isBefore(DateTime.now()) ? Colors.red : Colors.green),
      );
    }
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        showChoiceDialog(trA);
//        if (trA.petActivity != null) {
//          Tools().navigatorPush(
//            context,
//            MaterialPageRoute(
//              builder: (context) => PetActivityPage(
//                petActivity: trA.petActivity,
//                fromHealthBook: true,
//              ),
//            ),
//          );
//        }
      },
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            children: <Widget>[
              Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      trA.treatment.type == 0 && trA.treatment.acronym?.trim() != ''
                          ? '${trA.treatment.valence ?? ''} (${trA.treatment.acronym})'
                          : (trA.treatment.valence ?? ''),
                      style: const TextStyle(
                        color: Colors.blue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                children: <Widget>[
                  Expanded(child: Container()),
                  _statusWidget,
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }*/

  Widget _buildTreatmentType(BuildContext context,
      {required List<TreatmentActivity> treatments}) {
    List<TreatmentActivity> todoList = <TreatmentActivity>[];
    List<TreatmentActivity> todoNowList = <TreatmentActivity>[];
    List<TreatmentActivity> upToDateList = <TreatmentActivity>[];

    for (var t in treatments) {
      if (t.petActivity == null) {
        todoList.add(t);
        continue;
      }
      if (t.petActivity?.dateEnd != null &&
          t.petActivity!.dateEnd!.isBefore(DateTime.now()) &&
          t.treatment.type != 3) {
        todoNowList.add(t);
      } else {
        upToDateList.add(t);
      }
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildGroupSeparator(treatments.first.treatment.type),
        _buildTodoList(context,
            label: 'APPLICATION_MOBILE_TREATMENTS_STATE_TODO_NULL'.tr('to do'),
            color: Colors.deepOrangeAccent,
            treatments: todoList),
        _buildTodoList(context,
            label:
                'APPLICATION_MOBILE_TREATMENTS_STATE_TODO_NOW'.tr('to do now'),
            color: Colors.deepOrange,
            treatments: todoNowList),
        _buildTodoList(context,
            label: 'APPLICATION_MOBILE_TREATMENTS_STATE_UP_TO_DATE'
                .tr('up to date'),
            color: Colors.lightGreen,
            treatments: upToDateList,
            displayNeutering: true),
      ],
    );
  }

  Widget _buildTodoList(BuildContext context,
      {required List<TreatmentActivity> treatments,
      String? label,
      Color? color,
      bool displayNeutering = false}) {
    if (treatments.isEmpty) {
      return const SizedBox.shrink();
    }
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (treatments.first.treatment.type != 3)
              Chip(
                label: Text(label!),
                backgroundColor: color,
                labelStyle: Theme.of(context)
                    .textTheme
                    .caption
                    ?.copyWith(color: Colors.white),
              )
            else if (displayNeutering && treatments.first.treatment.type == 3)
              Chip(
                label:
                    Text('APPLICATION_MOBILE_TREATMENTS_STATE_DONE'.tr('done')),
                backgroundColor: Colors.lightBlueAccent,
                labelStyle: Theme.of(context)
                    .textTheme
                    .caption
                    ?.copyWith(color: Colors.white),
              )
            else
              const SizedBox(height: 1.0),

            ///endif
            ...treatments.map((t) {
              return InkWell(
                onTap: () => showChoiceDialog(t),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 5.0),
                  child: Text(
                    t.treatment.valence ?? '',
                    style: Theme.of(context).textTheme.subtitle1,
                  ),
                ),
              );
            }).toList(),
            const SizedBox(height: 5.0),
          ],
        ),
      ),
    );
  }
}
