import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:multi_select_flutter/multi_select_flutter.dart';
import 'package:mybuddy/Api/mb_api_pet_activity.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/models/treatment_type.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';

class AddEditTreatment extends StatefulWidget {
  final Pet? pet;

  final PetActivity? petActivity;
  final Treatment? treatment;

  const AddEditTreatment({Key? key, this.pet, this.treatment, this.petActivity})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _AddEditTreatmentState();
}

class _AddEditTreatmentState extends State<AddEditTreatment> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  late String title;
  late PetActivity _petActivity;
  late List<Pet> pets;
  late List<TreatmentType> treatmentTypes;
  late TextEditingController _dateStartController;
  late TextEditingController _dateEndController;
  late List<Treatment> _treatments;
  List<Treatment?> _selectedTreatments = <Treatment?>[];
  Pet? _pet;
  bool isSubmitting = false;

  @override
  void initState() {
    super.initState();
    title = widget.petActivity == null
        ? 'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_ADD'.tr()
        : 'APPLICATION_MOBILE_BUTTON_LABEL_EDIT'.tr();

    /// get select arrays
    pets = Data().getPets();
    treatmentTypes = Ref().get().treatmentTypes;

    _pet = widget.pet;
    if (widget.petActivity == null) {
      _petActivity = PetActivity();
      _petActivity.activityId = 9;
      _petActivity.dateStart = DateTime.now();
      _petActivity.treatments = [];
      if (widget.treatment != null) {
        _petActivity.typeId = widget.treatment!.type;

        /// not pre selected because people won't see that we can select
        /// multiple valences the first time
//        _petActivity.treatments = [widget.treatment];
      }
    } else {
      _petActivity = widget.petActivity!.copy();
      _pet = _petActivity.pets.first;
    }
    _dateStartController = TextEditingController();
    _dateStartController.text =
        Data().dateTimeToUserDateStr(_petActivity.dateStart);
    _dateEndController = TextEditingController();
    _dateEndController.text =
        Data().dateTimeToUserDateStr(_petActivity.dateEnd);
  }

  @override
  Widget build(BuildContext context) {
    _treatments = Ref().getTreatmentForPetAndType(_pet, _petActivity.typeId);
    if (_treatments.length == 1) {
      _petActivity.treatments = _treatments;
    }

    return Scaffold(
      appBar: MBAppBar(
        title: Text(title, maxLines: 2),
        leading: BackButton(
          onPressed: () => Tools().navigatorPop(value: false),
        ),
      ),
      persistentFooterButtons: [
        ElevatedButton(
          onPressed: isSubmitting ? null : () async => _submitForm(context),
          child: Container(
            width: double.infinity,
            alignment: Alignment.center,
            child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr('')),
          ),
        ),
      ],
      body: SafeArea(
        top: false,
        bottom: false,
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.disabled,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              children: <Widget>[
                const SizedBox(height: 15.0),
                DropdownButtonFormField<Pet>(
                  value: _pet,
                  decoration: InputDecoration(
                    hintText:
                        'APPLICATION_MOBILE_FIELD_LABEL_WHICH_PET'.tr() + '*',
                    labelText:
                        'APPLICATION_MOBILE_FIELD_LABEL_WHICH_PET'.tr() + '*',
                  ),
                  onChanged: (Pet? newValue) {
                    setState(() {
                      if (newValue != null) {
                        _pet = newValue;
                        _petActivity.treatments = [];
                      }
                    });
                  },
                  validator: (val) {
                    return val != null
                        ? null
                        : 'APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_TYPE'.tr();
                  },
                  items: pets.map((Pet pet) {
                    return DropdownMenuItem<Pet>(
                      value: pet,
                      child: Text(pet.name),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 15.0),
                DropdownButtonFormField<TreatmentType>(
                  value: Ref().get().getTreatmentType(_petActivity.typeId),
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_TYPE'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_TYPE'.tr() + '*',
                  ),
                  onChanged: (TreatmentType? newValue) {
                    setState(() {
                      if (newValue != null) {
                        _petActivity.typeId = newValue.id;
                        _petActivity.treatments = [];
                      }
                    });
                  },
                  validator: (val) {
                    return val != null
                        ? null
                        : 'APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_TYPE'.tr();
                  },
                  items: treatmentTypes.map((TreatmentType trType) {
                    return DropdownMenuItem<TreatmentType>(
                      value: trType,
                      child: Text(trType.code.tr()),
                    );
                  }).toList(),
                ),
                Visibility(
                  visible: _treatments.length > 1,
                  child: Column(
                    children: [
                      const SizedBox(height: 15.0),
                      MultiSelectDialogField(
                        title: Text(
                          'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_VALENCE'
                                  .tr() +
                              '*',
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                        items: _treatments.map((Treatment treatment) {
                          return MultiSelectItem<Treatment?>(
                            treatment,
                            '${treatment.valence} (${treatment.acronym})',
                          );
                        }).toList(),
                        onConfirm: (List<Treatment?> results) {
                          setState(() {
                            _selectedTreatments =
                                List<Treatment?>.from(results);
                          });
                        },
                        buttonText: Text(
                          'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_VALENCE'
                              .tr(),
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        buttonIcon: const Icon(Icons.checklist_outlined),
                        confirmText: Text(
                          'APPLICATION_MOBILE_BUTTON_LABEL_OK'.tr(),
                        ),
                        cancelText: Text(
                          'APPLICATION_MOBILE_BUTTON_LABEL_CANCEL'.tr(),
                        ),
                        selectedColor: Theme.of(context).colorScheme.secondary,
                        selectedItemsTextStyle: TextStyle(
                          color: Theme.of(context).colorScheme.secondary,
                          fontWeight: FontWeight.w500,
                        ),
                        validator: (val) {
                          if (val == null || val.isEmpty) {
                            return 'APPLICATION_MOBILE_HB_ERROR_NO_VALENCE'
                                .tr();
                          }
                          return null;
                        },
                        initialValue: _petActivity.treatments!,
                        onSaved: (value) {
                          if (value == null) return;
                          setState(() {
                            _petActivity.treatments =
                                List<Treatment>.from(value);
                          });
                        },
                      ),
                      if (_selectedTreatments.isEmpty)
                        Container(
                          padding: const EdgeInsets.all(10.0),
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'APPLICATION_MOBILE_LABEL_FIELD_NO_SELECTION'
                                .tr("None selected"),
                            style: Theme.of(context).textTheme.titleSmall,
                          ),
                        ),

                      ///endif
                    ],
                  ),
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  decoration: InputDecoration(
                    hintText:
                        'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_NAME'
                            .tr(),
                    labelText:
                        'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_NAME'
                            .tr(),
                  ),
                  initialValue: _petActivity.treatmentName,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  onSaved: (val) => _petActivity.treatmentName = val ?? '',
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  decoration: InputDecoration(
                    hintText:
                        'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_LOT_NUMBER'
                            .tr(),
                    labelText:
                        'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_LOT_NUMBER'
                            .tr(),
                  ),
                  initialValue: _petActivity.lotNumber,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  onSaved: (val) => _petActivity.lotNumber = val ?? '',
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  decoration: InputDecoration(
                    hintText:
                        'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_RELEASE_DATE'
                            .tr(),
                    labelText:
                        'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_RELEASE_DATE'
                            .tr(),
                  ),
                  controller: _dateStartController,
                  readOnly: true,
                  validator: (val) => _petActivity.dateStart != null &&
                          _petActivity.dateStart!.isBefore(DateTime.now())
                      ? null
                      : 'APPLICATION_MOBILE_MESSAGE_INVALID_DATE'.tr(),
                  onTap: _chooseDateTime,
                ),
                Visibility(
                  visible: _petActivity.typeId != 3,
                  child: Column(
                    children: [
                      const SizedBox(height: 15.0),
                      TextFormField(
                        decoration: InputDecoration(
                          hintText:
                              'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_NEXT_DATE'
                                  .tr(),
                          labelText:
                              'APPLICATION_MOBILE_LABEL_HEALTHBOOK2_VACCINE_NEXT_DATE'
                                  .tr(),
                        ),
                        controller: _dateEndController,
                        readOnly: true,
                        validator: (_) => _petActivity.typeId == 3 ||
                                (_petActivity.dateStart != null &&
                                    _petActivity.dateEnd != null &&
                                    _petActivity.dateEnd!
                                        .isAfter(_petActivity.dateStart!))
                            ? null
                            : 'APPLICATION_MOBILE_HB_ERROR_RELEASE_DATE_AFTER_NEXT'
                                .tr(),
                        onTap: _chooseReminder,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _chooseDateTime() async {
    DateTime now = DateTime.now();
    DateTime initialDate = _petActivity.dateStart ?? now;
    if (initialDate.isAfter(now)) initialDate = now;

    DateTime? date = await Tools().common.showDateDialog(
          context: context,
          initialDate: initialDate,
          firstDate: DateTime(now.year - 15, 1, 1),
          lastDate: now,
        );

    date ??= initialDate;
    if (date.isAfter(now)) date = now;

    setState(() {
      _petActivity.dateStart = date;
      _dateStartController.text =
          Data().dateTimeToUserDateStr(_petActivity.dateStart);
    });
  }

  Future<void> _chooseReminder() async {
    DateTime? initialDate = _petActivity.dateEnd;
    if (initialDate == null) {
      initialDate = _petActivity.dateStart ?? DateTime.now();
      initialDate =
          DateTime(initialDate.year + 1, initialDate.month, initialDate.day);
    }

    DateTime? date = await Tools().common.showDateDialog(
          context: context,
          initialDate: initialDate,
          firstDate: _petActivity.dateStart ?? DateTime.now(),
          lastDate: DateTime(DateTime.now().year + 10, 31, 12),
        );
    setState(() {
      _petActivity.dateEnd = date ?? initialDate;
      _dateEndController.text =
          Data().dateTimeToUserDateStr(_petActivity.dateEnd);
    });
  }

  Future<void> _submitForm(BuildContext context) async {
    final FormState? form = _formKey.currentState;

    if (form == null || !form.validate()) {
      Tools().common.showValidationErrorMessage(context);
      return;
    }

    setState(() {
      isSubmitting = true;
    });

    form.save();

    _petActivity.pets = [_pet!];

    MBResponse response = await MbApiPetActivity()
        .addTreatmentRequest(context, _petActivity, widget.petActivity == null);

    if (response.success) {
      Tools().navigatorPop(value: true);
    }
    setState(() {
      isSubmitting = false;
    });
  }
}
