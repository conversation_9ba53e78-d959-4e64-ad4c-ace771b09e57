import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/pets_bloc.dart';
import 'package:mybuddy/blocs/walk_provider.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/ui/components/mb_home_card.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/components/widget_add_new_button.dart';
import 'package:mybuddy/ui/templates/pet/pet_pageview.dart';

class WidgetPetList extends StatelessWidget {
  const WidgetPetList({Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<PetsBloc>(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        AddNewButton(
            label: "APPLICATION_MOBILE_LABEL_ADD_YOUR_DOG".tr(),
            onPressed: () => {Tools().pet.addPetAction(context, null)}),
        Expanded(
            child: StreamBuilder<List<Pet>>(
          stream: bloc.stream,
          builder: (context, snapshot) {
            if (!snapshot.hasData) return Container();
        List<Pet> pets = WalkProvider.of().getPetsWithSelectedAtBeginning();//snapshot.data!;
            if (pets.isEmpty) {
              return Center(
                child: Text('APPLICATION_MOBILE_LABEL_NO_DOG_SENTENCE'.tr()),
              );
            }
            return ListView.builder(
              itemBuilder: (context, position) {
                return GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Tools().navigatorPush(
                        PetPageView(pet: pets[position]),
                      );
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 3.0, horizontal: 13.0),
                      child: OnePet(pet: pets[position]),
                    ));
              },
              itemCount: pets.length,
            );
          },
        )),
      ],
    );
  }
}

class OnePet extends StatelessWidget {
  final Pet pet;

  const OnePet({Key? key, required this.pet}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    bool isSelected = WalkProvider.of().isPetSelected(pet);
    return MBCard(
      borderColor: isSelected ? const Color(0xffADD2C2) : const Color(0xffCECECE),
      icon: MBDogAvatar(
        backgroundColor: isSelected
            ? const Color(0xff419563)
            : Colors.transparent,
        size: 50,
        avatarURL: pet.avatarUrl,
        deceased: pet.deceased,
      ),
      title: Text(
        pet.name,
        style: Theme.of(context).textTheme.headline5?.copyWith(
              fontSize: 18.0,
            ),
      ),
      subtitle: Row(
        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          subDataTile(context, "assets/icon/dog_walk.png", "walks", pet.totalWalks.toString()),
          subDataTile(context, "assets/icon/miles_purple_icon.png", "miles",
              pet.totalMileWithTwoDecimal),
          if(!pet.deceased)
          subDataTile(context, "assets/icon/weight_blue.png", Data().getUnitWeight().shortName, pet.getWeight)
          else
            Container(width: (MediaQuery.of(context).size.width-120)/3,),
        ],
      ),
    );
  }

  Widget subDataTile(
      BuildContext context, String icon, String title, String value) {
    return Expanded(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(icon, width: 16.0),
          const SizedBox(width: 5.0),
          RichText(
            text: TextSpan(
              text: "$value ",
              style: Theme.of(context).textTheme.subtitle1?.copyWith(
                    fontSize: 12.0,
                  ),
              children: <TextSpan>[
                TextSpan(
                  text: title,
                  style: Theme.of(context)
                      .textTheme
                      .subtitle2
                      ?.copyWith(fontSize: 10),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
