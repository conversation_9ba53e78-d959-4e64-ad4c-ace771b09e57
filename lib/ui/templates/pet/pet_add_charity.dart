import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/dog_charity_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class PetAddCharity extends StatelessWidget {
  PetAddCharity({Key? key}) : super(key: key);

  DogCharityController controller = DogCharityController.of();

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Form(
      key: controller.formKey,
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(15, 15, 15, 0),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () {
                        Tools().navigatorPop();
                      },
                      icon: const Icon(
                        Icons.clear,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'APPLICATION_MOBILE_PET_SHELTER_ENTER'.tr(),
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(
                      width: 45,
                    ),
                  ],
                ),
              ),
              const Divider(),
              _formNameField(context),
              _formCityField(context),
              _formStateField(context),
              _saveButton(context, size),
            ],
          ),
        ),
      ),
    );
  }

  Widget _formNameField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0, left: 15, right: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'APPLICATION_MOBILE_FIELD_LABEL_PET_NAME'.tr() + " *",
            style: Theme.of(context)
                .textTheme
                .labelMedium
                ?.copyWith(fontSize: 15, fontWeight: FontWeight.w600),
          ),
          const SizedBox(
            height: 8,
          ),
          TextFormField(
            focusNode: controller.name,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            decoration: InputDecoration(
              hintText: 'APPLICATION_MOBILE_FIELD_LABEL_PET_NAME'.tr(),
            ),
            controller: controller.nameController,
            onFieldSubmitted: (value) {
              controller.name.unfocus();
              FocusScope.of(context).requestFocus(controller.city);
            },
            validator: (val) {
              if (val?.trim().isEmpty ?? true) {
                return 'APPLICATION_MOBILE_ERROR_NAME_EMPTY'.tr();
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _formCityField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0, left: 15, right: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'APPLICATION_MOBILE_FIELD_LABEL_NAME_CITY'.tr() + " *",
            style: Theme.of(context)
                .textTheme
                .labelMedium
                ?.copyWith(fontSize: 15, fontWeight: FontWeight.w600),
          ),
          const SizedBox(
            height: 8,
          ),
          TextFormField(
              focusNode: controller.city,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              decoration: InputDecoration(
                hintText: 'APPLICATION_MOBILE_FIELD_LABEL_NAME_CITY'.tr(),
              ),
              controller: controller.cityController,
              validator: (val) {
                if (val?.trim().isEmpty ?? true) {
                  return 'APPLICATION_MOBILE_ERROR_CITY_EMPTY'.tr();
                }
                return null;
              }),
        ],
      ),
    );
  }

  Widget _formStateField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0, left: 15, right: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'APPLICATION_MOBILE_FIELD_LABEL_STATE'.tr() + " *",
            style: Theme.of(context)
                .textTheme
                .labelMedium
                ?.copyWith(fontSize: 15, fontWeight: FontWeight.w600),
          ),
          const SizedBox(
            height: 8,
          ),
          TextFormField(
            autovalidateMode: AutovalidateMode.onUserInteraction,
            decoration: InputDecoration(
              hintText: 'APPLICATION_MOBILE_FIELD_LABEL_STATE'.tr(),
              suffixIcon: const Icon(
                Icons.arrow_drop_down,
                size: 24.0,
              ),
            ),
            readOnly: true,
            controller: controller.stateController,
            validator: (val) {
              if (val?.trim().isEmpty ?? true) {
                return 'APPLICATION_MOBILE_ERROR_STATE_EMPTY'.tr();
              }
              return null;
            },
            onTap: () {
              controller.selectState();
            },
          ),
        ],
      ),
    );
  }

  Widget _saveButton(context, Size size) {
    return Padding(
      padding: EdgeInsets.fromLTRB(15, size.height * .2, 15, 20),
      child: ElevatedButton(
        onPressed: controller.saveShelter,
        child: Container(
          width: double.infinity,
          alignment: Alignment.center,
          child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SAVE'.tr()),
        ),
      ),
    );
  }
}
