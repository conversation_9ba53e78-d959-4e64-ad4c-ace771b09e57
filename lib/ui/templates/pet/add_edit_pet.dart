// ignore_for_file: curly_braces_in_flow_control_structures

import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/pet_bloc.dart';
import 'package:mybuddy/class/mb_text_input_formatters.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/controllers/dog_form_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/reconciliation.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/ui/components/forms/choice_chip_form_field.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';

import 'widget_pet_avatar_with_choice.dart';
import 'widgets/charity_confirmation_dialog.dart';
import 'widgets/label_form_with_dialog.dart';

class AddEditPetForm extends StatelessWidget {
  final String? title;
  final Pet? pet;
  final ReconcilablePet? pimsPet;
  final bool onLogin;

  AddEditPetForm(
      {Key? key, this.pet, this.title, this.pimsPet, this.onLogin = false})
      : super(key: key);

  late DogFormController controller;

  @override
  Widget build(BuildContext context) {
    controller = Get.put(DogFormController(pet: pet));

    TextStyle? labelTextStyle = Theme.of(context)
        .textTheme
        .labelMedium
        ?.copyWith(fontSize: 14, fontWeight: FontWeight.w600);

    return Scaffold(
      key: controller.scaffoldKey,
      backgroundColor: const Color(0xfffbfbfb),
      appBar: MBAppBar(
        title: Text(
          title ?? 'APPLICATION_MOBILE_BUTTON_LABEL_DOG_ADD'.tr(),
          maxLines: 2,
          style: Theme.of(context).textTheme.headline6,
        ),
      ),
      persistentFooterButtons: [
        Obx(() {
          return ElevatedButton(
            onPressed: controller.isSubmitting
                ? null
                : () async => await controller.submitForm(onLogin),
            child: Container(
              width: double.infinity,
              alignment: Alignment.center,
              child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SAVE'.tr()),
            ),
          );
        }),
      ],
      body: SafeArea(
        top: false,
        bottom: true,
        child: GestureDetector(
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);

            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: Form(
            key: controller.formKey,
            autovalidateMode: AutovalidateMode.disabled,
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
              child: Column(
                children: <Widget>[
                  BlocProvider<PetBloc>(
                    bloc: PetBloc(
                        petId:
                            !controller.isNew ? controller.addEditPet.id : 0),
                    child: PetAvatarWithChoice(
                      returnAvatar: (File file) {
                        controller.avatar = file;
                      },
                    ),
                  ),
                  _formSectionWrapper([
                    _formNameField(context, labelTextStyle),
                    _formGenderField(context, labelTextStyle),
                    _formBreedField(context, labelTextStyle),
                    _formCrossedField(context, labelTextStyle),
                    _formSecondaryBreedField(context, labelTextStyle),
                    _formWeightField(context, labelTextStyle),
                  ]),
                  _formSectionWrapper([
                    _formBirthDateField(context, labelTextStyle),
                    _formAdoptionDateField(context),
                    _formAdoptedFromCharityField(context, labelTextStyle),
                    Obx(
                      () => controller.adoptedFromCharity
                          ? _formAdoptedCharityField(context, labelTextStyle)
                          : _formWhereDidYouGetDogField(
                              context,
                              labelTextStyle,
                            ),
                    )
                  ]),
                  _formSectionWrapper([
                    _formActivityStatusField(context),
                    _formNewWalkField(context),
                  ]),
                  _formSectionWrapper([
                    _formRainbowBridgeField(context),
                  ]),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _formSectionWrapper(List<Widget> fieldWidgets) {
    return Container(
      margin: const EdgeInsets.only(top: 15),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: const Color(0xffB6B7B9),
        ),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(children: [...fieldWidgets, const SizedBox(height: 15)]),
    );
  }

  Widget _formNameField(BuildContext context, TextStyle? labelStyle) {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'APPLICATION_MOBILE_FIELD_LABEL_PET_NAME'.tr() + " *",
            style: labelStyle,
          ),
          const SizedBox(
            height: 8,
          ),
          TextFormField(
              autovalidateMode: AutovalidateMode.onUserInteraction,
              decoration: InputDecoration(
                hintText: 'APPLICATION_MOBILE_FIELD_HINT_PET_NAME'.tr(),
                hintStyle: Theme.of(context)
                    .textTheme
                    .labelMedium
                    ?.copyWith(fontSize: 14),
              ),
              initialValue: controller.addEditPet.name,
              inputFormatters: [
                LengthLimitingTextInputFormatter(256),
                FilteringTextInputFormatter.allow(controller.textRegex),
              ],
              validator: controller.nameValidator,
              onSaved: (val) {
                controller.addEditPet.name = val!.trim();
                controller.petNameForBirthDateSheet = val.trim();
              }),
        ],
      ),
    );
  }

  Widget _formGenderField(BuildContext context, TextStyle? labelStyle) {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'APPLICATION_MOBILE_FIELD_LABEL_PET_GENDER'.tr() + " *",
            style: labelStyle,
          ),
          const SizedBox(
            height: 8,
          ),
          Obx(() {
            return DropdownButtonFormField<Gender>(
              autovalidateMode: AutovalidateMode.onUserInteraction,
              value: controller.gender,
              decoration: InputDecoration(
                hintText: 'APPLICATION_MOBILE_SENTENCE_LABEL_PET_GENDER'.tr(),
                hintStyle: Theme.of(context)
                    .textTheme
                    .labelMedium
                    ?.copyWith(fontSize: 14),
              ),
              onChanged: (Gender? newValue) {
                controller.gender = newValue;
              },
              validator: controller.genderValidator,
              items: Ref().get().genders.map((Gender gender) {
                return DropdownMenuItem<Gender>(
                  value: gender,
                  child: Text(gender.name),
                );
              }).toList(),
              onSaved: (Gender? value) {
                if (value != null) {
                  controller.addEditPet.genderId = value.id;
                }
              },
            );
          }),
        ],
      ),
    );
  }

  Widget _formBirthDateField(BuildContext context, TextStyle? labelStyle) {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'APPLICATION_MOBILE_FIELD_LABEL_BIRTHDATE_2'.tr() + " *",
            style: labelStyle,
          ),
          const SizedBox(height: 8),
          TextFormField(
            autovalidateMode: AutovalidateMode.onUserInteraction,
            decoration: InputDecoration(
              hintText: 'APPLICATION_MOBILE_SENTENCE_LABEL_BIRTH_DATE'.tr(),
              hintStyle: Theme.of(context)
                  .textTheme
                  .labelMedium
                  ?.copyWith(fontSize: 14),
              prefixIcon: Icon(
                Icons.calendar_today_outlined,
                color: Theme.of(context).primaryColor,
              ),
            ),
            controller: controller.birthDateController,
            readOnly: true,
            keyboardType: TextInputType.datetime,
            validator: controller.birthDateValidator,
            onTap: () => controller.chooseBirthDate(context),
          ),
        ],
      ),
    );
  }

  Widget _formAdoptionDateField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LabelFormWithDialog(
            label: 'APPLICATION_MOBILE_FIELD_LABEL_ADOPTION_DATE'.tr() + " *",
            dialogContent: 'APPLICATION_CONTENT_INFORMATION_ADOPTION_DATE'.tr(),
          ),
          TextFormField(
            autovalidateMode: AutovalidateMode.onUserInteraction,
            decoration: InputDecoration(
              hintText: 'APPLICATION_MOBILE_SENTENCE_LABEL_ADOPTION_DATE'.tr(),
              hintStyle: Theme.of(context)
                  .textTheme
                  .labelMedium
                  ?.copyWith(fontSize: 14),
              prefixIcon: Icon(
                Icons.calendar_today_outlined,
                color: Theme.of(context).primaryColor,
              ),
            ),
            controller: controller.adoptionDateController,
            readOnly: true,
            keyboardType: TextInputType.datetime,
            validator: controller.adoptionDateValidator,
            onTap: () => controller.chooseAdoptionDateSheet(context),
          ),
        ],
      ),
    );
  }

  Widget _formAdoptedFromCharityField(
      BuildContext context, TextStyle? labelStyle) {
    return Padding(
      padding: const EdgeInsets.only(top: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'APPLICATION_MOBILE_FIELD_LABEL_ADOPTED_FROM_CHARITY'.tr() + ' *',
            style: labelStyle,
          ),
          const SizedBox(height: 5.0),
          SizedBox(
            width: double.infinity,
            child: Wrap(
              spacing: 5.0,
              runSpacing: 5.0,
              children: yesNoChoices.map((e) {
                return Obx(() {
                  return ChoiceChipFormField(
                    label: e['label'],
                    isSelected: controller.adoptedFromCharity == e['value'],
                    onSelected: (selected) {
                      controller.adoptedFromCharity = e['value'];
                    },
                    width: 60,
                  );
                });
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _formAdoptedCharityField(BuildContext context, TextStyle? labelStyle) {
    void _openCharityConfirmationDialog(bool? value) {
      if (!controller.adoptedFromSelectedCharity)
        Get.dialog(
          Dialog(
            backgroundColor: Colors.white,
            child: CharityConfirmationDialog(
              controller.userWoofTraxShelter,
              onConfirm: () {
                controller.adoptedFromSelectedCharity = value ?? false;
                FocusScopeNode currentFocus = FocusScope.of(context);
                if (!currentFocus.hasPrimaryFocus) {
                  currentFocus.unfocus();
                }
              },
            ),
          ),
        );
      else {
        controller.adoptedFromSelectedCharity = false;
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
        }
      }
    }

    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: Obx(
        () => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'APPLICATION_MOBILE_FIELD_LABEL_ADOPTED_CHARITY'.tr() + ' *',
              style: labelStyle,
            ),
            const SizedBox(height: 8),
            TextFormField(
              autovalidateMode: AutovalidateMode.onUserInteraction,
              controller: controller.adoptedCharityController,
              readOnly: true,
              decoration: InputDecoration(
                hintText:
                    'APPLICATION_MOBILE_SENTENCE_LABEL_ADOPTED_CHARITY'.tr(),
                hintStyle: Theme.of(context)
                    .textTheme
                    .labelMedium
                    ?.copyWith(fontSize: 14),
                suffixIcon: const Icon(
                  Icons.arrow_drop_down,
                  size: 24.0,
                ),
              ),
              validator: controller.adoptedCharityValidator,
              onTap: () {
                controller.selectCharity(context);
              },
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  height: 20,
                  width: 20,
                  margin: const EdgeInsets.only(right: 5),
                  child: Checkbox(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(3),
                    ),
                    side: const BorderSide(color: Color(0xffD9D9D9), width: 1),
                    activeColor: const Color(0xff419563),
                    value: controller.adoptedFromSelectedCharity,
                    onChanged: _openCharityConfirmationDialog,
                  ),
                ),
                Expanded(
                    child: Text(
                  'APPLICATION_MOBILE_SENTENCE_LABEL_ADOPTED_FROM_SELECTED_CHARITY'
                      .tr(),
                  style: Theme.of(context)
                      .textTheme
                      .labelMedium
                      ?.copyWith(fontSize: 13),
                )),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _formWhereDidYouGetDogField(
      BuildContext context, TextStyle? labelStyle) {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'APPLICATION_MOBILE_FIELD_LABEL_WHERE_DID_YOU_GET_DOG'.tr(),
            style: labelStyle,
          ),
          const SizedBox(height: 8),
          TextFormField(
            autovalidateMode: AutovalidateMode.onUserInteraction,
            initialValue: controller.addEditPet.otherThanShelter,
            inputFormatters: [LengthLimitingTextInputFormatter(100)],
            maxLength: 100,
            onSaved: (val) =>
                controller.addEditPet.otherThanShelter = val!.trim(),
            decoration: const InputDecoration(
              counterStyle: TextStyle(color: Color(0xff419563)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _formCrossedField(BuildContext context, TextStyle? labelStyle) {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'APPLICATION_MOBILE_SENTENCE_LABEL_PUREBRED'
                .tr('Is your dog purebred?'),
            style: labelStyle,
          ),
          const SizedBox(height: 5.0),
          SizedBox(
            width: double.infinity,
            child: Wrap(
              spacing: 5.0,
              runSpacing: 5.0,
              children: yesNoChoices.map((e) {
                return Obx(() {
                  return ChoiceChipFormField(
                    label: e['label'],
                    isSelected: controller.pureBreed == e['value'],
                    onSelected: (selected) {
                      controller.pureBreed = e['value'];
                    },
                    width: 60,
                  );
                });
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _formBreedField(BuildContext context, TextStyle? labelStyle) {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'APPLICATION_MOBILE_FIELD_LABEL_PRIMARY_BREED'.tr() + " *",
            style: labelStyle,
          ),
          const SizedBox(
            height: 8,
          ),
          TextFormField(
            autovalidateMode: AutovalidateMode.onUserInteraction,
            controller: controller.breedController,
            enabled: controller.species != null,
            readOnly: true,
            decoration: InputDecoration(
              hintText: 'APPLICATION_MOBILE_SENTENCE_LABEL_BREED'.tr(),
              hintStyle: Theme.of(context)
                  .textTheme
                  .labelMedium
                  ?.copyWith(fontSize: 14),
              suffixIcon: const Icon(
                Icons.arrow_drop_down,
                size: 24.0,
              ),
            ),
            validator: controller.breedValidator,
            onTap: () {
              controller.selectBreed();
            },
            onSaved: (String? value) =>
                controller.addEditPet.breedId = controller.breed?.id,
          ),
        ],
      ),
    );
  }

  Widget _formSecondaryBreedField(BuildContext context, TextStyle? labelStyle) {
    return Obx(() {
      if (controller.pureBreed ?? true) {
        return const SizedBox.shrink();
      }
      return Padding(
        padding: const EdgeInsets.only(top: 15.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'APPLICATION_MOBILE_FIELD_LABEL_SECONDARY_BREED'.tr(),
              style: labelStyle,
            ),
            const SizedBox(
              height: 8,
            ),
            TextFormField(
              controller: controller.secondaryBreedController,
              enabled: controller.species != null,
              readOnly: true,
              decoration: InputDecoration(
                hintText:
                    'APPLICATION_MOBILE_SENTENCE_LABEL_SECONDARY_BREED'.tr(),
                hintStyle: Theme.of(context)
                    .textTheme
                    .labelMedium
                    ?.copyWith(fontSize: 14),
                suffixIcon: const Icon(
                  Icons.arrow_drop_down,
                  size: 24.0,
                ),
              ),
              onTap: () {
                controller.selectBreed(primary: false);
              },
            ),
          ],
        ),
      );
    });
  }

  Widget _formActivityStatusField(BuildContext context) {
    return Visibility(
      visible: controller.species != null && controller.species!.id == 2,
      child: Padding(
        padding: const EdgeInsets.only(top: 10.0),
        child: Column(
          children: [
            LabelFormWithDialog(
              label: 'WOOFTRAX_LABEL_ACTIVITY_STATUS'
                  .tr('Auto select for new walks?'),
              dialogContent: 'WOOFTRAX_CONTENT_INFORMATION_ACTIVITY_STATUS'.tr(
                  'Select Yes, and this dog will show on your Home screen as selected for new walks.'),
            ),
            SizedBox(
              width: double.infinity,
              child: Wrap(
                spacing: 5.0,
                runSpacing: 5.0,
                children: yesNoChoices.map((e) {
                  return Obx(() {
                    return ChoiceChipFormField(
                      label: e['label'],
                      isSelected: controller.autoSelect == e['value'],
                      onSelected: (selected) {
                        controller.autoSelect = e['value'];
                      },
                      width: 60,
                    );
                  });
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _formNewWalkField(BuildContext context) {
    return Obx(() {
      if (controller.autoSelect) {
        return const SizedBox.shrink();
      }
      return Column(
        children: [
          LabelFormWithDialog(
            label: 'WOOFTRAX_LABEL_NEW_WALK_STATUS_STATUS'
                .tr('Enable for new walks?'),
            dialogContent: 'WOOFTRAX_CONTENT_INFORMATION_ACTIVITY_STATUS'.tr(
                'Select Yes, and this dog will show on your Home screen as unselected for new walks. Select No, and this dog will not show on your Home screen.'),
          ),
          SizedBox(
            width: double.infinity,
            child: Wrap(
              spacing: 5.0,
              runSpacing: 5.0,
              children: yesNoChoices.map((e) {
                return ChoiceChipFormField(
                  label: e['label'],
                  isSelected: controller.newWalk == e['value'],
                  onSelected: (selected) {
                    controller.newWalk = e['value'];
                  },
                  width: 60,
                );
              }).toList(),
            ),
          ),
        ],
      );
    });
  }

  Widget _formWeightField(BuildContext context, TextStyle? labelStyle) {
    if (controller.addEditPet.deceased) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'APPLICATION_MOBILE_FIELD_LABEL_UNIT_WEIGHT'.tr(),
            style: labelStyle,
          ),
          const SizedBox(
            height: 8,
          ),
          Row(
            children: [
              Expanded(
                child: SizedBox(
                  height: 45,
                  child: TextFormField(
                    controller: controller.weightController,
                    decoration: InputDecoration(
                      hintText:
                          'APPLICATION_MOBILE_FIELD_LABEL_UNIT_WEIGHT'.tr(),
                      hintStyle: Theme.of(context)
                          .textTheme
                          .labelMedium
                          ?.copyWith(fontSize: 14),
                      suffix: const Text(
                        'lbs',
                        style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Color(0xff939393)),
                      ),
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(controller.weightRegex),
                      NumericalRangeFormatter(min: 0.0, max: 999.99)
                    ],
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    onSaved: (val) {
                      if (val != null && val.isNotEmpty) {
                        controller.addEditPet.weight = double.parse(val);
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(
                width: 8,
              ),
              GestureDetector(
                onTap: controller.weightDecrement,
                child: Container(
                  height: 40,
                  width: 40,
                  decoration: BoxDecoration(
                      border: Border.all(color: const Color(0xffB6B7B9)),
                      shape: BoxShape.circle),
                  child: const Center(child: Icon(Icons.remove)),
                ),
              ),
              const SizedBox(
                width: 8,
              ),
              GestureDetector(
                onTap: controller.weightIncrement,
                child: Container(
                  height: 40,
                  width: 40,
                  decoration: BoxDecoration(
                      border: Border.all(color: const Color(0xffB6B7B9)),
                      shape: BoxShape.circle),
                  child: const Center(child: Icon(Icons.add)),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _formRainbowBridgeField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LabelFormWithDialog(
            label: 'APPLICATION_MOBILE_SENTENCE_LABEL_RAINBOW'.tr() + " *",
            dialogContent: 'WOOFTRAX_CONTENT_INFORMATION_ACTIVITY_STATUS'.tr(
                'Walk in memory of a beloved pet by selecting Yes. Your dog’s photo will show a small rainbow icon.'),
          ),
          Obx(() {
            return DropdownButtonFormField<bool>(
              value: controller.rainbowBridge,
              decoration: InputDecoration(
                hintText: 'APPLICATION_MOBILE_SENTENCE_LABEL_RAINBOW'.tr(),
              ),
              onChanged: (bool? newValue) {
                controller.rainbowBridge = newValue ?? false;
              },
              validator: (val) {
                return val != null
                    ? null
                    : 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_SPECIES'
                        .tr();
              },
              items: yesNoChoices.map((choice) {
                return DropdownMenuItem<bool>(
                  value: choice["value"],
                  child: Text(choice["label"]),
                );
              }).toList(),
              onSaved: (bool? value) {
                controller.addEditPet.deceased = value ?? false;
              },
            );
          }),
        ],
      ),
    );
  }
}

List<Map<String, dynamic>> yesNoChoices = [
  {'label': 'APPLICATION_MOBILE_BUTTON_LABEL_YES'.tr(), 'value': true},
  {'label': 'APPLICATION_MOBILE_BUTTON_LABEL_NO'.tr(), 'value': false}
];
