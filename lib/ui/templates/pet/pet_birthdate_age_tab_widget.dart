import 'package:flutter/material.dart';
import 'package:get/state_manager.dart';
import 'package:mybuddy/controllers/dog_form_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/account/owner_set_daily_goals.dart';

List<int> yearValues = [for (var i = 0; i <= 34; i++) i];
List<int> monthValues = [for (var i = 0; i <= 15; i++) i];

class PetBirthDateAgeTabWidget extends StatelessWidget {
  const PetBirthDateAgeTabWidget(this.controller, {Key? key}) : super(key: key);

  final DogFormController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 10),
          child: Text(
            "'Age' to estimate or 'Birthdate' to enter your pet's exact birthday",
            textAlign: TextAlign.center,
            style: TextStyle(color: Color(0xff939393), fontSize: 13),
          ),
        ),
        Expanded(
          child: Stack(
            children: [
              Obx(
                () => Row(
                  children: [
                    Expanded(
                      child: NumberCarousel(
                        showIcon: false,
                        hideLastValue: 32,
                        axis: Axis.vertical,
                        textStyle:
                            Theme.of(context).textTheme.headline6!.copyWith(
                                  color: const Color(0xff192943),
                                  fontWeight: FontWeight.w400,
                                  fontSize: 14,
                                ),
                        selectedTextStyle:
                            Theme.of(context).textTheme.headline6!.copyWith(
                                  color: const Color(0xff419563),
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                        numbers: yearValues,
                        onSelect: (int value) {
                          if (value < 31) {
                            controller.ageYears = value;
                            controller.updateBirthDateusingAge();
                          }
                        },
                        selected: controller.ageYears ?? 0,
                        trailingTextBuilder: (int value) =>
                            value == 1 ? " year" : " years",
                      ),
                    ),
                    Expanded(
                      child: NumberCarousel(
                        showIcon: false,
                        hideLastValue: 13,
                        axis: Axis.vertical,
                        textStyle:
                            Theme.of(context).textTheme.headline6!.copyWith(
                                  color: const Color(0xff192943),
                                  fontWeight: FontWeight.w400,
                                  fontSize: 14,
                                ),
                        selectedTextStyle:
                            Theme.of(context).textTheme.headline6!.copyWith(
                                  color: const Color(0xff419563),
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                        numbers: monthValues,
                        onSelect: (int value) {
                          // ignore last value, its just placeholder to stop
                          // alignment issue on last value
                          if (value < 12) {
                            controller.ageMonths = value;
                            controller.updateBirthDateusingAge();
                          }
                        },
                        selected: controller.ageMonths ?? 0,
                        trailingTextBuilder: (int value) =>
                            value == 1 ? " month" : " months",
                      ),
                    ),
                  ],
                ),
              ),
              _makeGradient(Alignment.bottomCenter, Alignment.topCenter),
              _makeGradient(Alignment.topCenter, Alignment.bottomCenter),
            ],
          ),
        ),
        _saveButton(context, () {
          controller.saveBirthDate(BirthDateType.age);
        }),
      ],
    );
  }

  Widget _makeGradient(Alignment begin, Alignment end) {
    return IgnorePointer(
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: begin,
            end: end,
            colors: <Color>[
              Colors.white,
              Colors.white.withOpacity(0.0),
            ],
            stops: const [0, 0.4],
            tileMode: TileMode.clamp,
          ),
        ),
      ),
    );
  }

  Widget _saveButton(context, void Function() onPressed) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(5, 5, 5, 10),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 10)),
        onPressed: onPressed,
        child: Container(
          width: double.infinity,
          alignment: Alignment.center,
          child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SAVE'.tr()),
        ),
      ),
    );
  }
}
