import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/state_manager.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/dog_form_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/pet/pet_birthdate_age_tab_widget.dart';

import 'widgets/pet_birthdate_tab_widget.dart';

class PetBirthDateAge extends StatefulWidget {
  const PetBirthDateAge({Key? key}) : super(key: key);

  @override
  _PetBirthDateAgeState createState() => _PetBirthDateAgeState();
}

class _PetBirthDateAgeState extends State<PetBirthDateAge>
    with SingleTickerProviderStateMixin {
  late TabController _controller;
  final DogFormController _dogFormController = DogFormController.of();

  @override
  void initState() {
    super.initState();
    _controller = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(15, 15, 15, 0),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Tools().navigatorPop(),
                  icon: const Icon(Icons.clear),
                ),
                Expanded(
                  child: Obx(
                    () => Text(
                      'APPLICATION_MOBILE_PET_BIRTHDATE_AGE_TITLE'.tr() +
                          _dogFormController.petNameForBirthDateSheet,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                const SizedBox(width: 45),
              ],
            ),
          ),
          const Divider(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 25),
            child: Column(
              children: [
                TabBar(
                  controller: _controller,
                  tabs: <Tab>[
                    Tab(
                      child: Text(
                        'APPLICATION_MOBILE_PET_BIRTHDATE_AGE_TAB'.tr('Age'),
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                    Tab(
                      child: Text(
                        'APPLICATION_MOBILE_PET_BIRTHDATE_DATE_TAB'
                            .tr('Birthdate'),
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 15, right: 15, top: 10),
              child: TabBarView(
                controller: _controller,
                // physics: const NeverScrollableScrollPhysics(),
                children: [
                  PetBirthDateAgeTabWidget(_dogFormController),
                  PetBirthdateTabWidget(_dogFormController),
                ],
              ),
            ),
          ),
          // _saveButton(context, size),
        ],
      ),
    );
  }
}
