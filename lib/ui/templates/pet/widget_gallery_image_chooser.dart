import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';

class WidgetGalleryImage extends StatelessWidget {
  final Pet? pet;

  const WidgetGalleryImage({Key? key, this.pet}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    List<PetActivity> petActivities =
        Data().getFilteredPetActivities(petId: pet?.id, typeFilter: 5);

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: <Widget>[
            TextButton(
              onPressed: () {
                Tools().navigatorPop();
              },
              child: Text('APPLICATION_MOBILE_BUTTON_LABEL_CANCEL'.tr()),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(10),
                child: GridView.builder(
                  // Create a grid with 2 columns. If you change the scrollDirection to
                  // horizontal, this produces 2 rows.
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    ///no.of items in the horizontal axis
                    crossAxisCount: 3,
                    mainAxisSpacing: 10.0,
                    crossAxisSpacing: 10.0,
                    childAspectRatio: 1.0,
                  ),
                  // Generate 100 widgets that display their index in the List.
                  itemBuilder: (BuildContext context, int index) {
                    return GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () async {
                        Tools().navigatorPop(
                          value: petActivities[index].imageUrl ?? petActivities[index].imageId?.toImageUrl(),
                        );
                      },
                      child: Container(
//                    margin: EdgeInsets.all(15.0),
//                    padding: EdgeInsets.all(3.0),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                        ),
                        child: ClipRect(
                          child: CachedNetworkImage(
                            imageUrl: petActivities[index].imageUrl ??
                                petActivities[index].imageId?.toImageUrl() ?? '',
                            fit: BoxFit.cover,
                            placeholder: (context, url) =>
                                const CircularProgressIndicator(),
                            errorWidget: (context, url, error) =>
                                const Icon(Icons.error),
                          ),
                        ),
                      ),
                    );
                  },
                  itemCount: petActivities.length,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
