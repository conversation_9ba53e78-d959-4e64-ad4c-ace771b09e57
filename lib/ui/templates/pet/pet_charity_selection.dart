import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/dog_charity_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/pagination_widget.dart';
import 'package:mybuddy/ui/templates/pet/pet_add_charity.dart';

class PetCharitySelection extends StatelessWidget {
  PetCharitySelection({Key? key}) : super(key: key);

  DogCharityController _charityListController = DogCharityController.of();

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return SizedBox(
      height: size.height * 0.85,
      child: Column(
        children: [
          const SizedBox(height: 15),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15.0),
            child: Row(
              children: [
                IconButton(
                  onPressed: () {
                    Tools().navigatorPop();
                  },
                  icon: const Icon(
                    Icons.clear,
                  ),
                ),
                Expanded(
                  child: Text(
                    'APPLICATION_MOBILE_PET_SHELTER_SELECT'.tr(),
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(
                  width: 45,
                ),
              ],
            ),
          ),
          const Divider(),
          Padding(
            padding: const EdgeInsets.fromLTRB(15, 10, 15, 0),
            child: TextField(
              decoration: InputDecoration(
                prefixIcon: const Icon(
                  Icons.search,
                  color: Color(0xff25323B),
                  size: 24,
                ),
                hintText: 'WOOFTRAX_SEARCH_SHELTER_LABEL'
                    .tr('Search by Name, State, City, or Zip Code'),
              ),
              style: const TextStyle(
                color: Color(0xff25323B),
                fontSize: 13,
                fontWeight: FontWeight.w400,
              ),
              keyboardType: TextInputType.text,
              onChanged: (value) => _charityListController.searchText = value,
            ),
          ),
          const SizedBox(height: 15),
          Expanded(child: _getList(context)),
          const SizedBox(height: 20),
          _addButton(context),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _addButton(context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15.0),
      child: OutlinedButton(
          onPressed: () async {
            Tools().navigatorPop();
            _charityListController.makeShelterEmpty();
            await showModalBottomSheet(
              context: context,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(15.0),
                  topRight: Radius.circular(15.0),
                ),
              ),
              isScrollControlled: true,
              builder: (BuildContext context) {
                return PetAddCharity();
              },
            );
          },
          style: OutlinedButton.styleFrom(
            minimumSize: const Size(double.infinity, 50),
            foregroundColor: Theme.of(context).colorScheme.secondary,
            side: BorderSide(
              color: Theme.of(context).colorScheme.secondary,
              width: 1,
            ),
          ),
          child: const Text(
            "+ Not on list? Enter manually",
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          )),
    );
  }

  Widget _getList(BuildContext context) {
    return PaginationWidget(
        controller: _charityListController,
        child: (context, index) {
          return Obx(() {
            final shelter = _charityListController.dataList[index];
            bool isSelected =
                _charityListController.isShelterSelected(shelter.id);
            return Card(
              margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
              shape: isSelected
                  ? RoundedRectangleBorder(
                      side: BorderSide(
                          color: Theme.of(context).colorScheme.secondary),
                      borderRadius:
                          const BorderRadius.all(Radius.circular(8.0)),
                    )
                  : null,
              child: ListTile(
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8.0)),
                ),
                // dense: true,
                minVerticalPadding: 8.0,
                title: Text(
                  shelter.name,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: const Color(0xff474747),
                      fontWeight: FontWeight.w700,
                      fontSize: 15),
                ),
                subtitle: Text(
                  '${shelter.city}, ${shelter.state}, ${shelter.zipCode ?? ''}',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: const Color(0xff5A5A5A),
                      fontWeight: FontWeight.w400,
                      fontSize: 12),
                ),
                // isThreeLine: true,
                trailing: isSelected
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.secondary,
                      )
                    : null,
                selected: isSelected,
                selectedTileColor: const Color(0xffDEF9ED),
                onTap: () {
                  _charityListController.selectedShelter = shelter;
                  Tools().navigatorPop();
                },
              ),
            );
          });
        });
  }
}
