import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import '../../../blocs/walk_provider.dart';

class PetCardWidget extends StatelessWidget {
  final Pet pet;

  const PetCardWidget({Key? key, required this.pet}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(10.0, 15.0, 10.0, 10.0),
      child: Row(
        children: [
          MBDogAvatar(
            avatarURL: pet.avatarUrl,
            deceased: pet.deceased,
            backgroundColor: Get.find<WalkProvider>().isPetSelected(pet)
                ? const Color(0xff419563)
                : Colors.transparent,
          ),
          const SizedBox(
            width: 15,
          ),
          Expanded(
            child: Container(
              height: 62.0,
              decoration: BoxDecoration(
                color: const Color(0xFFFCFCFC),
                borderRadius: BorderRadius.circular(16.0),
                border: Border.all(
                  color: const Color(0xFFDAE2EB),
                  width: 1.0,
                ),
              ),
              child: Row(
                children: [
                  subDataTile(context, "assets/icon/dog_walk.png", "Walks",
                      "${pet.totalWalks}"),
                  const VerticalDivider(
                    indent: 8,
                    endIndent: 8,
                  ),
                  subDataTile(context, "assets/icon/miles_purple_icon.png", "Miles",
                      pet.totalMileWithTwoDecimal),
                  if (!pet.deceased) ...[
                    const VerticalDivider(
                      indent: 8,
                      endIndent: 8,
                    ),
                    subDataTile(context, "assets/icon/weight_blue.png", "Weight",
                        pet.weight?.toStringAsFixed(2)??"0.0"),
                  ]
                ],
              ),
            ),
          ),
          const SizedBox(
            width: 15,
          ),
        ],
      ),
    );
  }

  Widget subDataTile(
      BuildContext context, String icon, String title, String value) {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(icon, width: 16.0),
              const SizedBox(width: 5.0),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(0xFF939393),
                  fontSize: 12.0,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 5.0),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontSize: 16.0,
              fontWeight: FontWeight.w600,
              color: const Color(0xff474747),
            ),
          ),
        ],
      ),
    );
  }
}
