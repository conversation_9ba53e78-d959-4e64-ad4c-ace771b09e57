import 'package:charts_common/common.dart' as common
    show BasicDateTimeTickFormatterSpec, BasicNumericTickFormatterSpec;
import 'package:charts_flutter/flutter.dart';
import 'package:datetime_picker_formfield/datetime_picker_formfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:intl/intl.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/pet_weight_controller.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/pet/diary/petactivity_page.dart';

// class DataSet {
//   DateTime time;
//   int number;
//
//   DataSet(this.time, this.number);
// }

class PetWeightPage extends StatefulWidget {
  final Pet pet;

  const PetWeightPage({Key? key, required this.pet}) : super(key: key);

  @override
  _PetWeightPageState createState() => _PetWeightPageState();
}

class _PetWeightPageState extends State<PetWeightPage> {
  late PetActivity _pa;

  final _formKey = GlobalKey<FormState>();

  PetWeightController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Obx(() => bodyWidget());
  }

  Widget bodyWidget() {
    DateTime? firstWeightDateTime;
    DateTime? lastWeightDateTime;
    Widget chart;
    if (controller.dataList.isNotEmpty &&
        (firstWeightDateTime = controller.dataList.first.dateStart) != null &&
        (lastWeightDateTime = controller.dataList.last.dateStart) != null) {
      int dayRange =
          lastWeightDateTime!.difference(firstWeightDateTime!).inDays;
      // print('day range $dayRange number ${controller.dataList.length}');
      DateTimeTickProviderSpec provider;
      DateTimeTickFormatterSpec formatter =
          common.BasicDateTimeTickFormatterSpec.fromDateFormat(
              DateFormat(Data().getUserDateFormat(shortyear: true)));
      if (controller.dataList.length == 1) {
        provider = StaticDateTimeTickProviderSpec(
          <TickSpec<DateTime>>[
            TickSpec<DateTime>(
                firstWeightDateTime.subtract(const Duration(days: 1))),
            TickSpec<DateTime>(firstWeightDateTime),
            TickSpec<DateTime>(
                firstWeightDateTime.add(const Duration(days: 1))),
          ],
        );
      } else if (controller.dataList.length == 2 || dayRange < 4) {
        provider = const DateTimeEndPointsTickProviderSpec();
      } else {
        provider = StaticDateTimeTickProviderSpec(
          <TickSpec<DateTime>>[
            TickSpec<DateTime>(firstWeightDateTime),
            TickSpec<DateTime>(
                firstWeightDateTime.add(Duration(days: dayRange ~/ 3))),
            TickSpec<DateTime>(
                firstWeightDateTime.add(Duration(days: 2 * dayRange ~/ 3))),
            TickSpec<DateTime>(lastWeightDateTime),
          ],
        );
      }
      chart = Card(
        child: Padding(
          padding: const EdgeInsets.all(15.0),
          child: TimeSeriesChart(
            [
              Series<PetActivity, DateTime>(
                id: '${'APPLICATION_MOBILE_FIELD_LABEL_UNIT_WEIGHT'.tr()}'
                    ' (${Data().getUnitWeight().shortName})',
                colorFn: (_, __) => MaterialPalette.blue.shadeDefault,
                domainFn: (PetActivity pa, int? index) {
                  if (pa.dateStart == null) {
                    return firstWeightDateTime!
                        .add(Duration(days: index!)); //TODO NS MID control
                  }
                  return pa.dateStart!;
                },
                measureFn: (PetActivity pa, _) {
                  if (pa.weight == null) {
                    return null;
                  }
                  return Data().convertWeightToUserUnit(pa.weight!);
                },
                data: controller.dataList,
              ),
            ],
            animate: false,
            primaryMeasureAxis: NumericAxisSpec(
              tickFormatterSpec: common.BasicNumericTickFormatterSpec(
                _formatWeight,
              ),
              tickProviderSpec: const BasicNumericTickProviderSpec(
                dataIsInWholeNumbers: false,
                zeroBound: false,
//                        desiredTickCount:
              ),
            ),
            behaviors: [SeriesLegend()],
            selectionModels: [
              SelectionModelConfig(changedListener: (SelectionModel model) {
                if (model.hasDatumSelection) {
                  String date = Data().dateTimeToUserDateStr(
                    model.selectedSeries[0].domainFn(
                      model.selectedDatum[0].index,
                    ),
                  );
                  String value = model.selectedSeries[0]
                          .measureFn(
                            model.selectedDatum[0].index,
                          )!
                          .toStringAsPrecision(3) +
                      ' ${Data().getUnitWeight().shortName}';
                  Tools().common.showSimpleDialog(
                    context,
                    '$date - $value',
                    optionalButtons: <Widget>[
                      TextButton(
                        onPressed: () async {
                          await Tools().navigatorPush(
                            PetActivityPage(
                              petActivityIndex: model.selectedDatum[0].index!,
                            ),
                          );
                          Tools().navigatorPop();
                          setState(() {});
                        },
                        child: Text(
//                            'APPLICATION_MOBILE_BUTTON_LABEL_EDIT'.tr()),
                            'APPLICATION_MOBILE_BUTTON_LABEL_SEE'.tr()),
                      ),
//                      TextButton(
//                        child: Text(widget
//                            .'APPLICATION_MOBILE_BUTTON_LABEL_DELETE'.tr()),
//                        onPressed: () async {
//                          MBResponse response =
//                          await Api().removePetActivityRequest(context, controller.dataList[model.selectedDatum[0].index]);
//                          if (response.success) {
//                            Tools().navigatorPop();
//                          }
//                          setState(() {});
//                        },
//                      ),
                    ],
                  );
                }
              })
            ],
            defaultRenderer: LineRendererConfig(includePoints: true),
            domainAxis: DateTimeAxisSpec(
              tickFormatterSpec: formatter,
              //  .fromDateFormat('M/d/y'),
              tickProviderSpec: provider,
            ),
          ),
        ),
      );
    } else {
      chart = Center(
        child: Text(
          'APPLICATION_MOBILE_WEIGHT_CHART_NO_DATA'.tr(),
        ),
      );
    }
    return Scaffold(
      appBar: MBAppBar(
        title: Text(
          '${widget.pet.name}\'s ${'APPLICATION_MOBILE_FIELD_LABEL_UNIT_WEIGHT'.tr()}',
          maxLines: 2,
        ),
      ),
      body: Column(
        children: <Widget>[
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: chart,
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              await _showAndUpdatePetWeight();
              setState(() {});
            },
            child: Text(
              'APPLICATION_MOBILE_BUTTON_LABEL_WEIGHT_ADD'.tr(),
            ),
          ),
          Container(height: 20),
        ],
      ),
//      ),
      floatingActionButton: null,
    );
  }

  Future<bool> _showAndUpdatePetWeight() async {
    _pa = PetActivity();
    _pa.typeId = 2;
    _pa.activityId = 6;
    _pa.pets.add(widget.pet);
    _pa.dateStart = DateTime.now();
    bool? result = await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'APPLICATION_MOBILE_BUTTON_LABEL_WEIGHT_ADD'.tr(),
            maxLines: 2,
          ),
          content: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: TextFormField(
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    decoration: InputDecoration(
                      hintText:
                          'APPLICATION_MOBILE_FIELD_LABEL_UNIT_WEIGHT'.tr(),
                      labelText:
                          'APPLICATION_MOBILE_FIELD_LABEL_UNIT_WEIGHT'.tr(),
                      suffixText: Data().getUnitWeight().shortName,
                    ),
                    initialValue: _pa.weight?.toLocalizedString(),
                    inputFormatters: [LengthLimitingTextInputFormatter(10)],
                    validator: (val) {
                      if (val == null || val.isEmpty || val.trim() == '') {
                        return 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_WEIGHT'
                            .tr();
                      }
                      // TODO max weight restricted less than to 2000 KG ??
                      double maxWeightAllowed = 2000.0.toUserWeight();
                      double? parsed = double.tryParse(val);
                      if (parsed != null && parsed > maxWeightAllowed) {
                        return 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_MAX_WEIGHT'
                            .tr('Enter a weight less than %w',
                                {'%w': maxWeightAllowed.toStringWithUnit()});
                      }
                      return null;
                    },
                    onSaved: (val) {
                      if (val != null) {
                        _pa.weight =
                            Data().convertWeightFromUserUnitStr(val.trim());
                      }
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0, bottom: 16.0),
                  child: DateTimeField(
                    decoration: InputDecoration(
                      hintText: 'APPLICATION_MOBILE_LABEL_WEIGHT_DATE'.tr(),
                      labelText: 'APPLICATION_MOBILE_LABEL_WEIGHT_DATE'.tr(),
                    ),
                    format: DateFormat('yyyy-MM-dd HH:mm'),
                    validator: (val) {
                      if (val == null) {
                        return 'APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_DATE_AND_HOUR'
                            .tr();
                      }
                      if (widget.pet.birthDate != null &&
                          val.isBefore(widget.pet.birthDate!)) {
                        return 'APPLICATION_MOBILE_MESSAGE_INVALID_DATE'.tr();
                      }
                      return null;
                    },
                    initialValue: _pa.dateStart,
                    onSaved: (val) => _pa.dateStart = val,
                    onShowPicker:
                        (BuildContext context, DateTime? currentValue) async {
                      final date = await Tools().common.showDateDialog(
                            context: context,
                            firstDate: widget.pet.birthDate ?? DateTime(1900),
                            initialDate: currentValue ?? DateTime.now(),
                            lastDate: DateTime.now(),
                          );
                      if (date != null) {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: TimeOfDay.fromDateTime(
                              currentValue ?? DateTime.now()),
                        );
                        return DateTimeField.combine(date, time);
                      } else {
                        return currentValue;
                      }
                    },
                  ),
                ),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      if (await _submitForm(context)) {
                        Tools().navigatorPop(value: true);
                      }
                    },
                    child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr()),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );

    return result ?? false;
  }

  Future<bool> _submitForm(BuildContext context) async {
    final FormState? form = _formKey.currentState;

    if (form == null || !form.validate()) {
      Tools().common.showValidationErrorMessage(context);
      return false;
    }
    form.save();

    return controller.submitWeight(context, _pa);
  }

  String _formatWeight(num? weight) {
    if (weight == null) {
      return '';
    }
    return weight.toDouble().toLocalStringAsPrecision(3);
  }
}
