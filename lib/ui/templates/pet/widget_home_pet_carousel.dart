import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/pets_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/ui/components/mb_dynamic_image_widget.dart';

class WidgetHomePetCarousel extends StatelessWidget {
  const WidgetHomePetCarousel({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<PetsBloc>(context);
    return StreamBuilder<List<Pet>>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return Container();
        List<Pet> pets = snapshot.data!;
        List imageIds = [];
        for (Pet pet in pets) {
          imageIds.add(pet);
        }
        if (Data().getFavoriteClinic() != null) {
          imageIds.add(Data().getFavoriteClinic());
        }
        return imageIds.isNotEmpty ? DynamicImageWidget(imageIds) : const SizedBox.expand();
        // return Column(
        //   children: <Widget>[
        //     Visibility(
        //       visible: imageIds.isNotEmpty,
        //       child: SizedBox(
        //         height: 295,
        //         width: MediaQuery.of(context).size.width,
        //         child: DynamicImageWidget(imageIds),
        //       ),
        //     ),
        //     Visibility(
        //       visible: imageIds.isEmpty,
        //       child: Container(
        //         height: 70,
        //       ),
        //     ),
        //     Visibility(
        //       visible: Data().getFavoriteClinic() != null,
        //       child: Padding(
        //         padding: const EdgeInsets.only(left: 8.0, right: 8.0, bottom: 4.0),
        //         child: MBClinicCardWidget(Data().getFavoriteClinic()),
        //       ),
        //     )
        //   ],
        // );
      },
    );
  }
}
