import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/state_manager.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/dog_form_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/pet/pet_birthdate_age_tab_widget.dart';
import 'package:mybuddy/ui/templates/pet/widgets/pet_adoptiondate_widget.dart';

import 'widgets/pet_birthdate_tab_widget.dart';

class PetAdoptionDate extends StatefulWidget {
  const PetAdoptionDate({Key? key}) : super(key: key);

  @override
  _PetAdoptionDateState createState() => _PetAdoptionDateState();
}

class _PetAdoptionDateState extends State<PetAdoptionDate>
    with SingleTickerProviderStateMixin {
  final DogFormController _dogFormController = DogFormController.of();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(15, 15, 15, 0),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Tools().navigatorPop(),
                  icon: const Icon(Icons.clear),
                ),
                Expanded(
                  child: Text(
                    'APPLICATION_MOBILE_PET_ADOPTION_TITLE'.tr(),
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 45),
              ],
            ),
          ),
          const Divider(),
          Expanded(
            child: PetAdoptionDateWidget(_dogFormController),
          ),
        ],
      ),
    );
  }
}
