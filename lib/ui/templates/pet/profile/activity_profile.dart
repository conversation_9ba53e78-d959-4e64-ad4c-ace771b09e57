import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/pet_weight_controller.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/models/workout.dart';
import 'package:mybuddy/ui/components/mb_home_card.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/mb_petactivity_item.dart';
import 'package:mybuddy/ui/components/pagination_widget.dart';
import 'package:mybuddy/ui/templates/pet/diary/pet_diary_page.dart';
import 'package:mybuddy/ui/templates/workout/workout_card_widget.dart';

import '../../../../controllers/pet_workout_controller.dart';

class ActivityWidget extends StatelessWidget {
  final Pet pet;

  /// pet Activity screen
  /// shows the last 10 pet activities
  /// including reminders, appointments, notes,
  /// hospitalization, treatment, workout, health visits
  ActivityWidget({Key? key, required this.pet}) : super(key: key);

  PetWorkoutController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return PaginationWidget(
        controller: controller,
        child: (context, index) {
          return WorkoutCardWidget(workout: controller.dataList[index]);
        });
  }
}
