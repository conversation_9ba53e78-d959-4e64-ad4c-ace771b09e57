import 'package:flutter/material.dart';
import 'package:mybuddy/models/pet.dart';

import '../../../components/detail_table.dart';

class ProfileWidget extends StatelessWidget {
  final Pet pet;

  /// pet Profile screen
  const ProfileWidget({Key? key, required this.pet}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: <PERSON>umn(
        children: [
          DetailTable(
            data: [
              DetailTableRow("Title", pet.name),
              DetailTableRow("Gender", pet.genderName),
              DetailTableRow("Status", pet.deceaseStatus),
              DetailTableRow("Primary Breed", pet.primaryBreedName),
              DetailTableRow("Purebred", pet.pureBreed),
              if (pet.crossed)
                DetailTableRow("Secondary Breed", pet.secondaryBreedName),
              if (!pet.deceased)
                DetailTableRow("Weight", pet.getWeightWithUnit),
              DetailTableRow("Auto-Selected", pet.autoSelected()),
              if (pet.autoSelected() != "Yes")
                DetailTableRow(
                    "Enabled for new activities", pet.enableForNewWalks()),
              DetailTableRow("Birthday", pet.birthDay),
              DetailTableRow(
                "Adoption date / date dog joined family",
                pet.adoptionDay,
              ),
              DetailTableRow("Adopted from", pet.adoptedFrom),
            ],
          ),
          if (pet.otherThanShelter != null && pet.otherThanShelter!.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(
                bottom: 15,
                left: 15,
                right: 15,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
              width: double.infinity,
              decoration: BoxDecoration(
                color: const Color(0xFFFCFCFC),
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(
                  color: const Color(0xFFDAE2EB),
                  width: 1.0,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Note',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Color(0xff474747),
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    pet.otherThanShelter!,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: Color(0xff474747),
                    ),
                  ),
                ],
              ),
            )
        ],
      ),
    );
  }
}
