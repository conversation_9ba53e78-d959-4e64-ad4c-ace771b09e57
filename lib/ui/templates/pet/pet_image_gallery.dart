import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';

import 'package:mybuddy/Api/mb_api_pet_activity.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/pet_gallery_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/pet/widget_pet_image_list.dart';

import '../../../Api/mb_response.dart';

class PetImageGallery extends StatefulWidget {
  final Pet pet;

  PetImageGallery({Key? key, required this.pet}) : super(key: key);

  @override
  State<PetImageGallery> createState() => _PetImageGalleryState();
}

class _PetImageGalleryState extends State<PetImageGallery> {
  late PetGalleryController controller;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    controller = Get.put(PetGalleryController(petId: widget.pet.id));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(
          widget.pet.name + '\'s Images',
          maxLines: 2,
        ),
        // actions: <Widget>[
        //   MBHamburger(),
        // ],
      ),
      // endDrawer: MBDrawer(),
      floatingActionButton: Builder(
        builder: (context) => FloatingActionButton(
          heroTag: 'add',
          onPressed: () {
            importImage(context);
          },
          child: const Icon(Icons.add_a_photo),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: WidgetImagePetList(pet: widget.pet),
      ),
    );
  }

  Future<void> importImage(BuildContext context) async {
    File? image = await Tools().image.pickAndCropAvatar(context,titleOnCropper: "Crop your dog picture");
    if (image != null) {
      MBResponse response = await MbApiPetActivity()
          .addImageActivityRequest(context, widget.pet, image);
      if (response.success) {
        PetActivity pa = PetActivity.fromJson(response.body["petActivity"]);
        controller.addImage(pa);
      }
    } else {
      Tools.debugPrint('image import abort.');
    }
  }
}
