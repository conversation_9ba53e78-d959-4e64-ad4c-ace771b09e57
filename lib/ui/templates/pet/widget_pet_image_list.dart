import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/pet_gallery_controller.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/ui/templates/pet/pet_image_view.dart';

import '../../../models/pet.dart';
import '../../components/pagination_widget.dart';

class WidgetImagePetList extends StatelessWidget {
  final Pet pet;

  WidgetImagePetList({Key? key, required this.pet}) : super(key: key);

  PetGalleryController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return PaginationWidget(
      gridView: true,
        controller: controller,
        child: (context, index) {
          return GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              Tools().navigatorPush(PetImageView(
                  petActivity: controller.dataList[index], title: pet.name));
            },
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
              ),
              child: ClipRect(
                child: CachedNetworkImage(
                  imageUrl: controller.dataList[index].imageUrl ?? '',
                  fit: BoxFit.cover,
                  placeholder: (context, url) =>
                      const Center(child: CircularProgressIndicator()),
                  errorWidget: (context, url, error) => const Icon(Icons.error),
                ),
              ),
            ),
          );
        });
  }
}
