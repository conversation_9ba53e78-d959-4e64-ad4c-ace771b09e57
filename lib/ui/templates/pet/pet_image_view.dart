import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';

import 'package:mybuddy/Api/mb_api_pet_activity.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:photo_view/photo_view.dart';

import '../../../controllers/pet_gallery_controller.dart';

class PetImageView extends StatelessWidget {
  final PetActivity petActivity;
  final String title;

  PetImageView({Key? key, required this.petActivity, required this.title})
      : super(key: key);

  PetGalleryController controller = Get.find();

//'${_pageController == null ? 1 : _pageController.page}/${petActivities.length}'
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(title, maxLines: 2),
        // actions: <Widget>[
        //   MBHamburger(),
        // ],
      ),
      // endDrawer: MBDrawer(),
      floatingActionButton: Builder(
        builder: (context) => FloatingActionButton(
          backgroundColor: Theme.of(context).colorScheme.error,
          heroTag: 'delete',
          onPressed: () async {
            await Tools().common.showValidDialog(
              context,
              onValid: () async {
                MBResponse response = await MbApiPetActivity()
                    .removePetActivityRequest(context, petActivity);
                if (response.success) {
                  controller.removeImage(petActivity);
                  Tools().navigatorPop();
                }
              },
            );
          },
          child: const Icon(Icons.delete),
        ),
      ),
      body: PhotoView(
        imageProvider: CachedNetworkImageProvider(
          petActivity.imageUrl ?? '',
        ),
      ),
    );
  }
}
