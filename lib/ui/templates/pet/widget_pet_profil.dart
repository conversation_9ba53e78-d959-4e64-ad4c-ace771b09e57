import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:mybuddy/Api/mb_api_pet.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/pet_bloc.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/ui/components/mb_base_home_screen.dart';
// import 'package:mybuddy/ui/templates/pet/diary/add_note.dart';

import '../../../blocs/walk_provider.dart';
import '../../../pages/banner_widget.dart';
import '../../components/mb_stackable_avatar.dart';
import 'profile/activity_profile.dart';
import 'profile/grid_items_profile.dart';
import 'widget_pet_tile.dart';

// enum PetProfileSection {
//   Weight,
//   BirthDate,
//   Diary,
//   Photos,
//   HealthBook,
//   Prevention,
//   HealthPlan,
//   Insurance,
//   Edit,
//   Delete
// }

class WidgetPetProfile extends StatelessWidget {
  const WidgetPetProfile({Key? key}) : super(key: key);

  void menuBottomSheet(Pet pet, BuildContext context) {
    List<String> options = [
      'APPLICATION_MOBILE_BUTTON_LABEL_EDIT'.tr("Edit"),
      'APPLICATION_MOBILE_BUTTON_LABEL_DELETE'.tr("Delete"),
      // 'APPLICATION_MOBILE_BUTTON_LABEL_ACTIVITY_CREATE_NOTE'.tr("Create a Note")
    ];

    showModalBottomSheet(
        context: context,
        builder: (BuildContext bc) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                height: 10,
              ),
              ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (ctx, index) {
                    String text = options[index];
                    return TextButton(
                        onPressed: () async {
                          Tools().navigatorPop();
                          if (text == "Edit") {
                            await Tools().pet.addPetAction(context, pet);
                          } else if (text == "Delete") {
                            Tools().common.showCustomDialog(
                              prefixContent: [
                                MBDogAvatar(
                                  avatarURL: pet.avatarUrl,
                                  deceased: pet.deceased,
                                ),
                                const SizedBox(height: 8,),
                              ],
                              title: "Are you sure you want to delete ${pet.name} ?",
                              description: "Deleting this pet will not erase its past activities on Wooftrax.",
                              buttonColor: const Color(0xffE14646),
                              validTitle: 'APPLICATION_MOBILE_BUTTON_LABEL_DELETE'
                                  .tr("Delete"),
                              rejectTitle: 'APPLICATION_MOBILE_BUTTON_LABEL_CANCEL'
                                  .tr("Cancel"),
                              onValid: () async {
                                MBResponse response = await MbApiPet().deletePet(context,pet);
                                if(response.success){
                                  Tools().navigatorPop();
                                  Tools().common.showMessage(context, response.body["message"]);
                                  WalkProvider.of(Get.context!).refreshDogs();
                                }
                              },
                            );
                          }
                          // else {
                          //   Tools().navigatorPush(AddNoteForm(
                          //     title:
                          //         'APPLICATION_MOBILE_BUTTON_LABEL_ACTIVITY_CREATE_NOTE'
                          //             .tr("Create a Note"),
                          //     pet: pet,
                          //   ));
                          // }
                        },
                        child: Center(
                            child: Text(
                          text,
                          style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: text == "Delete"
                                  ? const Color(0xffE14646)
                                  : Theme.of(context).colorScheme.secondary),
                        )));
                  },
                  separatorBuilder: (ctx, index) {
                    return const Divider();
                  },
                  itemCount: options.length),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    PetBloc bloc = BlocProvider.of<PetBloc>(context);
    return StreamBuilder<Pet?>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return Container();

        Pet pet = snapshot.data!;
        Tools().pet.getHealthBookData(pet);
        // String birthText = '';
        // switch (SettingsDelegate().get().petProfileBirthDatePreferredMode) {
        //   case 0:
        //     birthText = Data().formatDurationFromNowTo(pet.nextBirthday(),
        //         limitTodDay: true,
        //         prefix: 'APPLICATION_MOBILE_WORD_IN_DURATION'.tr());
        //     break;
        //   case 1:
        //     birthText = Data().dateTimeToUserDateStr(pet.birthDate);
        //     break;
        //   case 2:
        //   default:
        //     birthText = Data()
        //         .formatDurationToNowFrom(pet.birthDate, maxDurationType: 2);
        // }

        List<Widget> actions = <Widget>[];
        actions.add(IconButton(
          onPressed: () {
            menuBottomSheet(pet, context);
          },
          icon: const Icon(
            Icons.more_vert_rounded,
            color: Color(0xff162941),
          ),
        ));

//TODO LOW implement profile modification function of image ratio
        return DefaultTabController(
          length: 2,
          child: Scaffold(
            appBar: AppBar(
              actions: actions,
              leading: BackButton(
                color: Theme.of(context).primaryColor,
                onPressed: () => Tools().navigatorPop(),
              ),
              title: Text(
                pet.name,
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontSize: 22, fontWeight: FontWeight.w700),
              ),
            ),
            body: Column(
              children: [
                PetCardWidget(pet: pet),
                const TabBar(
                  tabs: [
                    Tab(text: 'Profile'),
                    Tab(text: 'Activity'),
                    // Tab(text: 'Statistics'),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    children: <Widget>[
                      /// profile section
                      ProfileWidget(pet: pet),

                      /// activity section
                      ActivityWidget(pet: pet),
                    ],
                  ),
                ),
                const BannerAdWidget(),
                const SizedBox(height: 20.0),
              ],
            ),
          ),
        );
      },
    );
  }
}
