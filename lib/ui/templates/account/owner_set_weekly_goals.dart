import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/set_weekly_goals_controller.dart';
import 'package:mybuddy/controllers/weekly_stats_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/decimal_number_picker.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';

import 'owner_set_daily_goals.dart';

class SetWeeklyGoalsScreen extends StatelessWidget {
  SetWeeklyGoalsScreen({Key? key, this.fromSetting = true}) : super(key: key);
  final bool fromSetting;

  SetWeeklyGoalsController controller = SetWeeklyGoalsController.of();
  WeeklyStatsController statsController = WeeklyStatsController.of();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xfffbfbfb),
      appBar: MBAppBar(
        iconTheme:
            IconTheme.of(context).copyWith(color: const Color(0xff162941)),
        title: Text(
          'APPLICATION_MOBILE_PROFILE_TITLE_WEEKLY_GOALS'.tr(),
          style: Theme.of(context).textTheme.headline6,
        ),
      ),
      body: SafeArea(
        child: Column(children: [
          const SizedBox(height: 20),
          if (!fromSetting)
            Padding(
              padding: const EdgeInsets.fromLTRB(50, 0, 50, 20),
              child: Text(
                  'APPLICATION_MOBILE_PROFILE_DESCRIPTION_WEEKLY_GOALS'.tr(),
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      fontSize: 17, fontWeight: FontWeight.w500)),
            ),
          _walkCard(context),
          const SizedBox(height: 20),
          _distanceCard(context)
        ]),
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: double.infinity,
            margin: EdgeInsets.symmetric(
                vertical: fromSetting ? 25.0 : 15.0, horizontal: 15),
            child: Obx(
              () => ElevatedButton(
                onPressed: controller.loading
                    ? null
                    : () async {
                        await controller.updateUser();
                        statsController.updateThisWeeksGoals();
                        statsController.refreshHomeStats();
                      },
                style: ElevatedButton.styleFrom(elevation: 0),
                child: Text(
                  'APPLICATION_MOBILE_PROFILE_SAVE_GOALS'.tr(),
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ),
          if (!fromSetting)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.fromLTRB(15, 0, 15, 25),
              child: Obx(
                () => OutlinedButton(
                    onPressed: controller.loading
                        ? null
                        : () {
                            Tools().navigatorPop();
                          },
                    style: OutlinedButton.styleFrom(
                      fixedSize: const Size(double.infinity, 45),
                      foregroundColor: Theme.of(context).colorScheme.secondary,
                      side: BorderSide(
                        color: Theme.of(context).colorScheme.secondary,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      "APPLICATION_MOBILE_PROFILE_GOALS_SET_LATER".tr(),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    )),
              ),
            )
        ],
      ),
    );
  }

  Widget _walkCard(BuildContext context) {
    return Card(
        child: Container(
      padding: const EdgeInsets.only(top: 20.0, left: 20.0, right: 20.0),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(
          'APPLICATION_MOBILE_PROFILE_WALKS'.tr(),
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: const Color(0xff282828),
              fontSize: 16,
              fontWeight: FontWeight.w700),
        ),
        const SizedBox(height: 20),
        Obx(
          () => NumberCarousel(
              numbers: controller.walks,
              onSelect: (int number) {
                controller.walkSelectedValue = number;
              },
              selected: controller.walkSelectedValue),
        ),
      ]),
    ));
  }

  Widget _distanceCard(BuildContext context) {
    return Card(
        child: Container(
      padding: const EdgeInsets.only(top: 20.0, left: 20.0, right: 20.0),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(
          'APPLICATION_MOBILE_FIELD_LABEL_MILES'.tr(),
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: const Color(0xff282828),
              fontSize: 16,
              fontWeight: FontWeight.w700),
        ),
        const SizedBox(height: 20),
        Obx(
          () => NumberCarousel(
            numbers: controller.distanceList,
            onSelect: (int number) {
              controller.distanceSelectedValue = number;
            },
            selected: controller.distanceSelectedValue,
          ),
        )
      ]),
    ));
  }
}

/// This class is a wrapper on top of Carousel to make it number specific
class DecimalNumberCarousel extends StatelessWidget {
  final List<double> numbers;
  final Function(double) onSelect;
  final double selected;
  final int decimalPlace;

  const DecimalNumberCarousel({
    Key? key,
    required this.numbers,
    required this.onSelect,
    required this.selected,
    this.decimalPlace = 5,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: DecimalNumberPicker(
        itemCount: numbers.length - 3,
        value: selected,
        minValue: numbers[0],
        maxValue: numbers[numbers.length - 1],
        itemHeight: 50,
        itemWidth: 70,
        axis: Axis.horizontal,
        selectedTextStyle: Theme.of(context).textTheme.headline6!.copyWith(
            color: const Color(0xff3B3C3F),
            fontWeight: FontWeight.w500,
            fontSize: 30),
        textStyle: Theme.of(context).textTheme.headline6!.copyWith(
            color: const Color(0xffC5C3C3),
            fontWeight: FontWeight.w400,
            fontSize: 22),
        onChanged: (number) {
          onSelect(number);
        },
      ),
    );
  }
}
