import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/class/constants.dart' show wooftraxPrivacyPolicyUrl;
import 'package:mybuddy/ui/templates/about_page.dart';
import 'package:mybuddy/ui/templates/account/settings/settings_item_widget.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/parameters_page.dart';

class AppSection extends StatelessWidget {
  const AppSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10.0),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10.0),
            alignment: Alignment.topLeft,
            child: Text(
              'APPLICATION',
              style: Theme.of(context).textTheme.headline6,
            ),
          ),
          const Divider(),
          if(AppConfig.of(context).isMyBuddy)
            SettingsItem(
              title: 'APPLICATION_MOBILE_LABEL_SETTINGS'.tr(),
              action: () => Tools().navigatorPush(const ParametersPage()),
            ),
          SettingsItem(
            title: 'APPLICATION_MOBILE_TITLE_ABOUT'.tr(),
            action: () {
                if (AppConfig.of(context).isWoofTrax) {
                  CommonTools().launchURL(wooftraxPrivacyPolicyUrl, context, null, true);
                } else {
                  Tools().navigatorPush(const AboutPage());
                }
            },
          ),
        ],
      ),
    );
  }
}
