import 'package:flutter/material.dart';

class SettingsItem extends StatelessWidget {
  final String title;
  final void Function()? action;

  const SettingsItem({Key? key, required this.title, this.action})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: action,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 15.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodyText1,
            ),
            Icon(
              Icons.arrow_forward_ios_outlined,
              size: 20.0,
              color: Theme.of(context).disabledColor,
            ),
          ],
        ),
      ),
    );
  }
}
