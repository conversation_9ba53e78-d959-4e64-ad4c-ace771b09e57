import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';

class FooterSection extends StatelessWidget {
  const FooterSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10.0),
      child: Column(
        children: [
          Text(
            'Version ' + Tools().appVersion,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.labelMedium!.copyWith(
                  color: const Color.fromRGBO(127, 127, 127, 1.0),
                  fontSize: 13.0,
                ),
          ),
        ],
      ),
    );
  }
}
