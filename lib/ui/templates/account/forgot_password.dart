import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/account/reset_password.dart';

import '../../../Api/mb_response.dart';
import '../../../Tools/tools.dart';
import '../../../Tools/tools_dynamic_links.dart';
import '../../components/timer_button.dart';

class ForgotPassword extends StatefulWidget {
  const ForgotPassword({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ForgotPasswordState();
}

class _ForgotPasswordState extends State<ForgotPassword> {
  GlobalKey<dynamic> timerButtonKey = GlobalKey();
  final TextEditingController _email = TextEditingController();
  bool _emailReadOnly = false,
      _isAllowedForNextAttempt = true,
      _buttonVisible = true;
  int _attempts = 0;

  @override
  Widget build(BuildContext ctx) {
    return Scaffold(
      backgroundColor: const Color(0xFFFBFBFB),
      appBar: AppBar(
        elevation: 1,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(15.0),
          ),
        ),
        title: Text(
          'APPLICATION_MOBILE_TITLE_FORGOT_PASSWORD'.tr(),
          style: Theme.of(ctx).textTheme.headline6,
        ),
      ),
      bottomNavigationBar: !_buttonVisible
          ? null
          : _emailReadOnly
              ? TimerButton(
                  key: timerButtonKey,
                  onPressed: () async {
                    MBResponse response = await MbApiOwner().resendRequest(
                        context, _email.text, LINK_TYPE_FORGOT_PASSWORD,
                        successMsg: "Email Sent!");
                    if (response.success) {
                      timerButtonKey.currentState.startTimer();
                      setState(() {
                        _attempts =
                            response.body["resendLinkValidation"]["attempts"];
                        _isAllowedForNextAttempt =
                            response.body["resendLinkValidation"]
                                ["isAllowedForNextAttempt"];
                      });
                    }
                  },
                  timerCompleted: (val) {
                    if (val) {
                      setState(() {
                        _buttonVisible =
                            _emailReadOnly && _isAllowedForNextAttempt;
                      });
                    }
                  },
                )
              : Container(
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(
                      vertical: 20.0, horizontal: 15),
                  child: ElevatedButton(
                    onPressed: _email.text.isEmpty
                        ? null
                        : () async {
                            if (_email.text.isEmail()) {
                              if (_emailReadOnly == false) {
                                MBResponse response = await MbApiOwner()
                                    .forgotPasswordRequest(
                                        context, _email.text);
                                if (response.success) {
                                  setState(() {
                                    _emailReadOnly = true;
                                    _attempts =
                                        response.body["resendLinkValidation"]
                                            ["attempts"];
                                    _isAllowedForNextAttempt =
                                        response.body["resendLinkValidation"]
                                            ["isAllowedForNextAttempt"];
                                  });
                                }
                              }
                            }
                          },
                    child: Text(
                      'APPLICATION_MOBILE_TITLE_RESET_PASSWORD'.tr(),
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ),
      body: Builder(
        builder: (context) {
          return SingleChildScrollView(
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.all(10.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 15.0),
                  if (_emailReadOnly) ...[
                    Text(
                      "APPLICATION_MOBILE_TEXT_RESET_EMAIL_SENT".tr(),
                      style: const TextStyle(color: Colors.black),
                    ),
                    const SizedBox(height: 25.0),
                  ],
                  TextFormField(
                    decoration: InputDecoration(
                        hintText: 'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'
                                .tr() +
                            '*',
                        labelText: _emailReadOnly
                            ? null
                            : 'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'
                                    .tr() +
                                '*',
                        fillColor: Colors.grey[200],
                        filled: _emailReadOnly),
                    keyboardType: TextInputType.emailAddress,
                    controller: _email,
                    inputFormatters: [LengthLimitingTextInputFormatter(256)],
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    onChanged: (val) {
                      setState(() {});
                    },
                    validator: (val) => val == null ||
                            val.isEmpty ||
                            !val.isEmail()
                        ? 'APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_MAIL'.tr()
                        : null,
                    readOnly: _emailReadOnly,
                  ),
                  if (!_buttonVisible) ...[
                    const SizedBox(height: 35.0),
                    Text("APPLICATION_MOBILE_TEXT_RESET_LIMIT_ERROR".tr(),
                        style: const TextStyle(
                          fontWeight: FontWeight.w700,
                        ))
                  ]
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
