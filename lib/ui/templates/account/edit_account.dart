import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/owner_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/account/widget_owner_avatar_with_choice.dart';

class EditAccount extends StatefulWidget {
  const EditAccount({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _EditAccountState();
}

class _EditAccountState extends State<EditAccount> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  late Owner _owner;
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xfffbfbfb),
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_PROFILE_ITEM_EDIT_PROFILE'.tr(),
          style: Theme.of(context)
              .textTheme
              .headline5
              ?.copyWith(fontSize: 20, fontWeight: FontWeight.w700),
        ),
      ),
      bottomNavigationBar: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 15),
        child: Builder(
          builder: (context) => ElevatedButton(
            onPressed: isLoading ? null : () => _submitForm(context),
            child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SAVE_PROFILE'.tr()),
          ),
        ),
      ),
      body: formBody(),
    );
  }

  Widget formBody() {
    return SafeArea(
      top: true,
      bottom: false,
      child: Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.disabled,
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.only(top: 15.0, bottom: 25.0),
                child: BlocProvider<OwnerBloc>(
                  bloc: OwnerBloc(),
                  child: const OwnerAvatarWithChoice(),
                ),
              ),
              TextFormField(
                decoration: InputDecoration(
                  hintText: 'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'.tr(),
                  labelText:
                      'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'.tr(),
                ),
                initialValue: _owner.email,
                enabled: false,
              ),
              const SizedBox(height: 20.0),
              TextFormField(
                keyboardType: TextInputType.text,
                decoration: InputDecoration(
                  hintText:
                      'APPLICATION_MOBILE_FIELD_LABEL_USER_FIRSTNAME'.tr() +
                          '*',
                  labelText:
                      'APPLICATION_MOBILE_FIELD_LABEL_USER_FIRSTNAME'.tr() +
                          '*',
                ),
                initialValue: _owner.firstName,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(256),
                  FilteringTextInputFormatter.allow(
                    RegExp('[a-zA-Z\\ \\-\\.]'),
                  ),
                ],
                validator: (val) => val == null ||
                        val.isEmpty ||
                        val.trim() == ''
                    ? 'APPLICATION_MOBILE_MESSAGE_USER_FORM_FIRSTNAME_REQUIRED'
                        .tr()
                    : null,
                onSaved: (val) => _owner.firstName = val!.trim(),
              ),
              const SizedBox(height: 20.0),
              TextFormField(
                keyboardType: TextInputType.text,
                decoration: InputDecoration(
                  hintText:
                      'APPLICATION_MOBILE_FIELD_LABEL_USER_LAST_name'.tr() +
                          '*',
                  labelText:
                      'APPLICATION_MOBILE_FIELD_LABEL_USER_LAST_name'.tr() +
                          '*',
                ),
                initialValue: _owner.lastName,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(256),
                  FilteringTextInputFormatter.allow(
                      RegExp('[a-zA-Z\\ \\-\\.]')),
                ],
                validator: (val) => val == null ||
                        val.isEmpty ||
                        val.trim() == ''
                    ? 'APPLICATION_MOBILE_MESSAGE_USER_FORM_LASTNAME_REQUIRED'
                        .tr()
                    : null,
                onSaved: (val) => _owner.lastName = val!.trim(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _owner = Data().get().owner.copy();
  }

  Future<void> _submitForm(BuildContext context) async {
    final FormState? form = _formKey.currentState;

    if (form == null || !form.validate()) {
      Tools().common.showValidationErrorMessage(context);
      return;
    }
    form.save();

    setState(() {
      isLoading = true;
    });

    Tools.debugPrint('Form register');
    Tools.debugPrint('OWNER: ${_owner.toString()}');
    Tools.debugPrint('========================================');
    Tools.debugPrint('Submitting to back end...');

    MBResponse response =
        await MbApiOwner().updateUserRequest(context, owner: _owner);

    if (response.success) {
      Tools()
          .common
          .showMessage(context, 'APPLICATION_MOBILE_PROFILE_UPDATED'.tr());
      Tools().navigatorPop(value: true);
    } else {
      setState(() {
        isLoading = true;
      });
    }
  }
}
