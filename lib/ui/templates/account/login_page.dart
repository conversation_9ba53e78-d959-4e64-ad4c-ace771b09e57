import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/mb_divider_or.dart';
import 'package:mybuddy/ui/templates/account/email_verification.dart';
import 'package:mybuddy/ui/templates/account/forgot_password.dart';

import '../../../controllers/social_login_controller.dart';
import '../../components/social_error_message.dart';
import 'auth_login_button.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final GlobalKey<FormState> _loginFormKey = GlobalKey<FormState>();
  final _loginController = TextEditingController();
  final _passwordController = TextEditingController();
  bool isLoading = false;

  late FocusNode _emailFocus;

  late FocusNode _pwdFocus;
  late bool _passwordVisible;

  final SocialLoginController _socialLoginController =
      SocialLoginController.of();

  @override
  void dispose() {
    _loginController.dispose();
    _passwordController.dispose();

    _emailFocus.dispose();
    _pwdFocus.dispose();

    super.dispose();
  }

  @override
  void initState() {
    _passwordVisible = false;
    _emailFocus = FocusNode();
    _pwdFocus = FocusNode();
    _loginController.text = SettingsDelegate().prefs.getString('login') ?? '';
    _passwordController.text = '';

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFBFBFB),
      appBar: AppBar(
        elevation: 1,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(15.0),
          ),
        ),
        leading: BackButton(
          onPressed: () {
            _socialLoginController.hideErrorAndPerformSpecificAction(() {
              Tools().navigatorPop();
            });
          },
        ),
        title: Text(
          'Sign In',
          style: Theme.of(context).textTheme.headline6,
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(
          horizontal: 15.0,
        ),
        child: Form(
          key: _loginFormKey,
          child: AutofillGroup(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: 25.0,
                    top: 35.0,
                  ),
                  child: TextFormField(
                    controller: _loginController,
                    focusNode: _emailFocus,
                    textCapitalization: TextCapitalization.none,
                    keyboardType: TextInputType.emailAddress,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    autofillHints: const [AutofillHints.username],
                    textInputAction: TextInputAction.next,
                    maxLines: 1,
                    validator: (val) {
                      if (val == null || val.trim() == '' || !val.isEmail()) {
                        return 'APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_MAIL'
                            .tr();
                      }
                      return null;
                    },
                    decoration: InputDecoration(
                      hintText:
                          'APPLICATION_MOBILE_FIELD_LABEL_USER_EMAIL'.tr(),
                      labelText:
                          'APPLICATION_MOBILE_FIELD_LABEL_USER_EMAIL'.tr() +
                              '*',
                    ),
                    onFieldSubmitted: (_) {
                      _emailFocus.unfocus();
                      FocusScope.of(context).requestFocus(_pwdFocus);
                    },
                  ),
                ),
                TextFormField(
                  controller: _passwordController,
                  maxLines: 1,
                  focusNode: _pwdFocus,
                  autofillHints: const [AutofillHints.password],
                  textInputAction: TextInputAction.done,
                  obscureText: !_passwordVisible,
                  decoration: InputDecoration(
                    hintText:
                        'APPLICATION_MOBILE_FIELD_LABEL_USER_PASSWORD'.tr(),
                    labelText:
                        'APPLICATION_MOBILE_FIELD_LABEL_USER_PASSWORD'.tr() +
                            '*',
                    suffixIcon: IconButton(
                      icon: Icon(
                        // Based on passwordVisible state choose the icon
                        _passwordVisible
                            ? FontAwesomeIcons.eye
                            : FontAwesomeIcons.eyeSlash,
                        color: Theme.of(context).primaryColor,
                      ),
                      onPressed: () {
                        // Update the state i.e. toogle the state of passwordVisible variable
                        setState(() {
                          _passwordVisible = !_passwordVisible;
                        });
                      },
                    ),
                  ),
                  validator: (val) {
                    if (val == null || val.trim() == '' || val.length < 6) {
                      return 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MIN_LENGTH'
                          .tr();
                    }
                    return null;
                  },
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 20, bottom: 35),
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      _socialLoginController.hideErrorAndPerformSpecificAction(
                          () => Tools().navigatorPush(const ForgotPassword()));
                    },
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        'APPLICATION_MOBILE_BUTTON_LABEL_FORGOT_PASSWORD'.tr(),
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.secondary),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: double.infinity,
                  child: Builder(
                    builder: (context) => ElevatedButton(
                      onPressed: isLoading
                          ? null
                          : () {
                              _socialLoginController.hideErrorAndPerformSpecificAction(
                                  () async => await login(
                                        context,
                                        _loginController.text,
                                        _passwordController.text,
                                      ));
                            },
                      child: Text(
                        'APPLICATION_MOBILE_MESSAGE_SIGN_IN'.tr(),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 15.0),
                const DividerOrWidget(),
                Obx(() {
                  if (_socialLoginController.isError) {
                    return Column(
                      children: [
                        const SizedBox(height: 10),
                        SocialErrorMessage(
                          message: _socialLoginController.errorMessage,
                          icon: _socialLoginController.errorIcon,
                        ),
                      ],
                    );
                  } else {
                    return const SizedBox.shrink();
                  }
                }),
                const SizedBox(height: 10),
                AuthLoginButton(
                  label: 'APPLICATION_MOBILE_BUTTON_LABEL_CONTINUE_WITH_APPLE'
                      .tr(),
                  icon: const Icon(
                    FontAwesomeIcons.apple,
                    size: 20,
                  ),
                  onPressed: _socialLoginController.appleLogin,
                ),
                const SizedBox(height: 10),
                AuthLoginButton(
                  label: 'APPLICATION_MOBILE_BUTTON_LABEL_CONTINUE_WITH_GOOGLE'
                      .tr(),
                  icon: SvgPicture.asset(
                    'assets/icon/google_logo.svg',
                    width: 17,
                    height: 17,
                  ),
                  onPressed: _socialLoginController.googleLogin,
                ),
                const SizedBox(height: 10),
                AuthLoginButton(
                  label:
                      'APPLICATION_MOBILE_BUTTON_LABEL_CONTINUE_WITH_FACEBOOK'
                          .tr(),
                  icon: SvgPicture.asset(
                    'assets/icon/fb_logo.svg',
                  ),
                  onPressed: _socialLoginController.fbLogin,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> login(
      BuildContext context, String login, String password) async {
    setState(() {
      isLoading = true;
    });
    FormState? form = _loginFormKey.currentState;
    if (form == null || !form.validate()) {
      Tools().common.showValidationErrorMessage(context);
      setState(() {
        isLoading = false;
      });
      return;
    }
    form.save();
    FocusManager.instance.primaryFocus?.unfocus();

    MBResponse response = await MbApiOwner()
        .loginRequest(context, login.trim(), password, refresh: false);

    setState(() {
      isLoading = false;
    });

    if (response.success) {
      await Tools().navigatorToHome();
    } else {
      String? errorCode = response.body['errorCode'];
      if (errorCode == 'ERROR_EMAIL_NOT_VERIFIED') {
        // Show pending verification screen
        Tools().navigatorPop();
        Tools().navigatorPush(
            EmailVerification(email: login.trim(), type: EmailVerificationType.login,));
      }
    }
  }
}
