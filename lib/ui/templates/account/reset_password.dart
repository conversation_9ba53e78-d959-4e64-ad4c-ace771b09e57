import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/account/process_finished_screens.dart';

class ResetPassword extends StatefulWidget {
  ResetPassword({Key? key, required this.email, required this.code})
      : super(key: key);
  final String email, code;

  @override
  State<StatefulWidget> createState() => _ResetPasswordState();
}

class _ResetPasswordState extends State<ResetPassword> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController email = TextEditingController();
  TextEditingController password = TextEditingController();
  TextEditingController cpassword = TextEditingController();
  bool hidePassword = true, hideCPassword = true;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    setState(() {
      email.text = widget.email;
    });
  }

  void resetPassword() {
    FormState? form = _formKey.currentState;
    if (form == null || !form.validate()) {
      return;
    }
    form.save();
    if (password.text == cpassword.text) {
      MbApiOwner().resetPasswordRequest(context, {
        "email": widget.email,
        "code": widget.code,
        "password": password.text,
        "cpassword": cpassword.text
      }).then((response) {
        if (response.success) {
          Tools().navigatorReplacement(const PasswordSaved());
        }
      });
    }
  }

  @override
  Widget build(BuildContext ctx) {
    return Scaffold(
      backgroundColor: const Color(0xFFFBFBFB),
      appBar: AppBar(
        elevation: 1,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(15.0),
          ),
        ),
        leading: const SizedBox.shrink(),
        title: Text(
          'APPLICATION_MOBILE_TITLE_RESET_PASSWORD'.tr(),
          style: Theme.of(ctx).textTheme.headline6,
        ),
      ),
      bottomNavigationBar: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 15),
        child: ElevatedButton(
          onPressed: resetPassword,
          child: Text(
            'APPLICATION_MOBILE_LABEL_SAVE_PASSWORD'.tr(),
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ),
      body: Builder(
        builder: (context) {
          return SingleChildScrollView(
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.all(10.0),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    const SizedBox(height: 15.0),
                    TextFormField(
                      decoration: InputDecoration(
                          hintText:
                              'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'
                                      .tr() +
                                  '*',
                          labelText:
                              'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'
                                      .tr() +
                                  '*',
                          fillColor: Colors.grey[200],
                          filled: true),
                      keyboardType: TextInputType.emailAddress,
                      controller: email,
                      inputFormatters: [LengthLimitingTextInputFormatter(256)],
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      validator: (val) => val == null ||
                              val.isEmpty ||
                              !val.isEmail()
                          ? 'APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_MAIL'.tr()
                          : null,
                      readOnly: true,
                    ),
                    SizedBox(
                      height: 25,
                    ),
                    TextFormField(
                      decoration: InputDecoration(
                        hintText: 'APPLICATION_MOBILE_FIELD_LABEL_USER_PASSWORD'
                                .tr() +
                            '*',
                        labelText:
                            'APPLICATION_MOBILE_FIELD_LABEL_USER_PASSWORD'
                                    .tr() +
                                '*',
                        suffixIcon: IconButton(
                          onPressed: () {
                            setState(() {
                              hidePassword = !hidePassword;
                            });
                          },
                          icon: Icon(hidePassword
                              ? FontAwesomeIcons.eyeSlash
                              : FontAwesomeIcons.eye),
                          iconSize: 16.0,
                        ),
                      ),
                      controller: password,
                      obscureText: hidePassword,
                      inputFormatters: [LengthLimitingTextInputFormatter(32)],
                      validator: (val) => val == null ||
                              val.trim() == '' ||
                              val.length < 6
                          ? 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MIN_LENGTH'
                              .tr()
                          : null,
                      onSaved: (val) => password.text = val!,
                    ),
                    SizedBox(
                      height: 25,
                    ),
                    TextFormField(
                      decoration: InputDecoration(
                        hintText:
                            'APPLICATION_MOBILE_FIELD_LABEL_USER_CONFIRM_PASSWORD'
                                    .tr() +
                                '*',
                        labelText:
                            'APPLICATION_MOBILE_FIELD_LABEL_USER_CONFIRM_PASSWORD'
                                    .tr() +
                                '*',
                        suffixIcon: IconButton(
                          onPressed: () {
                            setState(() {
                              hideCPassword = !hideCPassword;
                            });
                          },
                          icon: Icon(hideCPassword
                              ? FontAwesomeIcons.eyeSlash
                              : FontAwesomeIcons.eye),
                          iconSize: 16.0,
                        ),
                      ),
                      controller: cpassword,
                      obscureText: hideCPassword,
                      inputFormatters: [LengthLimitingTextInputFormatter(32)],
                      validator: (val) => val == null ||
                              val.trim() == '' ||
                              val.length < 6
                          ? 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MIN_LENGTH'
                              .tr()
                          : val != password.text
                              ? 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MATCH'
                                  .tr()
                              : null,
                      onSaved: (val) => cpassword.text = val!,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
