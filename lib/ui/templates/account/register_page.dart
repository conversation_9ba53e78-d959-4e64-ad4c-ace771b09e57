import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/controllers/register_user_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/email_textfield_with_validation.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  RegisterUserController controller = RegisterUserController.of();

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    ///init country with locale, can't be done in init state
    if (controller.country == null) {
      // Defaulting the country to US, so that we get the co
      Tools.debugPrint('current locale : US');
      controller.country = Ref().get().getCountryByShortLabel("US");
      controller.owner.setCountry(controller.country!);
    }
  }

  @override
  Widget build(BuildContext context) {
    // bool _valid = controller.agreementsAccepted && (form?.validate() ?? false);
    return Scaffold(
      backgroundColor: const Color(0xFFFBFBFB),
      appBar: AppBar(
        elevation: 1,
        leading: BackButton(
          onPressed: () => Tools().navigatorPop(),
        ),
        leadingWidth: 40.0,
        title: Text(
          'APPLICATION_MOBILE_REGISTER_TITLE_2'.tr('Create Account'),
          maxLines: 2,
          style: Theme.of(context).textTheme.headline6,
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(vertical: 25, horizontal: 15),
        child: Obx(
          () => ElevatedButton(
            onPressed: !controller.isLoading &&
                    controller.agreementsAccepted &&
                    !controller.isEmailChecking
                ? () async {
                    await controller.submitForm(context);
                  }
                : null,
            child: Text(
              'APPLICATION_MOBILE_REGISTER_TITLE_2'.tr('Create Account'),
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Container(
          width: MediaQuery.of(context).size.width,
          padding: const EdgeInsets.all(15.0),
          child: Form(
            key: controller.formKey,
            autovalidateMode: AutovalidateMode.disabled,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 10.0),
                EmailTextFieldWithValidation(controller, labelText: 'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'.tr() + '*',),
                const SizedBox(height: 25.0),
                TextFormField(
                  keyboardType: TextInputType.name,
                  textCapitalization: TextCapitalization.words,
                  decoration: InputDecoration(
                    hintText:
                        'APPLICATION_MOBILE_FIELD_LABEL_USER_FIRSTNAME'.tr() +
                            '*',
                    labelText:
                        'APPLICATION_MOBILE_FIELD_LABEL_USER_FIRSTNAME'.tr() +
                            '*',
                  ),
                  initialValue: controller.owner.firstName,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(256),
                    FilteringTextInputFormatter.allow(
                      RegExp('[a-zA-Z\\ \\-\\.]'),
                    ),
                  ],
                  validator: (val) => val == null || val.trim() == ''
                      ? 'APPLICATION_MOBILE_MESSAGE_USER_FORM_FIRSTNAME_REQUIRED'
                          .tr()
                      : null,
                  onSaved: (val) =>
                      controller.owner.firstName = val?.trim() ?? '',
                ),
                const SizedBox(height: 25.0),
                TextFormField(
                  keyboardType: TextInputType.name,
                  textCapitalization: TextCapitalization.words,
                  decoration: InputDecoration(
                    hintText:
                        'APPLICATION_MOBILE_FIELD_LABEL_USER_LAST_name'.tr() +
                            '*',
                    labelText:
                        'APPLICATION_MOBILE_FIELD_LABEL_USER_LAST_name'.tr() +
                            '*',
                  ),
                  initialValue: controller.owner.lastName,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(256),
                    FilteringTextInputFormatter.allow(
                      RegExp('[a-zA-Z\\ \\-\\.]'),
                    ),
                  ],
                  validator: (val) => val == null || val.trim() == ''
                      ? 'APPLICATION_MOBILE_MESSAGE_USER_FORM_LASTNAME_REQUIRED'
                          .tr()
                      : null,
                  onSaved: (val) =>
                      controller.owner.lastName = val?.trim() ?? '',
                ),
                const SizedBox(height: 25.0),
                Obx(
                  () => TextFormField(
                    decoration: InputDecoration(
                      hintText:
                          'APPLICATION_MOBILE_FIELD_LABEL_USER_PASSWORD'.tr() +
                              '*',
                      labelText:
                          'APPLICATION_MOBILE_FIELD_LABEL_USER_PASSWORD'.tr() +
                              '*',
                      suffixIcon: IconButton(
                        onPressed: () {
                          controller.hidePassword = !controller.hidePassword;
                        },
                        icon: Icon(controller.hidePassword
                            ? FontAwesomeIcons.eyeSlash
                            : FontAwesomeIcons.eye),
                        color: Theme.of(context).colorScheme.secondary,
                        iconSize: 16.0,
                      ),
                    ),
                    controller: controller.passwordController,
                    obscureText: controller.hidePassword,
                    inputFormatters: [LengthLimitingTextInputFormatter(32)],
                    validator: (val) => val == null ||
                            val.trim() == '' ||
                            val.length < 6
                        ? 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MIN_LENGTH'
                            .tr()
                        : null,
                    onSaved: (val) => controller.passwordController.text = val!,
                  ),
                ),
                const SizedBox(height: 25.0),
                Obx(
                  () => TextFormField(
                    decoration: InputDecoration(
                      hintText:
                          'APPLICATION_MOBILE_FIELD_LABEL_USER_CONFIRM_PASSWORD'
                                  .tr() +
                              '*',
                      labelText:
                          'APPLICATION_MOBILE_FIELD_LABEL_USER_CONFIRM_PASSWORD'
                                  .tr() +
                              '*',
                      suffixIcon: IconButton(
                        onPressed: () {
                          controller.hideConfirmPassword =
                              !controller.hideConfirmPassword;
                        },
                        icon: Icon(controller.hideConfirmPassword
                            ? FontAwesomeIcons.eyeSlash
                            : FontAwesomeIcons.eye),
                        color: Theme.of(context).colorScheme.secondary,
                        iconSize: 16.0,
                      ),
                    ),
                    controller: controller.confirmPasswordController,
                    obscureText: controller.hideConfirmPassword,
                    inputFormatters: [LengthLimitingTextInputFormatter(32)],
                    validator: (val) {
                      if (val != controller.passwordController.text) {
                        return 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MATCH'
                            .tr();
                      }
                      return null;
                    },
                    onSaved: (val) =>
                        controller.confirmPasswordController.text = val!,
                  ),
                ),
                // Spacer(),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 30.0),
                  child: Row(
                    children: <Widget>[
                      Obx(
                        () => Checkbox(
                          value: controller.agreementsAccepted,
                          onChanged: (bool? value) {
                            controller.agreementsAccepted = value ?? false;
                          },
                        ),
                      ),
                      Expanded(child: _getTermsAndConditions(context)),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _getTermsAndConditions(BuildContext context) {
    TextStyle linkStyle = const TextStyle(color: Colors.blue);
    return RichText(
      text: TextSpan(
        style: Theme.of(context).textTheme.bodyMedium,
        children: <TextSpan>[
          const TextSpan(text: 'By registering with WoofTrax you accept our '),
          TextSpan(
              text: 'Privacy Policy',
              style: linkStyle,
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  controller.openPrivacyPolicy();
                }),
          const TextSpan(text: ' and '),
          TextSpan(
              text: 'Terms and Conditions.',
              style: linkStyle,
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  controller.openTermsAndConditions();
                }),
        ],
      ),
    );
  }
}
