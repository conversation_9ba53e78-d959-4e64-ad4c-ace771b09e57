import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';

class AuthLoginButton extends StatelessWidget {
  final String label;
  final Widget icon;
  final Function() onPressed;

  const AuthLoginButton({
    required this.label,
    required this.icon,
    required this.onPressed,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: OutlinedButton.icon(
        label: Text(label),
        icon: icon,
        style: ElevatedButton.styleFrom(
          side: const BorderSide(color: Color(0xffCECECE)),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(40.0),
            ),
          ),
          backgroundColor: Colors.white,
        ),
        onPressed: onPressed,
      ),
    );
  }
}
