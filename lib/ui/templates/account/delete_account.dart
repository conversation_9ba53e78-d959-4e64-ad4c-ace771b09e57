import 'package:flutter/material.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/ref_settings.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_html_widget.dart';

class DeleteAccountPage extends StatelessWidget {
  DeleteAccountPage({Key? key}) : super(key: key);

  RefSettings settings = Data().refSettings;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFBFBFB),
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_PROFILE_ITEM_SELECT_DELETE_ACCOUNT'.tr(),
          style: Theme.of(context)
              .textTheme
              .headline5
              ?.copyWith(fontSize: 20, fontWeight: FontWeight.w700),
        ),
      ),
      body: Safe<PERSON><PERSON>(
        child: Padding(
          padding: const EdgeInsets.all(30.0),
          child: MBHtmlWidget(htmlSrc: settings.deleteAccountPageContent ?? ''),
        ),
      ),
    );
  }
}
