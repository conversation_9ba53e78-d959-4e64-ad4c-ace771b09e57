import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/account/change_email.dart';
import 'package:mybuddy/ui/templates/account/change_password.dart';
import 'package:mybuddy/ui/templates/account/edit_account.dart';

import 'delete_account.dart';
import 'owner_settings_page.dart';

class OwnerProfileSettingsPage extends StatefulWidget {
  const OwnerProfileSettingsPage({Key? key}) : super(key: key);

  @override
  _OwnerProfileSettingsPageState createState() =>
      _OwnerProfileSettingsPageState();
}

class _OwnerProfileSettingsPageState extends State<OwnerProfileSettingsPage> {
  @override
  Widget build(BuildContext context) {
    List<ProfileItem> profileItems = [
      ProfileItem(
        'APPLICATION_MOBILE_PROFILE_ITEM_EDIT_PROFILE'.tr(),
        'assets/icon/edit.png',
        onPressed: () async => await Tools().navigatorPush(
          const EditAccount(),
        ),
      ),
      ProfileItem(
        'APPLICATION_MOBILE_PROFILE_ITEM_CHANGE_EMAIL'.tr(),
        'assets/icon/envelop.png',
        onPressed: () async => await Tools().navigatorPush(
          const ChangeEmail(),
        ),
      ),
      ProfileItem(
        'APPLICATION_MOBILE_PROFILE_ITEM_CHANGE_PASSWORD'.tr(),
        'assets/icon/lock.png',
        onPressed: () async => await Tools().navigatorPush(
          const ChangePassword(),
        ),
      ),
      ProfileItem(
        'APPLICATION_MOBILE_PROFILE_ITEM_SELECT_DELETE_ACCOUNT'.tr(),
        'assets/icon/delete.png',
        onPressed: () async => await Tools().navigatorPush(
          DeleteAccountPage(),
        ),
      ),
    ];
    return Scaffold(
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_PROFILE_ITEM_SELECT_SETTINGS'.tr(),
          style: Theme.of(context)
              .textTheme
              .headlineMedium
              ?.copyWith(fontSize: 20, fontWeight: FontWeight.w700),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: profileItems
              .map<Widget>((item) => makeProfileItem(context, item))
              .toList(),
        ),
      ),
    );
  }

  SizedBox makeProfileItem(
    BuildContext context,
    ProfileItem item,
  ) {
    return SizedBox(
      width: double.infinity,
      child: TextButton.icon(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(
            horizontal: 25.0,
            vertical: 17.0,
          ),
          alignment: Alignment.centerLeft,
        ),
        onPressed: item.onPressed,
        icon: Image.asset(
          item.icon,
          width: 18.0,
          height: 18.0,
        ),
        label: Text(
          item.label,
          style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                fontWeight: FontWeight.w400,
              ),
        ),
      ),
    );
  }
}
