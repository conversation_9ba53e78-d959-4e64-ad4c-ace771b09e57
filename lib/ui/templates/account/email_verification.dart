import 'dart:async';

import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

import '../../../Api/mb_api_owner.dart';
import '../../../Api/mb_response.dart';
import '../../../Tools/tools_dynamic_links.dart';
import '../../components/timer_button.dart';

enum EmailVerificationType { signup, login, changeEmail }

class EmailVerification extends StatefulWidget {
  final String email;
  final EmailVerificationType type;
  //this parameter is used only for change email case
  final String? oldEmail;

  // constructor to make oldEmail optional
  const EmailVerification({Key? key, required this.email, required this.type, this.oldEmail}) : super(key: key);

  // another constructor to make oldEmail mandatory for the change email
  const EmailVerification.changeEmail({Key? key,required this.email, required this.oldEmail, required this.type}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _EmailVerificationState();
}

class _EmailVerificationState extends State<EmailVerification> {
  int _attempts = 0;
  GlobalKey<dynamic> timerButtonKey = GlobalKey();
  bool _isAllowedForNextAttempt = true;

  @override
  Widget build(BuildContext ctx) {
    return Scaffold(
      backgroundColor: const Color(0xFFFBFBFB),
      appBar: AppBar(
        elevation: 1,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(15.0),
          ),
        ),
        title: Text(
          'APPLICATION_MOBILE_TITLE_PENDING_VERIFICATION'.tr(),
          style: Theme.of(ctx).textTheme.headline6,
        ),
      ),
      bottomNavigationBar: !_isAllowedForNextAttempt
          ? null
          : TimerButton(
              key: timerButtonKey,
              kickOffTimer: widget.type != EmailVerificationType.login,
              onPressed: onResendPressed,
            ),
      body: Builder(
        builder: (context) {
          return SingleChildScrollView(
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.all(15.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _renderInfoText(),
                  const SizedBox(height: 35.0),
                  if (!_isAllowedForNextAttempt)
                    Text(
                        "APPLICATION_MOBILE_TEXT_VERIFICATION_LIMIT_ERROR".tr(),
                        style: const TextStyle(
                            fontWeight: FontWeight.w700, fontSize: 16))
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _renderInfoText() {
    if (widget.type == EmailVerificationType.signup) {
      return RichText(
          text: TextSpan(
              text: "APPLICATION_MOBILE_TEXT_PENDING_VERIFICATION_TEXT1".tr(),
              style: const TextStyle(
                  color: Colors.black, fontSize: 16, height: 2.1),
              children: [
            TextSpan(
                text: widget.email,
                style: const TextStyle(
                  fontWeight: FontWeight.w700,
                )),
            TextSpan(
                text:
                    '. ${"APPLICATION_MOBILE_TEXT_PENDING_VERIFICATION_TEXT2".tr()}')
          ]));
    } else if (widget.type == EmailVerificationType.changeEmail) {
      return RichText(
          text: TextSpan(
              text: "APPLICATION_MOBILE_TEXT_PENDING_VERIFICATION_TEXT1".tr(),
              style:
                  const TextStyle(color: Colors.black, fontSize: 16, height: 2),
              children: [
            TextSpan(
                text: widget.email,
                style: const TextStyle(
                  fontWeight: FontWeight.w700,
                )),
            TextSpan(
                text:
                    '. ${"APPLICATION_MOBILE_TEXT_PENDING_VERIFICATION_TEXT4".tr()}')
          ]));
    } else {
      return RichText(
          text: TextSpan(
              text: "APPLICATION_MOBILE_TEXT_PENDING_VERIFICATION_TEXT3".tr(),
              style:
                  const TextStyle(color: Colors.black, fontSize: 16, height: 2),
              children: [
            TextSpan(
                text: widget.email,
                style: const TextStyle(
                  fontWeight: FontWeight.w700,
                )),
            TextSpan(
                text:
                    '.\n\n${"APPLICATION_MOBILE_TEXT_PENDING_VERIFICATION_TEXT2".tr()}')
          ]));
    }
  }

  void onResendPressed() async {
    late MBResponse response;

    if (widget.type == EmailVerificationType.changeEmail) {
      response = await MbApiOwner().resendChangeEmailLinkRequest(
          context, widget.oldEmail ?? "", widget.email,
          successMsg: "Email Sent!");
    } else {
      response = await MbApiOwner().resendRequest(
          context, widget.email, LINK_TYPE_VERIFY_EMAIL,
          successMsg: "Email Sent!");
    }

    if (response.success) {
      timerButtonKey.currentState.startTimer();
      setState(() {
        _attempts =
        response.body["resendLinkValidation"]["attempts"];
        _isAllowedForNextAttempt =
        response.body["resendLinkValidation"]
        ["isAllowedForNextAttempt"];
      });
    }
  }
}
