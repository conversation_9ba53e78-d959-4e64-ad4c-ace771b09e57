import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/owner_bloc.dart';
import 'package:mybuddy/blocs/tasks_bloc.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/pet/all_pets_page.dart';
import 'package:mybuddy/ui/templates/service/all_services_page.dart';
import 'owner_settings_page.dart';
import 'profile/profile_widgets.dart';

class OwnerProfilePage extends StatefulWidget {
  final int? selectedTab;
  const OwnerProfilePage({Key? key, this.selectedTab}) : super(key: key);

  @override
  _OwnerProfilePageState createState() => _OwnerProfilePageState();
}

class _OwnerProfilePageState extends State<OwnerProfilePage>
    with SingleTickerProviderStateMixin {
  TabController? _controller;
  List<String> tabs = <String>[
    // 'Activity',
    'Upcoming',
    'Badges',
    'Avatars',
    // 'Statistics',
  ];

  @override
  void initState() {
    super.initState();
    _controller = TabController(length: tabs.length, initialIndex: widget.selectedTab ?? 0, vsync: this);
  }

  @override
  void dispose() {
    _controller!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<OwnerBloc>(
      bloc: OwnerBloc(),
      child: Scaffold(
        appBar: MBAppBar(
          elevation: 0.0,
          actions: [
            IconButton(
              onPressed: () => Tools().navigatorPush(const PetsPage()),
              icon: const Icon(
                FontAwesomeIcons.dog,
                size: 16.0,
              ),
            ),
            IconButton(
              onPressed: () => Tools().navigatorPush(const OwnerSettingsPage()),
              icon: const Icon(
                FontAwesomeIcons.cog,
                size: 16.0,
              ),
            ),
          ],
          bottom: const PreferredSize(
            preferredSize: Size(double.infinity, 160.0),
            child: OwnerProfileHeader(),
          ),
        ),
        body: Column(
          children: [
            Container(
              color: Colors.white,
              child: TabBar(
                controller: _controller,
                isScrollable: true,
                tabs: tabs.map((t) => Tab(child: Text(t))).toList(),
              ),
            ),
            Expanded(
              child: BlocProvider<TasksBloc>(
                bloc: TasksBloc(),
                child: TabBarView(
                  controller: _controller,
                  children: const <Widget>[
                    // Center(
                    //   child: Text(
                    //       'APPLICATION_MOBILE_MESSAGE_NOT_AVAILABLE_YET'.tr()),
                    // ),
                    UpcomingScreen(),
                    BadgeScreen(),
                    PublicAvatarScreen(),
                    // Center(
                    //   child: Text(
                    //       'APPLICATION_MOBILE_MESSAGE_NOT_AVAILABLE_YET'.tr()),
                    // ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}