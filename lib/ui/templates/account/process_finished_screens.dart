import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_dynamic_links.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/account/login_page.dart';
import 'package:mybuddy/ui/templates/onboarding/landing_page.dart';

class ProcessFinished extends StatelessWidget {
  const ProcessFinished(
      {Key? key,
      required this.title,
      this.description = "",
      this.buttonTitle = "Continue",
      required this.iconName,
      required this.onPressed,
      this.onCrossIcon,
      this.showCrossIcon = false})
      : super(key: key);
  final String title, description, buttonTitle, iconName;
  final VoidCallback? onPressed, onCrossIcon;
  final bool showCrossIcon;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: onPressed == null
          ? null
          : Container(
              width: double.infinity,
              margin:
                  const EdgeInsets.symmetric(vertical: 20.0, horizontal: 15),
              child: ElevatedButton(
                onPressed: onPressed,
                child: Text(
                  buttonTitle,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ),
      body: Builder(
        builder: (context) {
          return Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: Stack(
              children: [
                if (showCrossIcon)
                  Padding(
                    padding: const EdgeInsets.only(top: 60.0, right: 10.0),
                    child: Align(
                      alignment: Alignment.topRight,
                      child: IconButton(
                        onPressed: onCrossIcon,
                        icon: Image.asset('assets/images/ic_cross.png',
                            width: 20, height: 20),
                      ),
                    ),
                  ),
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        height: 78,
                        width: 78,
                        padding: const EdgeInsets.symmetric(horizontal: 25),
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                                color: Theme.of(context).colorScheme.secondary,
                                width: 2)),
                        child: Image.asset(
                          iconName,
                          color: Theme.of(context).colorScheme.secondary,
                        ),
                      ),
                      const SizedBox(
                        height: 50,
                      ),
                      Text(
                        title,
                        style: const TextStyle(fontSize: 24),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Text(
                        description,
                        style: const TextStyle(
                            fontSize: 15, color: Colors.black87, height: 1.5),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }
}

class EmailVerified extends StatelessWidget {
  EmailVerified({Key? key, required this.type}) : super(key: key);
  String type;

  @override
  Widget build(BuildContext context) {
    return ProcessFinished(
      title: "APPLICATION_MOBILE_TITLE_EMAIL_VERIFIED".tr(),
      description: "APPLICATION_MOBILE_TEXT_EMAIL_VERIFIED".tr(),
      onPressed: buttonPressed,
      iconName: 'assets/icon/check_icon.png',
    );
  }

  void buttonPressed() {
    if (type == LINK_TYPE_VERIFY_CHANGE_EMAIL) {
      Tools().navigatorPopAllUntilHome();
    } else {
      Tools().navigatorPopUntilLandingPage(const LoginPage());
    }
  }
}

class PasswordSaved extends StatelessWidget {
  const PasswordSaved({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ProcessFinished(
      title: "APPLICATION_MOBILE_TITLE_PASSWORD_SAVED".tr(),
      description: "APPLICATION_MOBILE_TEXT_PASSWORD_SAVED".tr(),
      onPressed: () {
        Tools().navigatorPopUntilLandingPage(const LoginPage());
      },
      iconName: 'assets/icon/check_icon.png',
    );
  }
}

class ResetPasswordLinkExpired extends StatefulWidget {
  const ResetPasswordLinkExpired({Key? key, required this.data})
      : super(key: key);
  final PendingDynamicLinkData data;

  @override
  State<ResetPasswordLinkExpired> createState() =>
      _ResetPasswordLinkExpiredState();
}

class _ResetPasswordLinkExpiredState extends State<ResetPasswordLinkExpired> {
  bool resend = false;

  @override
  Widget build(BuildContext context) {
    return ProcessFinished(
      title: "APPLICATION_MOBILE_TITLE_LINK_EXPIRED".tr(),
      description: "APPLICATION_MOBILE_TEXT_RESET_VERIFY_EXPIRED".tr(),
      onPressed: resend
          ? null
          : () {
              Uri uri = widget.data.link;
              String email = uri.queryParameters['email'] ?? "";
              MbApiOwner()
                  .resendRequest(context, email, LINK_TYPE_FORGOT_PASSWORD,
                      successMsg: "Email Sent!")
                  .then((value) {
                setState(() {
                  resend = true;
                });
              });
            },
      showCrossIcon: true,
      iconName: 'assets/icon/link_expired.png',
      buttonTitle: "Resend Link",
    );
  }
}

class EmailVerificationLinkExpired extends StatefulWidget {
  const EmailVerificationLinkExpired({Key? key, required this.data})
      : super(key: key);
  final PendingDynamicLinkData data;

  @override
  State<EmailVerificationLinkExpired> createState() =>
      _EmailVerificationLinkExpiredState();
}

class _EmailVerificationLinkExpiredState
    extends State<EmailVerificationLinkExpired> {
  bool resend = false;

  @override
  Widget build(BuildContext context) {
    return ProcessFinished(
      title: "APPLICATION_MOBILE_TITLE_LINK_EXPIRED".tr(),
      description: "APPLICATION_MOBILE_TEXT_EMAIL_VERIFY_EXPIRED".tr(),
      onPressed: !resend ? onPressed : null,
      onCrossIcon: onCrossIconPressed,
      showCrossIcon: true,
      iconName: 'assets/icon/link_expired.png',
      buttonTitle: "Resend Link",
    );
  }

  void onCrossIconPressed() {
    Uri uri = widget.data.link;
    String? linkType = uri.queryParameters['linkType'] ?? "";

    if (linkType == LINK_TYPE_VERIFY_CHANGE_EMAIL) {
      Tools().navigatorPop();
    } else {
      Tools().navigatorPopUntilLandingPage(const LandingPage());
    }
  }

  Future<void> onPressed() async {
    if (resend) {
      return;
    }

    Uri uri = widget.data.link;
    String? linkType = uri.queryParameters['linkType'] ?? "";

    if (linkType == LINK_TYPE_VERIFY_CHANGE_EMAIL) {
      String oldEmail = uri.queryParameters['old_email'] ?? "";
      String newEmail = uri.queryParameters['new_email'] ?? "";

      await MbApiOwner().resendChangeEmailLinkRequest(
          context, oldEmail, newEmail,
          successMsg: "Email Sent!");
    } else {
      String email = uri.queryParameters['email'] ?? "";

      await MbApiOwner()
          .resendRequest(context, email, linkType, successMsg: "Email Sent!");
    }

    setState(() {
      resend = true;
    });
  }
}
