import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/tasks_bloc.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/models/reward.dart';
import 'package:mybuddy/models/task.dart';
import 'package:mybuddy/ui/components/mb_asset_badge.dart';
import 'package:mybuddy/ui/components/mb_progress_bar_widget.dart';

class BadgeScreen extends StatefulWidget {
  const BadgeScreen({Key? key}) : super(key: key);

  @override
  _BadgeScreenState createState() => _BadgeScreenState();
}

class _BadgeScreenState extends State<BadgeScreen> {
  late List<Reward> referencedBadges;
  List<Reward> earnedBadges = <Reward>[];
  late TasksBloc bloc;

  void getEarnedBadges(List<Task> tasks) {
    for (Task task in tasks) {
      if (task.owned.isNotEmpty) {
        for (Reward ownedReward in task.owned) {
          if (ownedReward.type == 0 && !earnedBadges.contains(ownedReward)) {
            earnedBadges.add(ownedReward);
          }
        }
      }
    }
  }

  @override
  void initState() {
    super.initState();
    referencedBadges = Ref().referenceBadges;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    bloc = BlocProvider.of<TasksBloc>(context);
  }

  @override
  void didUpdateWidget(covariant BadgeScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    bloc.update();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<Task>>(
      stream: bloc.stream,
      builder: (BuildContext context, AsyncSnapshot<List<Task>> snapshot) {
        List<Task> ownerTasks = <Task>[];
        if (snapshot.hasData) {
          ownerTasks = snapshot.data!;
          getEarnedBadges(ownerTasks);
        }

        return SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Text(
                  referencedBadges.isEmpty
                      ? ''
                      : 'Badges unlocked ${earnedBadges.length}/${referencedBadges.length}',
                  style: Theme.of(context).textTheme.subtitle2,
                ),
              ),
              if (referencedBadges.isNotEmpty)
                ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: referencedBadges.length,
                  itemBuilder: (BuildContext context, int index) {
                    Reward badge = referencedBadges[index];
                    RewardState state = RewardState.locked;

                    /// find the task linked to the badge executed by owner
                    Task? ownerTask = ownerTasks
                        .singleWhereOrNull((t) => t.code == badge.taskCode);

                    /// handle badge's state
                    if (ownerTask != null) {
                      /// owner has executed the task and earned the badge
                      if (earnedBadges.contains(badge)) {
                        state = RewardState.earned;
                      } else {
                        /// retrieve all badges with same task
                        List<Reward> taskRewards = referencedBadges
                            .where((b) => b.taskCode == ownerTask.code)
                            .toList();

                        /// remove all badges already earned
                        taskRewards
                            .removeWhere((tr) => earnedBadges.contains(tr));

                        /// this badge is in progress if it's first remaining badge
                        if (taskRewards.indexOf(badge) == 0) {
                          state = RewardState.inProgress;
                        }
                      }
                    }

                    /// Prevent Unsupported operation: Infinity or NaN toInt
                    double progress = 0.0;
                    if (state == RewardState.inProgress) {
                      progress = badge.count == 0
                          ? 0.0
                          : ownerTask!.ownerTaskCount / badge.count;
                    }

                    /// handle subtitle and earned badge date
                    String subtitle =
                        'APPLICATION_MOBILE_SUBTITLE_BADGE_${badge.code}' +
                            (state == RewardState.locked ? '_LOCKED' : '');
                    String? timestamp = state == RewardState.earned
                        ? earnedBadges
                            .singleWhere((b) => b == badge)
                            .ownerReleaseDate!
                            .toDateFormat()
                        : null;

                    return ListTile(
                      contentPadding: const EdgeInsets.all(10.0),
                      leading: AssetReward(reward: badge, state: state),
                      title: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'APPLICATION_MOBILE_TITLE_BADGE_${badge.code}'
                                .tr(''),
                            style: Theme.of(context).textTheme.bodyText1,
                          ),
                          if (state == RewardState.inProgress)
                            Text(
                              '${ownerTask!.ownerTaskCount}/${badge.count}',
                              style: Theme.of(context).textTheme.subtitle2,
                            )
                          else if (timestamp != null)
                            Text(
                              timestamp,
                              style: Theme.of(context).textTheme.subtitle2,
                            ),

                          ///endif
                        ],
                      ),
                      subtitle: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (state == RewardState.inProgress)
                            MBProgressBar(value: progress),

                          ///endif
                          Text(
                            subtitle.tr(state == RewardState.locked
                                ? 'Badge is locked'
                                : 'Badge subtitle'),
                          ),
                        ],
                      ),
                      trailing: state == RewardState.locked
                          ? Icon(
                              Icons.lock,
                              size: 20.0,
                              color: Theme.of(context).primaryColor,
                            )
                          : null,
                    );
                  },
                )
              else
                Center(
                  child: Text(
                    'APPLICATION_MOBILE_BADGE_LIST_EMPTY'
                        .tr('No badge available'),
                    style: Theme.of(context).textTheme.bodyText1,
                  ),
                ),

              ///endif
            ],
          ),
        );
      },
    );
  }
}
