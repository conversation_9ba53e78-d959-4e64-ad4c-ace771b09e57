import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/owner_bloc.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';

class OwnerProfileCard extends StatelessWidget {
  const OwnerProfileCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final OwnerBloc bloc = BlocProvider.of<OwnerBloc>(context);

    return Container(
      width: double.infinity,
      height: 100.0,
      padding: const EdgeInsets.symmetric(horizontal: 25.0),
      child: StreamBuilder<Owner>(
          stream: bloc.stream,
          builder: (BuildContext context, AsyncSnapshot<Owner> snapshot) {
            if (!snapshot.hasData) {
              return const SizedBox.shrink();
            }
            Owner owner = snapshot.data!;
            Map<String, dynamic> buildLevel = owner.points.toLevel();

            return Row(
              children: [
                MBAvatar(
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  imageUrl: owner.privateAvatarUrl,
                  noImageUrl: Text(owner.initials),
                ),
                const SizedBox(width: 15.0),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        owner.getFullName(),
                        style: Theme.of(context).textTheme.labelMedium,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: 'Level ',
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelMedium!
                                      .copyWith(
                                        color: const Color.fromRGBO(
                                          127,
                                          127,
                                          127,
                                          1.0,
                                        ),
                                        fontSize: 13.0,
                                      ),
                                ),
                                TextSpan(
                                  text: buildLevel['level'].toString(),
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineSmall!
                                      .copyWith(
                                          fontSize: 13.0,
                                          color:
                                              Theme.of(context).indicatorColor),
                                ),
                                TextSpan(
                                  text: ' | ',
                                  style: TextStyle(
                                    color: Theme.of(context).disabledColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                TextSpan(
                                  text: 'Points ',
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelMedium!
                                      .copyWith(
                                        color: const Color.fromRGBO(
                                          127,
                                          127,
                                          127,
                                          1.0,
                                        ),
                                        fontSize: 13.0,
                                      ),
                                ),
                                TextSpan(
                                  text: owner.points.toPointsLong(),
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineSmall!
                                      .copyWith(
                                        fontSize: 13.0,
                                        color: const Color.fromRGBO(
                                          243,
                                          174,
                                          86,
                                          1,
                                        ),
                                      ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      if (owner.isActiveWalker) ...[
                        const SizedBox(
                          height: 3,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: Theme.of(context).colorScheme.secondary,
                              size: 15,
                            ),
                            const SizedBox(width: 5.0),
                            Text(
                              'Active Walker',
                              style: TextStyle(
                                fontSize: 14.0,
                                color: Theme.of(context).colorScheme.secondary,
                              ),
                            ),
                          ],
                        ),
                      ]
                    ],
                  ),
                ),
              ],
            );
          }),
    );
  }
}
