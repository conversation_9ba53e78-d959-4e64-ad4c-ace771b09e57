import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/tasks_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/reward.dart';
import 'package:mybuddy/models/task.dart';
import 'package:mybuddy/ui/components/mb_asset_badge.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../Tools/tools.dart';

class PublicAvatarScreen extends StatefulWidget {
  const PublicAvatarScreen({Key? key}) : super(key: key);

  @override
  _PublicAvatarScreenState createState() => _PublicAvatarScreenState();
}

class _PublicAvatarScreenState extends State<PublicAvatarScreen>{
  final List<Reward> referenceAvatars = Ref().referenceAvatars;
  List<Reward> earnedAvatars = <Reward>[];
  Owner owner = Data().get().owner;
  int level = 1;
  late int? selectedAvatarId;
  late bool isAware;
  static const String isAwarePrefName = 'IsAwareAvatar';

  Future<void> checkAwareness() async {
    SharedPreferences prefs = SettingsDelegate().prefs;
    isAware = prefs.containsKey(isAwarePrefName)
        ? (prefs.getBool(isAwarePrefName) ?? false)
        : false;
  }

  Future<void> validAwareness() async {
    await (await SharedPreferences.getInstance()).setBool(isAwarePrefName, true);
    isAware = true;
  }

  void changeAvatar(int id) {
    setState(() {
      selectedAvatarId = id != selectedAvatarId ? id : null;
    });
    owner.publicAvatarId = selectedAvatarId;
    MbApiOwner().updateUserRequest(null, owner: owner);
  }

  @override
  void initState() {
    super.initState();
    selectedAvatarId = owner.publicAvatarId;
    isAware = false;
  }

  @override
  Widget build(BuildContext context) {
    final TasksBloc bloc = BlocProvider.of<TasksBloc>(context);
    if (!isAware) {
      WidgetsBinding.instance
          ?.addPostFrameCallback((_) => _unlockAvatarInfo(context));
    }

    return StreamBuilder<List<Task>>(
      stream: bloc.stream,
      builder: (BuildContext context, AsyncSnapshot<List<Task>> snapshot) {
        List<Task> ownerTasks;
        if (snapshot.hasData) {
          ownerTasks = snapshot.data!;
          if (earnedAvatars.isEmpty) {
            for (Task task in ownerTasks) {
              if (task.owned.isNotEmpty) {
                for (Reward ownedReward in task.owned) {
                  if (ownedReward.type == 1 && !earnedAvatars.contains(ownedReward)) {
                    earnedAvatars.add(ownedReward);
                  }
                }
              }
            }
          }
        }

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Text(
                  referenceAvatars.isEmpty
                      ? ''
                      : 'Avatars unlocked ${earnedAvatars.length}/${referenceAvatars.length}',
                  style: Theme.of(context).textTheme.subtitle2,
                ),
              ),
              if (referenceAvatars.isNotEmpty)
                GridView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                  ),
                  itemCount: referenceAvatars.length,
                  itemBuilder: (BuildContext context, int index) {
                    Reward referenceAvatar = referenceAvatars[index];
                    bool itemUnlocked = earnedAvatars.contains(referenceAvatar);
                    return Container(
                      alignment: Alignment.center,
                      child: Stack(
                        children: [
                          InkWell(
                            onTap: itemUnlocked
                                ? () => changeAvatar(referenceAvatar.id)
                                : null,
                            child: AssetReward(
                              reward: referenceAvatar,
                              size: 90.0,
                              state: itemUnlocked
                                  ? RewardState.earned
                                  : RewardState.locked,
                            ),
                          ),
                          Positioned(
                            right: 0.0,
                            bottom: 0.0,
                            child: itemUnlocked
                                ? (selectedAvatarId != null &&
                                        selectedAvatarId == referenceAvatar.id
                                    ? Icon(
                                        Icons.check_circle,
                                        size: 16.0,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .secondary,
                                      )
                                    : const SizedBox.shrink())
                                : const Icon(
                                    Icons.lock,
                                    size: 16.0,
                                    color: Colors.amber,
                                  ),
                          ),
                        ],
                      ),
                    );
                  },
                )
              else
                Center(
                  child: Text(
                    'APPLICATION_MOBILE_AVATAR_LIST_EMPTY'
                        .tr('No avatar available'),
                    style: Theme.of(context).textTheme.bodyText1,
                  ),
                ),

              ///endif
            ],
          ),
        );
      },
    );
  }

  Future<PersistentBottomSheetController?> _unlockAvatarInfo(
      BuildContext context) async {
    await checkAwareness();
    if (isAware) return null;
    Size mediaSize = MediaQuery.of(context).size;
    return Scaffold.of(context).showBottomSheet((context) {
      return Container(
        padding: const EdgeInsets.fromLTRB(30.0, 30.0, 30.0, 10.0),
        width: mediaSize.width,
        height: mediaSize.height,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Image.asset(
              'assets/images/wooftrax_unlock_avatars.png',
              fit: BoxFit.contain,
              height: mediaSize.height / 5,
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Unlock avatars and display your achievements to your community and friends',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyText1,
                ),
                const SizedBox(height: 10.0),
                Text(
                  'Badges are visible to other Users on the map and in the leaderboard',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.caption,
                ),
              ],
            ),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  validAwareness();
                  Tools().navigatorPop();
                },
                child: const Text('See avatars'),
              ),
            ),
          ],
        ),
      );
    }, backgroundColor: Theme.of(context).backgroundColor);
  }

}
