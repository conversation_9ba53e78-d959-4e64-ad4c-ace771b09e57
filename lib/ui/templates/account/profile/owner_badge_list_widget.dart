import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/tasks_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/reward.dart';
import 'package:mybuddy/models/task.dart';
import 'package:mybuddy/ui/components/mb_asset_badge.dart';

class OwnerBadgeList extends StatelessWidget {
  const OwnerBadgeList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TasksBloc bloc = BlocProvider.of<TasksBloc>(context);
    bloc.update();

    return StreamBuilder<List<Task>>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        List<Reward> badges = <Reward>[];
        if (snapshot.hasData) {
          for (Task task in snapshot.data!) {
            if (task.owned.isNotEmpty) {
              for (Reward reward in task.owned) {
                if (!badges.contains(reward)) {
                  badges.add(reward);
                }
              }
            }
          }
          //sort from newest to oldest
          badges.sort((a, b) {
            return b.ownerReleaseDate!.compareTo(a.ownerReleaseDate!);
          });
        }

        return SizedBox(
          width: double.infinity,
          height: 50.0,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding:
                const EdgeInsets.symmetric(horizontal: 15.0, vertical: 0.0),
            itemCount: badges.length,
            itemBuilder: (BuildContext context, int index) {
              return GestureDetector(
                  onTap: () => showBadgeDescription(context, badges[index]),
                  child: AssetReward(reward: badges[index]));
            },
          ),
        );
      },
    );
  }

  Future<void> showBadgeDescription(BuildContext context, Reward badge) async {
    return await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: SingleChildScrollView(
            child: Column(
              children: [
                AssetReward(reward: badge, state: RewardState.earned),
                Text(
                  badge.desc.tr(),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(Data().dateTimeToUserDateStr(badge.ownerReleaseDate))
              ],
            ),
          ),
        );
      },
    );
  }
}
