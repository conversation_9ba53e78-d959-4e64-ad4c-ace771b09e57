import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/appointments_bloc.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/reminders_bloc.dart';
import 'package:mybuddy/ui/templates/apt/widgets/appointment_request_list_widget.dart';
import 'package:mybuddy/ui/templates/reminder/widgets/reminder_list_widget.dart';

class UpcomingScreen extends StatelessWidget {
  const UpcomingScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(10.0, 10.0, 10.0, 75.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocProvider<RemindersBloc>(
            bloc: RemindersBloc(),
            child: const ReminderList(),
          ),
          <PERSON><PERSON><PERSON><PERSON><AppointmentsBloc>(
            bloc: AppointmentsBloc(),
            child: const AppointmentRequestList(),
          ),
        ],
      ),
    );
  }
}
