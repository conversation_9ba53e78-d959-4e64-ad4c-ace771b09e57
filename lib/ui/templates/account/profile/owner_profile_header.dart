import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/tasks_bloc.dart';
import 'package:mybuddy/ui/templates/workout/workout_summary_widget.dart';

import 'owner_badge_list_widget.dart';
import 'profile_card_widget.dart';

class OwnerProfileHeader extends StatelessWidget {
  const OwnerProfileHeader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const OwnerProfileCard(),
        BlocProvider<TasksBloc>(
          bloc: TasksBloc(),
          child: const OwnerBadgeList(),
        ),
        const Padding(
          padding: EdgeInsets.only(left: 15.0, top: 10.0),
          child: OwnerWorkoutSummary(),
        ),
      ],
    );
  }
}
