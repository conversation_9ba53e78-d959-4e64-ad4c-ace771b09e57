import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';

class ChangePassword extends StatefulWidget {
  const ChangePassword({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ChangePasswordState();
}

class _ChangePasswordState extends State<ChangePassword> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController password = TextEditingController();
  TextEditingController cpassword = TextEditingController();
  bool hidePassword = true, hideCPassword = true;

  @override
  void initState() {
    super.initState();
  }

  void changePassword() {
    FormState? form = _formKey.currentState;
    if (form == null || !form.validate()) {
      return;
    }
    form.save();
    if (password.text == cpassword.text) {
      MbApiOwner()
          .changePasswordRequest(
        context,
        password.text,
      )
          .then((response) {
        if (response.success) {
          Tools().common.showMessage(
                context,
                "APPLICATION_MOBILE_CHANGE_PASSWORD_SUCCESS".tr(),
              );
          Tools().navigatorPop(value: true);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    Owner _owner = Data().get().owner;
    return Scaffold(
      backgroundColor: const Color(0xFFFBFBFB),
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_TITLE_CHANGE_PASSWORD'.tr(),
          style: Theme.of(context).textTheme.headline6,
        ),
      ),
      bottomNavigationBar: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 15),
        child: ElevatedButton(
          onPressed: changePassword,
          child: Text(
            'APPLICATION_MOBILE_LABEL_SAVE_PASSWORD'.tr(),
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ),
      body: Builder(
        builder: (context) {
          return SingleChildScrollView(
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.only(
                top: 25.0,
                left: 20.0,
                right: 20.0,
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    const SizedBox(height: 20.0),
                    TextFormField(
                      decoration: InputDecoration(
                        hintText:
                            'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'.tr(),
                        labelText:
                            'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'.tr(),
                      ),
                      initialValue: _owner.email,
                      enabled: false,
                    ),
                    const SizedBox(height: 20.0),
                    TextFormField(
                      decoration: InputDecoration(
                        hintText:
                            'APPLICATION_MOBILE_FIELD_LABEL_USER_NEW_PASSWORD'
                                    .tr() +
                                '*',
                        labelText:
                            'APPLICATION_MOBILE_FIELD_LABEL_USER_NEW_PASSWORD'
                                    .tr() +
                                '*',
                        suffixIcon: IconButton(
                          onPressed: () {
                            setState(() {
                              hidePassword = !hidePassword;
                            });
                          },
                          icon: Icon(hidePassword
                              ? FontAwesomeIcons.eyeSlash
                              : FontAwesomeIcons.eye),
                          iconSize: 16.0,
                        ),
                      ),
                      controller: password,
                      obscureText: hidePassword,
                      inputFormatters: [LengthLimitingTextInputFormatter(32)],
                      validator: (val) => val == null ||
                              val.trim() == '' ||
                              val.length < 6
                          ? 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MIN_LENGTH'
                              .tr()
                          : null,
                      onSaved: (val) => password.text = val!,
                    ),
                    SizedBox(
                      height: 25,
                    ),
                    TextFormField(
                      decoration: InputDecoration(
                        hintText:
                            'APPLICATION_MOBILE_FIELD_LABEL_USER_CONFIRM_NEW_PASSWORD'
                                    .tr() +
                                '*',
                        labelText:
                            'APPLICATION_MOBILE_FIELD_LABEL_USER_CONFIRM_NEW_PASSWORD'
                                    .tr() +
                                '*',
                        suffixIcon: IconButton(
                          onPressed: () {
                            setState(() {
                              hideCPassword = !hideCPassword;
                            });
                          },
                          icon: Icon(hideCPassword
                              ? FontAwesomeIcons.eyeSlash
                              : FontAwesomeIcons.eye),
                          iconSize: 16.0,
                        ),
                      ),
                      controller: cpassword,
                      obscureText: hideCPassword,
                      inputFormatters: [LengthLimitingTextInputFormatter(32)],
                      validator: (val) => val == null ||
                              val.trim() == '' ||
                              val.length < 6
                          ? 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MIN_LENGTH'
                              .tr()
                          : val != password.text
                              ? 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MATCH'
                                  .tr()
                              : null,
                      onSaved: (val) => cpassword.text = val!,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
