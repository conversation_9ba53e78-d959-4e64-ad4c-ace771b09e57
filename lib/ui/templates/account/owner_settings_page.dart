import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/owner_bloc.dart';
import 'package:mybuddy/class/constants.dart';
import 'package:mybuddy/controllers/home_tab_controller.dart';
import 'package:mybuddy/controllers/root_controller.dart';
import 'package:mybuddy/controllers/set_weekly_goals_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/account/owner_set_weekly_goals.dart';
import 'package:mybuddy/ui/templates/account/profile/profile_card_widget.dart';
import 'package:mybuddy/ui/templates/account/settings/settings_footer_section.dart';
import 'package:mybuddy/ui/templates/home/<USER>/home_screens.dart';
import 'package:mybuddy/ui/templates/pet/all_pets_page.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../controllers/offline_workout_sync_controller.dart';
import '../onboarding/charity_selection_page.dart';
import 'owner_profile_setting_page.dart';

class OwnerSettingsPage extends StatefulWidget {
  const OwnerSettingsPage({Key? key}) : super(key: key);

  @override
  _OwnerSettingsPageState createState() => _OwnerSettingsPageState();
}

class ProfileItem {
  final String label;
  final String icon;
  final void Function()? onPressed;

  ProfileItem(this.label, this.icon, {this.onPressed});
}

class _OwnerSettingsPageState extends State<OwnerSettingsPage> {
  @override
  Widget build(BuildContext context) {
    List<ProfileItem> profileItems = [
      ProfileItem(
        'APPLICATION_MOBILE_PROFILE_ITEM_ACTIVITY_HISTORY'.tr(),
        'assets/icon/activity_history.png',
        onPressed: () {
          Tools().navigatorPop();
          Get.find<HomeTabController>().tabSwitcher(HomeTab.history);
        },
      ),
      ProfileItem(
        'APPLICATION_MOBILE_SETTING_READ_TITLE'.tr('Read Articles'),
        'assets/icon/read.png',
        onPressed: () async => await Tools().navigatorPush(
          const ReadScreen(),
        ),
      ),
      ProfileItem(
        // launch privacy
        'APPLICATION_MOBILE_PROFILE_ITEM_INVITE'.tr(),
        'assets/icon/invite_share.png',
        onPressed: () async {
          await Share.share(
              "I’m using the free WoofTrax app to support my local animal charity just by walking my dog. Use this link to download WoofTrax and you can join me in earning donations every time you walk your dog! $wooftraxAppLink",
              subject: "Join WoofTrax");
        },
      ),
      ProfileItem(
        'APPLICATION_MOBILE_PROFILE_ITEM_MANAGE_DOGS'.tr(),
        'assets/icon/dog.png',
        onPressed: () async => await Tools().navigatorPush(
          const PetsPage(),
        ),
      ),
      ProfileItem(
        'APPLICATION_MOBILE_PROFILE_TITLE_WEEKLY_GOALS'.tr(),
        'assets/icon/paws.png',
        onPressed: () async => await Tools()
            .navigatorPush(
              SetWeeklyGoalsScreen(),
            )
            .then((value) => Get.delete<SetWeeklyGoalsController>()),
      ),
      ProfileItem(
        'APPLICATION_MOBILE_PROFILE_ITEM_SELECT_CHARITY'.tr(),
        'assets/icon/handshake_gray.png',
        onPressed: () async =>
            await Tools().navigatorPush(const CharitySelectionPage()),
      ),
      ProfileItem(
        'APPLICATION_MOBILE_PROFILE_ITEM_SELECT_SETTINGS'.tr(),
        'assets/icon/settings.png',
        onPressed: () async => await Tools().navigatorPush(
          const OwnerProfileSettingsPage(),
        ),
      ),
      ProfileItem(
        // launch privacy
        'APPLICATION_MOBILE_PROFILE_ITEM_PRIVACY_POLICY'.tr(),
        'assets/icon/shield.png',
        onPressed: () async => await launch("https://www.wooftrax.com/privacy"),
      ),
      ProfileItem(
        // logout
        'APPLICATION_MOBILE_BUTTON_LABEL_USER_LOGOUT'.tr(),
        'assets/icon/signout.png',
        onPressed: () async {
          if (!OfflineWorkoutSyncController.of().isUnSyncWalksAvailable) {
            Tools().common.showValidDialog(
              context,
              text: 'APPLICATION_MOBILE_MESSAGE_USER_CONFIRM_LOGOUT'.tr(),
              onValid: () async {
                MBResponse response = await MbApiOwner().logoutRequest(context);
                if (response.success) {
                  RootController.of.initiateNotSignedInState();
                  Tools().navigatorPopAll();
                }
              },
            );
          } else {
            Tools().common.showCustomDialogWithThreeButtons(
                  title:
                      'APPLICATION_MOBILE_TEXT_LOGOUT_TITLE_WHEN_UNSYNC_WALKS_AVAILABLE'
                          .tr(),
                  description:
                      'APPLICATION_MOBILE_TEXT_LOGOUT_DESC_WHEN_UNSYNC_WALKS_AVAILABLE'
                          .tr(),
                  thirdTitle: 'APPLICATION_MOBILE_BUTTON_LABEL_CANCEL'.tr(),
                  secondTitle:
                      'APPLICATION_MOBILE_BUTTON_LABEL_USER_LOGOUT'.tr(),
                  firstTitle:
                      'APPLICATION_MOBILE_TEXT_LOGOUT_UPLOAD_BUTTON'.tr(),
                  thirdButtonColor: const Color(0xFFF3AE56),
                  secondButtonColor: const Color(0xFFE14646),
                  isSecondButtonFilled: true,
                  onSecond: () async {
                    MBResponse response =
                        await MbApiOwner().logoutRequest(context);
                    if (response.success) {
                      RootController.of.initiateNotSignedInState();
                      Tools().navigatorPopAll();
                    }
                  },
                  onFirst: () async {
                    Tools().navigatorPopAllUntilHome();
                    Get.find<HomeTabController>().tabSwitcher(HomeTab.home);
                  },
                );
          }
        },
      ),
    ];
    return BlocProvider<OwnerBloc>(
        bloc: OwnerBloc(),
        child: Scaffold(
          appBar: MBAppBar(
            title: Text(
              'APPLICATION_MOBILE_LABEL_PROFILE'.tr(),
              style: Theme.of(context)
                  .textTheme
                  .headline5
                  ?.copyWith(fontSize: 20, fontWeight: FontWeight.w700),
            ),
          ),
          body: SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(
                      top: 10.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const OwnerProfileCard(),
                        // prev profile page widgets:
                        // const ProfileSection(),
                        // const AppSection(),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              children: profileItems
                                  .map<Widget>(
                                      (item) => makeProfileItem(context, item))
                                  .toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const FooterSection(),
              ],
            ),
          ),
        ));
  }

  SizedBox makeProfileItem(
    BuildContext context,
    ProfileItem item,
  ) {
    return SizedBox(
      width: double.infinity,
      child: TextButton.icon(
        style: ElevatedButton.styleFrom(
          primary: Colors.white,
          padding: const EdgeInsets.symmetric(
            horizontal: 25.0,
            vertical: 17.0,
          ),
          alignment: Alignment.centerLeft,
        ),
        onPressed: item.onPressed,
        icon: Image.asset(
          item.icon,
          width: 21.0,
          height: 18.0,
        ),
        label: Text(
          item.label,
          style: Theme.of(context).textTheme.bodyText1!.copyWith(
                fontWeight: FontWeight.w400,
              ),
        ),
      ),
    );
  }
}
