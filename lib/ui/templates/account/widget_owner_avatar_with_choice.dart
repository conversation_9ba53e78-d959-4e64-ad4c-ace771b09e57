import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/owner_bloc.dart';
import 'package:mybuddy/blocs/pet_bloc.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';


class OwnerAvatarWithChoice extends StatelessWidget {
  /// need to be wrapped by [BlocProvider] of [PetBloc]
  const OwnerAvatarWithChoice({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final OwnerBloc bloc = BlocProvider.of<OwnerBloc>(context);

    return StreamBuilder<Owner>(
      stream: bloc.stream,
      builder: (BuildContext context, AsyncSnapshot<Owner> snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }
        Owner owner = snapshot.data!;

        return MBAvatar(
          size: 90.0,
          imageUrl: owner.privateAvatarUrl,
          noImageUrl: Image.asset("assets/images/user_placeholder.jpg",fit: BoxFit.contain,),
          stackedChild: Container(
            padding: const EdgeInsets.all(6.0),
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.photo_camera_outlined,
              size: 15.0,
            ),
          ),
          onTap: () async {
            File? result = await Tools().image.pickAndCropAvatar(
                  context,
                  entity: owner,
                );
            if (result != null) {
              unawaited(MbApiOwner().updateUserRequest(
                context,
                owner: owner,
                avatar: result,
              ));
            }
            bloc.update();
          },
        );
      },
    );
  }
}
