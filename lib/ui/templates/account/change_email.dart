import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/account/change_password.dart';

import '../../../controllers/change_email_controller.dart';
import 'forgot_password.dart';

class ChangeEmail extends StatefulWidget {
  const ChangeEmail({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ChangeEmailState();
}

class _ChangeEmailState extends State<ChangeEmail> {
  final ChangeEmailController _controller = Get.put(ChangeEmailController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFBFBFB),
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_TITLE_CHANGE_EMAIL'.tr("Change Email"),
          style: Theme.of(context).textTheme.headline6,
        ),
      ),
      bottomNavigationBar: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 15),
        child: ElevatedButton(
          onPressed: _controller.changeEmail,
          child: Text(
            'APPLICATION_MOBILE_BUTTON_TEXT_SAVE_CHANGES'.tr(),
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ),
      body: Builder(
        builder: (context) {
          return SingleChildScrollView(
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.only(
                top: 25.0,
                left: 20.0,
                right: 20.0,
              ),
              child: Form(
                key: _controller.formKey,
                child: Column(
                  children: [
                    const SizedBox(height: 20.0),
                    TextFormField(
                      decoration: InputDecoration(
                        hintText:
                            'APPLICATION_MOBILE_FIELD_LABEL_CURRENT_EMAIL_ADDRESS'
                                .tr(),
                        labelText:
                            'APPLICATION_MOBILE_FIELD_LABEL_CURRENT_EMAIL_ADDRESS'
                                .tr(),
                      ),
                      initialValue: _controller.email,
                      enabled: false,
                    ),
                    const SizedBox(
                      height: 20.0,
                    ),
                    TextFormField(
                      decoration: InputDecoration(
                        hintText:
                            'APPLICATION_MOBILE_FIELD_LABEL_NEW_EMAIL_ADDRESS'
                                    .tr() +
                                '*',
                        labelText:
                            'APPLICATION_MOBILE_FIELD_LABEL_NEW_EMAIL_ADDRESS'
                                    .tr() +
                                '*',
                      ),
                      controller: _controller.newEmail,
                      keyboardType: TextInputType.emailAddress,
                      inputFormatters: [LengthLimitingTextInputFormatter(256)],
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      validator: (val) =>
                      val == null || val.isEmpty || !val.isEmail()
                          ? 'APPLICATION_MOBILE_MESSAGE_SERVICE_ERROR_MAIL'.tr()
                          : null,
                      onSaved: (val) => _controller.newEmail.text = val!,
                    ),
                    const SizedBox(height: 20.0),
                    Obx(()=>TextFormField(
                        decoration: InputDecoration(
                          hintText: 'APPLICATION_MOBILE_FIELD_LABEL_YOUR_PASSWORD'
                                  .tr() +
                              '*',
                          labelText:
                              'APPLICATION_MOBILE_FIELD_LABEL_YOUR_PASSWORD'
                                      .tr() +
                                  '*',
                          suffixIcon: IconButton(
                            onPressed: _controller.togglePasswordVisibility,
                            icon: Icon(_controller.hidePassword
                                ? FontAwesomeIcons.eyeSlash
                                : FontAwesomeIcons.eye),
                            iconSize: 16.0,
                            color: Theme.of(context).colorScheme.secondary,
                          ),
                        ),
                        controller: _controller.password,
                        obscureText: _controller.hidePassword,
                        inputFormatters: [LengthLimitingTextInputFormatter(32)],
                        validator: (val) => val == null ||
                                val.trim() == '' ||
                                val.length < 6
                            ? 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MIN_LENGTH'
                                .tr()
                            : null,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 20, bottom: 35),
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          Tools().navigatorPush(const ChangePassword());
                        },
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: Text(
                            'APPLICATION_MOBILE_TITLE_CHANGE_PASSWORD'.tr()+"?",
                            style: TextStyle(
                                color: Theme.of(context).colorScheme.secondary),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
