import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/home_stats_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/numberpicker.dart';

class SetDailyGoalsScreen extends StatefulWidget {
  const SetDailyGoalsScreen({Key? key}) : super(key: key);

  @override
  State<SetDailyGoalsScreen> createState() => _SetDailyGoalsScreenState();
}

class _SetDailyGoalsScreenState extends State<SetDailyGoalsScreen> {
  late Owner _owner;
  bool isLoading = false;
  late List<int> walks;
  late List<int> distance;
  late int walkSelected, distanceSelected;
  final HomeStatsController statsController = HomeStatsController.of();

  @override
  void initState() {
    super.initState();
    _owner = Data().get().owner;
    walks = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]; // In future can get it from server
    distance = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    distanceSelected = 2; // Setting default to 2
    walkSelected = 3; // Setting default to 3

    if (_owner.dailyWalkingGoalDistance != null) {
      distanceSelected = _owner.dailyWalkingGoalDistance! != 0
          ? _owner.dailyWalkingGoalDistance!
          : 2;
    }
    if (_owner.dailyWalkingGoalWalks != null) {
      walkSelected = _owner.dailyWalkingGoalWalks! != 0
          ? _owner.dailyWalkingGoalWalks!
          : 3;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xfffbfbfb),
      appBar: MBAppBar(
        iconTheme:
            IconTheme.of(context).copyWith(color: const Color(0xff162941)),
        title: Text(
          'APPLICATION_MOBILE_PROFILE_TITLE_DAILY_GOALS'.tr(),
          style: Theme.of(context).textTheme.headline6,
        ),
      ),
      body: SafeArea(
        child: Column(children: [
          const SizedBox(height: 20),
          _walkCard(),
          const SizedBox(height: 20),
          _distanceCard()
        ]),
      ),
      bottomNavigationBar: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 15),
        child: ElevatedButton(
          onPressed: isLoading
              ? null
              : () async {
                  await _updateUser();
                  statsController.updateTodaysGoals();
                  statsController.refreshHomeStats();
                },
          child: Text(
            'APPLICATION_MOBILE_PROFILE_SAVE_GOALS'.tr(),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
        ),
      ),
    );
  }

  Widget _walkCard() {
    return Card(
        child: Container(
      padding: const EdgeInsets.only(top: 20.0, left: 20.0, right: 20.0),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(
          'APPLICATION_MOBILE_PROFILE_WALK_COUNTS'.tr(),
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: const Color(0xff282828),
              fontSize: 16,
              fontWeight: FontWeight.w700),
        ),
        const SizedBox(height: 20),
        NumberCarousel(
            numbers: walks,
            onSelect: (number) {
              setState(() {
                walkSelected = number;
              });
            },
            selected: walkSelected),
        Center(
            child: Image.asset(
          "assets/images/ic_slider_pointer.png",
          width: 15,
          height: 20,
        ))
      ]),
    ));
  }

  Widget _distanceCard() {
    return Card(
        child: Container(
      padding: const EdgeInsets.only(top: 20.0, left: 20.0, right: 20.0),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'APPLICATION_MOBILE_FIELD_LABEL_DISTANCE'.tr(),
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: const Color(0xff282828),
                  fontSize: 16,
                  fontWeight: FontWeight.w700),
            ),
            Text(
              'APPLICATION_MOBILE_LABEL_MILES'.tr(),
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: const Color(0xff282828),
                  fontSize: 16,
                  fontWeight: FontWeight.w500),
            ),
          ],
        ),
        const SizedBox(height: 20),
        NumberCarousel(
            numbers: walks,
            onSelect: (number) {
              setState(() {
                distanceSelected = number;
              });
            },
            selected: distanceSelected),
        Center(
            child: Image.asset(
          "assets/images/ic_slider_pointer.png",
          width: 15,
          height: 20,
        ))
      ]),
    ));
  }

  Future<void> _updateUser() async {
    setState(() {
      isLoading = true;
    });

    _owner.dailyWalkingGoalDistance = distanceSelected;
    _owner.dailyWalkingGoalWalks = walkSelected;

    MBResponse response = await MbApiOwner().updateUserRequest(
      context,
      owner: _owner,
    );

    if (!response.success) {
      setState(() {
        isLoading = false;
      });
      return;
    }

    Tools().common.showMessage(
          context,
          "Goals Updated!",
          Theme.of(context).colorScheme.secondary,
        );

    Tools().navigatorPop(value: true);
  }
}

/// This class is a wrapper on top of Carousel to make it number specific
class NumberCarousel extends StatelessWidget {
  final List<int> numbers;
  final Function(int) onSelect;
  final int selected;
  final Axis axis;
  final bool showIcon;
  final TextStyle? textStyle;
  final TextStyle? selectedTextStyle;
  final String Function(int value)? trailingTextBuilder;

  /// last value alignment bugs out in vertical, set this on index to truncate values
  final double hideLastValue;

  const NumberCarousel({
    Key? key,
    required this.numbers,
    required this.onSelect,
    required this.selected,
    this.axis = Axis.horizontal,
    this.showIcon = true,
    this.textStyle,
    this.selectedTextStyle,
    this.trailingTextBuilder,
    this.hideLastValue = 1e9,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: NumberPicker(
        itemCount: numbers.length - 3,
        value: selected,
        minValue: numbers[0],
        maxValue: numbers[numbers.length - 1],
        itemHeight: axis == Axis.vertical ? 40 : 50,
        itemWidth: axis == Axis.vertical ? double.infinity : 40,
        axis: axis,
        step: 1,
        selectedTextStyle: selectedTextStyle ??
            Theme.of(context).textTheme.headline6!.copyWith(
                  color: const Color(0xff3B3C3F),
                  fontWeight: FontWeight.w500,
                  fontSize: 30,
                ),
        textStyle: textStyle ??
            Theme.of(context).textTheme.headline6!.copyWith(
                  color: const Color(0xffC5C3C3),
                  fontWeight: FontWeight.w400,
                  fontSize: 22,
                ),
        onChanged: (int number) {
          onSelect(number);
        },
        showIcon: showIcon,
        trailingTextBuilder: trailingTextBuilder,
        hideLastValue: hideLastValue.toInt(),
      ),
    );
  }
}
