import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/announcements_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/announcement.dart';
import 'package:mybuddy/ui/components/internet_widget_switcher.dart';
import 'package:mybuddy/ui/templates/announcement/announcement_page.dart';

import '../../components/pagination_widget.dart';

class WidgetAnnouncementList extends StatefulWidget {
  WidgetAnnouncementList({Key? key}) : super(key: key);

  @override
  State<WidgetAnnouncementList> createState() => _WidgetAnnouncementListState();
}

class _WidgetAnnouncementListState extends State<WidgetAnnouncementList> {
  final AnnouncementsController controller = Get.put(AnnouncementsController());

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    controller.initialFetch();
  }

  @override
  Widget build(BuildContext context) {
    return InternetWidgetSwitcher(
        onlineWidget: PaginationWidget(
          controller: controller,
          emptyWidget: _emptyWidget(context),
          child: (context, index) {
            return GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  Tools().navigatorPush(
                    AnnouncementPage(announcement: controller.dataList[index]),
                  );
                },
                child: oneAnnouncement(context, controller.dataList[index]));
          },
        ),
        retryMethod: controller.initialFetch);
  }

  Widget _emptyWidget(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Center(
          child: Text(
            'APPLICATION_MOBILE_TEXT_NO_ANNOUNCEMENTS'.tr("No Announcements"),
            style: Theme.of(context).textTheme.displayMedium?.copyWith(
                fontSize: 21, fontWeight: FontWeight.w700, color: Colors.black),
          ),
        ),
      ],
    );
  }

  Widget oneAnnouncement(BuildContext context, Announcement announcement) =>
      Stack(
        children: <Widget>[
          SizedBox(
            height: 160,
            width: MediaQuery.of(context).size.width,
            child: CachedNetworkImage(
              imageUrl: announcement.mainImageUrl,
              fit: BoxFit.cover,
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
          ),
          Positioned(
            bottom: 5,
            left: 8,
            right: 8,
            child: Text(
              announcement.title,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 15.0,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: <Shadow>[
                  Shadow(
                    offset: Offset(.1, 0.1),
                    blurRadius: 3.0,
                    color: Color.fromARGB(255, 0, 0, 0),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
}
