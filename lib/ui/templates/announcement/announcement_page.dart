import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_task.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/models/announcement.dart';
import 'package:mybuddy/ui/components/mb_html_widget.dart';
import 'package:mybuddy/ui/components/mb_text_shadow_widget.dart';

class AnnouncementPage extends StatefulWidget {
  final Announcement announcement;

  const AnnouncementPage({Key? key, required this.announcement})
      : super(key: key);

  @override
  _AnnouncementPageState createState() => _AnnouncementPageState();
}

class _AnnouncementPageState extends State<AnnouncementPage> {
  late ScrollController controller;
  late bool done;
  String? content;

  void readingListener() {
    double position = controller.position.pixels;
    double endPage = controller.position.maxScrollExtent;

    /// Prevent Unsupported operation: Infinity or NaN toInt
    if (endPage.isInfinite || endPage.isNaN || endPage == 0.0) {
      endPage = controller.position.viewportDimension;
    }

    if (!done && (position / endPage) > 0.5) {
      done = true;
      MbApiTask().setAnnouncementRead(widget.announcement.id);
    }
  }

  @override
  void initState() {
    super.initState();
    controller = ScrollController();
    done = false;

    content = widget.announcement.description?.replaceAll('&controls=0"', '');
    content = content?.replaceAll(
      '></iframe></div>',
      ' allowfullscreen="allowfullscreen"></iframe></div>',
    );
    content = content?.replaceAll(' href=', ' target="_blank" href=');

    /// considering content length less than 1500 characters
    /// has no scrolling behavior.
    /// automatically send setRead to api
    if (content == null || content!.length < 1500) {
      MbApiTask().setAnnouncementRead(widget.announcement.id);
    } else {
      controller.addListener(readingListener);
    }
  }

  @override
  void dispose() {
    controller.removeListener(readingListener);
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        controller: controller,
        slivers: <Widget>[
          ///First sliver is the App Bar
          SliverAppBar(
            ///Properties of app bar
            backgroundColor: Theme.of(context).primaryColorLight,
            leading: BackButton(
              color: Colors.white,
              onPressed: () => Tools().navigatorPop(),
            ),
            floating: false,
            pinned: false,
            expandedHeight: widget.announcement.imageId == null ? 0 : 295,

            ///Properties of the App Bar when it is expanded
            flexibleSpace: FlexibleSpaceBar(
              centerTitle: true,
              title: MBTextShadow(label: widget.announcement.title),
              background: widget.announcement.imageId == null
                  ? null
                  : CachedNetworkImage(
                      imageUrl: widget.announcement.mainImageUrl,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.error),
                    ),
            ),
          ),
          SliverSafeArea(
            top: false,
            minimum: const EdgeInsets.only(top: 8, right: 8, left: 8),
            sliver: SliverToBoxAdapter(
              child: content != null ? MBHtmlWidget(htmlSrc: content!) : null,
            ),
          ),
        ],
      ),
    );
  }
}
