import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/Api/mb_api_pims.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';

class ReconciliationOwnerPage extends StatefulWidget {
  final MBService service;

  const ReconciliationOwnerPage({Key? key, required this.service}) : super(key: key);

  @override
  _ReconciliationOwnerPageState createState() =>
      _ReconciliationOwnerPageState();
}

class _ReconciliationOwnerPageState extends State<ReconciliationOwnerPage> {
  late String _phone;
  late String _email;
  late String _pimsOwnerId;

  @override
  void initState() {
    super.initState();
    _phone = '';
    _email = '';
    _pimsOwnerId = '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_TITLE_USER_MANUAL_RECONCILIATION'.tr(),
          maxLines: 2,
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: <Widget>[
          Text(
            'APPLICATION_MOBILE_TXT_WHY_MANUAL_RECONCILIATION'.tr(),
            maxLines: null,
            textAlign: TextAlign.justify,
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 25.0),
          TextFormField(
            maxLines: 1,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              hintText:
                  'APPLICATION_MOBILE_FIELD_LABEL_OTHER_PHONE_NUMBER'.tr(),
              labelText:
                  'APPLICATION_MOBILE_FIELD_LABEL_OTHER_PHONE_NUMBER'.tr(),
            ),
            initialValue: _phone,
            inputFormatters: [LengthLimitingTextInputFormatter(256)],
            onChanged: (val) {
              setState(() {
                _phone = val.trim();
              });
            },
          ),
          const SizedBox(height: 15.0),
          TextFormField(
            maxLines: 1,
            keyboardType: TextInputType.emailAddress,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            decoration: InputDecoration(
              hintText:
                  'APPLICATION_MOBILE_FIELD_LABEL_OTHER_EMAIL_ADDRESS'.tr(),
              labelText:
                  'APPLICATION_MOBILE_FIELD_LABEL_OTHER_EMAIL_ADDRESS'.tr(),
            ),
            initialValue: _email,
            inputFormatters: [LengthLimitingTextInputFormatter(256)],
            onChanged: (val) {
              setState(() {
                _email = val.trim();
              });
            },
          ),
          const SizedBox(height: 15.0),
          TextFormField(
            maxLines: 1,
            decoration: InputDecoration(
              hintText: 'APPLICATION_MOBILE_FIELD_LABEL_USER_PIMS_ID'.tr(),
              labelText: 'APPLICATION_MOBILE_FIELD_LABEL_USER_PIMS_ID'.tr(),
            ),
            initialValue: _pimsOwnerId,
            inputFormatters: [LengthLimitingTextInputFormatter(256)],
            validator: (val) => val == null || val.isEmpty
                ? 'APPLICATION_MOBILE_LABEL_FIELDS_REQUIRED'.tr()
                : null,
            onChanged: (val) {
              setState(() {
                _pimsOwnerId = val.trim();
              });
            },
          ),
          Container(
            width: double.infinity,
            margin: const EdgeInsets.symmetric(vertical: 25.0),
            child: Builder(builder: (context) {
              return ElevatedButton(
                onPressed: (_pimsOwnerId != '' || _phone != '' || _email != '')
                    ? () => _submitForm(context)
                    : null,
                child: Text(
                  'APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr(),
                ),
              );
            }),
          ),
          Visibility(
            visible: widget.service.pimsId == 4,
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: <Widget>[
                    Text(
                      'APPLICATION_MOBILE_TITLE_LABEL_GIVE_CONSENT'
                          .tr()
                          .removeAllHtmlTags(),
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.only(top: 25.0),
                      child: ElevatedButton(
                        onPressed: () async {
                          MBResponse response = await MbApiPims()
                              .askVetopartnerConsent(context, widget.service);
                          if (response.success) {
                            await Tools().common.showSimpleDialog(
                                context,
                                'APPLICATION_MOBILE_BUTTON_LABEL_CONSENT_SENT'
                                    .tr());
                            Tools().navigatorPop();
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          primary: Theme.of(context).primaryColorLight,
                          textStyle: const TextStyle(fontSize: 14.0),
                        ),
                        child: Text(
                          'APPLICATION_MOBILE_BUTTON_LABEL_GIVE_CONSENT'
                              .tr()
                              .changeBrTag(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _submitForm(BuildContext context) async {
    MBResponse response = await MbApiPims().userReconciliationRequest(
        context, widget.service,
        pimsOwnerId: _pimsOwnerId, phone: _phone, email: _email);

    if (response.success) {
      Tools().navigatorPop();
    }
  }
}
