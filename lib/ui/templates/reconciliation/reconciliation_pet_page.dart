import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Api/mb_api_pet.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/reconciliation.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';

import 'widgets/pet_data_property_widget.dart';

class ReconciliationPetPage extends StatefulWidget {
  final MBService? service;
  final List<ReconcilablePet> pimsPets;
  final Pet? pet;

  const ReconciliationPetPage(
      {Key? key, this.service, required this.pimsPets, this.pet})
      : super(key: key);

  @override
  _ReconciliationPetPageState createState() => _ReconciliationPetPageState();
}

class _ReconciliationPetPageState extends State<ReconciliationPetPage> {
  ReconcilablePet? _pimsPet;

  @override
  void initState() {
    super.initState();
    _pimsPet = widget.pimsPets.singleWhereOrNull((pp) {
      return pp.data != null &&
          pp.data!.name.toLowerCase().trim() == widget.pet?.name.toLowerCase().trim();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_TITLE_RECONCILIATION_PET_LINK'.tr(),
          maxLines: 2,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: ListView(
          children: <Widget>[
            Card(
              child: ListTile(
                leading: const Icon(
                  FontAwesomeIcons.paw,
                  color: Colors.blue,
                ),
                title: Text(
                  widget.pet!.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
            Card(
              child: ListTile(
                leading: const Icon(FontAwesomeIcons.fileMedical),
                title: DropdownButton<ReconcilablePet>(
                  isExpanded: true,
                  value: _pimsPet,
                  items: widget.pimsPets.map((ReconcilablePet pimsPet) {
                    return DropdownMenuItem<ReconcilablePet>(
                      value: pimsPet,
                      child: Text(pimsPet.data?.name ?? ''),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _pimsPet = value;
                    });
                  },
                ),
                subtitle: Padding(
                  padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                  child: Visibility(
                    visible: _pimsPet?.data != null,
                    child: Builder(
                      builder: (context) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            PetDataPropertyWidget(
                              name: 'APPLICATION_MOBILE_LABEL_SPECIES'.tr(),
                              value: _pimsPet?.data?.speciesName,
                            ),
                            PetDataPropertyWidget(
                              name: 'APPLICATION_MOBILE_FIELD_LABEL_BREED'.tr(),
                              value: _pimsPet?.data?.breedName,
                            ),
                            PetDataPropertyWidget(
                              name: 'APPLICATION_MOBILE_FIELD_LABEL_BIRTHDATE'.tr(),
                              value: Data().dateTimeToUserDateStr(
                                  _pimsPet?.data?.birthDate),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.only(top: 20.0),
              child: Builder(
                builder: (context) => ElevatedButton(
                  onPressed: _pimsPet == null
                      ? null
                      : () {
                          _submitForm(context);
                        },
                  child: Text(
                    'APPLICATION_MOBILE_BUTTON_RECONCILIATION_PET_LINK'.tr(),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _submitForm(BuildContext context) async {
    if (_pimsPet != null) {
      MBResponse response = await MbApiPet()
          .editPetRequest(context, widget.pet!, pimsPet: _pimsPet);

      if (response.success) {
        Tools().navigatorPop();
      }
    }
  }
}
