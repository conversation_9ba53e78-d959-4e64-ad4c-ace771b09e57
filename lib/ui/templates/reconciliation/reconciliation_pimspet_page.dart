import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Api/mb_api_pet.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/reconciliation.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/pet/add_edit_pet.dart';
import 'widgets/pet_data_property_widget.dart';

enum Choice { none, existing, import }

class ReconciliationPimsPetPage extends StatefulWidget {
  final MBService? service;
  final ReconcilablePet? pimsPet;

  const ReconciliationPimsPetPage({Key? key, this.service, this.pimsPet})
      : super(key: key);

  @override
  _ReconciliationPimsPetPageState createState() =>
      _ReconciliationPimsPetPageState();
}

class _ReconciliationPimsPetPageState extends State<ReconciliationPimsPetPage> {
  late List<Pet> pets;
  late Choice importChoice;
  Pet? _pet;

  /// false existing animal, true import this animal

  @override
  void initState() {
    super.initState();
    pets = Data().getUnlinkPets(widget.service);
    _pet = pets.singleWhereOrNull((p) {
      return widget.pimsPet?.data != null &&
        widget.pimsPet!.data!.name.toLowerCase().trim() ==
            p.name.toLowerCase().trim();
    });
    importChoice = _pet != null ? Choice.existing : Choice.none;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_TITLE_RECONCILIATION_PIMSPET_LINK'.tr(),
          maxLines: 2,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: ListView(
          children: <Widget>[
            Card(
              child: ListTile(
                leading: const Icon(FontAwesomeIcons.fileMedical),
                title: Text(widget.pimsPet?.data?.name ?? ''),
                subtitle: Padding(
                  padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                  child: Visibility(
                    visible: widget.pimsPet?.data != null,
                    child: Builder(
                      builder: (BuildContext context) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            PetDataPropertyWidget(
                              name: 'APPLICATION_MOBILE_LABEL_SPECIES'.tr(),
                              value: widget.pimsPet?.data?.speciesName,
                            ),
                            PetDataPropertyWidget(
                              name: 'APPLICATION_MOBILE_FIELD_LABEL_BREED'.tr(),
                              value: widget.pimsPet?.data?.breedName,
                            ),
                            PetDataPropertyWidget(
                              name: 'APPLICATION_MOBILE_FIELD_LABEL_BIRTHDATE'.tr(),
                              value: Data().dateTimeToUserDateStr(
                                  widget.pimsPet?.data?.birthDate),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: DropdownButton<Choice>(
                isExpanded: true,
                value: importChoice,
                items: [
                  DropdownMenuItem<Choice>(
                    value: Choice.none,
                    child: Text(
                      'APPLICATION_MOBILE_LABEL_RECONCILIATION_SELECT_CHOICE'
                          .tr(),
                    ),
                  ),
                  DropdownMenuItem<Choice>(
                    value: Choice.existing,
                    child: Text(
                      'APPLICATION_MOBILE_FIELD_OPTION_PET_ALREADY_CREATED'.tr(),
                    ),
                  ),
                  DropdownMenuItem<Choice>(
                    value: Choice.import,
                    child:
                        Text('APPLICATION_MOBILE_FIELD_OPTION_CREATE_PET'.tr()),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      importChoice = value;
                    });
                  }
                },
              ),
            ),
            Visibility(
              visible: importChoice == Choice.existing,
              child: Card(
                child: ListTile(
                  leading: const Icon(FontAwesomeIcons.paw, color: Colors.blue),
                  title: DropdownButton<Pet>(
                    isExpanded: true,
                    value: _pet,
                    items: pets.map((Pet pet) {
                      return DropdownMenuItem<Pet>(
                        value: pet,
                        child: Text(pet.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _pet = value;
                      });
                    },
                  ),
                ),
              ),
            ),
            Visibility(
              visible: importChoice != Choice.none,
              child: Container(
                padding: const EdgeInsets.only(top: 20.0),
                child: Builder(
                  builder: (context) {
                    String labelCode;
                    labelCode = importChoice == Choice.existing
                        ? 'APPLICATION_MOBILE_BUTTON_RECONCILIATION_PET_LINK'
                        : 'APPLICATION_MOBILE_FIELD_OPTION_CREATE_PET';
                    return ElevatedButton(
                      onPressed:
                          (importChoice == Choice.existing && _pet != null) ||
                                  importChoice == Choice.import
                              ? () {
                                  _submitForm(context);
                                }
                              : null,
                      child: Text(labelCode.tr()),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _submitForm(BuildContext context) async {
    MBResponse response;
    if (importChoice == Choice.existing) {
      response = await MbApiPet().editPetRequest(
        context,
        _pet!,
        pimsPet: widget.pimsPet,
      );
      if (response.success) {
        Tools().navigatorPop();
      }
    } else {
      Tools().navigatorPop();
      await Tools().navigatorPush(
        AddEditPetForm(
          pimsPet: widget.pimsPet,
          title: 'APPLICATION_MOBILE_LABEL_RECONCILIATION_IMPORT'.tr() +
              (widget.pimsPet?.data?.name ?? ''),
        ),
      );
    }
  }
}
