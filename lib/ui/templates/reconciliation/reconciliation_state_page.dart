import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Api/mb_api_pims.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/reconciliation.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/reconciliation/reconciliation_owner_page.dart';
import 'package:mybuddy/ui/templates/reconciliation/reconciliation_pet_page.dart';
import 'package:mybuddy/ui/templates/reconciliation/reconciliation_pimspet_page.dart';

class ReconciliationStatePage extends StatefulWidget {
  const ReconciliationStatePage({Key? key}) : super(key: key);

  @override
  _ReconciliationStatePageState createState() =>
      _ReconciliationStatePageState();
}

class _ReconciliationStatePageState extends State<ReconciliationStatePage> {
  late List<MBService> _services;
  MBService? _service;
  late List<Pet> _pets;

  @override
  void initState() {
    super.initState();
    _services = Data().getPimsService();
    if (_services.isNotEmpty) {
      _service = _services.first;
    }
  }

  @override
  Widget build(BuildContext context) {
    /// need to be done each time to refresh pet state
    _pets = Data().getPets();
    return FutureBuilder<MBResponse?>(
      future: getPimsPetsForService(context, _service),
      builder: (BuildContext context, AsyncSnapshot<MBResponse?> snapshot) {
        List<ReconcilablePet> pimsPets = <ReconcilablePet>[];

        if (snapshot.hasData) {
          List? list = snapshot.data!.body['pets'] as List?;
          if (list != null) {
            pimsPets =
                list.map((data) => ReconcilablePet.fromJson(data)).toList();
          }
        }

        return Scaffold(
          appBar: MBAppBar(
            title: Text(
              'APPLICATION_MOBILE_TITLE_RECONCILIATION_STATE'.tr(),
              maxLines: 2,
            ),
          ),
          body: Padding(
            padding: const EdgeInsets.all(8.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Visibility(
                    visible: _services.length > 1,
                    child: Center(
                      child: DropdownButton<MBService>(
                        isExpanded: true,
                        value: _service,
                        items: _services.map((MBService service) {
                          return DropdownMenuItem<MBService>(
                            value: service,
                            child: Text(service.name ?? ''),
                          );
                        }).toList(),
                        onChanged: _onChangedDropDownItem,
                      ),
                    ),
                  ),
                  cardWidget(
                    icon: FontAwesomeIcons.hospital,
                    child: ListTile(
                      title: Text(
                        _service?.name ?? '',
                        maxLines: 2,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      trailing: Icon(
                        _service?.ownerReconciliated != null &&
                                _service!.ownerReconciliated!
                            ? FontAwesomeIcons.check
                            : FontAwesomeIcons.syncAlt,
                        color: _service?.ownerReconciliated != null &&
                                _service!.ownerReconciliated!
                            ? Colors.green
                            : Colors.red,
                      ),
                      onTap: () async {
                        if (_service?.ownerReconciliated != null &&
                            !_service!.ownerReconciliated!) {
                          await Tools().navigatorPush(
                            ReconciliationOwnerPage(
                              service: _service!,
                            ),
                          );
                          setState(() {});
                        }
                      },
                    ),
                  ),
                  Visibility(
                    visible: _service?.ownerReconciliated != null &&
                        _service!.ownerReconciliated! &&
                        _pets.isNotEmpty,
                    child: cardWidget(
                      icon: FontAwesomeIcons.paw,
                      child: Column(
                        children: _pets.map<Widget>(
                          (_pet) {
                            bool isReconciled = _pet.pimsServices
                                .any((ps) => ps.id == _service?.id);
                            return ListTile(
                              title: Text(
                                _pet.name,
                                maxLines: 2,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                              trailing: Icon(
                                isReconciled
                                    ? FontAwesomeIcons.check
                                    : (pimsPets.isNotEmpty
                                        ? FontAwesomeIcons.syncAlt
                                        : FontAwesomeIcons.times),
                                color: isReconciled ? Colors.green : Colors.red,
                              ),
                              onTap: () async {
                                if (!isReconciled && pimsPets.isNotEmpty) {
                                  await Tools().navigatorPush(
                                    ReconciliationPetPage(
                                      service: _service,
                                      pimsPets: pimsPets,
                                      pet: _pet,
                                    ),
                                  );

                                  setState(() {});
                                }
                              },
                            );
                          },
                        ).toList(),
                      ),
                    ),
                  ),
                  Visibility(
                    visible: _pets.isEmpty,
                    child: cardWidget(
                      icon: FontAwesomeIcons.paw,
                      child: ListTile(
                        title: Text(
                          'APPLICATION_MOBILE_LABEL_RECONCILIATION_NO_PET'.tr(),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Visibility(
                    visible: pimsPets.isNotEmpty,
                    child: cardWidget(
                      icon: FontAwesomeIcons.fileMedical,
                      child: Column(
                        children: pimsPets.map<Widget>(
                          (_pimsPet) {
                            return ListTile(
                              title: Text(
                                _pimsPet.data?.name ?? '',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              trailing: const Icon(
                                FontAwesomeIcons.plus,
                                color: Colors.orange,
                              ),
                              onTap: () async {
                                await Tools().navigatorPush(
                                  ReconciliationPimsPetPage(
                                    service: _service,
                                    pimsPet: _pimsPet,
                                  ),
                                );
                                setState(() {});
                              },
                            );
                          },
                        ).toList(),
                      ),
                    ),
                  ),
                  Visibility(
                    visible: snapshot.connectionState == ConnectionState.done &&
                        pimsPets.isEmpty,
                    child: cardWidget(
                      icon: FontAwesomeIcons.fileMedical,
                      child: ListTile(
                        title: Text(
                          'APPLICATION_MOBILE_LABEL_RECONCILIATION_NO_PIMSPET'
                              .tr(),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget cardWidget({IconData? icon, required Widget child}) => Card(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.only(right: 20.0),
                child: Icon(
                  icon,
                  color: Colors.blue,
                ),
              ),
              Expanded(
                child: child,
              ),
            ],
          ),
        ),
      );

  Future<MBResponse?> getPimsPetsForService(
      BuildContext context, MBService? service) async {
    if (service != null &&
        service.ownerReconciliated != null &&
        service.ownerReconciliated!) {
      return await MbApiPims().getPimsState(context, service);
    }
    return null;
  }

  void _onChangedDropDownItem(MBService? newService) {
    if (_service != newService) {
      setState(() {
        _service = newService;
      });
    }
  }
}
