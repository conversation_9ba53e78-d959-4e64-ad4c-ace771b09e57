import 'package:flutter/material.dart';

class PetDataPropertyWidget extends StatelessWidget {
  final String? value;
  final String name;
  const PetDataPropertyWidget({Key? key, this.value, required this.name}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (value == null || value!.trim() == '') {
      return Container();
    }
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(name),
          Text(
            value!,
            style: const TextStyle(fontWeight: FontWeight.bold),
          )
        ],
      ),
    );
  }
}
