import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/class/constants.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/ui/templates/onboarding/pet_selection_page.dart';

import '../../../controllers/onlogin_controller.dart';
import 'charity_selection_page.dart';

class OnLoginPage extends StatefulWidget {
  const OnLoginPage({Key? key}) : super(key: key);

  @override
  _OnLoginPageState createState() => _OnLoginPageState();
}

class _OnLoginPageState extends State<OnLoginPage> {
  final OnLoginController controller = OnLoginController.of();

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        if (controller.currentPage > 0) {
          controller.previousPage();
        }
        return Future.value(false);
      },
      child: Scaffold(
        body: Obx(
          () => PageView(
            controller: controller.pageController,
            physics: const NeverScrollableScrollPhysics(),
            children: _getPages(),
          ),
        ),
      ),
    );
  }

  List<Widget> _getPages() {
    return [
      if (controller.isShelterNotSelected) _addShelterPage(),
      if (controller.pets.isEmpty && shouldShowPetOption) _addDogWoofTrax(),
    ];
  }

  Widget _addDogWoofTrax() {
    return PetSelectionPage();
  }

  Widget _addShelterPage() {
    return const CharitySelectionPage(
      fromSettings: false,
    );
  }

  bool get shouldShowPetOption =>
      SettingsDelegate().prefs.getBool(isFirstLogin) ?? false;
}
