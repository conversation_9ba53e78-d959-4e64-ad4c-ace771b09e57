import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/charity_selection_controller.dart';
import 'package:mybuddy/controllers/onlogin_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/pagination_widget.dart';

import '../../../models/owner.dart';
import '../../../models/shelter.dart';

class CharitySelectionPage extends StatefulWidget {
  const CharitySelectionPage({Key? key, this.fromSettings = true})
      : super(key: key);
  final bool fromSettings;

  @override
  State<CharitySelectionPage> createState() => _CharitySelectionPageState();
}

class _CharitySelectionPageState extends State<CharitySelectionPage> {
  late CharitySelectionController _charitySelectionController;
  late OnLoginController controller;

  @override
  void initState() {
    super.initState();
    _charitySelectionController = CharitySelectionController.of();
    controller = OnLoginController.of();
  }

  Future<void> updateCharity() async {
    await _charitySelectionController.updateShelter();
    if (controller.pets.isNotEmpty || widget.fromSettings) {
      Tools().navigatorPop(value: true);
    } else {
      controller.nextPage();
    }
  }

  @override
  Widget build(BuildContext context) {
    Owner _owner = Data().data?.owner ?? Owner();
    return Scaffold(
      backgroundColor: const Color(0xFFFBFBFB),
      appBar: AppBar(
        title: Text(
          'Animal Charity',
          style: Theme.of(context).textTheme.headline6,
        ),
        leading: widget.fromSettings
            ? IconButton(
                icon: const Icon(Icons.arrow_back_ios),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              )
            : const SizedBox.shrink(),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.fromLTRB(15, 10, 15, 25),
        child: Obx(
          () => ElevatedButton(
            onPressed: _charitySelectionController.isShelterChanged
                ? updateCharity
                : !widget.fromSettings &&
                        _charitySelectionController.selectedShelter.id !=
                            Shelter.none().id
                    ? updateCharity
                    : null,
            child: Text(
              'APPLICATION_MOBILE_BUTTON_LABEL_SAVE'.tr(),
            ),
          ),
        ),
      ),
      body: Container(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_owner.shelter != null)
              RichText(
                text: TextSpan(
                  text: 'Your animal charity: ',
                  style: const TextStyle(
                    color: Color(0xff8C8C8C),
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                  ),
                  children: [
                    TextSpan(
                      text:
                          '"${_owner.shelter!.name} ${_owner.shelter!.city} ${_owner.shelter!.state}"',
                      style: const TextStyle(
                        color: Color(0xff192943),
                        fontSize: 15,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              )
            else
              const Text(
                'Select an animal charity to walk for',
                style: TextStyle(
                  color: Color(0xff192943),
                  fontSize: 15,
                  fontWeight: FontWeight.w700,
                ),
              ),
            const SizedBox(height: 15),
            TextField(
              decoration: InputDecoration(
                prefixIcon: const Icon(Icons.search, color: Color(0xff25323B)),
                hintText: 'WOOFTRAX_SEARCH_SHELTER_LABEL'
                    .tr('Search by Name, State, City, or Zip Code'),
              ),
              style: const TextStyle(
                color: Color(0xff25323B),
                fontSize: 13,
                fontWeight: FontWeight.w400,
              ),
              keyboardType: TextInputType.text,
              onChanged: (value) =>
                  _charitySelectionController.searchText = value,
            ),
            const SizedBox(height: 15),
            Expanded(child: _getList(context)),
          ],
        ),
      ),
    );
  }

  Widget _getList(BuildContext context) {
    return PaginationWidget(
        controller: _charitySelectionController,
        child: (context, index) {
          return Obx(() {
            final Shelter shelter = _charitySelectionController.dataList[index];
            bool isSelected =
                _charitySelectionController.isShelterSelected(shelter.id);
            return Card(
              margin: const EdgeInsets.symmetric(vertical: 5),
              shape: isSelected
                  ? RoundedRectangleBorder(
                      side: BorderSide(
                          color: Theme.of(context).colorScheme.secondary),
                      borderRadius:
                          const BorderRadius.all(Radius.circular(8.0)),
                    )
                  : null,
              child: ListTile(
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8.0)),
                ),
                // dense: true,
                minVerticalPadding: 8.0,
                title: Text(
                  shelter.name,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      color: const Color(0xff474747),
                      fontWeight: FontWeight.w700,
                      fontSize: 15),
                ),
                subtitle: Text(
                  '${shelter.city}, ${shelter.state}, ${shelter.zipCode ?? ''}',
                  style: Theme.of(context).textTheme.subtitle2?.copyWith(
                      color: const Color(0xff5A5A5A),
                      fontWeight: FontWeight.w400,
                      fontSize: 12),
                ),
                // isThreeLine: true,
                trailing: isSelected
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.secondary,
                      )
                    : null,
                selected: isSelected,
                selectedTileColor: const Color(0xffDEF9ED),
                onTap: () {
                  _charitySelectionController.selectedShelter = shelter;
                },
              ),
            );
          });
        });
  }
}
