import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:introduction_screen/introduction_screen.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

import '../../../controllers/root_controller.dart';

class DiscoverWoofTraxPage extends StatefulWidget {
  const DiscoverWoofTraxPage({Key? key}) : super(key: key);

  @override
  _DiscoverWoofTraxPageState createState() => _DiscoverWoofTraxPageState();
}

class _DiscoverWoofTraxPageState extends State<DiscoverWoofTraxPage> {
  final introKey = GlobalKey<IntroductionScreenState>();

  void _onIntroEnd(context) {
    RootController.of.initiateNotSignedInState();
  }

  @override
  Widget build(BuildContext context) {
    const List<BoxShadow> boxShadow = [
      BoxShadow(
        color: Color(0x1f000000),
        offset: Offset(0, 3),
        blurRadius: 6,
        spreadRadius: -4,
      ),
      BoxShadow(
        color: Color(0x14000000),
        offset: Offset(0, 6),
        blurRadius: 16,
      ),
      BoxShadow(
        color: Color(0x0D000000),
        offset: Offset(0, 9),
        blurRadius: 28,
        spreadRadius: 8,
      )
    ];

    return IntroductionScreen(
      key: introKey,
      pages: [
        createPageView(
          'assets/images/onboarding_1.svg',
          'APPLICATION_MOBILE_ON_BOARDING_01_TITLE'.tr(),
          'APPLICATION_MOBILE_ON_BOARDING_01_SUB_TITLE'.tr(),
        ),
        createPageView(
          'assets/images/onboarding_2.svg',
          'APPLICATION_MOBILE_ON_BOARDING_02_TITLE'.tr(),
          'APPLICATION_MOBILE_ON_BOARDING_02_SUB_TITLE'.tr(),
        ),
        createPageView(
          'assets/images/onboarding_3.svg',
          'APPLICATION_MOBILE_ON_BOARDING_03_TITLE'.tr(),
          'APPLICATION_MOBILE_ON_BOARDING_03_SUB_TITLE'.tr(),
        ),
        /* Not in UX
        PageViewModel(
          image: const SizedBox.expand(),
          title: 'Convert your PetSmiles into money !',
          body: 'And support an animal association',
          decoration: pageDecoration,
        ),
        */
      ],
      scrollPhysics: const BouncingScrollPhysics(),
      onDone: () => _onIntroEnd(context),
      onSkip: () => _onIntroEnd(context),
      showSkipButton: true,
      controlsMargin: const EdgeInsets.only(bottom: 10),
      skipStyle: TextButton.styleFrom(alignment: Alignment.centerLeft),
      skip: const Text(
        'Skip',
        style: TextStyle(color: Colors.white, fontSize: 13),
      ),
      overrideNext: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          GestureDetector(
            onTap: () {
              introKey.currentState?.next();
            },
            child: Container(
              height: 36,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: boxShadow,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: const [
                  Text(
                    'Next',
                    style: TextStyle(
                      color: Color(0xff218359),
                      fontSize: 13,
                    ),
                  ),
                  SizedBox(width: 5),
                  Icon(
                    FontAwesomeIcons.chevronRight,
                    color: Color(0xff218359),
                    size: 14,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      overrideDone: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          GestureDetector(
            onTap: () {
              _onIntroEnd(context);
            },
            child: Container(
              height: 36,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                gradient: const LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Color(0xffFFC780),
                    Color(0xffF3AE56),
                  ],
                ),
                boxShadow: boxShadow,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: const [
                  Text(
                    'Done',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 13,
                    ),
                  ),
                  SizedBox(width: 5),
                  Icon(
                    FontAwesomeIcons.chevronRight,
                    color: Colors.white,
                    size: 14,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      globalBackgroundColor: const Color(0xff218359),
      dotsDecorator: const DotsDecorator(
        size: Size.square(6),
        spacing: EdgeInsets.all(3),
        color: Colors.white,
        activeColor: Color(0xffF3AE56),
        activeSize: Size.square(8),
      ),
    );
  }

  PageViewModel createPageView(String bgImage, String title, String subtitle) {
    return PageViewModel(
      image: Center(
        child: SvgPicture.asset(
          bgImage,
          fit: BoxFit.cover,
        ),
      ),
      titleWidget: RichText(
        text: TextSpan(
          style: const TextStyle(
            color: Color(0xff218359),
            fontSize: 24,
          ),
          children: [
            TextSpan(
              text: title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            TextSpan(
              text: subtitle,
            ),
          ],
        ),
      ),
      body: '',
      decoration: const PageDecoration(
        titlePadding: EdgeInsets.only(top: 0, left: 7, right: 7),
        bodyFlex: 6,
        fullScreen: true,
      ),
    );
  }
}
