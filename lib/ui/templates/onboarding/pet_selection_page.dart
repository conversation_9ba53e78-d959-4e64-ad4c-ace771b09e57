import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/pet/add_edit_pet.dart';

import '../../../controllers/onlogin_controller.dart';

class PetSelectionPage extends StatelessWidget {
  PetSelectionPage({Key? key}) : super(key: key);
  final OnLoginController controller = OnLoginController.of();

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).size.height;
    const textStyle = TextStyle(color: Colors.grey, fontSize: 16);

    return Material(
      child: Container(
        height: height,
        padding: const EdgeInsets.fromLTRB(25, 0, 25, 0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(),
            Text(
              'WOOFTRAX_WALK_WITH_DOG_DESCRIPTION'
                  .tr('Let’s add your first walking companion.'),
              textAlign: TextAlign.center,
              style: textStyle,
            ),
            const SizedBox(height: 25),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  await Tools().navigatorPush(AddEditPetForm(onLogin: true));
                  controller.assignPets();
                  if (!controller.isShelterNotSelected) {
                    Tools().navigatorPop(value: true);
                  }
                },
                child: Text(
                  'WOOFTRAX_ADD_A_DOG'.tr('Add a Dog'),
                ),
              ),
            ),
            SizedBox(height: height * 0.05),
            Row(
              children: <Widget>[
                const Expanded(child: Divider(thickness: 1)),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text('APPLICATION_MOBILE_OR'.tr('OR'),
                      style: const TextStyle(
                        color: Colors.grey,
                        fontWeight: FontWeight.w700,
                        fontSize: 17,
                      )),
                ),
                const Expanded(child: Divider(thickness: 1)),
              ],
            ),
            SizedBox(height: height * 0.05),
            ClipRRect(
              borderRadius: BorderRadius.circular(75),
              child: Image.asset(
                'assets/images/ic_dog_lexi.png',
                width: height * 0.16,
                height: height * 0.16,
              ),
            ),
            const SizedBox(height: 25),
            Text(
              'WOOFTRAX_WALK_WITH_LEXI_DESCRIPTION'.tr(
                  'Don\'t have a dog? You can choose to walk with Lexi to help animal charities everywhere.'),
              textAlign: TextAlign.center,
              style: textStyle,
            ),
            const SizedBox(height: 25),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Tools().navigatorPop(value: true);
                },
                child: Text(
                  'WOOFTRAX_WALK_WITH_LEXI'.tr('Walk with Lexi'),
                ),
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }
}
