import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/onboarding/set_password_page.dart';

import '../../../Tools/tools_migration.dart';
import '../../../controllers/root_controller.dart';
import '../../../controllers/social_login_controller.dart';
import '../../components/landing_page_bottom_sheet.dart';

class LandingPage extends StatefulWidget {
  const LandingPage({Key? key}) : super(key: key);

  @override
  State<LandingPage> createState() => _LandingPageState();
}

class _LandingPageState extends State<LandingPage> {
  @override
  void initState() {
    super.initState();
    _checkIfWoofTraxUser();
  }

  /// Check if it is a WoofTrax user by looking into the SharedPreferences,
  /// if user email exists then this is a WoofTrax user and we navigate to
  /// the password reset screen
  void _checkIfWoofTraxUser() async {
    MigrationXamarinToFlutter obj = MigrationXamarinToFlutter();
    Map<String, String?> userInfo = await obj.getWooftraxUserInfo();
    String? email = userInfo["email"];

    if (email != null) {
      await Future.delayed(const Duration(milliseconds: 200));
      Tools().common.showDialogWithLoading();
      await Future.delayed(const Duration(seconds: 1));

      String? firstName = userInfo["firstName"];
      String? lastName = userInfo["lastName"];
      String? objectId = userInfo["objectId"];

      Tools().navigatorPop();

      Tools().navigatorPush(SetPasswordPage(
          email: email,
          objectId: objectId!,
          firstName: firstName!,
          lastName: lastName!));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  'assets/images/bg_landing_page.png',
                ),
                fit: BoxFit.cover,
              ),
            ),
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              child: woofTraxHeader(context),
            ),
          ),
          LandingPageBottomWidget(),
        ],
      ),
    );
  }

  Widget woofTraxHeader(BuildContext context) => Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          const Spacer(),
          Image.asset(
            "assets/images/ic_landing_wooftrax.png",
            width: 230,
            height: 60,
          ),
          Container(
            width: MediaQuery.of(context).size.width * 3 / 5,
            margin: const EdgeInsets.symmetric(vertical: 10.0),
            child: Text(
              'APPLICATION_MOBILE_LOGIN_PAGE_DESCRIPTION'.tr(),
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontSize: 20.0,
                  fontWeight: FontWeight.normal),
            ),
          ),
          const SizedBox(height: 15),
          OutlinedButton(
            onPressed: () {
              SocialLoginController.of().hideErrorAndPerformSpecificAction(
                  () => RootController.of.initiateOnBoardingState());
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.white,
              padding: const EdgeInsets.only(
                  top: 15.0, bottom: 15.0, left: 40.0, right: 40.0),
              side: const BorderSide(color: Colors.white),
            ),
            child: Text(
              'APPLICATION_MOBILE_LABEL_DISCOVER'.tr('Discover'),
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(color: Colors.white, fontSize: 16.0),
            ),
          ),
          const Spacer(flex: 4),
        ],
      );
}
