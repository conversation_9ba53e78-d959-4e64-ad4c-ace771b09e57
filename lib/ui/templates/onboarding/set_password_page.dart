import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_migration.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

/// This screen is responsible for allowing the user to set a password for its
/// WoofTrax email. As this screen will be shown once when the user upgrades the
/// legacy Xamarin app to Flutter based app.
class SetPasswordPage extends StatefulWidget {
  final String email;
  final String objectId;
  final String firstName;
  final String lastName;

  const SetPasswordPage(
      {Key? key,
      required this.email,
      required this.firstName,
      required this.objectId,
      required this.lastName})
      : super(key: key);

  @override
  _SetPasswordPageState createState() => _SetPasswordPageState();
}

class _SetPasswordPageState extends State<SetPasswordPage> {
  bool _passwordSubmitted = false;
  bool _isSubmitting = false;
  late bool _passwordVisible;
  late bool _confirmPasswordVisible;
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final GlobalKey<FormState> _loginFormKey = GlobalKey<FormState>();
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    _passwordVisible = false;
    _confirmPasswordVisible = false;
    super.initState();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () {
          return Future.value(false);
        },
        child: Scaffold(
          bottomNavigationBar: Padding(
            padding: const EdgeInsets.only(bottom: 35),
            child: _passwordSubmitted
                ? _continueButton(context)
                : _submitButton(context),
          ),
          body: SafeArea(
            child: Container(
              color: const Color(0xFFFBFBFB),
              child: Container(child: showCurrentWidget(context)),
            ),
          ),
        ));
  }

  Widget showCurrentWidget(BuildContext context) {
    return _passwordSubmitted
        ? _userPasswordSetSuccess(context)
        : _askUserPassword(context);
  }

  Widget _askUserPassword(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Column(
      children: [
        Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(width: 0.5, color: Color(0x50757575)),
            ),
          ),
          width: double.infinity,
          height: 60.0,
          child: Center(
            child: Text('APPLICATION_MOBILE_NEW_WELCOME_TITLE'.tr(),
                style: theme.textTheme.headline5),
          ),
        ),
        Expanded(
          child: Container(
            margin: const EdgeInsets.all(15),
            child: Form(
              key: _loginFormKey,
              child: ListView(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 40.0),
                    child: Text(
                        "${'APPLICATION_MOBILE_NEW_WELCOME_HEY'.tr()} ${widget.firstName} ${widget.lastName}!",
                        style: theme.textTheme.headline6),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 30.0),
                    child: Text('APPLICATION_MOBILE_NEW_WELCOME_HEADER'.tr(),
                        style: theme.textTheme.bodyText2),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 50.0),
                    child: Text('APPLICATION_MOBILE_NEW_WELCOME_BODY'.tr(),
                        style: theme.textTheme.bodyText2),
                  ),
                  _formEmailField(context),
                  _formPasswordField(context),
                  _formConfirmPasswordField(context),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _formEmailField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 35.0),
      child: TextFormField(
        decoration: InputDecoration(
          hintText: 'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'.tr(),
          labelText: 'APPLICATION_MOBILE_FIELD_LABEL_EMAILADDRESS'.tr(),
        ),
        initialValue: widget.email,
        enabled: false,
      ),
    );
  }

  Widget _formPasswordField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 35.0),
      child: AutofillGroup(
        child: TextFormField(
          controller: _passwordController,
          maxLines: 1,
          autofillHints: const [AutofillHints.password],
          textInputAction: TextInputAction.done,
          obscureText: !_passwordVisible,
          decoration: InputDecoration(
            hintText: 'APPLICATION_MOBILE_FIELD_LABEL_USER_PASSWORD'.tr(),
            labelText: 'APPLICATION_MOBILE_FIELD_LABEL_USER_PASSWORD'.tr(),
            suffixIcon: IconButton(
              icon: Icon(
                // Based on passwordVisible state choose the icon
                _passwordVisible
                    ? FontAwesomeIcons.eye
                    : FontAwesomeIcons.eyeSlash,
                color: Theme.of(context).primaryColor,
              ),
              onPressed: () {
                // Update the state i.e. toogle the state of passwordVisible variable
                setState(() {
                  _passwordVisible = !_passwordVisible;
                });
              },
            ),
          ),
          onFieldSubmitted: (val) {
            focusNode.requestFocus();
          },
          validator: (val) {
            if (val == null || val.trim() == '' || val.length < 6) {
              return 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MIN_LENGTH'
                  .tr();
            }
            return null;
          },
        ),
      ),
    );
  }

  Widget _formConfirmPasswordField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 35.0),
      child: AutofillGroup(
        child: TextFormField(
          focusNode: focusNode,
          controller: _confirmPasswordController,
          maxLines: 1,
          autofillHints: const [AutofillHints.password],
          textInputAction: TextInputAction.done,
          obscureText: !_confirmPasswordVisible,
          decoration: InputDecoration(
            hintText:
                'APPLICATION_MOBILE_FIELD_LABEL_USER_CONFIRM_PASSWORD'.tr(),
            labelText:
                'APPLICATION_MOBILE_FIELD_LABEL_USER_CONFIRM_PASSWORD'.tr(),
            suffixIcon: IconButton(
              icon: Icon(
                // Based on passwordVisible state choose the icon
                _confirmPasswordVisible
                    ? FontAwesomeIcons.eye
                    : FontAwesomeIcons.eyeSlash,
                color: Theme.of(context).primaryColor,
              ),
              onPressed: () {
                // Update the state i.e. toogle the state of passwordVisible variable
                setState(() {
                  _confirmPasswordVisible = !_confirmPasswordVisible;
                });
              },
            ),
          ),
          validator: (val) {
            if (val == null || val.trim() == '' || val.length < 6) {
              return 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MIN_LENGTH'
                  .tr();
            } else if (val != _passwordController.text) {
              return 'APPLICATION_MOBILE_MESSAGE_USER_FORM_PASSWORD_MATCH'.tr();
            }
            return null;
          },
        ),
      ),
    );
  }

  Widget _submitButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 15, right: 15),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () => _isSubmitting ? null : _submitData(context),
          child: Text(
            'APPLICATION_MOBILE_NEW_WELCOME_SAVE_PASSWORD'.tr(),
          ),
        ),
      ),
    );
  }

  void _submitData(BuildContext context) async {
    setState(() {
      _isSubmitting = true;
    });
    FormState? form = _loginFormKey.currentState;
    if (form == null || !form.validate()) {
      setState(() {
        _isSubmitting = false;
      });
      return;
    }
    form.save();

    // Before calling the initializeWtUserPassword api call we check if the
    // password is required to be set
    // Do the password reset
    MBResponse response = await MbApiOwner().initializeWtUserPassword(context, {
      'email': widget.email,
      'objectId': widget.objectId,
      'password': _passwordController.text,
    });

    if (response.success) {
      MBResponse loginResponse = await MbApiOwner().loginRequest(
          context, widget.email, _passwordController.text,
          refresh: false);
      if (loginResponse.success) {
        await MigrationXamarinToFlutter().clearWooftraxUserInfo();
        setState(() {
          _passwordSubmitted = true;
        });
      }
    }
    setState(() {
      _isSubmitting = false;
    });
  }

  Widget _userPasswordSetSuccess(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      height: double.infinity,
      width: double.infinity,
      margin: const EdgeInsets.only(right: 15.0, left: 15.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/ic_tick_with_circle.png',
            fit: BoxFit.fitWidth,
            width: 80.0,
            height: 80.0,
          ),
          Padding(
            padding: const EdgeInsets.only(top: 60.0),
            child: Text('APPLICATION_MOBILE_NEW_WELCOME_PASWORD_SAVED'.tr(),
                style: theme.textTheme.headline6),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 30.0),
            child: Text('APPLICATION_MOBILE_PASSWORD_SAVED_SUCCESS_BODY'.tr(),
                style: theme.textTheme.bodyText2),
          ),
        ],
      ),
    );
  }

  Widget _continueButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 15, right: 15, bottom: 20),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _navigateToHome(),
        child: Text(
          'APPLICATION_MOBILE_CONTINUE'.tr(),
        ),
      ),
    );
  }

  void _navigateToHome() {
    Tools().navigatorToHome(onLogin: false);
  }
}
