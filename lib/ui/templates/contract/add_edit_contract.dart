import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:mybuddy/Api/mb_api_pet_activity.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_pet_multi_form_field.dart';

class AddEditContract extends StatefulWidget {
  final String title;
  final Pet? pet;
  final PetActivity? contract;
  final int? contractType;

  const AddEditContract({Key? key, this.contractType = 1, this.contract, this.pet, required this.title})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _AddEditContractState();
}

class _AddEditContractState extends State<AddEditContract> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final RegExp _regexp = RegExp('[^A-Za-z0-9 ]');

  late PetActivity _contract;
  late List<Pet> pets;
  late TextEditingController _controllerDateStart;
  late TextEditingController _controllerDateEnd;
  late List<MBService> services;
  late bool isNew;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(widget.title, maxLines: 2),
        leading: BackButton(
          onPressed: () => Tools().navigatorPop(value: false),
        ),
      ),
      persistentFooterButtons: [
        SizedBox(
          width: double.infinity,
          child: Builder(
            builder: (context) => ElevatedButton(
              onPressed: () {
                _submitForm(context);
              },
              child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr()),
            ),
          ),
        ),
      ],
      body: SafeArea(
        top: false,
        bottom: false,
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.disabled,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              children: <Widget>[
                const SizedBox(height: 15.0,),
                MBPetMultiFormField(
                  validator: (val) {
                    if (_contract.pets.isEmpty) {
                      return 'APPLICATION_MOBILE_HB_ERROR_NO_PET'.tr();
                    }
                    return null;
                  },
                  initialValue: _contract.pets,
                  onSaved: (value) {
                    if (value == null) return;
                    setState(() {
                      _contract.pets = List<Pet>.from(value);
                    });
                  },
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_TITLE'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_TITLE'.tr() + '*',
                  ),
                  initialValue: _contract.title,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  validator: (val) => val == null || val.isEmpty
                      ? 'APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_TITLE'.tr()
                      : null,
                  onSaved: (val) => _contract.title = val,
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_COMPANY'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_COMPANY'.tr() + '*',
                  ),
                  initialValue: _contract.company,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  validator: (val) => val == null || val.isEmpty
                      ? 'APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_COMPANY'.tr()
                      : null,
                  onSaved: (val) => _contract.company = val,
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_CONTRACT_NUMBER'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_CONTRACT_NUMBER'.tr() + '*',
                  ),
                  initialValue: _contract.contractNumber,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(256),
                    FilteringTextInputFormatter.deny(_regexp)
                  ],
                  validator: (val) {
                    if(val == null || val.isEmpty || val.trim() == '') {
                      return 'APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_CONTRACT_NUMBER'.tr();
                    }
                    if (_regexp.hasMatch(val)) {
                      return 'APPLICATION_MOBILE_MESSAGE_INVALID_TEXT_FORM_FIELD'.tr('This field contains invalid characters');
                    }
                    return null;
                  },
                  onSaved: (val) => _contract.contractNumber = val?.trim(),
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  maxLines: 4,
                  keyboardType: TextInputType.multiline,
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_DESCRIPTION'.tr(),
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_DESCRIPTION'.tr(),
                  ),
                  initialValue: _contract.comment,
                  inputFormatters: [LengthLimitingTextInputFormatter(4096)],
                  onSaved: (val) => _contract.comment = val,
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_CONTRACT_START_DATE'.tr(),
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_CONTRACT_START_DATE'.tr(),
                  ),
                  controller: _controllerDateStart,
                  readOnly: true,
                  validator: (_) => _contract.dateStart != null
                      ? null
                      : 'APPLICATION_MOBILE_MESSAGE_INVALID_DATE'.tr(),
                  onTap: _chooseDateStart,
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_CONTRACT_END_DATE'.tr(),
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_CONTRACT_END_DATE'.tr(),
                  ),
                  controller: _controllerDateEnd,
                  readOnly: true,
                  validator: (_) => (_contract.dateStart != null &&
                          _contract.dateEnd != null &&
                          _contract.dateEnd!.isAfter(_contract.dateStart!))
                      ? null
                      : 'APPLICATION_MOBILE_MESSAGE_INVALID_DATE'.tr(),
                  onTap: _chooseDateEnd,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();

    /// get select arrays
    pets = Data().getPets();
    services = Data().getClinicsForMessage();

    _controllerDateStart = TextEditingController();
    _controllerDateEnd = TextEditingController();
    if (widget.contract != null) {
      isNew = false;
      _contract = widget.contract!.copy();
    } else {
      isNew = true;
      _contract = PetActivity();
      _contract.activityId = 3;
      _contract.dateStart = DateTime.now();
      _contract.typeId = widget.contractType;
      if (widget.pet != null) {
        _contract.pets.add(widget.pet!);
      }
    }

    _controllerDateStart.text = Data().dateTimeToUserDateStr(_contract.dateStart);
    _controllerDateEnd.text = Data().dateTimeToUserDateStr(_contract.dateEnd);
  }

  Future<void> _chooseDateEnd() async {
    DateTime now = DateTime.now();
    DateTime firstDate = _contract.dateStart ?? DateTime(now.year - 10, 1, 1);
    DateTime initialDate = _contract.dateEnd ?? _contract.dateStart!.add(const Duration(days: 364));

    DateTime? date = await Tools().common.showDateDialog(
          context: context,
          initialDate: initialDate.isAfter(firstDate) ? initialDate : firstDate,
          firstDate: _contract.dateStart ?? DateTime(now.year - 10, 1, 1),
          lastDate: DateTime(now.year + 10, 1, 1),
        );
    setState(() {
      _contract.dateEnd = date ?? initialDate;
      _controllerDateEnd.text = Data().dateTimeToUserDateStr(_contract.dateEnd);
    });
  }

  Future<void> _chooseDateStart() async {
    DateTime now = DateTime.now();
    DateTime initialDate = _contract.dateStart ?? now;

    DateTime? date = await Tools().common.showDateDialog(
          context: context,
          initialDate: initialDate,
          firstDate: DateTime(now.year - 10, 1, 1),
          lastDate: DateTime(now.year + 10, 1, 1),
        );

    setState(() {
      _contract.dateStart = date ?? initialDate;
      _controllerDateStart.text = Data().dateTimeToUserDateStr(_contract.dateStart);
    });
  }

  Future<void> _submitForm(BuildContext context) async {
    final FormState? form = _formKey.currentState;
    if (form == null || !form.validate()) {
      Tools().common.showValidationErrorMessage(context);
      return;
    }
    form.save();

    MBResponse response;
    response = isNew
        ? await MbApiPetActivity().addPetActivityRequest(context, _contract)
        : await MbApiPetActivity().editPetActivityRequest(context, _contract); //TODO MIDDLE maybe concat 2 apis

    if (response.success) {
      Tools().navigatorPop(value: true);
    }
  }
}
