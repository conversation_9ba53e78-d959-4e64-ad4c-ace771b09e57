import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/contracts_bloc.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_petactivity_item.dart';
import 'package:mybuddy/ui/templates/contract/add_edit_contract.dart';

class WidgetContractList extends StatelessWidget {
  final Pet pet;
  final int contractType;

  const WidgetContractList(
      {Key? key, required this.pet, required this.contractType})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    String type = (contractType == 1)
        ? 'APPLICATION_MOBILE_LABEL_WELLNESS'
        : 'APPLICATION_MOBILE_LABEL_INSURANCES';
    final bloc = BlocProvider.of<ContractsBloc>(context);
    return StreamBuilder<List<PetActivity>>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return Container();
        List<PetActivity> contracts = snapshot.data!;
        return Scaffold(
          backgroundColor: Colors.white,
          floatingActionButton: FloatingActionButton(
            heroTag: 'add-contract',
            onPressed: () async {
              await Tools().navigatorPush(
                AddEditContract(
                  contractType: contractType,
                  pet: pet,
                  title: (contractType == 1
                          ? 'APPLICATION_MOBILE_BUTTON_LABEL_WELLNESS_ADD'
                          : 'APPLICATION_MOBILE_BUTTON_LABEL_INSURANCE_ADD')
                      .tr(),
                ),
              );
              bloc.update();
            },
            child: const Icon(FontAwesomeIcons.plus),
          ),
          appBar: MBAppBar(
            title: Text(
              type.tr(),
              maxLines: 2,
            ),
          ),
          body: contracts.isEmpty
              ? Center(
                  child: Text(
                    (contractType == 1
                            ? 'APPLICATION_MOBILE_TEXT_ACTIVITY_NO_WELLNESS'
                            : 'APPLICATION_MOBILE_TEXT_ACTIVITY_NO_INSURANCE')
                        .tr(),
                  ),
                )
              : ListView.separated(
                  itemBuilder: (context, position) {
                    return GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
//                  Tools().navigatorPush(
//                    context,
//                    MaterialPageRoute(
//                      builder: (context) => ContractPage(pet: pets[position]),
//                    ),
//                  );
                      },
                      child: MBPetActivityItem(
                        contracts[position],
                      ),
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) =>
                      const Divider(),
                  itemCount: contracts.length,
                ),
        );
      },
    );
  }
}
