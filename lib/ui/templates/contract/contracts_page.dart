import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/contracts_bloc.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/ui/templates/contract/widget_contract_list.dart';

class ContractsPage extends StatelessWidget {
  final Pet pet;
  final int contractType;

  const ContractsPage({Key? key, required this.pet, this.contractType = 1}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ContractsBloc>(
      bloc: ContractsBloc(pet: pet, contractType: contractType),
      child: WidgetContractList(pet: pet, contractType: contractType),
    );
  }
}
