import 'package:flutter/material.dart';
import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_html_widget.dart';

class AboutPage extends StatelessWidget {
  const AboutPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String prefix = AppConfig.of(context).prefix.toUpperCase();
    String content = ('APPLICATION_MOBILE_TEXT_ABOUT_LEGALS_' + prefix).tr();
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: MBAppBar(
        title: Text('APPLICATION_MOBILE_TITLE_LEGALS'.tr(), maxLines: 2),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: MBHtmlWidget(htmlSrc: content),
        ),
      ),
    );
  }
}
