import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/challenge_detail_controller.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/challenge.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_html_widget.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/templates/challenge/challenge_rules.dart';

import '../../../pages/banner_widget.dart';

class ChallengeDetailsPage extends StatelessWidget {
  final Challenge challenge;

  ChallengeDetailsPage({Key? key, required this.challenge}) : super(key: key);

  late ChallengeDetailController controller;

  @override
  Widget build(BuildContext context) {
    controller = Get.put(ChallengeDetailController());
    controller.setChallenge(challenge);
    return Obx(
      () => Scaffold(
        appBar: MBAppBar(
          title: Text(
            'APPLICATION_MOBILE_TITLE_CHALLENGE'.tr(),
            style: Theme.of(context)
                .textTheme
                .titleMedium!
                .copyWith(fontSize: 20, fontWeight: FontWeight.w700),
          ),
          actions: [
            if (!controller.subscribed)
              IconButton(
                onPressed: () => Tools().navigatorPush(
                    ChallengeRulesPage(challenge: controller.challenge)),
                icon: Image.asset(
                  "assets/icon/book.png",
                  height: 20,
                ),
              )
            else
              IconButton(
                onPressed: () {
                  _menuBottomSheet();
                },
                icon: const Icon(
                  Icons.more_vert_rounded,
                ),
              ),
          ],
        ),
        bottomNavigationBar: controller.loading ? null : _bottomButton(),
        body: controller.loading
            ? const Center(
                child: CircularProgressIndicator.adaptive(),
              )
            : SingleChildScrollView(
                padding: const EdgeInsets.all(15.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MBCachedImage(
                      imageUrl: controller.challenge.imageDetailUrl,
                      size: MediaQuery.of(context).size.width - 30.0,
                      radius: 10.0,
                      aspectRatio: 3 / 2.0,
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    _titleSection(),

                    /// description section
                    MBHtmlWidget(htmlSrc: controller.challenge.description!),
                  ],
                ),
              ),
      ),
    );
  }

  void _menuBottomSheet() {
    BuildContext context = Get.context!;
    showModalBottomSheet(
        context: context,
        builder: (BuildContext bc) {
          List<ChallengeSettings> options = ChallengeSettings.values.toList();
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                height: 10,
              ),
              ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (ctx, index) {
                    var text = options[index].toText()!;
                    return TextButton(
                        onPressed: () async {
                          Tools().navigatorPop();
                          await options[index]
                              .toPush(context, controller.challenge);
                        },
                        child: Center(
                            child: Text(
                          text,
                          style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.secondary),
                        )));
                  },
                  separatorBuilder: (ctx, index) {
                    return const Divider();
                  },
                  itemCount: options.length),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        });
  }

  Widget _titleSection() {
    BuildContext context = Get.context!;
    return Card(
      margin: EdgeInsets.zero,
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.all(15.0),
              child: MBCachedImage(
                imageUrl: controller.challenge.imageUrl,
                size: MediaQuery.of(context).size.width * 0.2,
                radius: 10.0,
              ),
            ),
            Container(
              color: const Color(0xffd4d4d4),
              width: 1,
            ),
            Expanded(
                child: Padding(
              padding: const EdgeInsets.all(15.0),
              child: Column(
                children: [
                  Text(
                    controller.challenge.title!,
                    style: Theme.of(context)
                        .textTheme
                        .headline6!
                        .copyWith(fontSize: 14, fontWeight: FontWeight.w600),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Divider(height: 30.0),
                  Row(
                    children: [
                      Expanded(
                          child: _infoTile(
                              controller.challenge.totalWalks.toPointsLong(),
                              "Total Walks",
                              "assets/icon/walk.png",
                              const Color(0xff4D9C6D))),
                      Expanded(
                          child: _infoTile(
                              controller.challenge.totalDistance.toInt().toPointsLong(),
                              "Total Miles",
                              "assets/icon/challenge_distance.png",
                              const Color(0xff4D9C6D))),
                    ],
                  ),
                  if (controller.subscribed)
                    Row(
                      children: [
                        Expanded(
                            child: _infoTile(
                                controller.challenge.yourWalks.toPointsLong(),
                                "Your Walks",
                                "assets/icon/walk.png",
                                const Color(0xff636363))),
                        Expanded(
                            child: _infoTile(
                                controller.challenge.yourDistance.toString(),
                                "Your Miles",
                                "assets/icon/challenge_distance.png",
                                const Color(0xff636363))),
                      ],
                    )
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _infoTile(String title, String subTitle, String icon, Color color) {
    BuildContext context = Get.context!;
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 0,
      minLeadingWidth: 0,
      horizontalTitleGap: 12,
      dense: true,
      leading: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Image.asset(
          icon,
          color: color,
        ),
      ),
      title: Text(
        title,
        style: Theme.of(context)
            .textTheme
            .titleMedium!
            .copyWith(fontSize: 14, fontWeight: FontWeight.w500, color: color),
      ),
      subtitle: Text(
        subTitle,
        style: Theme.of(context)
            .textTheme
            .subtitle1!
            .copyWith(fontSize: 11, fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _bottomButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0, left: 15, right: 15),
      child:
          // (Data().get().challengeCompleted(controller.challenge.completedTask))
          //     ? Container(
          //         width: double.infinity,
          //         margin: const EdgeInsets.symmetric(vertical: 10.0),
          //         alignment: Alignment.center,
          //         child: Text(
          //           'APPLICATION_MOBILE_LABEL_OWNER_CHALLENGE_COMPLETED'
          //               .tr('Challenge completed !'),
          //           style: Theme.of(context).textTheme.headline5,
          //         ),
          //       )
          //     :
          Obx(() => controller.subscribed
            ? const BannerAdWidget()
            : ElevatedButton(
                onPressed: controller.isSubmitting
                    ? null
                    : () {
                        controller.joinChallenge();
                      },
                child: Container(
                  height: 20,
                  width: double.infinity,
                  alignment: Alignment.center,
                  child: Text(
                      'APPLICATION_MOBILE_CHALLENGE_JOIN'.tr('Join Challenge')),
                ),
              ),
      ),
    );
  }
}

enum ChallengeSettings { rules, leave }

extension MapChallnegeSettings on ChallengeSettings {
  String? toText() {
    switch (this) {
      case ChallengeSettings.rules:
        return 'APPLICATION_MOBILE_CHALLENGE_RULES'.tr();
      case ChallengeSettings.leave:
        return 'APPLICATION_MOBILE_LEAVE_CHALLENGE'.tr();
      default:
        return '';
    }
  }

  Future<bool?> toPush(BuildContext context, Challenge challenge) async {
    switch (this) {
      case ChallengeSettings.rules:
        Tools().navigatorPush(ChallengeRulesPage(challenge: challenge));
        return true;

      case ChallengeSettings.leave:
        return await Tools().common.showWarningDialog(
          context,
          validTitle: 'APPLICATION_MOBILE_COMMUNITY_PACK_LEAVE'.tr("Leave"),
          rejectTitle: 'APPLICATION_MOBILE_LABEL_CANCEL'.tr("Cancel"),
          bgColor: const Color(0xffF2F0E4),
          textColor: Theme.of(context).colorScheme.secondary,
          title: 'APPLICATION_MOBILE_COMMUNITY_WARNING'.tr("Warning"),
          text: 'APPLICATION_MOBILE_COMMUNITY_PACK_DELETE_DESC'.tr(
              "Leave the ${challenge.title}?\n\nThis will delete your participation from this challenge. You may always rejoin at any time.\n\nNote: all of your walks are always counted for your selected animal charity."),
          onValid: () {
            Get.find<ChallengeDetailController>().leaveChallenge();
          },
        );
      default:
        return false;
    }
  }
}
