import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/challenges_controller.dart';
import 'package:mybuddy/extensions/datetime_extensions.dart';
import 'package:mybuddy/models/challenge.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/templates/challenge/challenge_details_page.dart';
import '../../../../Api/mb_api_challenge.dart';
import '../../../../controllers/challenge_detail_controller.dart';

class ChallengeHomeCardWidget extends StatelessWidget {
  final Challenge challenge;

  ChallengeHomeCardWidget({Key? key, required this.challenge})
      : super(key: key);

  final ChallengesController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        await Tools().navigatorPush(ChallengeDetailsPage(
          challenge: challenge,
        ));
      },
      child: Card(
        margin: EdgeInsets.zero,
        child: Container(
          padding: const EdgeInsets.all(15),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MBCachedImage(
                    imageUrl: challenge.imageUrl,
                    size: 85,
                    radius: 10.0,
                  ),
                  const SizedBox(
                    width: 20,
                  ),
                  _titleWithDesc(),
                ],
              ),
              const SizedBox(
                height: 10,
              ),
              const Divider(
                color: Color(0xffD4D4D4),
              ),
              _tileFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _tileFooter() {
    BuildContext context = Get.context!;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          challenge.dateEnd!.inDaysDifference() == 0
              ? 'Ending Today'
              : challenge.dateEnd!.inDaysDifference() == 1
                  ? 'Ending in 1 day'
                  : 'Ending in ${challenge.dateEnd!.inDaysDifference() ?? 0} days',
          style: Theme.of(context)
              .textTheme
              .subtitle1!
              .copyWith(fontSize: 12, fontWeight: FontWeight.w500),
        ),
        if (controller.activeTab == ChallengeTab.joined)
          const Padding(
            padding: EdgeInsets.only(top: 8.0),
            child: Icon(
              Icons.arrow_forward_ios_rounded,
              size: 18,
            ),
          )
        else
          RawMaterialButton(
            elevation: 0,
            constraints: const BoxConstraints(minWidth: 76.0, minHeight: 36.0),
            onPressed: () {
              controller.joinChallenge(challenge);
            },
            fillColor: Theme.of(context).colorScheme.secondary,
            shape: const StadiumBorder(
              side: BorderSide.none,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: Text(
              'Join',
              style: Theme.of(context).textTheme.headline6?.copyWith(
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                    color: Colors.white,
                  ),
            ),
          ),
      ],
    );
  }

  Widget _titleWithDesc() {
    BuildContext context = Get.context!;
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10.0),
            child: Text(
              challenge.title!,
              style: Theme.of(context)
                  .textTheme
                  .headline6!
                  .copyWith(fontWeight: FontWeight.w600, fontSize: 13),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Text(
            challenge.summary!,
            style: Theme.of(context)
                .textTheme
                .bodyText2!
                .copyWith(fontSize: 12, fontWeight: FontWeight.w400),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
