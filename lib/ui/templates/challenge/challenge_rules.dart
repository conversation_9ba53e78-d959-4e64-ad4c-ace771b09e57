import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/challenge.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_html_widget.dart';

class ChallengeRulesPage extends StatefulWidget {
  final Challenge challenge;

  const ChallengeRulesPage({Key? key, required this.challenge})
      : super(key: key);

  @override
  _ChallengeRulesPageState createState() => _ChallengeRulesPageState();
}

class _ChallengeRulesPageState extends State<ChallengeRulesPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_TITLE_CHALLENGE_RULES'.tr(),
          style: Theme.of(context)
              .textTheme
              .titleMedium!
              .copyWith(fontSize: 20, fontWeight: FontWeight.w700),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: MBHtmlWidget(htmlSrc: widget.challenge.rules!),
      ),
    );
  }
}
