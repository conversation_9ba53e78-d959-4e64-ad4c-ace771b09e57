import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Api/mb_api_challenge.dart';
import 'package:mybuddy/controllers/challenges_controller.dart';
import 'package:mybuddy/ui/components/internet_widget_switcher.dart';
import '../../components/mb_no_record_found.dart';
import '../../components/pagination_widget.dart';
import 'widgets/challenge_home_card_widget.dart';

class ChallengeHomePage extends StatelessWidget {
  ChallengeHomePage({Key? key}) : super(key: key);
  final ChallengesController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return InternetWidgetSwitcher(
        onlineWidget: PaginationWidget(
          controller: controller,
          emptyWidget: _emptyWidget(),
          child: (context, index) {
            return ChallengeHomeCardWidget(
                challenge: controller.dataList[index]);
          },
        ),
        retryMethod: controller.initialFetch);
  }

  Widget _emptyWidget() {
    BuildContext context = Get.context!;
    return Obx(()=>MBNoRecordFound(
        title: controller.emptyTitle,
        otherWidget: Text(
          controller.emptySubTitle,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 14.0, color: Colors.black),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
