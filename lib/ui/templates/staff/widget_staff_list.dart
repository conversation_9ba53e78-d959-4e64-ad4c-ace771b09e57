import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/staffs_bloc.dart';
import 'package:mybuddy/models/staff.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/templates/staff/staff_page.dart';
import 'package:mybuddy/extensions/number_extensions.dart';

class WidgetStaffList extends StatelessWidget {
  /// need to be wrapped in [BlocProvider] of [StaffsBloc]
  const WidgetStaffList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<StaffsBloc>(context);
    return StreamBuilder<List<Staff>>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return Container();
        List<Staff> staffs = snapshot.data!;
        return ListView.separated(
          itemBuilder: (context, position) {
            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                Tools().navigatorPush(
                  StaffPage(staff: staffs[position], clinic: bloc.service),
                );
              },
              child: oneStaff(staffs[position]),
            );
          },
          separatorBuilder: (BuildContext context, int index) =>
              const Divider(),
          itemCount: staffs.length,
        );
      },
    );
  }

  Widget oneStaff(Staff staff) {
    return Column(
      children: <Widget>[
        Row(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(3.0),
              height: 65,
              width: 65,
              child: MBAvatar(
                imageUrl: staff.imageUrl ?? staff.imageId?.toImageUrl(),
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.fromLTRB(12.0, 6.0, 12.0, 6.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          staff.getFullName(),
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            fontSize: 18.0,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        Text(
                          staff.jobTitle ?? '',
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
