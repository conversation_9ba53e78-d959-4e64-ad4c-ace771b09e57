import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/staff_bloc.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/models/staff.dart';
import 'package:mybuddy/ui/templates/staff/widget_staff.dart';

class StaffPage extends StatelessWidget {
  final Staff staff;
  final MBService clinic;

  const StaffPage({Key? key, required this.clinic, required this.staff})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<StaffBloc>(
      bloc: StaffBloc(staffId: staff.id, service: clinic),
      child: const WidgetStaff(),
    );
  }
}
