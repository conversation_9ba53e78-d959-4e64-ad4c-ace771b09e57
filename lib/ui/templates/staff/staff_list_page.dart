/// ignore: must_be_immutable
import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/staffs_bloc.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/staff/widget_staff_list.dart';

class StaffListPage extends StatelessWidget {
  final MBService clinic;

  const StaffListPage({Key? key, required this.clinic}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_LABEL_SERVICE_STAFF'.tr(),
          maxLines: 2,
        ),
      ),
      body: BlocProvider<StaffsBloc>(
        bloc: StaffsBloc(service: clinic),
        child: const WidgetStaffList(),
      ),
    );
  }
}
