import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/staff_bloc.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/staff.dart';
import 'package:mybuddy/ui/components/mb_list_tile.dart';

class WidgetStaff extends StatelessWidget {

  const WidgetStaff({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<StaffBloc>(context);
    return StreamBuilder<Staff?>(
      stream: bloc.stream,
      builder: (BuildContext context, AsyncSnapshot<Staff?> snapshot) {
        return snapshot.hasData
            ? staffWidget(context, snapshot.data!)
            : Container();
      },
    );
  }

  Widget staffWidget(BuildContext context, Staff staff) {
    return Scaffold(
      floatingActionButton: _floatingActionButton(context, staff),
      body: CustomScrollView(
        slivers: <Widget>[
          ///First sliver is the App Bar
          SliverAppBar(
            ///Properties of app bar
            backgroundColor: Colors.blue,
            leading: BackButton(
              color: staff.imageId != null ? Colors.grey[600] : Colors.white,
              onPressed: () => Tools().navigatorPop(),
            ),
            floating: false,
            pinned: true,
            expandedHeight: staff.imageId == null ? 0 : 295,

            ///Properties of the App Bar when it is expanded
            flexibleSpace: FlexibleSpaceBar(
              centerTitle: true,
              title: SizedBox(
                width: MediaQuery.of(context).size.width * 0.66,
                child: Text(
                  staff.getFullName(),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13.0,
                    fontWeight: FontWeight.bold,
                    shadows: <Shadow>[
                      Shadow(
                        offset: Offset(.1, 0.1),
                        blurRadius: 2.0,
                        color: Color.fromARGB(120, 0, 0, 0),
                      ),
                    ],
                  ),
                ),
              ),
              background: staff.imageId == null
                  ? null
                  : CachedNetworkImage(
                      imageUrl:
                          staff.imageUrl ?? staff.imageId?.toImageUrl() ?? '',
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.error),
                    ),
            ),
          ),
          SliverList(
            ///Lazy building of list
            delegate: SliverChildListDelegate(
              [
//                  _phoneRow(),
                MBListTile(
                  iconData: null,
                  text: staff.description,
                  color: Colors.black,
                ),
                MBListTile(
                  iconData: FontAwesomeIcons.phone,
                  text: staff.phone,
                  color: Colors.blue,
                  onPressed: () async {
                    String url = 'tel:' + staff.phone!.replaceAll(' ', '');
                    Tools().common.launchURL(url, context);
                  },
                ),
                MBListTile(
                  iconData: FontAwesomeIcons.envelope,
                  text: staff.email,
                  color: Colors.blue,
                  onPressed: () async {
                    String url = 'mailto:' + staff.email!.trim();
                    Tools().common.launchURL(url, context);
                  },
                ),
                _timetableWidget(context, staff)
              ],
            ),
          ),
        ],
      ),
    );
  }

  FloatingActionButton? _floatingActionButton(
      BuildContext context, Staff staff) {
    final bloc = BlocProvider.of<StaffBloc>(context);
    if (staff.acceptAppointment && (bloc.service.canMakeAppointment())) {
      return FloatingActionButton(
        heroTag: 'add-appointment',
        onPressed: () => Tools().appointment.appointmentAction(
            service: bloc.service, staff: staff, context: context),
        child: const Icon(FontAwesomeIcons.calendarAlt),
      );
    }

    return null;
  }

  Widget _timetableWidget(BuildContext context, Staff staff) {
    List<Widget> timeTable = [];

    if (staff.presences.isNotEmpty) {
      for (var o in staff.presences) {
        timeTable.add(
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 1, 90, 1),
            child: Row(
              children: <Widget>[
                Text(o.getDayCode().tr()),
                Expanded(
                  child: Text(
                    o.toFormattedTimeString(context),
                    textAlign: TextAlign.end,
                  ),
                )
              ],
            ),
          ),
        );
      }
    }
    if (staff.emergency != null) {
      List<String> strList = [];
      if (staff.emergency!.nights) {
        strList.add('APPLICATION_MOBILE_LABEL_EMERGENCY_NIGHTS'.tr());
      }
      if (staff.emergency!.sundays) {
        strList.add('APPLICATION_MOBILE_LABEL_EMERGENCY_SUNDAYS'.tr());
      }
      if (staff.emergency!.holidays) {
        strList.add('APPLICATION_MOBILE_LABEL_EMERGENCY_HOLIDAYS'.tr());
      }
      if (strList.isNotEmpty) {
        timeTable.add(const Divider());
        timeTable.add(
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 1, 90, 1),
            child: Text(
              '${'APPLICATION_MOBILE_LABEL_EMERGENCY_SERVICE'.tr()}: ${strList.join(', ')}',
            ),
          ),
        );
      }
    }

    if (timeTable.isEmpty) {
      return Container();
    }

    return Container(
      color: Colors.grey[300],
      child: Column(
        children: <Widget>[
          MBListTile(
            iconData: FontAwesomeIcons.clock,
            text: 'APPLICATION_MOBILE_LABEL_SERVICE_OPENING_HOURS'.tr(),
            color: Colors.black,
            bgColor: Colors.grey[300],
          ),
          Column(children: timeTable),
          const SizedBox(height: 10)
        ],
      ),
    );
  }
}
