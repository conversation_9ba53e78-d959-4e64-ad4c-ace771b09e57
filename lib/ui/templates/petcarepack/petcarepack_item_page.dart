import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_task.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/models/petcarepacks.dart';
import 'package:mybuddy/ui/components/mb_html_widget.dart';
import 'package:mybuddy/ui/components/mb_text_shadow_widget.dart';

class PetCarePackItemPage extends StatefulWidget {
  final PetCarePackItem item;
  final PetCarePack? petCarePack;

  const PetCarePackItemPage(
      {Key? key, required this.item, this.petCarePack})
      : super(key: key);

  @override
  _PetCarePackItemPageState createState() => _PetCarePackItemPageState();
}

class _PetCarePackItemPageState extends State<PetCarePackItemPage> {
  late ScrollController controller;
  late bool done;
  String? content;

  void readingListener() {
    double position = controller.position.pixels;
    double endPage = controller.position.maxScrollExtent;

    /// Prevent Unsupported operation: Infinity or NaN toInt
    if (endPage.isInfinite || endPage.isNaN || endPage == 0.0) {
      endPage = controller.position.viewportDimension;
    }

    if (!done && (position / endPage) > 0.5) {
      done = true;
      MbApiTask().setPetCarePackRead(widget.petCarePack);
    }
  }

  @override
  void initState() {
    super.initState();
    controller = ScrollController();
    done = false;

    content = widget.item.data?.replaceAll('&controls=0"', '');
    content = content?.replaceAll(
      '></iframe></div>',
      ' allowfullscreen="allowfullscreen"></iframe></div>',
    );

    /// considering content length less than 1500 characters
    /// has no scrolling behavior.
    /// automatically send setRead to api
    if (content == null || content!.length < 1500) {
      MbApiTask().setPetCarePackRead(widget.petCarePack);
    } else {
      controller.addListener(readingListener);
    }
  }

  @override
  void dispose() {
    controller.removeListener(readingListener);
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        controller: controller,
        slivers: <Widget>[
          ///First sliver is the App Bar
          SliverAppBar(
            ///Properties of app bar
            backgroundColor: Theme.of(context).primaryColorLight,
            leading: BackButton(
              color: Colors.white,
              onPressed: () => Tools().navigatorPop(),
            ),
            floating: false,
            pinned: false,
            expandedHeight: widget.item.imageId == null ? 0 : 295,

            ///Properties of the App Bar when it is expanded
            flexibleSpace: FlexibleSpaceBar(
              centerTitle: true,
              title: Container(
                width: MediaQuery.of(context).size.width * 0.66,
                alignment: Alignment.bottomCenter,
                child: MBTextShadow(label: widget.item.title),
              ),
              background: widget.item.imageId == null
                  ? null
                  : CachedNetworkImage(
                      imageUrl: widget.item.imageUrl ??
                          widget.item.imageId?.toImageUrl() ??
                          '',
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.error),
                    ),
            ),
          ),
          SliverToBoxAdapter(
            child: content != null ? MBHtmlWidget(htmlSrc: content!) : null,
          ),
        ],
      ),
    );
  }
}
