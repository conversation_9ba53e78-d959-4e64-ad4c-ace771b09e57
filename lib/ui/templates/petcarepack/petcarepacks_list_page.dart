import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/petcarepacks_bloc.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/petcarepack/widget_petcarepacks_list.dart';

class PetCarePacksListPage extends StatelessWidget {
  final bool isHomecare;

  const PetCarePacksListPage(this.isHomecare, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_TITLE_EDUCATION'.tr(),
          maxLines: 2,
        ),
      ),
      body: Bloc<PERSON>rovider<PetCarePacksBloc>(
        bloc: PetCarePacksBloc(isHomecare),
        child: const WidgetPetCarePackList(),
      ),
    );
  }
}
