import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/petcarepack.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/petcarepacks.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/petcarepack/petcarepack_item_page.dart';
import 'package:mybuddy/ui/templates/petcarepack/petcarepacks_list_page.dart';

class PreventionPage extends StatelessWidget {
  const PreventionPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_TITLE_EDUCATION'.tr(),
          maxLines: 2,
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Stack(
              children: <Widget>[
                Image.asset('assets/images/petCare.jpg'),
                Positioned(
                  bottom: 20,
                  left: 20,
                  right: 20,
                  child: Text(
                    'APPLICATION_MOBILE_TITLE_EDUCATION_FULL'
                        .tr()
                        .removeAllHtmlTags(),
                    maxLines: 5,
                    style: const TextStyle(
                      fontSize: 20.0,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: <Shadow>[
                        Shadow(
                          offset: Offset(.1, 0.1),
                          blurRadius: 2.0,
                          color: Color.fromARGB(120, 0, 0, 0),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            GestureDetector(
              onTap: () {
                Tools().navigatorPush(const PetCarePacksListPage(false));
              },
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            'APPLICATION_MOBILE_TITLE_EDUCATION_PAID'.tr(),
                            style: const TextStyle(
                              color: Colors.blue,
                              fontSize: 18,
                            ),
                          ),
                          Text(
                            'APPLICATION_MOBILE_BUTTON_LABEL_EDUCATION_PAID_LIGHT'
                                .tr(),
                          ),
                        ],
                      ),
                    ),
                    const Icon(
                      FontAwesomeIcons.chevronRight,
                      color: Colors.blue,
                    )
                  ],
                ),
              ),
            ),
            const Divider(),
            GestureDetector(
              onTap: () {
                Tools().navigatorPush(const PetCarePacksListPage(true));
              },
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            'APPLICATION_MOBILE_TITLE_EDUCATION_FREE'.tr(),
                            style: const TextStyle(
                              color: Colors.blue,
                              fontSize: 18,
                            ),
                          ),
                          Text(
                            'APPLICATION_MOBILE_BUTTON_LABEL_EDUCATION_FREE_LIGHT'
                                .tr(),
//                    style: TextStyle(color: Colors.blue, fontSize: 18),
                          ),
                        ],
                      ),
                    ),
                    const Icon(
                      FontAwesomeIcons.chevronRight,
                      color: Colors.blue,
                    )
                  ],
                ),
              ),
            ),
            const Divider(),
            _sourcesWidget()
          ],
        ),
      ),
    );
  }

  Widget _sourcesWidget() {
    PetCarePackItem? sourcesItem = PetCarePackDelegate().homeCare.sourcesItem;
    if (sourcesItem == null) {
      return Container();
    }
    return GestureDetector(
      onTap: () {
        Tools().navigatorPush(PetCarePackItemPage(item: sourcesItem, petCarePack: null,));
      },
      behavior: HitTestBehavior.translucent,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: <Widget>[
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    sourcesItem.title ?? '',
                    style: const TextStyle(color: Colors.blue, fontSize: 18),
                  ),
                ],
              ),
            ),
            const Icon(
              FontAwesomeIcons.chevronRight,
              color: Colors.blue,
            )
          ],
        ),
      ),
    );
  }
}
