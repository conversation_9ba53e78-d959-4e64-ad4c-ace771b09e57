import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/models/petcarepacks.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/templates/petcarepack/petcarepack_item_page.dart';

class PetCarePackPageFolder extends StatelessWidget {
  final List<PetCarePackItem> items;
  final String? title;
  final PetCarePack petCarePack;

  const PetCarePackPageFolder({Key? key, required this.items, this.title, required this.petCarePack}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(title: Text(title ?? '', maxLines: 2,),),
      body: ListView.separated(
        padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 5.0),
        itemBuilder: (context, position) {
          return GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              if (items[position].items.isEmpty) {
                Tools().navigatorPush(PetCarePackItemPage(item: items[position], petCarePack: petCarePack));
              } else {
                Tools().navigatorPush(
                  PetCarePackPageFolder(items: items[position].items, title: items[position].title, petCarePack: petCarePack),
                );
              }
            },
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    items[position].title ?? '',
                    style: Theme.of(context).textTheme.bodyText1,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(5.0),
                  child: MBCachedImage(
                    imageUrl: items[position].imageUrl,
                    size: 75.0,
                  ),
                ),
              ],
            ),
          );
        },
        separatorBuilder: (context, index) => const Divider(),
        itemCount: items.length,
      ),
    );
  }
}
