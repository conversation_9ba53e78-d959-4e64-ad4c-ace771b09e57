import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/petcarepacks_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/petcarepacks.dart';
import 'package:mybuddy/ui/templates/petcarepack/petcarepack_folder_page.dart';
import 'package:mybuddy/ui/templates/petcarepack/petcarepack_item_page.dart';

class WidgetPetCarePackList extends StatelessWidget {
  /// need to be wrapped in [BlocProvider] of [PetCarePacksBloc]
  const WidgetPetCarePackList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<PetCarePacksBloc>(context);
    List<int?> myPetBreeds = Data().getPets().map((e) => e.breedId).toList();
    return Column(
      children: <Widget>[
        Visibility(
          visible: !bloc.isHomeCare,
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: TextFormField(
              decoration: InputDecoration(
                hintText: 'APPLICATION_MOBILE_FIELD_LABEL_BREED'.tr(),
                suffixIcon: const Icon(FontAwesomeIcons.search),
              ),
              initialValue: '',
              inputFormatters: [LengthLimitingTextInputFormatter(256)],
              onChanged: (val) => bloc.advancedUpdate(val),
            ),
          ),
        ),
        Expanded(
          child: Scrollbar(
            child: StreamBuilder<List<PetCarePack>>(
              stream: bloc.stream,
              builder: (BuildContext context,
                  AsyncSnapshot<List<PetCarePack>> snapshot) {
                if (!snapshot.hasData) return Container();
                List<PetCarePack> petCarePacks = snapshot.data!;

                if (bloc.isHomeCare) {
                  return ListView.separated(
                    padding: EdgeInsets.zero,
                    separatorBuilder: (context, index) => Container(
                      height: 3,
                      color: Colors.white,
                    ),
                    itemBuilder: (context, position) {
                      return onePetCarePack(context, petCarePacks[position]);
                    },
                    itemCount: petCarePacks.length,
                  );
                }

                return GroupedListView(
                  elements: petCarePacks,
                  useStickyGroupSeparators: true,
                  groupBy: (dynamic pcp) =>
                      myPetBreeds.contains(pcp.breedId) ? 0 : pcp.speciesId,
                  groupSeparatorBuilder: _buildGroupSeparator,
                  itemBuilder: (context, dynamic pcp) =>
                      onePetCarePack(context, pcp),
                  separator: const SizedBox(height: 3.0),
                  cacheExtent: MediaQuery.of(context).size.height * 3,
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget onePetCarePack(BuildContext context, PetCarePack pcp) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (pcp.items.length == 1) {
          PetCarePackItem item = pcp.items.first;
          if (item.items.isEmpty) {
            Tools().navigatorPush(
              PetCarePackItemPage(item: item, petCarePack: pcp),
            );
          } else {
            Tools().navigatorPush(
              PetCarePackPageFolder(items: item.items, title: item.title, petCarePack: pcp),
            );
          }
        } else if (pcp.items.isNotEmpty) {
          Tools().navigatorPush(
            PetCarePackPageFolder(items: pcp.items, title: pcp.title, petCarePack: pcp),
          );
        } else if (pcp.online != null && pcp.online!) {
          Tools().pet.getPetCarePack(pcp, context);
        }
      },
      child: Stack(
        children: <Widget>[
          SizedBox(
            height: 110,
            width: MediaQuery.of(context).size.width,
            child: CachedNetworkImage(
              imageUrl: pcp.imageUrl ?? pcp.imageId?.toImageUrl() ?? '',
              fit: BoxFit.cover,
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
          ),
          Positioned(
            bottom: 5,
            left: 8,
            right: 8,
            child: Text(
              pcp.title,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 15.0,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: <Shadow>[
                  Shadow(
                    offset: Offset(.1, 0.1),
                    blurRadius: 3.0,
                    color: Color.fromARGB(255, 0, 0, 0),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupSeparator(dynamic speciesId) {
    String? sectionName;
    if (speciesId == 0) {
      sectionName = 'APPLICATION_MOBILE_TITLE_PETCARE_SUGGESTIONS'.tr();
    } else {
      sectionName = Ref().get().getSpecies(speciesId)?.name.tr();
    }
    return Container(
      color: Colors.orangeAccent, //[900],
      padding: const EdgeInsets.only(left: 20, top: 5, bottom: 5),
      child: Text(
        sectionName ?? '',
        style: const TextStyle(color: Colors.white, fontSize: 18),
      ),
    );
  }
}
