import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/reminders_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/list_header_widget.dart';
import 'package:mybuddy/ui/templates/pet/diary/petactivity_page.dart';

import '../add_edit_reminder.dart';
import '../reminders_page.dart';

class ReminderList extends StatelessWidget {

  /// must be wrapped in [BlocProvider] of [RemindersBloc]
  const ReminderList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<RemindersBloc>(context);

    return StreamBuilder<List<PetActivity>>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        bool isTooLong = false;
        List<PetActivity> reminders;
        if (!snapshot.hasData) {
          return SizedBox(
            width: double.infinity,
            height: 100.0,
            child: Text('APPLICATION_MOBILE_MESSAGE_ERROR_SERVER'.tr()),
          );
        }
        isTooLong = snapshot.data!.length > 3;
        reminders = isTooLong ? snapshot.data!.sublist(0, 3) : snapshot.data!;

        return Column(
          children: [
            ListHeaderWidget(
              title: 'APPLICATION_MOBILE_LABEL_MY_REMINDERS'.tr(),
              onPressed: () => Tools().navigatorPush(
                AddReminderForm(title: 'APPLICATION_MOBILE_BUTTON_LABEL_REMINDER'.tr()),
              ),
            ),
            if (reminders.isEmpty)
              SizedBox(
                width: double.infinity,
                height: 40.0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.add_alarm),
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.5,
                      child: Text(
                        'APPLICATION_MOBILE_TEXT_ACTIVITY_NO_REMINDER'.tr(),
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.subtitle2,
                      ),
                    ),
                  ],
                ),
              )
            else
              ListView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: reminders.length + (isTooLong ? 1 : 0),
                itemBuilder: (BuildContext context, int index) {
                  if (index == 3) {
                    return TextButton(
                      onPressed: () => Tools().navigatorPush(const RemindersPage()),
                      child: Text(
                        'APPLICATION_MOBILE_BUTTON_LABEL_ACTIVITY_SEE_ALL_REMINDERS'
                            .tr(),
                      ),
                    );
                  }
                  return ListTile(
                    title: Text(
                      reminders[index].title!,
                      // style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Text(Data().dateTimeToUserDateStr(
                        reminders[index].nextOccurrence)),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => Tools().navigatorPush(
                      PetActivityPage(petActivity: reminders[index]),
                    ),
                  );
                },
              ),

            ///endif
          ],
        );
      },
    );
  }
}
