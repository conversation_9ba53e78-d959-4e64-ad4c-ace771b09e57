import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/generic_bloc.dart';
import 'package:mybuddy/blocs/reminders_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/login_data.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_home_card.dart';
import 'package:mybuddy/ui/components/mb_petactivity_item.dart';
import 'package:mybuddy/ui/templates/apt/appointment_page.dart';
import 'package:mybuddy/ui/templates/pet/diary/petactivity_page.dart';

import 'add_edit_reminder.dart';

class WidgetReminderList extends StatelessWidget {
  const WidgetReminderList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<RemindersBloc>(context);
    return StreamBuilder<List<PetActivity>>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return Container();
        List<PetActivity> reminders = snapshot.data!;
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: MBAppBar(
            title: Text(
              'APPLICATION_MOBILE_LABEL_MY_REMINDERS'.tr(),
              maxLines: 2,
            ),
          ),
          body: reminders.isEmpty
              ? Center(
                  child: Text(
                    'APPLICATION_MOBILE_TEXT_ACTIVITY_NO_REMINDER'.tr(),
                  ),
                )
              : ListView.separated(
                  itemCount: reminders.length,
                  itemBuilder: (BuildContext context, int position) {
                    PetActivity reminder = reminders[position];
                    return MBPetActivityItem(
                      reminder,
                      showPets: true,
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) =>
                      const Divider(),
                ),
          floatingActionButton: FloatingActionButton(
            heroTag: 'add-reminder',
            onPressed: () {
              Tools().navigatorPush(
                AddReminderForm(
                  title: 'APPLICATION_MOBILE_BUTTON_LABEL_REMINDER'.tr(),
                ),
              );
            },
            child: const Icon(FontAwesomeIcons.plus),
          ),
        );
      },
    );
  }
}

class ReminderHomeWidget extends StatelessWidget {
  const ReminderHomeWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<GenericBloc>(context);

    return StreamBuilder<LoginData?>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        LoginData? thisData = snapshot.data;

        /// comings part
        List<PetActivity> incomings = Data().getFilteredPetActivities(
            chronoFilter: ChronoFilter.future, livingState: LivingState.alive);
        //TODO add more filters like in old home page

        return Visibility(
          visible: snapshot.hasData,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  left: 10.0,
                  bottom: 10.0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'APPLICATION_MOBILE_LABEL_MY_REMINDERS'.tr(),
                      style: Theme.of(context).textTheme.bodyText1!.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                    ),
                  ],
                ),
              ),
              Column(
                // shrinkWrap: true,
                children: incomings.map((incoming) {
                  Color? color;
                  DateTime? dateTime;
                  AppointmentRequest? apt;
                  String? title = '';
                  switch (incoming.activityId) {
                    case 1:
                      dateTime = incoming.dateStart;
                      break;
                    case 2:
                      title = '';
                      dateTime = incoming.nextOccurrence;
                      break;
                    default:
                      dateTime = incoming.dateStart;
                  }

                  if (dateTime != null && dateTime.isBefore(DateTime.now())) {
                    color = Colors.orangeAccent;
                  } else if (dateTime != null && dateTime
                      .isBefore(DateTime.now().add(const Duration(days: 14)))) {
                    color = Colors.greenAccent;
                  }

                  switch (incoming.activityId) {
                    case 1:
                      apt = Data().getAppointmentWithPetActivity(incoming.id);
                      if (apt == null) {
                        return const SizedBox.shrink();
                      }
                      title = thisData?.getService(apt.serviceId)?.name ?? '';
                      return MBHomeCard(
                        title,
                        subtitleText:
                            'APPLICATION_MOBILE_TITLE_APPOINTMENT'.tr() +
                                ' ' +
                                Data().dateTimeToUserDateStr(dateTime),
                        icon: FontAwesomeIcons.calendarAlt,
                        onTap: () {
                          Tools().navigatorPush(
                            AppointmentPage(appointment: apt!),
                          );
                        },
                        color: color,
                      );
                    case 2:
                      return MBHomeCard(
                        incoming.title,
                        subtitleText: 'APPLICATION_MOBILE_LABEL_REMINDER'.tr() +
                            ' ' +
                            Data().dateTimeToUserDateStr(dateTime),
                        icon: FontAwesomeIcons.clock,
                        onTap: () {
                          Tools().navigatorPush(
                            PetActivityPage(
                              petActivity: incoming,
                            ),
                          );
                        },
                        color: color,
                      );
                    default:
                      return MBHomeCard(
                        incoming.title ?? incoming.comment,
                        subtitleText: Data().dateTimeToUserDateStr(dateTime),
                        icon: FontAwesomeIcons.exclamation,
                        onTap: () {
                          Tools().navigatorPush(
                            PetActivityPage(
                              petActivity: incoming,
                            ),
                          );
                        },
                        color: color,
                      );
                  }
                }).toList(),
              ),
            ],
          ),
        );
      },
    );
  }
}
