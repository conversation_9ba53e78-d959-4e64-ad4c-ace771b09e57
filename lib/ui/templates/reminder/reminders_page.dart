import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/reminders_bloc.dart';
import 'package:mybuddy/ui/templates/reminder/widget_reminder_list.dart';

class RemindersPage extends StatelessWidget {
  const RemindersPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<RemindersBloc>(
      bloc: RemindersBloc(),
      child: const WidgetReminderList(),
    );
  }
}
