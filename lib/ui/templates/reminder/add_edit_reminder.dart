import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Api/mb_api_pet_activity.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/mb_text_input_formatters.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_pet_multi_form_field.dart';

class AddReminderForm extends StatefulWidget {
  final String title;
  final PetActivity? reminder;
  final Pet? pet;

  const AddReminderForm({Key? key, this.reminder, this.pet, required this.title}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _AddReminderFormState();
}

class _AddReminderFormState extends State<AddReminderForm> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late List<Pet> pets;
  late List<ReminderType> reminderTypes;
  late TextEditingController _controllerDateStart;
  late TextEditingController _controllerEndRecurrence;
  late TextEditingController _controllerIntervalRecurrence;

  late bool isNew;
  late PetActivity _reminder;

  int maxIntervalRecurrence = 999;

  int recurrenceUnitToMaxInterval(int? unit) {
    switch(unit) {
      case 1:
        return 999;
      case 2:
        return 260;
      case 3:
        return 60;
      case 4:
        return 5;
      default:
        return 999;
    }
  }

  @override
  void initState() {
    super.initState();

    /// get select arrays
    pets = Data().getPets();
    reminderTypes = Ref().getReminderTypes();
    if (widget.reminder == null) {
      isNew = true;
      _reminder = PetActivity();
      _reminder.activityId = 2;
      _reminder.dateStart = DateTime.now();
      if (widget.pet != null) {
        _reminder.pets.add(widget.pet!);
      }
    } else {
      isNew = false;
      _reminder = widget.reminder!.copy();
    }
    _controllerDateStart = TextEditingController();
    _controllerEndRecurrence = TextEditingController();
    _controllerIntervalRecurrence = TextEditingController();
    _controllerDateStart.text = Data().dateTimeToUserDateStr(_reminder.dateStart);
    _controllerEndRecurrence.text = Data().dateTimeToUserDateStr(_reminder.endRecurrence);
    _controllerIntervalRecurrence.text = _reminder.intervalRecurrenceValue != null
        ? _reminder.intervalRecurrenceValue.toString()
        : '';
  }
  @override
  void dispose() {
    _controllerDateStart.dispose();
    _controllerEndRecurrence.dispose();
    _controllerIntervalRecurrence.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(widget.title, maxLines: 2),
        leading: BackButton(
          onPressed: () => Tools().navigatorPop(value: false),
        ),
      ),
      body: SafeArea(
        top: false,
        bottom: false,
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.disabled,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              children: <Widget>[
                const SizedBox(height: 15.0),
                DropdownButtonFormField<ReminderType>(
                  value: Ref().get().getReminderType(_reminder.typeId),
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_TYPE'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_TYPE'.tr(),
                  ),
                  onChanged: (ReminderType? newValue) {
                    setState(() {
                      if (newValue != null) {
                        _reminder.typeId = newValue.id;
                      } else {
                        _reminder.typeId = null;
                      }
                    });
                  },
                  validator: (val) {
                    return val != null
                        ? null
                        : 'APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_TYPE'.tr();
                  },
                  items: reminderTypes.map((ReminderType reminderType) {
                    return DropdownMenuItem<ReminderType>(
                      value: reminderType,
                      child: Text(reminderType.label),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 15.0),
                MBPetMultiFormField(
                  validator: (val) {
                    if (_reminder.pets.isEmpty) {
                      return 'APPLICATION_MOBILE_HB_ERROR_NO_PET'.tr();
                    }
                    return null;
                  },
                  initialValue: _reminder.pets,
                  onSaved: (value) {
                    if (value == null) return;
                    setState(() {
                      _reminder.pets = List<Pet>.from(value);
                    });
                  },
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_DATE'.tr(),
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_DATE'.tr(),
                  ),
                  controller: _controllerDateStart,
                  validator: (val) => _reminder.dateStart != null
                      ? null
                      : 'APPLICATION_MOBILE_MESSAGE_INVALID_DATE'.tr(),
                  onTap: _chooseDateStart,
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_TITLE'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_TITLE'.tr() + '*',
                  ),
                  initialValue: _reminder.title,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  validator: (val) => val == null || val.isEmpty
                      ? 'APPLICATION_MOBILE_MESSAGE_ERROR_MISSING_FIELD'.tr()
                      : null,
                  onChanged: (val) => _reminder.title = val,
                  onSaved: (val) => _reminder.title = val,
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  maxLines: 4,
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_NOTES'.tr(),
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_NOTES'.tr(),
                  ),
                  initialValue: _reminder.comment,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  onSaved: (val) => _reminder.comment = val,
                ),
                SwitchListTile(
                  title: Text('APPLICATION_MOBILE_FIELD_LABEL_RECURRENT'.tr()),
                  value: (_reminder.intervalRecurrenceUnit != null &&
                      _reminder.intervalRecurrenceValue != null),
                  onChanged: (bool value) {
                    setState(() {
                      if (value) {
                        _reminder.intervalRecurrenceValue = 1;
                        _reminder.intervalRecurrenceUnit = 1;
                      } else {
                        _reminder.intervalRecurrenceValue = null;
                        _reminder.intervalRecurrenceUnit = null;
                        _reminder.endRecurrence = null;
                        _controllerIntervalRecurrence.text = '';
                        _controllerEndRecurrence.text = '';
                      }
                    });
                  },
                ),
                Visibility(
                  visible: (_reminder.intervalRecurrenceUnit != null &&
                      _reminder.intervalRecurrenceValue != null),
                  child: Column(
                    children: <Widget>[
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          Expanded(
                            child: Text(
                              'APPLICATION_MOBILE_FIELD_LABEL_RECURRENCE_TYPE'.tr() + ' *',
                              style: Theme.of(context).textTheme.bodyText1,
                            ),
                          ),
                          Expanded(
                            child: TextFormField(
                              controller: _controllerIntervalRecurrence,
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                LengthLimitingTextInputFormatter(3),
                                NumericalRangeFormatter(
                                  min: 1,
                                  max: maxIntervalRecurrence.toDouble(),
                                )
                              ],
                              onSaved: (val) {
                                setState(() {
                                  _reminder.intervalRecurrenceValue =
                                      ((val == null || val == '') ? 0 : int.parse(val));
                                });
                              },
                              validator: (val) {
                                return (_reminder.intervalRecurrenceUnit == null &&
                                            _reminder.intervalRecurrenceValue == null) ||
                                        (val != null && val != '' && int.parse(val) > 0)
                                    ? null
                                    : 'APPLICATION_MOBILE_MESSAGE_ERROR_MISSING_FIELD'.tr();
                              },
                              decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: 'Max ${maxIntervalRecurrence.round()}',
                                hintStyle: const TextStyle(fontSize: 12.0),
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Expanded(
                            child: DropdownButtonFormField<int>(
                              value: _reminder.intervalRecurrenceUnit,
                              onChanged: (int? newValue) {
                                setState(() {
                                  _reminder.intervalRecurrenceUnit = newValue;
                                  maxIntervalRecurrence = recurrenceUnitToMaxInterval(newValue);
                                  _controllerIntervalRecurrence.text = '';
                                });
                              },
                              validator: (val) {
                                return val != null
                                    ? null
                                    : 'APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_SERVICE'.tr();
                              },
                              items: [1, 2, 3, 4].map((int recurrenceUnitId) {
                                return DropdownMenuItem<int>(
                                  value: recurrenceUnitId,
                                  child: Text(
                                    PetActivity.getReminderUnitLabelCodeForId(recurrenceUnitId)
                                        .tr(),
                                  ),
                                );
                              }).toList(),
                              decoration: const InputDecoration(border: InputBorder.none),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10.0),
                      TextFormField(
                        controller: _controllerEndRecurrence,
                        validator: (val) => _reminder.endRecurrence == null
                            || (_reminder.dateStart != null
                            && _reminder.endRecurrence!.isAfter(_reminder.dateStart!))
                            ? null
                            : 'APPLICATION_MOBILE_MESSAGE_INVALID_DATE'.tr(),
                        onTap: _chooseEndRecurrence,
                        readOnly: true,
                        decoration: InputDecoration(
                          labelText: 'APPLICATION_MOBILE_FIELD_LABEL_RECURRENCE_END'.tr(),
                          hintText: 'APPLICATION_MOBILE_FIELD_LABEL_RECURRENCE_END'.tr(),
                          suffixIcon: IconButton(
                            iconSize: 15,
                            icon: const Icon(FontAwesomeIcons.trash),
                            onPressed: () {
                              setState(() {
                                _controllerEndRecurrence.text = '';
                                _reminder.endRecurrence = null;
                              });
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(vertical: 25.0),
                  child: Builder(
                    builder: (context) => ElevatedButton(
                      onPressed: () {
                        _submitForm(context);
                      },
                      child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr()),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _chooseDateStart() async {
    DateTime now = DateTime.now();
    DateTime initialDate = _reminder.dateStart ?? now;

    DateTime? date = await Tools().common.showDateDialog(
          context: context,
          initialDate: initialDate,
          firstDate: DateTime(now.year - 10, 1, 1),
          lastDate: DateTime(now.year + 10, 1, 1),
        );
    if (mounted) {
      setState(() {
        _reminder.dateStart = date ?? initialDate;
        _controllerDateStart.text = Data().dateTimeToUserDateStr(_reminder.dateStart);
      });
    }
  }

  Future<void> _chooseEndRecurrence() async {
    _reminder.intervalRecurrenceValue ??= 1;
    DateTime now = DateTime.now();
    DateTime initialDate = _reminder.dateStart ?? now;
    initialDate = _reminder.addReminderOccurenceDuration(initialDate)!;
    Tools.debugPrint(_reminder.intervalRecurrenceValue?.toString());
    Tools.debugPrint(_reminder.intervalRecurrenceUnit?.toString());
    Tools.debugPrint(initialDate.toIso8601String());

    DateTime? date = await Tools().common.showDateDialog(
          context: context,
          initialDate: initialDate,
          firstDate: DateTime(now.year - 10, 1, 1),
          lastDate: DateTime(now.year + 10, 1, 1),
        );
    if (mounted) {
      setState(() {
        _reminder.endRecurrence = date ?? initialDate;
        _controllerEndRecurrence.text = Data().dateTimeToUserDateStr(_reminder.endRecurrence);
      });
    }
  }

  Future<void> _submitForm(BuildContext context) async {
    final FormState? form = _formKey.currentState;

    if (form == null || !form.validate()) {
      Tools().common.showValidationErrorMessage(context);
      return;
    }
    form.save();

    MBResponse response;
    if (isNew) {
      response = await MbApiPetActivity().addPetActivityRequest(context, _reminder);
    } else {
      response = await MbApiPetActivity().editPetActivityRequest(context, _reminder);
    }
    if (response.success) {
      Tools().navigatorPop(value: true);
    }
  }
}
