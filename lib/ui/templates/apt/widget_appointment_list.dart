import 'package:flutter/material.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/appointments_bloc.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/ui/templates/apt/appointment_page.dart';
import 'package:mybuddy/ui/templates/apt/widgets/appointment_request_cause_widget.dart';
import 'package:mybuddy/ui/templates/apt/widgets/appointment_request_period_widget.dart';
import 'package:mybuddy/ui/templates/apt/widgets/appointment_request_pet_widget.dart';

import 'widgets/appointment_request_status_widget.dart';

class WidgetAppointmentList extends StatelessWidget {
  const WidgetAppointmentList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<AppointmentsBloc>(context);
    return StreamBuilder<List<AppointmentRequest>>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        List<AppointmentRequest> appointments;
        if (!snapshot.hasData) return Container();
        appointments = snapshot.data!;
        if (appointments.isEmpty) {
          return Center(
            child: Text('APPLICATION_MOBILE_TEXT_ACTIVITY_NO_APPOINTMENT'.tr()),
          );
        }
        return GroupedListView(
          elements: appointments,
          groupBy: (AppointmentRequest appointment) => appointment.petActivityId != null,
          groupSeparatorBuilder: _buildGroupSeparator,
          itemBuilder: (context, AppointmentRequest appointment) => GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              Tools().navigatorPush(
                AppointmentPage(appointment: appointment),
              );
            },
            child: _oneAppointment(appointment),
          ),
        );
      },
    );
  }

  Widget _buildGroupSeparator(bool accepted) {
    return Container(
      color: Colors.blue[900],
      child: Padding(
        padding: const EdgeInsets.only(left: 20, top: 5, bottom: 5),
        child: Text(
          (accepted
                  ? 'APPLICATION_MOBILE_TITLE_APPOINTMENT'
                  : 'APPLICATION_MOBILE_TITLE_APPOINTMENT_REQUEST')
              .tr(),
          style: const TextStyle(color: Colors.white, fontSize: 18),
        ),
      ),
    );
  }

  Widget _clinicWidget(AppointmentRequest appointment) {
    return Text(
      Data().get().getService(appointment.serviceId)?.name ?? '',
      style: const TextStyle(color: Colors.blue),
    );
  }

  Widget _oneAppointment(AppointmentRequest appointment) {
    return Row(
      children: <Widget>[
        Expanded(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 6.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                AppointmentRequestCauseWidget(appointmentRequest: appointment),
                AppointmentRequestPetWidget(appointmentRequest: appointment),
                AppointmentRequestPeriodWidget(appointmentRequest: appointment),
                AppointmentRequestStatusWidget(appointmentRequest: appointment),
                _clinicWidget(appointment),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
