import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/appointments_bloc.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/apt/widget_appointment_list.dart';

class AppointmentsPage extends StatefulWidget {
  const AppointmentsPage({Key? key}) : super(key: key);

  @override
  _AppointmentsPageState createState() => _AppointmentsPageState();
}

class _AppointmentsPageState extends State<AppointmentsPage>
{

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_TITLE_MY_APPOINTMENTS'.tr(),
          maxLines: 2,
        ),
      ),
      body: BlocProvider<AppointmentsBloc>(
        bloc: AppointmentsBloc(),
        child: const WidgetAppointmentList(),
      ),
      floatingActionButton: Visibility(
        visible: Data().getClinicsForAppointment().isNotEmpty,
        child: actionButton(),
      ),
    );
  }

  Widget actionButton() {
    return FloatingActionButton(
      heroTag: 'add-appointment',
      onPressed: () {
        Tools().appointment.appointmentAction(context: context);
      },
      child: const Icon(FontAwesomeIcons.calendarAlt),
    );
  }
}
