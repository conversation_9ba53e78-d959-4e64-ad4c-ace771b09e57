import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/appointment_bloc.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/ui/templates/apt/widget_appointment_detail.dart';

class AppointmentPage extends StatelessWidget {
  final AppointmentRequest appointment;

  const AppointmentPage({Key? key, required this.appointment}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AppointmentBloc>(
      bloc: AppointmentBloc(appointmentId: appointment.id),
      child: const WidgetAppointmentDetail(),
    );
  }
}
