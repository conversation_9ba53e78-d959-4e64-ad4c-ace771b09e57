import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/appointments_bloc.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/list_header_widget.dart';
import 'appointment_request_cause_widget.dart';
import 'appointment_request_period_widget.dart';

import '../appointment_page.dart';
import '../appointments_page.dart';

class AppointmentRequestList extends StatelessWidget {

  /// must be wrapped in [BlocProvider] of [AppointmentsBloc]
  const AppointmentRequestList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<AppointmentsBloc>(context);

    return StreamBuilder<List<AppointmentRequest>>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        bool isTooLong = false;
        List<AppointmentRequest> appointments;
        if (!snapshot.hasData) {
          return SizedBox(
            width: double.infinity,
            height: 100.0,
            child: Text('APPLICATION_MOBILE_MESSAGE_ERROR_SERVER'.tr()),
          );
        }
        isTooLong = snapshot.data!.length > 3;
        appointments = isTooLong ? snapshot.data!.sublist(0, 3) : snapshot.data!;


        //hide if no apt and can't make one
        bool canMakeApt =  Tools().appointment.canMakeAppointmentSomeWhere();
        if(!canMakeApt && appointments.isEmpty){
          return Container();
        }

        return Column(
          children: [
            ListHeaderWidget(
              title: 'APPLICATION_MOBILE_TITLE_MY_APPOINTMENTS'.tr(),
              onPressed: canMakeApt ? () => Tools().appointment.appointmentAction(context: context) : null
            ),
            if (appointments.isEmpty)
              SizedBox(
                width: double.infinity,
                height: 40.0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.event_note),
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.5,
                      child: Text(
                        'APPLICATION_MOBILE_TEXT_ACTIVITY_NO_APPOINTMENT'.tr(),
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.titleSmall,
                      ),
                    ),
                  ],
                ),
              )
            else
              ListView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: appointments.length + (isTooLong ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == 3) {
                    return TextButton(
                      onPressed: () => Tools().navigatorPush(const AppointmentsPage()),
                      child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SEE'.tr()),
                    );
                  }
                  return ListTile(
                    title: AppointmentRequestCauseWidget(appointmentRequest: appointments[index]),
                    subtitle: AppointmentRequestPeriodWidget(appointmentRequest: appointments[index]),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      Tools().navigatorPush(
                        AppointmentPage(appointment: appointments[index]),
                      );
                    },
                  );
                },
              ),

            ///endif
          ],
        );
      },
    );
  }
}
