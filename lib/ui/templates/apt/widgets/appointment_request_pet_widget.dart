import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/pet.dart';

class AppointmentRequestPetWidget extends StatelessWidget {
  final AppointmentRequest appointmentRequest;
  const AppointmentRequestPetWidget({Key? key, required this.appointmentRequest}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String petNames = '';
    for (Pet p in appointmentRequest.pets) {
      if (petNames != '') {
        petNames += ', ';
      }
      petNames += Data().get().getPet(p.id)?.name ?? '';
    }

    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: Row(
        children: <Widget>[
          const Icon(
            FontAwesomeIcons.paw,
            color: Colors.grey,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 10),
              child: Text(petNames),
            ),
          ),
        ],
      ),
    );
  }
}
