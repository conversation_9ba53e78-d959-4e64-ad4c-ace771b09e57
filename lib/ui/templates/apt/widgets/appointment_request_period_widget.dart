import 'package:flutter/material.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/period.dart';
import 'package:mybuddy/models/pet_activity.dart';

class AppointmentRequestPeriodWidget extends StatelessWidget {
  final AppointmentRequest appointmentRequest;
  const AppointmentRequestPeriodWidget({Key? key, required this.appointmentRequest}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    List<Widget> periods = [];
    if (appointmentRequest.petActivityId != null) {
      PetActivity? pa = Data().get().getPetActivity(appointmentRequest.petActivityId);
      if(pa != null) {
        String t = Data().dateTimeToUserDateStr(pa.dateStart, dayOfWeek: true) +
            ' ' +
            Data().dateTimeToUserTimeStr(pa.dateStart) +
            '    (' +
            Data().formatDurationFromNowTo(pa.dateStart) +
            ')';
        periods.add(Text(
          t,
          // style: const TextStyle(fontWeight: FontWeight.bold),
        ));
      }
    } else {
      for (Period p in appointmentRequest.periods) {
        String t = Data().dateTimeToUserDateStr(p.dateStart, dayOfWeek: true) +
            ' ' + Data().dateTimeToUserTimeStr(p.dateStart);
        if (p.dateEnd != null && !(p.dateEnd!.hour == 23 && p.dateEnd!.minute == 45)) {
          //detect end of day
          t += ' - ' + Data().dateTimeToUserTimeStr(p.dateEnd);
        }
        periods.add(Text(
          t,
          // style: const TextStyle(fontWeight: FontWeight.bold),
        ));
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: periods,
    );
  }
}
