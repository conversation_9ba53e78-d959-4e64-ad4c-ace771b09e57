import 'package:flutter/material.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/cause.dart';

class AppointmentRequestCauseWidget extends StatelessWidget {
  final AppointmentRequest appointmentRequest;
  const AppointmentRequestCauseWidget({Key? key, required this.appointmentRequest}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String textCode = '';
    for (Cause c in appointmentRequest.causes) {
      if (textCode != '') {
        textCode += ', ';
      }
      textCode += Ref().get().getAppointmentStatusLabel(c.id);
    }

    return Text(
      textCode,
      // style: const TextStyle(fontWeight: FontWeight.bold),
    );
  }
}
