import 'package:flutter/material.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class AppointmentRequestStatusWidget extends StatelessWidget {
  final AppointmentRequest appointmentRequest;
  const AppointmentRequestStatusWidget({Key? key, required this.appointmentRequest}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String status;
    Color color;
    if (appointmentRequest.status == 1 && appointmentRequest.petActivityId == null) {
      color = Colors.red;
      status = 'APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_REFUSED'.tr();
    } else if (appointmentRequest.petActivityId != null) {
      color = Colors.green;
      status = 'APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_ACCEPTED'.tr();
    } else {
      color = Colors.orangeAccent;
      status = 'APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_REQUESTED'.tr();
    }

    return Text(
      status,
      style: TextStyle(color: color),
    );
  }
}
