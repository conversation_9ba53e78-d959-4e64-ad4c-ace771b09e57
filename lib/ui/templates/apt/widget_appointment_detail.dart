import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/appointment_bloc.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/geolocation.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/cause.dart';
import 'package:mybuddy/models/image.dart';
import 'package:mybuddy/models/period.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/models/staff.dart';
import 'package:mybuddy/ui/components/mb_simple_text_widget.dart';
import 'package:mybuddy/ui/components/widget_image_clickable.dart';

class WidgetAppointmentDetail extends StatelessWidget {
  const WidgetAppointmentDetail({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<AppointmentBloc>(context);
    return StreamBuilder<AppointmentRequest?>(
        stream: bloc.stream,
        builder: (context, AsyncSnapshot<AppointmentRequest?> snapshot) {
          if (!snapshot.hasData) return Container();
          AppointmentRequest appointment = snapshot.data!;
          MBService? service = Data().get().getService(appointment.serviceId);
          PetActivity? petActivity = Data().get().getPetActivity(appointment.petActivityId);

          return Scaffold(
            floatingActionButton: Visibility(
              visible: petActivity != null
                  && petActivity.dateStart != null
                  && petActivity.dateStart!.isAfter(DateTime.now()),
              child: FloatingActionButton.extended(
                heroTag: 'add-appointment-to-calendar',
                onPressed: () {
                  if (petActivity == null) return;
                },
                icon: const Icon(FontAwesomeIcons.share),
                label: const Icon(FontAwesomeIcons.calendarAlt),
              ), // set it to false
            ),
            body: CustomScrollView(
              slivers: <Widget>[
                ///First sliver is the App Bar
                const SliverAppBar(
                  ///Properties of app bar
                  backgroundColor: Colors.blue,
                  floating: false,
                  pinned: true,
                  expandedHeight: 0,
                ),
                SliverList(
                  ///Lazy building of list
                  delegate: SliverChildListDelegate(
                    // appointment == null
                    //     ? []
                    //     : [
                    [
                      _statusWidget(appointment, petActivity),
                      _causeWidget(appointment),
                      _dateWidget(appointment, petActivity),
                      const Divider(),
                      _petWidget(appointment),
                      _staffWidget(appointment, service),
                      _clinicWidget(context, service),
                      const Divider(),
                      _commentWidget(appointment),
                      _noteWidget(appointment),
                      _imagesWidget(context, appointment),
                      _responseWidget(appointment),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  Widget _causeWidget(AppointmentRequest appointment) {
    if (appointment.causes.isEmpty) return Container();

    List<String?> strings = [];
    for (Cause c in appointment.causes) {
      strings.add(Ref().get().getAppointmentStatusLabel(c.id));
    }

    return Padding(
      padding: const EdgeInsets.only(left: 10, top: 15),
      child: Text(
        strings.join(', '),
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _clinicWidget(BuildContext context, MBService? service) {
    if (service == null) return Container();
    String _address = service.getFullAddress();
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (_address.trim() != '') {
          Tools().common.launchURL(GeoLocation.getMapLink(_address) ?? '', context);
        }
      },
      child: Padding(
        padding: const EdgeInsets.only(left: 20, top: 20, bottom: 10),
        child: Row(
          children: <Widget>[
            const Icon(Icons.location_on, color: Colors.blue),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 7),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(service.name ?? '', style: const TextStyle(color: Colors.blue),),
                    Text(_address),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _commentWidget(AppointmentRequest appointment) {
    return MBSimpleTextWidget(text: appointment.comment, icon: FontAwesomeIcons.quoteLeft);
  }

  Widget _dateWidget(AppointmentRequest appointment, PetActivity? petActivity) {
    if (appointment.periods.isEmpty) return Container();
    List<Widget> periods = [];
    if (petActivity != null) {
      String t = Data().dateTimeToUserDateStr(petActivity.dateStart, dayOfWeek: true) +
          ' ' +
          Data().dateTimeToUserTimeStr(petActivity.dateStart);
      periods.add(Text(t));
    } else {
      for (Period p in appointment.periods) {
        String t = Data().dateTimeToUserDateStr(p.dateStart, dayOfWeek: true) +
            ' ' +
            Data().dateTimeToUserTimeStr(p.dateStart);
        if (p.dateEnd != null && !(p.dateEnd!.hour == 23 && p.dateEnd!.minute == 45)) {
          //detect end of day
          t += ' - ' + Data().dateTimeToUserTimeStr(p.dateEnd);
        }
        periods.add(Text(t));
      }
    }
    return Padding(
      padding: const EdgeInsets.only(left: 15, right: 15),
      child: Row(
        children: <Widget>[
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: periods,
            ),
          ),
        ],
      ),
    );
  }

  Widget _imagesWidget(BuildContext context, AppointmentRequest appointment) {
    if (appointment.images.isEmpty) {
      return Container();
    }
    List<Widget> rowImageList = [];
    rowImageList.add(const Icon(Icons.photo, color: Colors.grey));
    for (MBImage i in appointment.images) {
      rowImageList.add(
        Padding(
          padding: const EdgeInsets.only(left: 7),
          child: SizedBox(
            width: MediaQuery.of(context).size.width / 7,
            height: MediaQuery.of(context).size.width / 7,
            child: CachedImageClickable(image: i, title: ''),
          ),
        ),
      );
    }
    return Padding(
      padding: const EdgeInsets.only(left: 20, top: 10),
      child: Row(children: rowImageList),
    );
  }

  Widget _noteWidget(AppointmentRequest appointment) {
    if (appointment.notes.isEmpty) {
      return Container();
    }

    PetActivity? note = Data().get().getPetActivity(appointment.notes[0].id);
    if (note == null) return Container();

    return MBSimpleTextWidget(
      text: note.comment,
      icon: FontAwesomeIcons.notesMedical,
    );
    //todo MIDDLE add images of notes
  }

  Widget _petWidget(AppointmentRequest appointment) {
    if (appointment.pets.isEmpty) return Container();
    return MBSimpleTextWidget(
      text: appointment.getPetListString(),
      icon: FontAwesomeIcons.paw,
    );
  }

  Widget _responseWidget(AppointmentRequest appointment) {
    return MBSimpleTextWidget(
      text: appointment.message,
      icon: FontAwesomeIcons.comment,
      color: Colors.black,
      bgColor: Colors.grey[300],
    );
  }

  Widget _staffWidget(AppointmentRequest appointment, MBService? service) {
    if (service == null || appointment.staffId == null) return Container();
    Staff? staff = service.findStaff(appointment.staffId);
    if (staff == null) return Container();
    return Padding(
      padding: const EdgeInsets.only(left: 20, top: 20),
      child: Row(
        children: <Widget>[
          const Icon(FontAwesomeIcons.userMd, color: Colors.blue),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 7),
              child: Text(
                staff.getFullName(),
                style: const TextStyle(color: Colors.blue),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _statusWidget(AppointmentRequest appointment, PetActivity? petActivity) {
    String status;
    Color colorBg;
    if (appointment.status == 1 && petActivity == null) {
      colorBg = Colors.red;
      status = 'APPLICATION_MOBILE_TEXT_APPOINTMENT_REFUSED';
    } else if (petActivity != null) {
      colorBg = Colors.green;
      status = 'APPLICATION_MOBILE_TEXT_APPOINTMENT_ACCEPTED';
    } else {
      colorBg = Colors.orangeAccent;
      status = 'APPLICATION_MOBILE_TEXT_APPOINTMENT_REQUESTED';
    }
    return Container(
      color: colorBg,
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Center(
          child: Text(
            status.tr(),
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ),
    ); //, style: TextStyle(color: color));
  }
}
