import 'dart:io';

import 'package:datetime_picker_formfield/datetime_picker_formfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/Api/mb_api_pet_activity.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/cause.dart';
import 'package:mybuddy/models/period.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/models/staff.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/mb_images_form_field.dart';
import 'package:mybuddy/ui/components/mb_pet_multi_form_field.dart';

class AddAppointmentRequestForm extends StatefulWidget {
  final String? title;
  final MBService? clinic;
  final Staff? staff;
  final Pet? pet;

  const AddAppointmentRequestForm(
      {Key? key, this.clinic, this.title, this.staff, this.pet})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _AddAppointmentRequestFormState();
}

//TODO LOW ajouter note dans apt request form

class DateTimeFormField extends FormField<DateTime> {
  DateTimeFormField(
      {Key? key,
      FormFieldSetter<DateTime>? onSaved,
      FormFieldValidator<DateTime>? validator,
      DateTime? initialValue,
      AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
      var onTapFunction})
      : super(
          key: key,
          onSaved: onSaved,
          validator: validator,
          initialValue: initialValue,
          autovalidateMode: autovalidateMode,
          builder: (FormFieldState<DateTime> state) {
            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: onTapFunction,
              child: Container(
                alignment: Alignment.center,
                height: 40,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                        width: 1.0,
                        color: initialValue == null && state.hasError
                            ? Theme.of(state.context).colorScheme.error
                            : Theme.of(state.context).disabledColor),
                  ),
                ),
                child: initialValue == null && state.hasError
                    ? Text(state.errorText!,
                        style: TextStyle(
                            color: Theme.of(state.context).colorScheme.error,
                            fontSize: 11))
                    : Text(Data().dateTimeToUserDateTimeStr(initialValue)),
              ),
            );
          },
        );
}

class TimeFormField extends FormField<String> {
  TimeFormField(
      {Key? key,
      FormFieldSetter<String>? onSaved,
      FormFieldValidator<String>? validator,
      String? initialValue,
      AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
      var onTapFunction})
      : super(
          key: key,
          onSaved: onSaved,
          validator: validator,
          initialValue: initialValue,
          autovalidateMode: autovalidateMode,
          builder: (FormFieldState<String> state) {
            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: onTapFunction,
              child: Container(
                alignment: Alignment.center,
                height: 40,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      width: 1.0,
                      color:
                          (initialValue == null || initialValue.trim() == '') &&
                                  state.hasError
                              ? Theme.of(state.context).colorScheme.error
                              : Theme.of(state.context).disabledColor,
                    ),
                  ),
                ),
                child: (initialValue == null || initialValue.trim() == '') &&
                        state.hasError
                    ? Text(
                        state.errorText!,
                        style: TextStyle(
                          color: Theme.of(state.context).colorScheme.error,
                          fontSize: 11,
                        ),
                      )
                    : Text(initialValue!),
              ),
            );
          },
        );
}

class _AddAppointmentRequestFormState extends State<AddAppointmentRequestForm> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  late AppointmentRequest _appointmentRequest;
  late List<MBService> services;
  MBService? _service;
  late List<Staff> _staffs;
  Staff? _staff;

  // List<Pet>? pets;
  late List<AppointmentRequestCause> causes;
  AppointmentRequestCause? _cause;
  List<File>? _images;

  @override
  Widget build(BuildContext context) {
    //init inherit properties

    return Scaffold(
      appBar: MBAppBar(
        title: Text(widget.title!, maxLines: 2),
      ),
      body: SafeArea(
        top: false,
        bottom: false,
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.disabled,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              children: <Widget>[
                const SizedBox(height: 15.0),
                MBPetMultiFormField(
                  initialValue: _appointmentRequest.pets,
                  validator: (dynamic val) {
                    if (val.isEmpty) {
                      return 'APPLICATION_MOBILE_HB_ERROR_NO_PET'.tr();
                    }
                    return null;
                  },
                  onSaved: (dynamic value) {
                    List<Pet> petsOnSaved = value.cast<Pet>();
                    if (petsOnSaved.isEmpty) return;
                    setState(() {
                      _appointmentRequest.pets.addAll(petsOnSaved);
                    });
                  },
                ),
                const SizedBox(height: 15.0),
                DropdownButtonFormField<MBService>(
                  value: _service,
                  decoration: InputDecoration(
                    hintText:
                        'APPLICATION_MOBILE_LABEL_SERVICE_CLINIC'.tr() + '*',
                    labelText: 'APPLICATION_MOBILE_LABEL_SERVICE_CLINIC'.tr(),
                  ),
                  onChanged: (MBService? newValue) {
                    setState(() {
                      if (newValue != null && newValue != _service) {
                        _service = newValue;
                        _appointmentRequest.serviceId = _service!.id;

                        /// reset staffs dropdown
                        _staff = null;
                        initStaff();
                      }
                    });
                  },
                  isExpanded: true,
                  validator: (val) {
                    return val != null
                        ? null
                        : 'APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_SERVICE'
                            .tr();
                  },
                  items: services.map((MBService service) {
                    return DropdownMenuItem<MBService>(
                      value: service,
                      child: Text(service.name ?? ''),
                    );
                  }).toList(),
                ),
                Visibility(
                  visible: _staffs.length > 2,
                  child: Column(
                    children: [
                      const SizedBox(height: 15.0),
                      DropdownButtonFormField<Staff>(
                        isExpanded: true,
                        value: _staff,
                        decoration: InputDecoration(
                          hintText:
                              'APPLICATION_MOBILE_FIELD_LABEL_WHICH_VET'.tr() +
                                  '*',
                          labelText:
                              'APPLICATION_MOBILE_FIELD_LABEL_WHICH_VET'.tr(),
                        ),
                        onChanged: (Staff? newValue) {
                          setState(() {
                            if (newValue != null && newValue != _staff) {
                              _staff = newValue;
                              if (_staff!.id != 0) {
                                _appointmentRequest.staffId = _staff!.id;
                              }
                            }
                          });
                        },
                        items: _staffs.map((Staff staff) {
                          return DropdownMenuItem<Staff>(
                            value: staff,
                            child: Text(staff.getFullName()),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 15.0),
                DropdownButtonFormField<AppointmentRequestCause>(
                  value: _cause,
                  decoration: InputDecoration(
                    hintText:
                        'APPLICATION_MOBILE_FIELD_LABEL_WHICH_CAUSE'.tr() + '*',
                    labelText:
                        'APPLICATION_MOBILE_FIELD_LABEL_WHICH_CAUSE'.tr(),
                  ),
                  onChanged: (AppointmentRequestCause? newValue) {
                    setState(() {
                      if (newValue != null && newValue != _cause) {
                        _cause = newValue;
                        //todo LOW move to appointmentRequestCause
                        List<Cause> causeList = [];
                        Cause thisCause = Cause();
                        thisCause.id = _cause!.id;
                        causeList.add(thisCause);
                        _appointmentRequest.causes = causeList;
                      }
                    });
                  },
                  validator: (val) {
                    return val != null
                        ? null
                        : 'APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_CAUSE'
                            .tr();
                  },
                  items: causes.map((AppointmentRequestCause cause) {
                    return DropdownMenuItem<AppointmentRequestCause>(
                      value: cause,
                      child: Text(cause.label),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 15.0),
                TextFormField(
                  maxLines: 4,
                  keyboardType: TextInputType.multiline,
                  decoration: InputDecoration(
                    hintText: 'APPLICATION_MOBILE_FIELD_LABEL_PRECISION'.tr(),
                    labelText: 'APPLICATION_MOBILE_FIELD_LABEL_PRECISION'.tr(),
                  ),
                  initialValue: _appointmentRequest.message,
                  inputFormatters: [LengthLimitingTextInputFormatter(256)],
                  onSaved: (val) => _appointmentRequest.message = val ?? '',
                ),
                _slotChoiceWidget(0),
                _slotChoiceWidget(1),
                ImagesFormField(
                  height: MediaQuery.of(context).size.width * 0.30,
                  context: context,
                  initialValue: _images,
                  onSaved: (val) => _images = val,
                  numberItems: 5,
                  text: 'APPLICATION_MOBILE_BUTTON_LABEL_NB_PHOTO_NOTE'.tr(),
                ),
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(vertical: 25.0),
                  child: Builder(
                    builder: (context) => ElevatedButton(
                      onPressed: () => _submitForm(context),
                      child: Text(
                        'APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr(),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void initStaff() {
    if (_service != null) {
      _staffs = _service!.getStaffForAppointment();
      _staff = _staffs.isEmpty ? null : _staffs[0];
    }
  }

  @override
  void initState() {
    super.initState();
    _appointmentRequest = AppointmentRequest();
    services = Data().getClinicsForAppointment();
    if (widget.clinic != null) {
      _service = widget.clinic;
    } else {
      MBService? _favClinic = Data().getFavoriteClinic();
      if (_favClinic != null && _favClinic.acceptAppointment == true) {
        _service = _favClinic;
      }
    }
    initStaff();
    if (widget.staff != null && _staffs.contains(widget.staff)) {
      _staff = widget.staff;
    }
    // pets = Data().getPets();
    causes = Ref().getCauses();
    _images = [];
    Period _period1 = Period();
    Period _period2 = Period();
    // _appointmentRequest.periods = [];
    _appointmentRequest.periods.add(_period1);
    _appointmentRequest.periods.add(_period2);
    // _appointmentRequest.pets = [];
    if (widget.pet != null) _appointmentRequest.pets.add(widget.pet!);
  }

  bool isValidFutureDate(String date) {
    if (date.isEmpty) return false;
    var d = strToDateTime(date);
    return d != null && d.isAfter(DateTime.now());
  }

  DateTime roundTo(DateTime date, {int minutes = 5}) {
    int modulo = date.minute % minutes;
    if (modulo <= 2) {
      return date.subtract(Duration(minutes: modulo));
    } else {
      return date.add(Duration(minutes: (5 - modulo)));
    }
  }

  DateTime? strToDateTime(String str) {
    return Data().dateTimeFromUserDateTimeStr(str);
  }

  Future<void> _chooseDate(int choice) async {
    DateTime now = DateTime.now();
    DateTime tomorrow = DateTime(now.year, now.month, now.day + 1, 9, 0, 0);
    DateTime? oldValue = _appointmentRequest.periods[choice].dateStart;

    DateTime initialDate = oldValue ?? tomorrow;

    DateTime? date = await Tools().common.showDateDialog(
        context: context,
        initialDate: initialDate,
        firstDate: now,
        lastDate: DateTime(now.year + 2, now.month, now.day));

    DateTime result;
    if (date != null) {
      final time = await showTimePicker(
          context: context, initialTime: TimeOfDay.fromDateTime(initialDate));
      result = DateTimeField.combine(date, time);
    } else {
      result = initialDate;
    }

    /// round minutes
    result = roundTo(result);

    setState(() {
      _appointmentRequest.periods[choice].dateStart = result;
      if (_appointmentRequest.periods[choice].dateEnd != null &&
          _appointmentRequest.periods[choice].dateEnd!.isBefore(result)) {
        _appointmentRequest.periods[choice].dateEnd = null;
      }
    });
  }

  Future<void> _chooseTime(BuildContext context, int choice) async {
    DateTime now = DateTime.now();
    DateTime tomorrow = DateTime(now.year, now.month, now.day + 1, 9, 0, 0);
    DateTime? oldValue = _appointmentRequest.periods[choice].dateEnd;
    DateTime? oldValueFrom = _appointmentRequest.periods[choice].dateStart;
    DateTime initialDate;
    if (oldValue == null) {
      initialDate = oldValueFrom ?? tomorrow;
    } else {
      initialDate = oldValue;
    }

    DateTime result;
    final TimeOfDay? time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(initialDate),
    );
    if (time != null) {
      result = DateTimeField.combine(initialDate, time);
    } else {
      result = initialDate;
    }

    /// round minutes
    result = roundTo(result);

    setState(() {
      if (oldValueFrom != null && result.isAfter(oldValueFrom)) {
        _appointmentRequest.periods[choice].dateEnd = result;
      }
    });
  }

  Widget _slotChoiceWidget(int choice) {
    String choiceStr =
        'APPLICATION_MOBILE_MESSAGE_APPOINTMENT_LABEL_REQUEST_SLOT'.tr() +
            ' ${choice + 1} ${choice == 0 ? '*' : ''}';
    DateTime? _start = _appointmentRequest.periods[choice].dateStart;
    DateTime? _end = _appointmentRequest.periods[choice].dateEnd;
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Column(
        children: <Widget>[
          Text(choiceStr),
          Row(
            children: <Widget>[
              Expanded(
                child: DateTimeFormField(
                  onSaved: (newValue) {},
                  validator: (newValue) {
                    if (choice == 0 && _start == null) {
                      return 'APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_DATE'
                          .tr();
                    }
                    return null;
                  },
                  initialValue: _start,
                  onTapFunction: () async {
                    await _chooseDate(choice);
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(8, 0, 8, 0),
                child: Text('APPLICATION_MOBILE_FIELD_LABEL_TO'.tr()),
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () async {
                  await _chooseTime(context, choice);
                },
                child: Builder(
                  builder: (BuildContext context) => Container(
                    alignment: Alignment.center,
                    width: MediaQuery.of(context).size.width / 4,
                    height: 40,
                    child: TimeFormField(
                      initialValue: Data().dateTimeToUserTimeStr(_end),
                      validator: (value) {
                        if (_start != null && _end == null) {
                          return 'APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_END_TIME'
                              .tr();
                        }
                        return null;
                      },
                    ),
                    // decoration: BoxDecoration(
                    //   border: Border(
                    //     bottom: BorderSide(
                    //         width: 1.0,
                    //         color: isDateEndValid
                    //             ? null
                    //             : Theme.of(context).colorScheme.error
                    //     ),
                    //   ),
                    // ),
                  ),
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.delete,
                  color: Theme.of(context).primaryColorLight,
                ),
                onPressed: () {
                  setState(() {
                    _appointmentRequest.periods[choice] = Period();
                  });
                },
              )
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _submitForm(BuildContext context) async {
    final FormState? form = _formKey.currentState;

    if (form == null || !form.validate()) {
      Tools().common.showValidationErrorMessage(context);
      return;
    }
    form.save();
    _appointmentRequest.serviceId = _service!.id;

    MBResponse response = await MbApiPetActivity().addAppointmentRequest(
      context,
      _appointmentRequest,
      images: _images,
    );
    if (response.success) {
      await Tools().common.showSimpleDialog(
          context, 'APPLICATION_MOBILE_MESSAGE_REQUEST_APPOINTMENT_OK'.tr());
      Tools().navigatorPop();
    }
  }
}
