import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/app_config.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';

class ParametersPage extends StatefulWidget {
  const ParametersPage({Key? key}) : super(key: key);

  @override
  _ParametersPageState createState() => _ParametersPageState();
}

class _ParametersPageState extends State<ParametersPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_LABEL_SETTINGS'.tr(),
          maxLines: 2,
        ),
        leading: BackButton(
          onPressed: () => Tools().navigatorPop(value: true),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: <Widget>[
            if (AppConfig.of(context).isMyBuddy)
              SwitchListTile(
                inactiveTrackColor: Theme.of(context).colorScheme.surface,
                activeColor: Theme.of(context).colorScheme.secondary,
                title: Text(
                    'APPLICATION_MOBILE_LABEL_SETTINGS_HOME_PET_SERVICE'.tr()),
                value: SettingsDelegate().get().forcePetView,
                onChanged: (bool value) {
                  if (SettingsDelegate().get().forcePetView != value) {
                    SettingsDelegate().setHomeInPetMode(value);
                    setState(() {});
                  }
                },
              ),

            ///endif
            SwitchListTile(
              inactiveTrackColor: Theme.of(context).colorScheme.surface,
              activeColor: Theme.of(context).colorScheme.secondary,
              title: Text('APPLICATION_MOBILE_LABEL_LANGUAGE_SYSTEM'.tr()),
              value: SettingsDelegate().get().lang == null,
              onChanged: (bool value) async {
                if (!value) {
                  await SettingsDelegate()
                      .setLang(SettingsDelegate().getLang());
                } else {
                  await SettingsDelegate().setLang(null);
                }
                setState(() {});
              },
            ),
            Visibility(
              visible: SettingsDelegate().get().lang != null,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: DropdownButtonFormField<String>(
                  value: SettingsDelegate().get().lang,
                  decoration: InputDecoration(
                    hintText:
                        'APPLICATION_MOBILE_FIELD_LABEL_COUNTRY'.tr() + '*',
                    labelText:
                        'APPLICATION_MOBILE_FIELD_LABEL_COUNTRY'.tr() + '*',
                  ),
                  onChanged: (String? newValue) async {
                    if (newValue != SettingsDelegate().get().lang) {
                      await SettingsDelegate().setLang(newValue);
                    }
                    setState(() {});
                  },
                  validator: (val) {
                    return val != null
                        ? null
                        : 'APPLICATION_MOBILE_LABEL_FIELDS_REQUIRED'.tr();
                  },
                  items: Ref().getActiveCountries().map((Country country) {
                    return DropdownMenuItem<String>(
                      value: country.shortLangLabel.toLowerCase(),
                      child: Text(country.shortLangLabel),
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
