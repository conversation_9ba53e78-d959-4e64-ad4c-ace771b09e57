import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/messages_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/models/message.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/empty_inbox_widget.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/templates/community/community_team_screen.dart';
import 'package:mybuddy/ui/templates/message/message_page.dart';

import 'package:mybuddy/extensions/datetime_extensions.dart';

class WidgetMessageList extends StatelessWidget {
  const WidgetMessageList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<MessagesBloc>(context);
    return StreamBuilder<List<MBMessage>>(
        stream: bloc.stream,
        builder: (context, snapshot) {
          List<MBMessage> messages;
          int separatorIndex;
          if (!snapshot.hasData) return Container();
          messages = snapshot.data!;
          separatorIndex = messages.lastIndexWhere((m) => m.serviceId != null);
          if (messages.isEmpty) {
            return const EmptyInboxWidget(type: InboxType.messages);
          }

          return Scrollbar(
            child: ListView.separated(
              itemBuilder: (context, position) {

                MBMessage message = messages[position];
                MBService? clinic;
                Team? team;

                if(message.serviceId != null) {
                  clinic = Data().get().getService(message.serviceId);
                } else {
                  team = Data().get().teams.singleWhereOrNull((t) => t.messageId == message.id);
                }

                return GestureDetector(
                  onTap: () async {
                    if(message.serviceId == null && team != null) {
                        await Tools().navigatorPush(TeamScreen(team: team));
                    } else {
                      await Tools().navigatorPush(MessagePage(message: message));
                    }
                    bloc.update();
                  },
                  behavior: HitTestBehavior.translucent,
                  child: oneMessage(context, message, clinic: clinic, team: team),
                );
              },
              separatorBuilder: (BuildContext context, int index) {
                if(separatorIndex == index) {
                  return const Divider(indent: 10.0, endIndent: 10.0);
                }
                return const SizedBox.shrink();
              },
              itemCount: messages.length,
            ),
          );
        },
    );
  }

  Widget oneMessage(BuildContext context, MBMessage message, {MBService? clinic, Team? team}) {
    String? title;
    String? url;
    String? subtitle;

    if(clinic != null) {
      title = clinic.name;
      url = clinic.imageUrl;
      subtitle = message.title + (message.items.isNotEmpty ? ' · ${message.items.last.content}' : '');
    } else if(team != null) {
      url = team.imageUrl;
      title = team.name;
      subtitle = (team.description ?? '') + ' · ${team.teamMembersCount} ' + 'APPLICATION_MOBILE_LABEL_COMMUNITY_TEAM_MEMBERS'.tr('members');
    }

    return ListTile(
      leading: MBAvatar(
        size: 45.0,
        imageUrl: url,
        backgroundColor: Theme.of(context).backgroundColor,
        noImageUrl: const Icon(Icons.image, size: 20.0),
      ),
      title: Text(
        title ?? '',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(fontWeight: message.read() ? FontWeight.normal : FontWeight.bold),
      ),
      subtitle: Text(
        subtitle ?? '',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: Theme.of(context).textTheme.caption,
      ),
      trailing: Text(
        (message.items.isEmpty ? message.timestamp : message.items.last.timestamp)?.toDateFormat() ?? '',
        style: Theme.of(context).textTheme.subtitle2,
      ),
    );
  }
}
