import 'dart:async';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:linkwell/linkwell.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_api_com.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/generic_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/models/login_data.dart';
import 'package:mybuddy/models/message.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:mybuddy/ui/components/widget_simple_image_dialog.dart';

class WidgetMessage extends StatefulWidget {
  final int messageId;

  const WidgetMessage({Key? key, required this.messageId}) : super(key: key);

  @override
  _WidgetMessageState createState() => _WidgetMessageState();
}

class _WidgetMessageState extends State<WidgetMessage> {
  final TextEditingController textEditingController = TextEditingController();

  late MBMessage message;
  MBService? service;
  Team? team;
  late bool canUpdate;

  @override
  void initState() {
    super.initState();

    message = Data().get().getMessage(widget.messageId) ??
        MBMessage.light(widget.messageId);
    if (message.serviceId != null) {
      service = Data().get().getService(message.serviceId);
      team = null;
    } else {
      service = null;
      team = Data()
          .get()
          .teams
          .singleWhereOrNull((t) => t.messageId == widget.messageId);
    }
    canUpdate = (service != null && service!.acceptMessage) || team != null;
  }

  @override
  void dispose() {
    textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<GenericBloc>(context);
    return StreamBuilder<LoginData?>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        if(!snapshot.hasData || snapshot.data == null ) {
          return Container();
        }
        //refresh message
        message = Data().get().getMessage(widget.messageId) ?? message;
        return RefreshIndicator(
                onRefresh: () async {
                  await refreshMessage();
                  return;
                },
                child: view(bloc),
              );
      },
    );
  }

  Widget line(MessageItem item) {
    MessageUser? messageUser = message.getItemSender(item.userId);
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: message.isFromOwner(item.userId)
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (!message.isFromOwner(item.userId) && messageUser != null)
                Padding(
                  padding: const EdgeInsets.only(right: 6.0),
                  child: MBAvatar(
                    imageUrl: messageUser.userImageUrl,
                    size: 30.0,
                    backgroundColor: Theme.of(context).primaryColorLight,
                    noImageUrl: Text(
                      messageUser.initials,
                      style: TextStyle(
                          fontSize: 10.0,
                          color: Theme.of(context).primaryColor),
                    ),
                  ),
                ),

              ///endif
              lineContent(item),
            ],
          ),
          Row(
            children: <Widget>[
              message.isFromOwner(item.userId)
                  ? Expanded(child: Container())
                  : Container(),
              Text(
                Data().dateTimeToUserDateTimeStr(item.timestamp),
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Visibility(
                visible: message.isFromOwner(item.userId) &&
                    message.messageUsers.any((mu) =>
                        mu.messageReadAt != null &&
                        mu.messageReadAt!.isAfter(item.timestamp)),
                child: const Icon(
                  FontAwesomeIcons.check,
                  color: Colors.green,
                  size: 13,
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget lineContent(MessageItem item) {
    if (item.content.trim() != '') {
      return Align(
        alignment: message.isFromOwner(item.userId)
            ? Alignment.topRight
            : Alignment.topLeft,
        child: Builder(builder: (context) {
          return Container(
            padding: const EdgeInsets.all(8.0),
            constraints: BoxConstraints(
                minWidth: 0, maxWidth: MediaQuery.of(context).size.width * 0.8),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(10.0)),
              color: message.isFromOwner(item.userId)
                  ? Theme.of(context).colorScheme.secondary
                  : Theme.of(context).primaryColorLight,
            ),
            child: LinkWell(
              item.content,
              textWidthBasis: TextWidthBasis.longestLine,
              style: TextStyle(
                color: message.isFromOwner(item.userId)
                    ? Colors.white
                    : Theme.of(context).primaryColor,
              ),
              linkStyle: message.isFromOwner(item.userId)
                  ? const TextStyle(
                      color: Colors.white, decoration: TextDecoration.underline)
                  : null,
            ),
          );
        }),
      );
    }
    if (item.imageId != null) {
      return Align(
        alignment: message.isFromOwner(item.userId)
            ? Alignment.topRight
            : Alignment.topLeft,
        child: Builder(
          builder: (context) => SizedBox(
            height: 70,
            child: GestureDetector(
              onTap: () async {
                await showDialog(
                  barrierColor: Colors.transparent,
                  context: context,
                  builder: (_) => SimpleImageDialog(
                    imageUrl: item.imageUrl ?? item.imageId?.toImageUrl(),
                    deleteIcon: false,
                  ),
                );
              },
              child: CachedNetworkImage(
                imageUrl: item.imageUrl ?? item.imageId?.toImageUrl() ?? '',
                fit: BoxFit.contain,
                errorWidget: (context, url, error) => const Icon(Icons.error),
              ),
            ),
          ),
        ),
      );
    }
    if (item.file != null) {
      return Align(
        alignment: message.isFromOwner(item.userId)
            ? Alignment.topRight
            : Alignment.topLeft,
        child: Builder(
          builder: (context) => SizedBox(
//              width:MediaQuery.of(context).size.width /2,
            height: 70,
            child: IconButton(
              icon: const Icon(FontAwesomeIcons.file),
              onPressed: () {
                Tools()
                    .common
                    .launchURL(MbBaseApi().getFileUrl(item.file!), context);
              },
            ),
          ),
        ),
      );
    }
    return Container();
  }

  Future<void> refreshMessage() async {
    await MbApiOwner().refreshLoginRequest();
  }

  Future<bool> validImage(BuildContext context, File image) async {
    List<Widget> list = <Widget>[
      Image(image: FileImage(image)),
      TextButton(
        onPressed: () {
          Tools().navigatorPop(removeLast: false, value: true);
        },
        child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SEND'.tr()),
      ),
      const Divider(),
      TextButton(
        onPressed: () {
          Tools().navigatorPop(removeLast: false, value: false);
        },
        child: Text('APPLICATION_MOBILE_BUTTON_LABEL_CANCEL'.tr()),
      )
    ];

    bool? confirmed = await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext ctx) {
        return AlertDialog(
          content: SingleChildScrollView(
            child: Column(
              children: list,
            ),
          ),
        );
      },
    );

    return confirmed ?? false;
  }

  Widget view(GenericBloc bloc) {
    return Builder(
      builder: (BuildContext context) {
        /// set all messages read if necessary
        for (MessageItem item in message.items.reversed.toList()) {
          if (message.messageUser?.messageReadAt != null &&
              item.timestamp.isAfter(message.messageUser!.messageReadAt!)) {
            MbApiCom().setReadMessageRequest(null, message);
            break;
          }
        }
        ScrollController _controller = ScrollController();

        SchedulerBinding.instance!.addPostFrameCallback((_) {
          _controller.jumpTo(_controller.position.maxScrollExtent);
        });

        return Column(
          children: <Widget>[
            Expanded(
              child: Scrollbar(
                child: ListView.builder(
                  controller: _controller,
                  padding: EdgeInsets.zero,
                  itemBuilder: (ctx, position) {
                    return line(message.items[position]);
                  },
                  itemCount: message.items.length,
                ),
              ),
            ),
            Visibility(
              visible: canUpdate,
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: <Widget>[
                      IconButton(
                        icon: const Icon(FontAwesomeIcons.camera),
                        color: Theme.of(context).colorScheme.secondary,
                        onPressed: () async {
                          File? image = await Tools()
                              .image
                              .pickImage(context, myBuddyGallery: true);
                          if (image == null) {
                            return;
                          }
                          if (await validImage(context, image)) {
                            await MbApiCom().addMessageItemRequest(
                                context, message,
                                image: image);
                            bloc.update();
                          }
                        },
                      ),
                      Expanded(
                        child: TextField(
                          maxLines: 2,
                          controller: textEditingController,
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.all(7.0),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(10.0),
                              ),
                            ),
                            filled: true,
                            fillColor: Colors.white70,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(FontAwesomeIcons.solidPaperPlane),
                        onPressed: () async {
                          String value = textEditingController.text;
                          if (value.trim() != '') {
                            MBResponse response =
                                await MbApiCom().addMessageItemRequest(
                              context,
                              message,
                              content: value,
                            );
                            if (response.success) {
                              textEditingController.text = '';
                              bloc.update();
                            }
                          }
                        },
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
