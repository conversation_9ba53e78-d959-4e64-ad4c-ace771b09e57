import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/generic_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/login_data.dart';
import 'package:mybuddy/ui/components/widget_badged_icon.dart';

class WidgetMessagesIcon extends StatelessWidget {
  final IconData? icon;
  final double? size;

  const WidgetMessagesIcon({Key? key, this.icon, this.size}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<GenericBloc>(context);
    return StreamBuilder<LoginData?>(
      stream: bloc.stream,
      builder: (context, snapshot) {
        int? value;
        if (snapshot.hasData) {
          value = Data().getUnreadMessageCount() + Data().getUnreadNotificationCount();
        }
        return BadgedIcon(
          iconData: icon ?? FontAwesomeIcons.comments,
          iconSize: size,
          badgeValue: value,
        );
      },
    );
  }
}
