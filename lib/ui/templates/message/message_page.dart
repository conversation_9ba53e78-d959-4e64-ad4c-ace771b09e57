import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/generic_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/message.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/templates/message/widget_message.dart';

class MessagePage extends StatelessWidget {
  final MBMessage message;
  final String routeName;

  MessagePage({Key? key, required this.message})
      : routeName = 'MessagePage-${message.id}',
        super(key: key);

  @override
  Widget build(BuildContext context) {
    MBService? clinic = Data().get().getService(message.serviceId);
    Tools.debugPrint('open route ${ModalRoute.of(context)!.settings.name}');
    return Scaffold(
      appBar: MBAppBar(
        title: Column(
          children: <Widget>[
            Text(message.title),
            if (clinic != null) Text(clinic.name ?? '') ///endif
          ],
        ),
      ),
      // endDrawer: const MBDrawer(),
      body: BlocProvider<GenericBloc>(
        bloc: GenericBloc(),
        child: WidgetMessage(messageId: message.id),
      ),
    );
  }
}

class MessagePageArguments {
  final MBMessage message;

  MessagePageArguments(this.message);
}
