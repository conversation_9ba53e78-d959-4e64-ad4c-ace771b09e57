import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/workout.dart';
import 'package:mybuddy/ui/templates/workout/workout_details.dart';
import 'package:shimmer/shimmer.dart';

class WorkoutGainPage extends StatefulWidget {
  const WorkoutGainPage(
      {Key? key,
      required this.workout,
      required this.winningWalk,
      required this.points})
      : super(key: key);
  final Workout workout;
  final bool winningWalk;
  final int points;

  @override
  _WorkoutGainPageState createState() => _WorkoutGainPageState();
}

class _WorkoutGainPageState extends State<WorkoutGainPage>
    with TickerProviderStateMixin {
  bool clicked = false;
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(vsync: this)
      ..addStatusListener((AnimationStatus status) {
        if (!clicked && status == AnimationStatus.completed) {
          setState(() {
            _controller.value = 0;
            clicked = true;
          });
        }
      });
  }

  @override
  void dispose() {
    _controller.removeListener(() {});
    _controller.removeStatusListener((status) {});
    _controller.dispose();
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(
                height: 60,
              ),
              const Text(
                'Walk Finished',
                style: TextStyle(
                    fontSize: 40,
                    fontWeight: FontWeight.w900,
                    color: Colors.orangeAccent),
              ),
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.4,
                child: FractionallySizedBox(
                  heightFactor: 1.9,
                  child: _getAnimationWidget(),
                ),
              ),
              AnimatedOpacity(
                opacity: clicked ? 1 : 0,
                duration: const Duration(milliseconds: 1000),
                child: Column(
                  children: [
                    Shimmer.fromColors(
                      period: const Duration(milliseconds: 1500),
                      baseColor: Colors.orange,
                      highlightColor: Colors.yellow,
                      child: Row(
                        children: [
                          Expanded(child: Container()),
                          Text(
                            '+${widget.points}',
                            style: const TextStyle(
                                fontSize: 60,
                                fontWeight: FontWeight.w900,
                                color: Colors.orangeAccent),
                          ),
                          Expanded(child: Container()),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    const Text('Points Earned',
                        style: TextStyle(
                            fontSize: 30, color: Colors.orangeAccent)),
                    const SizedBox(
                      height: 60,
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        await Tools().navigatorReplacement(
                            WorkoutDetails(widget.workout));
                      },
                      style: ElevatedButton.styleFrom(
                        shape: const StadiumBorder(),
                      ),
                      child: Text(
                        'APPLICATION_MOBILE_WOOFTRAX_WORKOUT_GAIN_EARN_BUTTON'
                            .tr('Continue'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _getAnimationWidget() {
    if (!clicked) {
      return Lottie.asset('assets/lottieFiles/jump.json',
          controller: _controller,
          fit: BoxFit.contain, onLoaded: (composition) {
        _controller
          ..duration = composition.duration
          ..forward();
      });
    }
    if (widget.winningWalk) {
      return Lottie.asset('assets/lottieFiles/winning-walk.json',
          controller: _controller,
          fit: BoxFit.contain, onLoaded: (composition) {
        _controller
          ..duration = composition.duration
          ..forward();
      });
    }
    return Lottie.asset('assets/lottieFiles/petsmile.json',
        controller: _controller, fit: BoxFit.contain, onLoaded: (composition) {
      _controller
        ..duration = composition.duration
        ..forward();
    });
  }
}
