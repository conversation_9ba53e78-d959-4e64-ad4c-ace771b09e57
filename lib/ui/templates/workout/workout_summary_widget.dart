import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/pet_workouts_bloc.dart';
import 'package:mybuddy/blocs/workouts_bloc.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/workout.dart';
import 'package:mybuddy/ui/components/earning_points_footer_component.dart';

import '../../../controllers/pet_weight_controller.dart';

class OwnerWorkoutSummary extends StatelessWidget {
  const OwnerWorkoutSummary({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<WorkoutsBloc>(
      bloc: WorkoutsBloc(),
      child: Builder(
        builder: (BuildContext context) {
          final bloc = BlocProvider.of<WorkoutsBloc>(context);
          return StreamBuilder(
            stream: bloc.stream,
            builder: (context, AsyncSnapshot<List<Workout>> snapshot) {
              return snapshot.hasData
                  ? _GenericRow(workouts: snapshot.data!, withPoints: true)
                  : const SizedBox.shrink();
            },
          );
        },
      ),
    );
  }
}

class PetWorkoutSummary extends StatelessWidget {
  final Pet pet;

  PetWorkoutSummary({Key? key, required this.pet}) : super(key: key);

  PetWeightController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return _GenericRow(
        totalMiles: controller.totalMiles.value,
        totalWalks: controller.totalWalks.value,
        workouts: [],
      );
    });
  }
}

class _GenericRow extends StatelessWidget {
  final List<Workout> workouts;
  final bool withPoints;
  final int? totalWalks;
  final double? totalMiles;

  const _GenericRow(
      {Key? key,
      required this.workouts,
      this.withPoints = false,
      this.totalWalks,
      this.totalMiles})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    Owner owner = Data().get().owner;

    TextStyle textStyle = TextStyle(
      fontSize: 12.0,
      color: Theme.of(context).primaryColor,
    );
    double totalDistance = 0.0;
    if (totalMiles != null) {
      totalDistance = totalMiles!;
    } else {
      for (var w in workouts) {
        totalDistance += w.distance;
      }
    }

    return Row(
      children: <Widget>[
        const Icon(
          FontAwesomeIcons.paw,
          size: 10.0,
        ),
        const SizedBox(width: 3.0),
        Text(
          '${totalWalks ?? workouts.length} walks',
          style: textStyle,
        ),
        const SizedBox(width: 6.0),
        const Icon(
          FontAwesomeIcons.shoePrints,
          size: 10.0,
        ),
        const SizedBox(width: 6.0),
        Text(
          '${totalDistance.toUserLength().toLocalStringAsFixed(1)} ${owner.units.toStringDistanceLabel} walked',
          style: textStyle,
        ),
        const SizedBox(width: 6.0),
        if (withPoints) const EarningPointsWidget(size: 12.0),

        ///endif
      ],
    );
  }
}
