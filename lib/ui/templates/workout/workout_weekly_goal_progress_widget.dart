import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/owner_bloc.dart';
import 'package:mybuddy/blocs/workouts_bloc.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/workout.dart';

const Color primaryColor = Color(0xFF532D84);
const Color secondaryColor = Color(0xFFFBE2FF);

class WeeklyGoalProgress extends StatelessWidget {
  final bool minified;

  /// must be wrapped into [BlocProvider] of [WorkoutsBloc]
  const WeeklyGoalProgress({Key? key, this.minified = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<WorkoutsBloc>(context);
    TextStyle? primaryStyle = Theme.of(context)
        .textTheme
        .headline6
        ?.copyWith(color: primaryColor);
    TextStyle? secondaryStyle = Theme.of(context)
        .textTheme
        .subtitle1
        ?.copyWith(color: Theme.of(context).disabledColor);
    TextStyle? minifiedStyle = Theme.of(context)
        .textTheme
        .caption
        ?.copyWith(color: primaryColor);

    return BlocProvider(
      bloc: OwnerBloc(),
      child: Builder(
        builder: (context) {
          final ownerBloc = BlocProvider.of<OwnerBloc>(context);
          return StreamBuilder<Owner>(
            stream: ownerBloc.stream,
            builder: (BuildContext context, AsyncSnapshot<Owner> ownerSnapshot) {
              Owner owner = ownerSnapshot.hasData
                  ? ownerSnapshot.data!
                  : Owner();

              bool hasWeeklyGoal = owner.weeklyWalkingGoal != null;
              int weeklyGoal = owner.weeklyWalkingGoal ?? 1;

              /// Prevent Unsupported operation: Infinity or NaN toInt
              if (weeklyGoal.isNegative) {
                weeklyGoal = weeklyGoal.abs();
              }
              if (weeklyGoal == 0) {
                weeklyGoal = 1;
              }

              DateTime now = DateTime.now();
              int currentDay = now.weekday;
              DateTime firstDayOfWeek = now.subtract(Duration(
                days: currentDay - 1,
                hours: now.hour,
                minutes: now.minute,
                seconds: now.second,
                milliseconds: now.millisecond,
                microseconds: now.microsecond,
              ));

              return StreamBuilder<List<Workout>>(
                stream: bloc.stream,
                builder: (BuildContext context,
                    AsyncSnapshot<List<Workout>> snapshot) {
                  double totalDistance = 0.0;
                  if (snapshot.hasData) {
                    for (Workout w in snapshot.data!) {
                      if (w.createdAt != null &&
                          firstDayOfWeek.isBefore(w.createdAt!)) {
                        totalDistance += w.distance;
                      }
                    }
                  }
                  return Container(
                    width: minified ? 48.0 : double.infinity,
                    decoration: BoxDecoration(
                      gradient: minified
                          ? const LinearGradient(
                              // Where the linear gradient begins and ends
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              // Add one stop for each color. Stops should increase from 0 to 1
                              // stops: [0.25, 1.0],
                              colors: [
                                Color.fromARGB(0, 255, 255, 255),
                                Color.fromARGB(255, 255, 255, 255),
                              ],
                            )
                          : null,
                      borderRadius: minified
                          ? const BorderRadius.vertical(
                              bottom: Radius.circular(12.0))
                          : null,
                    ),
                    margin: EdgeInsets.fromLTRB(
                      minified ? 10.0 : 30.0,
                      15.0,
                      10.0,
                      15.0,
                    ),
                    child: Flex(
                      mainAxisSize: MainAxisSize.min,
                      direction: minified ? Axis.vertical : Axis.horizontal,
                      children: [
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            SizedBox(
                              width: 42.0,
                              height: 42.0,
                              child: CircularProgressIndicator(
                                strokeWidth: 6.0,
                                value: hasWeeklyGoal
                                    ? totalDistance.toUserLength() / weeklyGoal
                                    : 0.0,
                                valueColor: const AlwaysStoppedAnimation<Color>(
                                    primaryColor),
                                backgroundColor: secondaryColor,
                              ),
                            ),
                            Image.asset("assets/icon/footprint.png",height: 18,)
                          ],
                        ),
                        SizedBox(
                          width: minified ? null : 10.0,
                          height: minified ? 3.0 : null,
                        ),
                        Flexible(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (!minified)
                                Text(
                                  'APPLICATION_MOBILE_TITLE_WALKING_WEEKLY_GOAL'
                                      .tr('WEEKLY GOAL'),
                                  style: primaryStyle,
                                ),

                              ///endif

                              if (hasWeeklyGoal)
                                RichText(
                                  textAlign: TextAlign.center,
                                  text: TextSpan(
                                    children: [
                                      /// total distance traveled for the current week
                                      TextSpan(
                                        text: totalDistance
                                            .toUserLength()
                                            .toLocalStringAsFixed(
                                                minified ? 1 : 3),
                                        style: minified
                                            ? minifiedStyle
                                            : primaryStyle,
                                      ),
                                      if (!minified) const TextSpan(text: ' '),

                                      ///endif

                                      if (!minified)

                                        /// user unit distance
                                        TextSpan(
                                          text:
                                              owner.units.toStringDistanceLabel,
                                          style: secondaryStyle,
                                        ),

                                      ///endif

                                      if (!minified) const TextSpan(text: ' '),

                                      ///endif

                                      TextSpan(
                                        text: ' / ',
                                        style: minified
                                            ? minifiedStyle
                                            : secondaryStyle,
                                      ),

                                      if (!minified) const TextSpan(text: ' '),

                                      ///endif

                                      /// weekly walking goal
                                      TextSpan(
                                        text: weeklyGoal
                                            .toUserLength()
                                            .toStringRounded(),
                                        style: minified
                                            ? minifiedStyle
                                            : secondaryStyle,
                                      ),
                                      const TextSpan(text: ' '),

                                      if (!minified)

                                        /// user unit distance
                                        TextSpan(
                                          text:
                                              owner.units.toStringDistanceLabel,
                                          style: secondaryStyle,
                                        ),

                                      ///endif
                                    ],
                                  ),
                                )
                              else
                                Text(
                                  minified
                                      ? '0.0/0'
                                      : 'APPLICATION_MOBILE_ERROR_NO_WALKING_WEEKLY_GOAL'
                                          .tr('No weekly goal'),
                                  style:
                                      minified ? minifiedStyle : secondaryStyle,
                                ),

                              ///endif

                              if (minified) const SizedBox(height: 3.0),

                              ///endif
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
