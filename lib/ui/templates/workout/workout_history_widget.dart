import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/bloc_provider.dart';
import 'package:mybuddy/blocs/workouts_bloc.dart';
import 'package:mybuddy/controllers/workout_history_controller.dart';
import 'package:mybuddy/controllers/workout_sync_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/mb_appbar.dart';
import 'package:mybuddy/ui/components/offline_message_banner.dart';
import 'package:mybuddy/ui/templates/workout/widgets/history_pagination_widget.dart';
import 'package:mybuddy/ui/templates/workout/widgets/workout_stats_widget.dart';
import 'package:mybuddy/ui/templates/workout/workout_history_filter.dart';
import 'package:mybuddy/ui/templates/workout/workout_sync_widget.dart';

import '../../../blocs/walk_provider.dart';
import '../../../class/workout_filter.dart';
import '../../components/mb_no_record_found.dart';
import '../../components/no_internet_widget.dart';
import 'workout_card_widget.dart';

class WorkoutsHistory extends StatelessWidget {
  WorkoutsHistory({Key? key}) : super(key: key);

  WorkoutHistoryController historyController = WorkoutHistoryController.of();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const OfflineMessageBanner(),
        Expanded(
          child: HistoryPaginationWidget(
            controller: historyController,
            emptyWidget: _emptyWidget(context),
            noInternetWidget: NoInternetWidget(
              title: "",
              retryMethod: (){
                WorkoutSyncController syncController = WorkoutSyncController.of();
                syncController.startSyncProcess();
              },
            ),
            leadingWidget: Column(
              children: [
                WorkoutStatsWidget(),
                const SizedBox(
                  height: 10,
                ),
              ],
            ),
            bottomDisplayWidget: const WorkoutSyncWidget(),
            child: (context, index) {
              return WorkoutCardWidget(workout: historyController.dataList[index]);
            },
          ),
        ),
      ],
    );
  }

  Widget _emptyWidget(BuildContext context) {
    final WalkProvider walkProvider = WalkProvider.of(context);

    return Center(
      child: MBNoRecordFound(
        title: 'APPLICATION_MOBILE_TEXT_NO_ACTIVITY'.tr("No Activities Found"),
        otherWidget: (historyController.filter.isFilterApplied())
            ? null
            : Column(
                children: [
                  Text(
                    'APPLICATION_MOBILE_TEXT_NO_WALKS_FOUND_DESC'.tr(),
                    style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: Color(0xffA2B2C6)),
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  SizedBox(
                      width: Get.width * 0.85,
                      child: ElevatedButton(
                        onPressed: () async =>
                            await Tools().location.openWorkoutPage(context),
                        child: Obx(
                          () => Text(
                            walkProvider.getStartWalkCardTitle(firstWalk: true),
                            style: const TextStyle(
                                fontSize: 14, fontWeight: FontWeight.w600),
                          ),
                        ),
                      ))
                ],
              ),
      ),
    );
  }
}

class WorkoutsHistoryPage extends StatefulWidget {
  WorkoutsHistoryPage({Key? key}) : super(key: key);

  @override
  State<WorkoutsHistoryPage> createState() => _WorkoutsHistoryPageState();
}

class _WorkoutsHistoryPageState extends State<WorkoutsHistoryPage> {
  WorkoutSyncController syncController = WorkoutSyncController.of();
  WorkoutHistoryController historyController = WorkoutHistoryController.of();

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    syncController.startSyncProcess();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<WorkoutsBloc>(
      bloc: WorkoutsBloc(),
      child: Scaffold(
        appBar: MBAppBar(
          title: Text(
            'APPLICATION_MOBILE_LABEL_TAB_BUDDYCREDITS_HISTORY'.tr(),
            style: const TextStyle(fontWeight: FontWeight.w700, fontSize: 22),
          ),
          actions: [
            _filterButton(),
          ],
        ),
        body: WorkoutsHistory(),
      ),
    );
  }

  Widget _filterButton() {
    return Obx(() {
      if (syncController.isSyncNotCompleted) {
          return const SizedBox();
      }
      return TextButton.icon(
        onPressed: () async {
          await Tools().navigatorPush(WorkoutsHistoryFilterPage());
          setState(() {});
        },
        icon: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                left: 4.0,
                top: 4.0,
              ),
              child: Image.asset(
                "assets/icon/filter.png",
                height: 18,
              ),
            ),
            if (historyController.filter.isFilterApplied())
              Container(
                width: 14,
                height: 14,
                decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.secondary,
                    borderRadius: BorderRadius.circular(100)),
                child: Center(
                  child: Text(
                    "${historyController.filter.getFilterCount()}",
                    style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w600),
                  ),
                ),
              )
          ],
        ),
        label: Padding(
          padding: const EdgeInsets.only(top: 4.0),
          child: Text(
            "APPLICATION_MOBILE_LABEL_TAB_BUDDYCREDITS_HISTORY_FILTER".tr(),
            style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Color(0xff1D3E3D)),
          ),
        ),
      );
    });
  }
}
