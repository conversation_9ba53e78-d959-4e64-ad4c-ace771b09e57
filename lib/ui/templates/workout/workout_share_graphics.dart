import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:intl/intl.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:ui' as ui;
import '../../../class/data.dart';
import '../../../models/walk_model.dart';
import '../../../models/workout.dart';
import '../../components/mb_stackable_avatar.dart';
import 'widgets/workout_info_tile.dart';

class WorkoutShareGraphicsScreen extends StatelessWidget {
  final Workout workout;

  const WorkoutShareGraphicsScreen(
    this.workout, {
    Key? key,
  }) : super(key: key);

  static GlobalKey previewContainer = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        RepaintBoundary(
          key: previewContainer,
          child: Container(
            decoration: BoxDecoration(
                color: const Color(0xffE8ECF0),
                borderRadius: BorderRadius.circular(8)),
            child: <PERSON><PERSON>(
              children: [
                _logoContainer(),
                <PERSON>umn(
                  children: [
                    _petContainer(context),
                    _workoutTile(context),
                    const SizedBox(
                      height: 15,
                    )
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(
          height: 15,
        ),
        Flexible(
            child: Align(
                alignment: Alignment.bottomCenter,
                child: _shareButton(context))),
        const SizedBox(
          height: 20,
        ),
      ],
    );
  }

  Widget _logoContainer() {
    return Container(
      height: 150,
      decoration: BoxDecoration(
          color: const Color(0xff439460),
          borderRadius: BorderRadius.circular(8)),
      padding: const EdgeInsets.only(left: 100, right: 100, bottom: 50),
      child: Image.asset(
        "assets/images/wooftrax.png",
        color: Colors.white,
      ),
    );
  }

  Widget _shareButton(context) {
    return Row(
      children: [
        Expanded(
          child: RawMaterialButton(
            onPressed: () async {
              final directory = await getApplicationDocumentsDirectory();
              final imagePath =
                  await File('${directory.path}/image.png').create();

              RenderRepaintBoundary boundary = previewContainer.currentContext!
                  .findRenderObject() as RenderRepaintBoundary;
              ui.Image image = await boundary.toImage(pixelRatio: 3.0);
              ByteData? byteData =
                  await image.toByteData(format: ui.ImageByteFormat.png);

              await imagePath.writeAsBytes(byteData!.buffer.asUint8List());

              await Share.shareFiles([imagePath.path]);
            },
            elevation: 10.0,
            fillColor: Theme.of(context).colorScheme.secondary,
            padding: const EdgeInsets.symmetric(vertical: 15.0),
            shape: const StadiumBorder(
              side: BorderSide.none,
            ),
            child: Text(
              'APPLICATION_MOBILE_LABEL_WORKOUT_SHARE_GRAPHIC'.tr(),
              style: Theme.of(context).textTheme.headline6?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    color: Colors.white,
                  ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _petContainer(context) {
    return Container(
      padding: const EdgeInsets.all(15),
      margin: const EdgeInsets.only(top: 90, left: 16, right: 16, bottom: 15),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8)),
      child: Column(
        children: [
          Text(
            "APPLICATION_MOBILE_TITLE_SHARE_PET_WALK".tr("I walked with"),
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context).textTheme.headline6?.copyWith(
                color: Colors.grey[800],
                fontSize: 13,
                fontWeight: FontWeight.w500),
          ),
          const Divider(
            color: Color(0xfff3f3f3),
          ),
          workout.pets.isNotEmpty
              ? FittedBox(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                        workout.getFilledPets().length > 5
                            ? 5
                            : workout.getFilledPets().length, (index) {
                      var data = workout.getFilledPets()[index];
                      if (index == 4) {
                        return Container(
                            margin: EdgeInsets.only(left: index == 0 ? 0 : 10),
                            width: 60,
                            child: Column(
                              children: [
                                const MBDefaultImage(size: 50),
                                const SizedBox(
                                  height: 10,
                                ),
                                Text(
                                  "${workout.getFilledPets().length - 4} more",
                                  overflow: TextOverflow.ellipsis,
                                  style: Theme.of(context)
                                      .textTheme
                                      .headline6
                                      ?.copyWith(
                                          color: const Color(0xff162941),
                                          fontSize: 11,
                                          fontWeight: FontWeight.w400),
                                ),
                              ],
                            ));
                      }
                      return Container(
                          margin: EdgeInsets.only(left: index == 0 ? 0 : 10),
                          width: 60,
                          child: Column(
                            children: [
                              MBDogAvatar(
                                avatarURL: data.avatarUrl,
                                deceased: data.deceased,
                                size: 50,
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Text(
                                data.name,
                                overflow: TextOverflow.ellipsis,
                                style: Theme.of(context)
                                    .textTheme
                                    .headline6
                                    ?.copyWith(
                                        color: const Color(0xff162941),
                                        fontSize: 11,
                                        fontWeight: FontWeight.w400),
                              ),
                            ],
                          ));
                    }),
                  ),
                )
              : Column(
                  children: [
                    const MBDefaultDogImage(size: 50),
                    const SizedBox(
                      height: 10,
                    ),
                    Text(
                      'APPLICATION_MOBILE_LABEL_PET_LEXI'.tr('Lexi'),
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context).textTheme.headline6?.copyWith(
                          color: const Color(0xff162941),
                          fontSize: 11,
                          fontWeight: FontWeight.w400),
                    ),
                  ],
                ),
          const Divider(
            color: Color(0xfff3f3f3),
          ),
          OverflowBar(
            overflowAlignment: OverflowBarAlignment.center,
            children: [
              Text(
                "APPLICATION_MOBILE_TITLE_SHARE_CHARITY_SUPPORT"
                    .tr("In Support of "),
                overflow: TextOverflow.ellipsis,
                textScaleFactor: 1,
                style: Theme.of(context).textTheme.headline6?.copyWith(
                    color: Colors.grey[500],
                    fontSize: 12,
                    fontWeight: FontWeight.w500),
              ),
              Text(
                workout.shelter?.name ?? "-",
                textScaleFactor: 1,
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.headline6?.copyWith(
                    color: Colors.black,
                    fontSize: 12,
                    fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _workoutTile(context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                WorkoutInfoTile(
                  "${workout.distance.toUserLength().toLocalStringAsFixed(2)} ${Data().get().owner.units.toStringDistanceLabel}",
                  'APPLICATION_MOBILE_TITLE_SHARE_DISTANCE'.tr("Distance"),
                  MoodType.values
                      .elementAt(workout.mood != null && workout.mood != 0
                          ? workout.mood! - 1
                          : 0)
                      .toIcon,
                  background: true,
                ),
                const SizedBox(
                  height: 15,
                ),
                WorkoutInfoTile(
                  workout.duration.toENTimeString(),
                  "APPLICATION_MOBILE_TITLE_SHARE_DURATION".tr("Duration"),
                  "assets/icon/clock.png",
                  background: true,
                ),
              ],
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                WorkoutInfoTile(
                  DateFormat.yMMMd('en_US').format(workout.createdAt!),
                  "APPLICATION_MOBILE_TITLE_SHARE_DATE".tr("Date"),
                  "assets/icon/calendar.png",
                  background: true,
                ),
                const SizedBox(
                  height: 15,
                ),
                WorkoutInfoTile(
                  "${workout.speed.toUserLength().toStringAsFixed(2)} ${Data().get().owner.units.toStringSpeedLabel}",
                  "APPLICATION_MOBILE_TITLE_SHARE_AVG_SPEED".tr("Avg. Speed"),
                  "assets/icon/speed_arrow.png",
                  background: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
