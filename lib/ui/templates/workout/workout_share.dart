import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/ui/templates/workout/workout_share_graphics.dart';
import 'package:mybuddy/ui/templates/workout/workout_share_map.dart';

import '../../../models/workout.dart';
import '../../components/mb_base_home_screen.dart';

class WorkoutShareScreen extends StatefulWidget {
  final Workout workout;
  final Uint8List? snapshot;

  const WorkoutShareScreen(
    this.workout,
    this.snapshot, {
    Key? key,
  }) : super(key: key);

  @override
  State<WorkoutShareScreen> createState() => _WorkoutShareScreenState();
}

class _WorkoutShareScreenState extends State<WorkoutShareScreen>
    with SingleTickerProviderStateMixin {
  late TabController tabController;

  List<TabModel> tabs = <TabModel>[];

  @override
  void initState() {
    super.initState();
    tabs.add(TabModel(
        title: 'APPLICATION_MOBILE_TITLE_GRAPHIC_SHARE'.tr('Graphic'),
        view: WorkoutShareGraphicsScreen(widget.workout)));
    if (widget.snapshot != null) {
      tabs.add(TabModel(
          title: 'APPLICATION_MOBILE_TITLE_MAP_SHARE'.tr('Map'),
          view: WorkoutShareMapScreen(widget.workout, widget.snapshot!)));
    }
    tabController = TabController(length: tabs.length, vsync: this);
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: HomeSliverScreen(
        expandedHeight: 0.0,
        automaticallyImplyLeading: true,
        primaryColor: const Color(0xff162941),
        title: 'APPLICATION_MOBILE_TITLE_SHARE'.tr('Share'),
        tabs: tabs.map((e) => e.title).toList(),
        tabController: tabController,
        tabFontSize: 15,
        tabFontWeight: FontWeight.w700,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: TabBarView(
            controller: tabController,
            physics: const NeverScrollableScrollPhysics(),
            children: tabs.map((e) => e.view).toList(),
          ),
        ),
      ),
    );
  }
}

class TabModel {
  String title;
  Widget view;

  TabModel({required this.title, required this.view});
}
