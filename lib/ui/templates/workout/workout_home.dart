import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/walk_provider.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/location_map_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/shelter.dart';
import 'package:mybuddy/ui/templates/workout/workout_mood_selection.dart';
import 'package:mybuddy/ui/templates/workout/workout_pet_selection.dart';

import '../../components/current_location_map_widget.dart';
import '../../components/mb_stackable_avatar.dart';
import '../home/<USER>/gps_indicator.dart';

class QuickStartWalkWidget extends StatefulWidget {
  const QuickStartWalkWidget({Key? key}) : super(key: key);

  @override
  _QuickStartWalkWidgetState createState() => _QuickStartWalkWidgetState();
}

class _QuickStartWalkWidgetState extends State<QuickStartWalkWidget>
    with AutomaticKeepAliveClientMixin {
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  GlobalKey bottomCardKey = GlobalKey();
  final LocationMapController controller = Get.put(LocationMapController());

  final Owner owner = Data().get().owner;
  late Shelter? _shelter;

  @override
  void initState() {
    super.initState();
    _shelter = owner.shelter;
  }

  @override
  Widget build(BuildContext context) {
    // super.build(context);
    final WalkProvider walkProvider = WalkProvider.of(context);

    return Scaffold(
      key: scaffoldKey,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        automaticallyImplyLeading: false,
        elevation: 0.0,
        backgroundColor: Colors.transparent,
      ),
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Padding(
            padding: EdgeInsets.only(
                bottom:
                    bottomCardKey.currentState?.context.size?.height ?? 180),
            child: CurrentLocationMapWidget(),
          ),
          Container(
            key: bottomCardKey,
            decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
                boxShadow: [
                  BoxShadow(
                      color: Color.fromRGBO(0, 0, 0, 0.0731),
                      offset: Offset(0, 0.75),
                      blurRadius: 2.21),
                  BoxShadow(
                      color: Color.fromRGBO(0, 0, 0, 0.105),
                      offset: Offset(0, 1.8),
                      blurRadius: 5.32),
                  BoxShadow(
                      color: Color.fromRGBO(0, 0, 0, 0.13),
                      offset: Offset(0, 3.38),
                      blurRadius: 10.02),
                  BoxShadow(
                      color: Color.fromRGBO(0, 0, 0, 0.155),
                      offset: Offset(0, 6.03),
                      blurRadius: 17.87),
                  BoxShadow(
                      color: Color.fromRGBO(0, 0, 0, 0.1869),
                      offset: Offset(0, 11.28),
                      blurRadius: 33.42),
                  BoxShadow(
                      color: Color.fromRGBO(0, 0, 0, 0.26),
                      offset: Offset(0, 27),
                      blurRadius: 80),
                ]),
            margin:
                EdgeInsets.only(top: MediaQuery.of(context).padding.top + 45.0),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  RichText(
                      text: TextSpan(
                          text: "Walking for ",
                          style: const TextStyle(
                              color: Color(0xff7F7F7F),
                              fontSize: 12,
                              fontWeight: FontWeight.w500),
                          children: [
                        TextSpan(
                            text: _shelter!.name,
                            style: const TextStyle(color: Color(0xff162941)))
                      ])),
                  const SizedBox(
                    height: 15,
                  ),
                  Row(
                    children: [
                      Obx(
                        () => selectionCard(
                            Image.asset(
                              "assets/icon/${walkProvider.type}_dual.png"
                                  .toLowerCase(),
                              height: 23,
                            ),
                            walkProvider.type, () {
                          scaffoldKey.currentState
                              ?.showBottomSheet((_) => WorkoutMoodSelection());
                        }),
                      ),
                      const SizedBox(
                        width: 15,
                      ),
                      Obx(
                        () => selectionCard(
                            walkProvider.pets.isEmpty
                                ? const MBDefaultDogImage(size: 23)
                                : MBAvatar(
                                    imageUrl: walkProvider.pets.first.avatarUrl,
                                    size: 23,
                                  ),
                            walkProvider.pets.isEmpty
                                ? "APPLICATION_MOBILE_LABEL_PET_LEXI".tr()
                                : walkProvider.pets.length > 1
                                    ? "${walkProvider.pets.length} pets"
                                    : walkProvider.pets.first.name, () {
                          scaffoldKey.currentState?.showBottomSheet(
                            (_) => WorkoutPetSelection(),
                          );
                        }),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () async {
                        await Tools().location.openWorkoutPage(context);
                      },
                      child: Obx(
                        () => Text(
                          walkProvider.getStartWalkCardTitle(),
                          style: const TextStyle(
                              fontSize: 18, fontWeight: FontWeight.w500),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            right: 0,
            top: 45,
            child: GPSIndicator(),
          ),
        ],
      ),
    );
  }

  Widget selectionCard(icon, title, onTap) {
    return Expanded(
      child: Card(
        shape: const RoundedRectangleBorder(
          side: BorderSide(
            color: Color(0xffCECECE),
            width: 0.5,
          ),
          borderRadius: BorderRadius.all(Radius.circular(8.0)),
        ),
        margin: EdgeInsets.zero,
        child: ListTile(
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0)),
          ),
          leading: icon is IconData
              ? Icon(
                  icon,
                  size: 23,
                  color: Theme.of(context).colorScheme.secondary,
                )
              : icon,
          minLeadingWidth: 0,
          horizontalTitleGap: 8,
          title: Text(
            title,
            style: Theme.of(context)
                .textTheme
                .bodyLarge!
                .copyWith(fontSize: 13, fontWeight: FontWeight.w500, overflow: TextOverflow.ellipsis),
          ),
          trailing: Icon(
            Icons.keyboard_arrow_down_rounded,
            size: 22.0,
            color: Theme.of(context).colorScheme.secondary,
          ),
          dense: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 8.0),
          tileColor: const Color(0xffF1F7F7),
          onTap: onTap,
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
