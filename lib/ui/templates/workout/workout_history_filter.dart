import 'package:flutter/material.dart';
import 'package:mybuddy/controllers/workout_history_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/workout/widgets/stacked_pet_images_widget.dart';
import 'package:mybuddy/ui/templates/workout/widgets/workout_filter_pet_selection.dart';

import '../../../Tools/tools.dart';
import '../../../class/workout_filter.dart';
import '../../../models/walk_model.dart';
import '../../components/mb_appbar.dart';
import '../../components/mb_stackable_avatar.dart';

class WorkoutsHistoryFilterPage extends StatefulWidget {
  WorkoutsHistoryFilterPage({Key? key}) : super(key: key);

  @override
  State<WorkoutsHistoryFilterPage> createState() =>
      _WorkoutsHistoryFilterPageState();
}

class _WorkoutsHistoryFilterPageState extends State<WorkoutsHistoryFilterPage> {
  WorkoutHistoryController controller = WorkoutHistoryController.of();

  late WorkoutFilter filter;
  late List<DateSelectionType> valuesWithoutAllTime;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    controller.updateFilterPetsObject();
    filter = controller.filter.copyWith();

    valuesWithoutAllTime = DateSelectionType.values
        .where((e) => e != DateSelectionType.allTime)
        .toList();

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFBFBFB),
      appBar: MBAppBar(
        title: Text(
          'APPLICATION_MOBILE_LABEL_TAB_BUDDYCREDITS_HISTORY_FILTER'.tr(),
          style: const TextStyle(fontWeight: FontWeight.w700, fontSize: 22),
        ),
        actions: [
          TextButton(
              onPressed: () {
                setState(() {
                  filter = WorkoutFilter.defaultFilter();
                });
                controller.applyFilter(filter);
                Tools().navigatorPop();
              },
              child: Text(
                  "APPLICATION_MOBILE_LABEL_TAB_BUDDYCREDITS_HISTORY_FILTER_CLEAR_ALL"
                      .tr(),
                  style: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: Color(0xff282828))))
        ],
      ),
      bottomNavigationBar: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 15),
        child: ElevatedButton(
          onPressed: () {
            controller.applyFilter(filter);
            Tools().navigatorPop();
          },
          child: Text(
            'APPLICATION_MOBILE_LABEL_WORKOUT_FILTER_APPLY'.tr(),
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 25,
            ),
            const Text("Pets",
                style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600)),
            const SizedBox(
              height: 10,
            ),
            Card(
              shape: const RoundedRectangleBorder(
                side: BorderSide(
                  color: Color(0xffCECECE),
                  width: 0.5,
                ),
                borderRadius: BorderRadius.all(Radius.circular(8.0)),
              ),
              margin: EdgeInsets.zero,
              child: ListTile(
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8.0)),
                ),
                leading: filter.pets.length == 1
                    ? MBDogAvatar(
                        avatarURL: filter.pets.first.avatarUrl,
                        deceased: filter.pets.first.deceased,
                        size: 25,
                      )
                    : PetStackedAvatar(
                        pets: filter.pets,
                        size: 25,
                        crossDirection: true,
                      ),
                horizontalTitleGap: -10,
                minLeadingWidth: 40,
                title: Text(
                  filter.pets.isEmpty
                      ? 'APPLICATION_MOBILE_LABEL_PET_LEXI'.tr('Lexi')
                      : filter.pets.length == 1
                          ? filter.pets.first.name
                          : "${filter.pets.length} Dogs",
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      overflow: TextOverflow.ellipsis),
                ),
                trailing: Icon(
                  Icons.keyboard_arrow_down_rounded,
                  size: 22.0,
                  color: Theme.of(context).colorScheme.secondary,
                ),
                dense: true,
                contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                onTap: () async {
                  await showModalBottomSheet(
                    isScrollControlled: true,
                    context: context,
                    builder: (_) => WorkoutFilterPetSelection(
                      filter: filter,
                      onFilterChanged: (filter) {
                        setState(() {
                          this.filter = filter;
                        });
                      },
                    ),
                  );
                },
              ),
            ),
            const SizedBox(
              height: 25,
            ),
            const Text("Activities",
                style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600)),
            const SizedBox(
              height: 10,
            ),
            Container(
              height: 50,
              child: ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                scrollDirection: Axis.horizontal,
                separatorBuilder: (ctx, index) {
                  return const SizedBox(
                    width: 15,
                  );
                },
                itemCount: MoodType.values.length,
                itemBuilder: (ctx, index) {
                  MoodType mood = MoodType.values[index];
                  Map<String, dynamic> moodMapped = mood.toMap;
                  bool isSelected = filter.isMoodSelected(mood);
                  return FilterCard(
                    isSelected: isSelected,
                    showSelectedIcon: false,
                    prefixIcon: isSelected
                        ? "${moodMapped['name']}_dual.png".toLowerCase()
                        : "${moodMapped['name']}_dual_gray.png".toLowerCase(),
                    label: moodMapped['name'],
                    width: (MediaQuery.of(context).size.width / 3) - 20,
                    onTap: () {
                      filter.setActivities(mood);
                      setState(() {});
                    },
                  );
                },
              ),
            ),
            const SizedBox(
              height: 25,
            ),
            const Text("Date Range",
                style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600)),
            const SizedBox(
              height: 10,
            ),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (ctx, index) {
                if (index == 2) {
                  return Column(
                    children: const [
                      SizedBox(
                        height: 15,
                      ),
                      Text(
                        "Or",
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.w500),
                      ),
                      SizedBox(
                        height: 15,
                      )
                    ],
                  );
                }
                return const SizedBox(
                  height: 15,
                );
              },
              itemCount: valuesWithoutAllTime.length,
              itemBuilder: (ctx, index) {
                DateSelectionType dateType = valuesWithoutAllTime[index];
                Map<String, dynamic> typeMapped = dateType.toMap;
                bool isSelected = filter.isDateTypeSelected(dateType);
                return FilterCard(
                  isSelected: isSelected,
                  showSelectedIcon: true,
                  prefixIcon: typeMapped['iconData'],
                  prefixIconColor: dateType == DateSelectionType.custom
                      ? const Color(0xff2E2C2D)
                      : null,
                  label: typeMapped['name'],
                  width: (MediaQuery.of(context).size.width / 3) - 20,
                  onTap: () async {
                    if (dateType == DateSelectionType.custom) {
                      DateTimeRange? range =
                          await Tools().common.showDateRangeDialog(
                                context: context,
                            helpText: "Workout History Date Filter",
                                initialDateRange: DateTimeRange(
                                    start: filter.startDate!,
                                    end: filter.endDate!),
                              );
                      if (range != null) {
                        filter.setDateRange(dateType, range: range);
                        setState(() {});
                      }
                    } else {
                      filter.setDateRange(dateType);
                      setState(() {});
                    }
                  },
                );
              },
            )
          ],
        ),
      ),
    );
  }
}

class FilterCard extends StatelessWidget {
  const FilterCard(
      {Key? key,
      required this.label,
      this.prefixIcon,
      this.prefixIconColor,
      required this.isSelected,
      required this.showSelectedIcon,
      required this.width,
      required this.onTap})
      : super(key: key);
  final prefixIcon;
  final bool showSelectedIcon, isSelected;
  final String label;
  final double width;
  final Color? prefixIconColor;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: isSelected
          ? const RoundedRectangleBorder(
              side: BorderSide(
                color: Color(0xffADD2C2),
                width: 1,
              ),
              borderRadius: BorderRadius.all(Radius.circular(8.0)),
            )
          : const RoundedRectangleBorder(
              side: BorderSide(
                color: Color(0xffCECECE),
                width: 0.5,
              ),
              borderRadius: BorderRadius.all(Radius.circular(8.0)),
            ),
      margin: EdgeInsets.zero,
      child: SizedBox(
        width: width,
        child: ListTile(
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0)),
          ),
          leading: prefixIcon == null
              ? null
              : prefixIcon is IconData
                  ? Icon(
                      prefixIcon,
                      size: 23,
                      color: prefixIconColor ??
                          Theme.of(context).colorScheme.secondary,
                    )
                  : Image.asset(
                      "assets/icon/$prefixIcon",
                      height: 23,
                    ),
          minLeadingWidth: 0,
          horizontalTitleGap: 8,
          title: Text(
            label,
            style: Theme.of(context)
                .textTheme
                .bodyLarge!
                .copyWith(fontSize: 13, fontWeight: FontWeight.w500),
          ),
          trailing: showSelectedIcon
              ? isSelected
                  ? Icon(
                      Icons.check,
                      size: 16.0,
                      color: Theme.of(context).colorScheme.secondary,
                    )
                  : null
              : null,
          dense: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 8.0),
          selected: isSelected,
          selectedTileColor: const Color(0xffDEF9ED),
          tileColor: const Color(0xffF1F7F7),
          onTap: onTap,
        ),
      ),
    );
  }
}
