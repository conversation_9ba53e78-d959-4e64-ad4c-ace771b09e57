import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/walk_provider.dart';
import 'package:mybuddy/models/walk_model.dart';
import 'package:mybuddy/ui/components/mb_drag_bar_indicator.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class WorkoutMoodSelection extends StatefulWidget {
  WorkoutMoodSelection({Key? key}) : super(key: key);

  @override
  State<WorkoutMoodSelection> createState() => _WorkoutMoodSelectionState();
}

class _WorkoutMoodSelectionState extends State<WorkoutMoodSelection> {
  
  final walkProvider = WalkProvider.of();
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const MBDragBarIndicator(),
          const SizedBox(
            height: 20,
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.only(bottom: 10.0),
            itemCount: MoodType.values.length,
            separatorBuilder: (BuildContext context, int index) {
              return const SizedBox(
                height: 15,
              );
            },
            itemBuilder: (BuildContext context, int index) {
              MoodType mood = MoodType.values[index];
              Map<String, dynamic> moodMapped = mood.toMap;
              bool isSelected =
                  walkProvider.mood == mood;
              return Card(
                margin: EdgeInsets.zero,
                shape: isSelected
                    ? const RoundedRectangleBorder(
                        side: BorderSide(
                          color: Color(0xffADD2C2),
                          width: 1,
                        ),
                        borderRadius: BorderRadius.all(Radius.circular(8.0)),
                      )
                    : const RoundedRectangleBorder(
                        side: BorderSide(
                            color: const Color(0xffCECECE), width: 0.5),
              borderRadius: BorderRadius.all(Radius.circular(8.0))
                ),
                child: ListTile(
                  shape: isSelected
                      ? const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(8.0)),
                  )
                      : const RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(8.0))
                  ),
                  leading: Image.asset(
                    "assets/icon/${moodMapped['name']}_dual.png".toLowerCase(),
                    height: 23,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                  title: Text(
                    moodMapped['name'],
                    style: Theme.of(context)
                        .textTheme
                        .bodyLarge!
                        .copyWith(fontSize: 13, fontWeight: FontWeight.w500),
                  ),
                  trailing: isSelected
                      ? Icon(
                          Icons.check,
                          size: 16.0,
                          color: Theme.of(context).colorScheme.secondary,
                        )
                      : null,
                  dense: true,
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 8.0, horizontal: 15),
                  selected: isSelected,
                  selectedTileColor: const Color(0xffDEF9ED),
                  tileColor: const Color(0xffF1F7F7),
                  minLeadingWidth: 0,
                  onTap: () {
                    walkProvider.setMood(mood);
                    setState(() {});
                  },
                ),
              );
            },
          ),
          const Divider(),
          const SizedBox(
            height: 8,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "${walkProvider.type} selected",
                style:
                    const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
              ElevatedButton(
                onPressed: () async {
                  Tools().navigatorPop();
                },
                child: const Text(
                  "Done",
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                    fixedSize: const Size(130, 45),
                    alignment: Alignment.center,
                    padding: EdgeInsets.zero,
                    primary: const Color(0xff218359)
                ),
              )
            ],
          ),
          const SizedBox(
            height: 20,
          ),
        ],
      ),
    );
  }
}
