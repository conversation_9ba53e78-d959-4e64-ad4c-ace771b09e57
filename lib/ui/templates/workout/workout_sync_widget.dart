import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mybuddy/controllers/workout_sync_controller.dart';

class WorkoutSyncWidget extends StatefulWidget {
  const WorkoutSyncWidget({Key? key}) : super(key: key);

  @override
  State<WorkoutSyncWidget> createState() => _WorkoutSyncWidgetState();
}

class _WorkoutSyncWidgetState extends State<WorkoutSyncWidget> {
  late WorkoutSyncController controller;

  @override
  void initState() {
    super.initState();
    controller = WorkoutSyncController.of();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(15.0),
      child: Center(
        child: Obx(() => Text(
              controller.syncInProgress()
                  ? "Your walks are loading... (${controller.syncProgressRoundedValue}%)"
                  : controller.isSyncStopped()
                      ? "Your remaining walks will be loaded once you’re online."
                      : "Your walks are loaded",
              style: const TextStyle(
                  color: Color(0xFF939393),
                  fontSize: 13,
                  fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            )),
      ),
    );
  }
}
