import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:mybuddy/blocs/walk_provider.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/ui/components/mb_drag_bar_indicator.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';

import '../../../../Tools/tools.dart';
import '../../../../class/data.dart';
import '../../../../class/workout_filter.dart';

class WorkoutFilterPetSelection extends StatefulWidget {
  WorkoutFilterPetSelection(
      {Key? key, required this.filter, required this.onFilterChanged})
      : super(key: key);

  final WorkoutFilter filter;
  final Function(WorkoutFilter) onFilterChanged;

  @override
  State<WorkoutFilterPetSelection> createState() =>
      _WorkoutFilterPetSelectionState();
}

class _WorkoutFilterPetSelectionState extends State<WorkoutFilterPetSelection> {
  late WorkoutFilter filter;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    setState(() {
      filter = widget.filter;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const MBDragBarIndicator(
            width: 20,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                  'APPLICATION_MOBILE_TITLE_WORKOUT_PET_SELECTION'
                      .tr('Select your Dog(s)'),
                  style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        color: const Color(0xff474747),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      )),
              TextButton(
                  onPressed: () {
                    filter.setAllPets(Data().get().pets);
                    setState(() {});
                  },
                  child: Text('Select all',
                      style: Theme.of(context)
                          .textTheme
                          .headlineSmall!
                          .copyWith(
                              color: const Color(0xff474747),
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              decoration: TextDecoration.underline)))
            ],
          ),
          Data().get().pets.isEmpty
              ? SizedBox(height: 80, child: _defaultDogListItem())
              : SizedBox(
                  height: 300,
                  child: ListView.separated(
                    shrinkWrap: true,
                    padding: const EdgeInsets.only(bottom: 10.0),
                    itemCount: Data().get().pets.length,
                    separatorBuilder: (BuildContext context, int index) {
                      return const SizedBox(
                        height: 15,
                      );
                    },
                    itemBuilder: (BuildContext context, int index) {
                      Pet pet = Data().get().pets[index].copy();
                      bool isSelected = filter.isPetSelected(pet);

                      return _petListItem(pet, isSelected);
                    },
                  ),
                ),
          const Divider(),
          const SizedBox(
            height: 8,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getPetSelectedCount(),
                style:
                    const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
              ElevatedButton(
                onPressed: () async {
                  widget.onFilterChanged(filter);
                  Tools().navigatorPop();
                },
                child: const Text(
                  "Done",
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                    fixedSize: const Size(130, 45),
                    alignment: Alignment.center,
                    padding: EdgeInsets.zero,
                    backgroundColor: Color(0xff218359)),
              )
            ],
          ),
          const SizedBox(
            height: 20,
          ),
        ],
      ),
    );
  }

  Widget _petListItem(Pet pet, bool isSelected) {
    return Card(
      margin: EdgeInsets.zero,
      shape: isSelected
          ? const RoundedRectangleBorder(
              side: BorderSide(
                color: Color(0xffADD2C2),
                width: 1,
              ),
              borderRadius: BorderRadius.all(Radius.circular(8.0)),
            )
          : const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8.0)),
              side: BorderSide(color: Color(0xffCECECE), width: 0.5)),
      child: ListTile(
        shape: isSelected
            ? const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0)),
              )
            : const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0))),
        leading: MBDogAvatar(
          avatarURL: pet.avatarUrl,
          deceased: pet.deceased,
          size: 45.0,
        ),
        title: Text(
          pet.name,
          style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                color: const Color(0xff474747),
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
        ),
        trailing: isSelected
            ? Icon(
                FontAwesomeIcons.check,
                size: 16.0,
                color: Theme.of(context).colorScheme.secondary,
              )
            : null,
        contentPadding:
            const EdgeInsets.symmetric(vertical: 8.0, horizontal: 15),
        selected: isSelected,
        selectedTileColor: const Color(0xffDEF9ED),
        tileColor: const Color(0xffF1F7F7),
        minLeadingWidth: 0,
        onTap: () {
          filter.setPets(pet);
          setState(() {});
        },
      ),
    );
  }

  Widget _defaultDogListItem() {
    return Card(
      margin: EdgeInsets.zero,
      shape: const RoundedRectangleBorder(
        side: BorderSide(
          color: Color(0xffADD2C2),
          width: 1,
        ),
        borderRadius: BorderRadius.all(Radius.circular(8.0)),
      ),
      child: ListTile(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8.0)),
        ),
        leading: const MBDefaultDogImage(size: 45.0),
        title: Text(
          'APPLICATION_MOBILE_LABEL_PET_LEXI'.tr('Lexi'),
          style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                color: const Color(0xff474747),
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
        ),
        trailing: Icon(
          FontAwesomeIcons.check,
          size: 16.0,
          color: Theme.of(context).colorScheme.secondary,
        ),
        contentPadding:
            const EdgeInsets.symmetric(vertical: 8.0, horizontal: 15),
        selectedTileColor: const Color(0xffDEF9ED),
        tileColor: const Color(0xffF1F7F7),
        minLeadingWidth: 0,
      ),
    );
  }

  String _getPetSelectedCount() {
    if (filter.pets.isEmpty) {
      return "1 Pet selected";
    }

    return "${filter.pets.length} ${filter.pets.length > 1 ? "Pets" : "Pet"} selected";
  }
}

class WorkoutPetSelectedWidget extends StatelessWidget {
  final double size;

  WorkoutPetSelectedWidget({Key? key, this.size = 60}) : super(key: key);

  List<Pet> pets =
      WalkProvider.of().getPetsWithSelectedAtBeginningOnlySelectable();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<WalkProvider>(builder: (walkProvider) {
      return Container(
          width: double.infinity,
          alignment: Alignment.center,
          child: pets.isNotEmpty
              ? ListView.builder(
                  shrinkWrap: true,
                  padding: const EdgeInsets.symmetric(horizontal: 15.0),
                  scrollDirection: Axis.horizontal,
                  itemCount: pets.length,
                  itemBuilder: (BuildContext context, int index) {
                    Pet pet = pets[index];
                    bool isSelected = walkProvider.isPetSelected(pet);

                    return GestureDetector(
                      onTap: () {
                        walkProvider.handlePet(pet);
                      },
                      child: Container(
                        width: size + 10,
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(left: index == 0 ? 0 : 10),
                        child: Column(
                          children: [
                            MBDogAvatar(
                              avatarURL: pet.avatarUrl,
                              deceased: pet.deceased,
                              backgroundColor: isSelected
                                  ? Theme.of(context).colorScheme.secondary
                                  : Colors.transparent,
                              size: size,
                            ),
                            const SizedBox(height: 10),
                            Text(
                              pet.name,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineSmall
                                  ?.copyWith(
                                      color: isSelected
                                          ? Theme.of(context)
                                              .colorScheme
                                              .secondary
                                          : const Color(0xff162941),
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                )
              : Column(
                  children: [
                    MBDefaultDogImage(
                        backgroundColor:
                            Theme.of(context).colorScheme.secondary,
                        size: size),
                    const SizedBox(height: 10),
                    Text(
                      'APPLICATION_MOBILE_LABEL_PET_LEXI'.tr('Lexi'),
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context)
                          .textTheme
                          .headlineSmall
                          ?.copyWith(
                              color: Theme.of(context).colorScheme.secondary,
                              fontSize: 14,
                              fontWeight: FontWeight.w500),
                    ),
                  ],
                ));
    });
  }
}
