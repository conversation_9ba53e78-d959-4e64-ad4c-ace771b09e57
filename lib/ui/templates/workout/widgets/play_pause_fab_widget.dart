import 'package:flutter/material.dart';

class FloatingPlayPauseActionButton extends StatelessWidget {
  final bool isWalking;
  final VoidCallback callback;

  const FloatingPlayPauseActionButton(
      {Key? key, required this.isWalking, required this.callback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
        onPressed: () {
          callback();
        },
        icon: Icon(
          isWalking ? Icons.pause : Icons.play_arrow,
        ),
        style: OutlinedButton.styleFrom(
          foregroundColor: Theme.of(context).colorScheme.secondary,
          side: BorderSide(
            color: Theme.of(context).colorScheme.secondary,
            width: 1,
          ),
        ),
        label: Text(
          isWalking ? "Pause" : "Resume",
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ));
  }
}
