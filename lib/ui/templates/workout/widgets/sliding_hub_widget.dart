import 'package:flutter/material.dart';
import 'package:mybuddy/blocs/walk_provider.dart';
import 'package:mybuddy/ui/templates/workout/widgets/distance_widget.dart';
import 'package:mybuddy/ui/templates/workout/widgets/duration_timer_widget.dart';
import 'package:mybuddy/ui/templates/workout/widgets/speed_widget.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import '../../../components/mb_stackable_avatar.dart';
import '../workout_mood_selection.dart';
import '../workout_pet_selection.dart';

class SlidingHubWidget extends StatelessWidget {
  final bool isExpanded;
  final Widget floatingPlayPauseButton;
  final Widget floatingFinishButton;
  final Function(bool) onClose;

  const SlidingHubWidget(
      {Key? key,
      required this.isExpanded,
      required this.floatingPlayPauseButton,
      required this.floatingFinishButton,
      required this.onClose})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final WalkProvider walkProvider = WalkProvider.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 20.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: const [
              DistanceWidget(),
              DurationTimerWidget(),
              SpeedWidget(),
            ],
          ),
          if (isExpanded) ...[
            const SizedBox(height: 20.0),
            selectionCard(
                walkProvider.pets.isEmpty
                    ? const MBDefaultDogImage(size: 40)
                    : MBAvatar(
                        imageUrl: walkProvider.pets.first.avatarUrl,
                        size: 40,
                      ),
                walkProvider.pets.isEmpty
                    ? "APPLICATION_MOBILE_LABEL_PET_LEXI".tr()
                    : walkProvider.pets.first.name,
                "${walkProvider.type} with", () {
              onClose(true);
              showBottomSheet(
                  builder: (_) => WorkoutPetSelection(), context: context);
            }, context,
                titleSuffix: walkProvider.pets.length > 1
                    ? " & ${walkProvider.pets.length - 1 > 1 ? "${walkProvider.pets.length - 1} others" : "${walkProvider.pets.length - 1} other"}"
                    : null),
            const SizedBox(
              height: 15,
            ),
            selectionCard(walkProvider.icon, walkProvider.type, "Activity", () {
              onClose(true);
              showBottomSheet(
                  builder: (_) => WorkoutMoodSelection(), context: context);
            }, context),
          ],
          const SizedBox(height: 20.0),
          Row(
            children: [
              Expanded(child: floatingPlayPauseButton),
              const SizedBox(width: 15.0),
              Expanded(child: floatingFinishButton),
            ],
          ),
        ],
      ),
    );
  }

  Widget selectionCard(icon, title, subtitle, onTap, context,
      {String? titleSuffix}) {
    return Card(
      shape: const RoundedRectangleBorder(
        side: BorderSide(
          color: Color(0xffCECECE),
          width: 0.5,
        ),
        borderRadius: BorderRadius.all(Radius.circular(8.0)),
      ),
      margin: EdgeInsets.zero,
      child: ListTile(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8.0)),
        ),
        minVerticalPadding: 10,
        leading: icon is IconData
            ? Container(
                height: 40,
                width: 40,
                decoration: BoxDecoration(
                    color: const Color(0xffDEF9ED),
                    shape: BoxShape.circle,
                    border: Border.all(color: const Color(0xffADD2C2))),
                child: Icon(
                  icon,
                  size: 23,
                  color: Theme.of(context).colorScheme.secondary,
                ),
              )
            : icon,
        minLeadingWidth: 0,
        horizontalTitleGap: 8,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xff808080)),
            ),
            OverflowBar(
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xff162941)),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                if (titleSuffix != null)
                  Text(
                    titleSuffix,
                    style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xff162941)),
                    maxLines: 1,
                  ),
              ],
            )
          ],
        ),
        trailing: TextButton(
          child: Text(
            "change",
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: const Color(0xff218359)),
          ),
          onPressed: onTap,
        ),
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 15.0),
        tileColor: const Color(0xffF1F7F7),
      ),
    );
  }
}
