import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/blocs/googlemap_tracking_provider.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/number_extensions.dart';

class SpeedWidget extends StatelessWidget {
  const SpeedWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ThemeData themeData = Theme.of(context);

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(()=>Text(
            TrackingProvider.of(context)
                .getSpeed,
            style: themeData.textTheme.headline5!
                .copyWith(fontSize: 23, fontWeight: FontWeight.w600),
          ),
        ),
        Text(Data().get().owner.units.toStringSpeedLabel.toUpperCase(),
            style: themeData.textTheme.subtitle2?.copyWith(
                color: const Color(0xff808080),
                fontSize: 11,
                fontWeight: FontWeight.w500)),
      ],
    );
  }
}
