import 'package:flutter/material.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/controllers/workout_history_controller.dart';
import 'package:mybuddy/controllers/workout_sync_controller.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/ui/components/stats_data_tile.dart';

import '../../../../class/workout_filter.dart';
import '../../../../controllers/home_summary_controller.dart';

class WorkoutStatsWidget extends StatelessWidget {
  WorkoutStatsWidget({Key? key}) : super(key: key);

  WorkoutHistoryController controller = WorkoutHistoryController.of();
  WorkoutSyncController syncController = WorkoutSyncController.of();
  HomeSummaryController homeSummaryController = HomeSummaryController.of();

  int totalWalks() {
    if (syncController.isSyncNotCompleted) {
      return homeSummaryController.homeSummary.totalWalks;
    }
    return controller.totalWalks;
  }

  String totalDistance() {
    if (syncController.isSyncNotCompleted) {
      return homeSummaryController.homeSummary.totalDistanceWithOneDecimal;
    }
    return controller.totalMiles;
  }

  String totalDuration() {
    if (syncController.isSyncNotCompleted) {
      return homeSummaryController.homeSummary.totalMinutes.toENTimeString();
    }
    return controller.totalMinutes;
  }

  String totalPoints() {
    if (syncController.isSyncNotCompleted) {
      return homeSummaryController.homeSummary.totalPointsInUSFormat;
    }
    return "${controller.totalPointsAsNumber + homeSummaryController.homeSummary.totalMigratedCharityPoints}";
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 15, right: 15, left: 15),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text("Summary",
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).primaryColor,
                  )),
              Text(controller.filter.dateType!.toName,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).primaryColor,
                  )),
              // const Icon(Icons.arrow_forward_ios_rounded, size: 15),
            ],
          ),
          const SizedBox(height: 15),
          Row(
            children: [
              Obx(() => StatsDataTile(
                  icon: "assets/icon/walk.png",
                  title: "Walks",
                  value: "${totalWalks()}")),
              const SizedBox(width: 10),
              Obx(
                () => StatsDataTile(
                    icon: "assets/icon/clock_blue.png",
                    title: "Time",
                    value: totalDuration()),
              )
            ],
          ),
          const SizedBox(height: 10),
          Row(
            children: [
              Obx(() => StatsDataTile(
                  icon: "assets/icon/miles_purple_icon.png",
                  title: "Miles",
                  value: totalDistance())),
              const SizedBox(width: 10),
              Obx(() => StatsDataTile(
                  icon: "assets/icon/yellow_paw.png",
                  title: "Charity Points",
                  value: totalPoints())),
            ],
          ),
        ],
      ),
    );
  }
}
