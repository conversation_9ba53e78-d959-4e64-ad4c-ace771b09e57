import 'package:flutter/material.dart';

import '../../../../models/pet.dart';
import '../../../components/mb_stackable_avatar.dart';

class PetStackedAvatar extends StatefulWidget {
  const PetStackedAvatar(
      {Key? key,
      required this.pets,
      this.avatarCount = 2,
      this.crossDirection = false,
      this.size})
      : super(key: key);

  final List<Pet> pets;
  final int avatarCount;
  final bool crossDirection;
  final double? size;

  @override
  State<PetStackedAvatar> createState() => _PetStackedAvatarState();
}

class _PetStackedAvatarState extends State<PetStackedAvatar> {
  List<Pet> sortedList = [];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _sortPetList();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.pets.isEmpty) {
      return MBDefaultDogImage(
        size: widget.size ?? 50,
      );
    }

    if (widget.crossDirection == true) {
      double size = widget.size ?? 40;
      return SizedBox(
        height: size * 1.5,
        width: size * widget.avatarCount,
        child: Stack(
          alignment: Alignment.center,
          children: List.generate(
              sortedList.length,
              (index) => Positioned(
                  left: (8 * index).toDouble(),
                  top: (size / 4 * index).toDouble(),
                  child: MBDogAvatar(
                    avatarURL: sortedList[index].avatarUrl,
                    deceased: sortedList[index].deceased,
                    // noImagePlaceHolderIcon: FontAwesomeIcons.paw,
                    size: size,
                  ))),
        ),
      );
    }
    return SizedBox(
      height: 60,
      width: widget.pets.length == 1
          ? 55
          : ((widget.size ?? 40) * widget.avatarCount).toDouble(),
      child: Stack(
        alignment: Alignment.center,
        children: List.generate(
            sortedList.length,
            (index) => Positioned(
                left: (20 * index).toDouble(),
                child: MBDogAvatar(
                  avatarURL: sortedList[index].avatarUrl,
                  deceased: sortedList[index].deceased,
                  // noImagePlaceHolderIcon: FontAwesomeIcons.paw,
                  size: widget.size ?? 50,
                ))),
      ),
    );
  }

  void _sortPetList() {
    if (widget.pets.isNotEmpty) {
      if (widget.pets.length > widget.avatarCount) {
        sortedList = widget.pets.sublist(0, widget.avatarCount);
      } else {
        sortedList = widget.pets.sublist(0);
      }
      sortedList.sort((a, b) => a.imageUrl == null ? -1 : 1);
    }
  }
}
