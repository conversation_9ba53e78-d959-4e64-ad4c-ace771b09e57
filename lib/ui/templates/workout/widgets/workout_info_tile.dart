import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';

class WorkoutInfoTile extends StatelessWidget {
  String title, subtitle;
  dynamic icon;
  bool background;
  bool verticalAlignment;

  WorkoutInfoTile(this.title, this.subtitle, this.icon,
      {this.background = false, this.verticalAlignment = false});

  @override
  Widget build(BuildContext context) {
    if (verticalAlignment) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _icon(),
          const SizedBox(
            height: 3,
          ),
          _title(context),
          _subTitle(context),
        ],
      );
    }

    return Container(
      decoration: BoxDecoration(
          color: background ? Colors.white : null,
          borderRadius: BorderRadius.circular(8)),
      child: ListTile(
        dense: true,
        minVerticalPadding: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        contentPadding: EdgeInsets.symmetric(horizontal: background ? 10 : 16),
        leading: _icon(),
        minLeadingWidth: 0,
        title: _title(context),
        subtitle: _subTitle(context),
      ),
    );
  }

  Widget _title(context) {
    return Text(
      title.useCorrectEllipsis(),
      overflow: TextOverflow.ellipsis,
      style: Theme.of(context).textTheme.headline6?.copyWith(
          color: const Color(0xff162941),
          fontSize: 14,
          fontWeight: FontWeight.w500),
    );
  }

  Widget _subTitle(context) {
    return Text(
      subtitle,
      overflow: TextOverflow.ellipsis,
      style: Theme.of(context).textTheme.bodyText1?.copyWith(
          color: const Color(0xff808080),
          fontWeight: FontWeight.w500,
          fontSize: 11),
    );
  }

  Widget _icon() {
    return icon is! String
        ? Icon(
      icon,
            color: const Color(0xff439460),
            size: 20,
          )
        : Image.asset(
      icon,
            height: 20,
            width: 20,
          );
  }
}
