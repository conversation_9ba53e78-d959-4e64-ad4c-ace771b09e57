import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mybuddy/blocs/googlemap_tracking_provider.dart';
import 'package:mybuddy/class/location_manager.dart';

class GoogleMapWalkWidget extends StatelessWidget {
  GoogleMapWalkWidget({Key? key}) : super(key: key);

  final trackingProvider = TrackingProvider.of();

  CameraPosition _getInitialCameraPosition() {
    return CameraPosition(
      target: LatLng(LocationManager().userLocation.latitude!,
          LocationManager().userLocation.longitude!),
      zoom: 17.0,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => GoogleMap(
        onMapCreated: trackingProvider.onMapCreated,
        initialCameraPosition: _getInitialCameraPosition(),
        markers: Set<Marker>.of(trackingProvider.markers.values),
        polylines: Set<Polyline>.of(trackingProvider.polylines.values),
        compassEnabled: true,
        myLocationEnabled: false,
        myLocationButtonEnabled: false,
        minMaxZoomPreference: const MinMaxZoomPreference(10.0, 20.0),
        tiltGesturesEnabled: false,
        gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
          Factory<OneSequenceGestureRecognizer>(
              () => PanGestureRecognizer()..onDown = _mapInteraction),
        }.toSet(),
      ),
    );
  }

  void _mapInteraction(DragDownDetails? details) {
    trackingProvider.disableNavigationMode();
  }
}
