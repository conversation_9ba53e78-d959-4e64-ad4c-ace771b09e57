import 'dart:io';
import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mybuddy/class/location_manager.dart';
import 'package:mybuddy/controllers/location_map_controller.dart';
import 'package:mybuddy/controllers/workout_details_map_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class WorkoutDetailsGoogleMapWidget extends StatefulWidget {
  WorkoutDetailsGoogleMapWidget({Key? key, required this.latLngList})
      : super(key: key);
  final List<List<LatLng>> latLngList;

  @override
  State<WorkoutDetailsGoogleMapWidget> createState() => _WorkoutDetailsGoogleMapWidgetState();
}

class _WorkoutDetailsGoogleMapWidgetState extends State<WorkoutDetailsGoogleMapWidget> {
  final WorkoutDetailsMapController controller = WorkoutDetailsMapController.of();

  @override
  void initState() {
    super.initState();
    controller.latLongsList = widget.latLngList;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        if (controller.isTrackNotAvailable) return _trackNotAvailableWidget();

        return GoogleMap(
          onMapCreated: controller.onMapCreated,
          initialCameraPosition: controller.initialCameraPosition(),
          myLocationEnabled: false,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: false,
          compassEnabled: true,
          markers: Set<Marker>.of(controller.markers.values),
          polylines: Set<Polyline>.of(controller.polylines.values),
        );
      },
    );
  }

  Widget _trackNotAvailableWidget() {
    return Container(
      color: const Color(0xffE5E5E5),
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'APPLICATION_MOBILE_MAP_NOT_AVAILABLE_TITLE'
                .tr('Map not available'),
            style: const TextStyle(
                color: Colors.black, fontSize: 21, fontWeight: FontWeight.w700),
          ),
          const SizedBox(height: 30),
          Image.asset("assets/images/map_not_available.png", height: 175),
          const SizedBox(height: 20),
          Text(
            'APPLICATION_MOBILE_MAP_NOT_AVAILABLE_DESCRIPTION'.tr(
                'Map cannot be displayed because your movement could not be detected.'),
            style: const TextStyle(
                color: Colors.black, fontSize: 14, fontWeight: FontWeight.w500,height: 1.5),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
