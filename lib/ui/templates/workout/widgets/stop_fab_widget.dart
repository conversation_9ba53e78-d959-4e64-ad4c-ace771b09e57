import 'package:flutter/material.dart';

class FloatingStopActionButton extends StatelessWidget {
  final VoidCallback callback;

  const FloatingStopActionButton({Key? key, required this.callback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
        onPressed: () {
          callback();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xff218359)
        ),
        icon: Image.asset(
          "assets/icon/finish_flag.png",
          height: 18,
        ),
        label: const Text(
          "Finish",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ));
  }
}
