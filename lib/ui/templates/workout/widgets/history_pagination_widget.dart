import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/ui/components/no_internet_widget.dart';
import '../../../../controllers/internet_controller.dart';
import '../../../../controllers/workout_history_controller.dart';

class HistoryPaginationWidget extends StatefulWidget {
  HistoryPaginationWidget(
      {Key? key,
      required this.controller,
      required this.child,
      this.emptyWidget,
      this.errorWidget,
      this.loadingWidget,
      this.bottomDisplayWidget,
      this.leadingWidget,
      this.noInternetWidget})
      : super(key: key);

  WorkoutHistoryController controller;
  Widget Function(BuildContext context, int index) child;
  Widget? emptyWidget;
  Widget? errorWidget;
  Widget? loadingWidget;
  Widget? leadingWidget;
  Widget? noInternetWidget;
  Widget? bottomDisplayWidget;

  @override
  State<HistoryPaginationWidget> createState() =>
      _HistoryPaginationWidgetState();
}

class _HistoryPaginationWidgetState extends State<HistoryPaginationWidget> {
  int extraWidgets = 0;

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        widget.controller.pageFetch();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.leadingWidget != null) extraWidgets = 1;
    return RefreshIndicator(
      onRefresh: () async {
        widget.controller.initialFetch();
      },
      child: Obx(
        () {
          if (widget.controller.loading) {
            return _loadingWidget(context);
          }

          if (widget.controller.shouldShowNoInternetWidget) {
            return Column(
              children: [
                if (widget.leadingWidget != null) widget.leadingWidget!,
                Expanded(
                    child: widget.noInternetWidget ?? const NoInternetWidget()),
              ],
            );
          }

          if (widget.controller.shouldShowEmptyWidget) {
            return _emptyWidget();
          }

          return _listView();
        },
      ),
    );
  }

  Widget _listView() {
    return ListView.separated(
      controller: _scrollController,
      primary: false,
      shrinkWrap: true,
      padding: const EdgeInsets.only(top: 10),
      physics: const AlwaysScrollableScrollPhysics(),
      separatorBuilder: (context, index) => const SizedBox(height: 0.0),
      itemCount: widget.controller.itemCount + extraWidgets,
      itemBuilder: (context, index) {
        if (index == 0 && widget.leadingWidget != null) {
          return widget.leadingWidget!;
        }

        if (index == widget.controller.itemCount) {
          if (widget.controller.error) {
            return _errorWidget(context);
          } else if (widget.bottomDisplayWidget != null &&
              widget.controller.shouldShowProgressWidget) {
            return widget.bottomDisplayWidget!;
          } else if (widget.controller.hasMore) {
            return _loadingWidget(context);
          }
        }
        return widget.child(context, index - extraWidgets);
      },
    );
  }

  Widget _errorWidget(BuildContext context) {
    if (widget.errorWidget != null) {
      return widget.errorWidget!;
    }
    return Center(
      child: TextButton(
        onPressed: () {
          widget.controller.loading = true;
          widget.controller.error = false;
          widget.controller.pageFetch();
        },
        child: Text(
          'Error while loading data, tap to try again',
          style: Theme.of(context).textTheme.headline6,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _loadingWidget(BuildContext context) {
    if (widget.loadingWidget != null) {
      return widget.loadingWidget!;
    }
    return Center(
      child: Text(
        'Loading...',
        style: Theme.of(context).textTheme.headline6,
      ),
    );
  }

  Widget _emptyWidget() {
    if (widget.emptyWidget != null) {
      return widget.emptyWidget!;
    }
    return const Center(
      child: Text("No record found"),
    );
  }
}
