import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/blocs/googlemap_tracking_provider.dart';
import 'package:mybuddy/ui/templates/workout/widgets/stop_fab_widget.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';
import '../../home/<USER>/gps_indicator.dart';
import 'sliding_hub_widget.dart';
import 'google_map_walk_widget.dart';
import 'play_pause_fab_widget.dart';

class WalkWidget extends StatefulWidget {
  const WalkWidget({Key? key}) : super(key: key);

  @override
  _WalkWidgetState createState() => _WalkWidgetState();
}

class _WalkWidgetState extends State<WalkWidget> {
  final PanelController _pc = PanelController();
  final trackingProvider = TrackingProvider.of();
  final BorderRadiusGeometry radius = const BorderRadius.only(
    topLeft: Radius.circular(24.0),
    topRight: Radius.circular(24.0),
  );

  late bool isExpanded;

  @override
  void initState() {
    super.initState();
    isExpanded = false;
  }

  void playPauseWalk() {
    if (_pc.isAttached) {
      trackingProvider.onStartPause(tellServer: true);
      bool isWalking = trackingProvider.isWalking.value;

      if (!isWalking && isExpanded || isWalking && !isExpanded) {
        if (_pc.isPanelOpen) {
          _pc.close();
        } else {
          _pc.open();
        }
      }
    }
  }

  void stopWalk() {
    if (_pc.isPanelOpen) {
      _pc.close();
    }

    trackingProvider.finishWalk();
  }

  @override
  Widget build(BuildContext context) {
    return SlidingUpPanel(
      borderRadius: radius,
      controller: _pc,
      onPanelClosed: () {
        setState(() => isExpanded = false);
      },
      onPanelOpened: () => setState(() => isExpanded = true),
      minHeight: 160,
      maxHeight: 340,
      panel: SlidingHubWidget(
        isExpanded: isExpanded,
        floatingPlayPauseButton: _getPlayPauseButton(),
        floatingFinishButton: _getStopButton(),
        onClose: (val) {
          if (val) {
            setState(() => isExpanded = false);
            _pc.close();
          }
        },
      ),
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 145),
            child: GoogleMapWalkWidget(),
          ),
          _getBackButton(),
          Positioned(
            right: 0,
            top: 35,
            child: GPSIndicator(),
          ),
          Obx(() => _showHideGpsWidget()),
          _showPausedWalkWidget()
        ],
      ),
    );
  }

  Widget _getPlayPauseButton() {
    return Obx(() {
      if (trackingProvider.isUploading.value) return const SizedBox();

      return Obx(() => FloatingPlayPauseActionButton(
            isWalking: trackingProvider.isWalking.value,
            callback: playPauseWalk,
          ));
    });
  }

  Widget _getStopButton() {
    return Obx(() {
      if (trackingProvider.isUploading.value) return const SizedBox();

      return FloatingStopActionButton(callback: stopWalk);
    });
  }

  Widget _getBackButton() {
    return Obx(() {
      if (trackingProvider.isUploading.value) return const SizedBox();

      return Positioned(
        left: 10,
        top: 35,
        child: FloatingActionButton(
          heroTag: 'switch-expansion',
          mini: true,
          backgroundColor: Colors.white,
          onPressed: () {
            trackingProvider.checkInAppOngoingWalk();
            Tools().navigatorPop();
          },
          child: const Icon(
            Icons.arrow_back_ios_new_rounded,
            color: Colors.black,
            size: 17,
          ),
        ),
      );
    });
  }

  Widget _showHideGpsWidget() {
    if (!trackingProvider.inNavigationMode.value) {
      return Positioned(
          right: 10,
          top: 90,
          child: FloatingActionButton(
            mini: true,
            backgroundColor: Colors.white,
            onPressed: () {
              trackingProvider.enableNavigationMode();
            },
            child: const Icon(
              Icons.gps_fixed_outlined,
              color: Colors.black,
              size: 17,
            ),
          ));
    }
    return const SizedBox();
  }

  Widget _showPausedWalkWidget() {
    return Positioned(
      top: 100,
      child: Obx(() {
        if (trackingProvider.walkPausedReason.value == WalkPauseReason.none) {
          return const SizedBox();
        }

        Tools.debugPrint("walk reason ${trackingProvider.walkPausedReason}");

        Map banner = trackingProvider.getPauseBanner();

        return Container(
          height: 32,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            color: banner['bgColor'],
          ),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  width: 3,
                ),
                if (banner["prefixIcon"] != null) ...[
                  Icon(
                    banner["prefixIcon"],
                    color: Colors.white,
                    size: 18,
                  ),
                  const SizedBox(
                    width: 8,
                  ),
                ],
                Flexible(
                  child: Text(
                    banner['text'],
                    style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(
                  width: 3,
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
