import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mybuddy/blocs/googlemap_tracking_provider.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/models/unit.dart';

import '../../../../blocs/walk_provider.dart';

class DistanceWidget extends StatelessWidget {
  const DistanceWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ThemeData themeData = Theme.of(context);

    Widget distanceText = Obx(()=>Text(
      TrackingProvider.of(context)
          .getDistance,
      style: themeData.textTheme.headline5!
          .copyWith(fontSize: 23, fontWeight: FontWeight.w600),
    ));

    Widget unitText = Text(
      Data().get().owner.units.toStringDistanceLabel,
      style: themeData.textTheme.subtitle2?.copyWith(
          color: const Color(0xff808080),
          fontSize: 11,
          fontWeight: FontWeight.w500),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        distanceText,
        unitText,
      ],
    );
  }
}
