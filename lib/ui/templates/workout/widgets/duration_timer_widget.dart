import 'dart:async';

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mybuddy/blocs/googlemap_tracking_provider.dart';

class DurationTimerWidget extends StatefulWidget {
  const DurationTimerWidget({Key? key}) : super(key: key);

  @override
  _TimerWidgetState createState() => _TimerWidgetState();
}

class _TimerWidgetState extends State<DurationTimerWidget> {
  late Timer _timer;

  String _getTime(BuildContext context) {
    return TrackingProvider.of(context).getTime;
  }

  @override
  void initState() {
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      setState(() {});
    });
    super.initState();
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ThemeData themeData = Theme.of(context);

    Text valueText = Text(
      _getTime(context),
      style: themeData.textTheme.headline5!
          .copyWith(fontSize: 23, fontWeight: FontWeight.w600),
    );

    Widget unitText = Text("Duration",
        style: themeData.textTheme.subtitle2?.copyWith(
            color: const Color(0xff808080),
            fontSize: 11,
            fontWeight: FontWeight.w500));

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        valueText,
        unitText,
      ],
    );
  }
}
