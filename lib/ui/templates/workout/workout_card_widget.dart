import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/datetime_extensions.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/workout.dart';
import 'package:mybuddy/ui/templates/workout/widgets/stacked_pet_images_widget.dart';
import 'package:mybuddy/ui/templates/workout/workout_details.dart';

import '../../../models/walk_model.dart';

class WorkoutCardWidget extends StatelessWidget {
  final Workout workout;

  const WorkoutCardWidget({Key? key, required this.workout}) : super(key: key);

  static const Color contentColor = Color(0xff627D98);

  @override
  Widget build(BuildContext context) {
    final Owner owner = Data().get().owner;
    String miles =
        workout.distance.toUserLength().toLocalStringAsFixed(2, false);
    String duration = workout.duration.toENTimeString();
    String points = '${workout.points ?? 0}';

    return Card(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
          side: const BorderSide(color: Color(0xffDAE2EB))),
      child: ListTile(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        onTap: () {
          Tools().navigatorPush(WorkoutDetails(workout));
        },
        contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 3),
        minLeadingWidth: 0,
        leading: _petAvatar(),
        horizontalTitleGap: 10,
        minVerticalPadding: 0,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Text(
                  workout.createdAt?.toDateFormatActivities() ?? '',
                  style: Theme.of(context).textTheme.headline6?.copyWith(
                        color: contentColor,
                        fontSize: 12,
                        fontWeight: FontWeight.normal,
                      ),
                ),
                Container(
                  color: contentColor,
                  height: 12,
                  width: 0.5,
                  margin: const EdgeInsets.symmetric(horizontal: 5),
                ),
                Text(
                  workout.createdAt?.toTimeFormat(lowerCase: true) ?? '',
                  style: Theme.of(context).textTheme.headline6?.copyWith(
                        color: contentColor,
                        fontSize: 12,
                        fontWeight: FontWeight.normal,
                      ),
                ),
              ],
            ),
            Image.asset(
              workout.isUploaded
                  ? "assets/icon/cloud-arrow-up.png"
                  : "assets/icon/cloud_slash_grey.png",
              width: 15,
            ),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 10),
          child: Row(
            children: [
              subDataTile(context, "assets/icon/miles_purple_icon.png",
                  "$miles ${owner.units.toStringDistanceShortLabel}",
                  flex: 3),
              const SizedBox(
                width: 5,
              ),
              subDataTile(context, "assets/icon/clock_blue.png", duration,
                  flex: 3),
              const SizedBox(
                width: 5,
              ),
              subDataTile(context, "assets/icon/yellow_paw.png", points,
                  initialSpace: true),
            ],
          ),
        ),
      ),
    );
  }

  Widget _petAvatar() {
    return SizedBox(
        width: 50,
        child: PetStackedAvatar(
          pets: workout.pets,
          crossDirection: true,
        ));
  }

  Widget subDataTile(BuildContext context, String icon, String text,
      {int flex = 2, bool initialSpace = false}) {
    Color contentColor = const Color(0xff474747);
    return Expanded(
      flex: flex,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (initialSpace) const SizedBox(width: 5),
          Image.asset(
            icon,
            height: 14,
            width: 14,
          ),
          const SizedBox(
            width: 3,
          ),
          Text(text,
              style: Theme.of(context).textTheme.subtitle1?.copyWith(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  color: contentColor)),
        ],
      ),
    );
  }
}

class WorkoutTileWidget extends StatelessWidget {
  final Workout workout;

  /// 2nd version of workout card
  const WorkoutTileWidget({Key? key, required this.workout}) : super(key: key);

  static const Color contentColor = Color(0xff627D98);

  @override
  Widget build(BuildContext context) {
    final Owner owner = Data().get().owner;

    return ListTile(
      contentPadding: EdgeInsets.zero,
      minLeadingWidth: 0,
      leading: _petAvatar(),
      horizontalTitleGap: 10,
      minVerticalPadding: 0,
      title: Text(
        "${workout.ownerFirstName ?? ''} ${'APPLICATION_MOBILE_LABEL_WALKED'.tr('walked with')} ${workout.pets.isEmpty ? 'APPLICATION_MOBILE_LABEL_PET_LEXI'.tr('Lexi') : workout.pets.length == 1 ? workout.pets.first.name : "${workout.pets.first.name} and ${workout.pets.length - 1} ${workout.pets.length > 2 ? "others" : "other"}"}",
        style: Theme.of(context).textTheme.headline6?.copyWith(
            color: const Color(0xff162941),
            fontSize: 13,
            fontWeight: FontWeight.w600),
        // maxLines: 1,
        // overflow: TextOverflow.ellipsis,
      ),
      subtitle: Padding(
        padding: const EdgeInsets.only(top: 8),
        child: IntrinsicHeight(
          child: Row(
            children: [
              DetailContainer(
                  icon: MoodType.values
                      .elementAt(workout.mood != null && workout.mood != 0
                          ? workout.mood! - 1
                          : 0)
                      .toIcon,
                  value:
                      workout.distance.toUserLength().toLocalStringAsFixed(2),
                  unit: owner.units.toStringDistanceLabel,
                  contentColor: contentColor),
              const SizedBox(
                width: 5,
              ),
              DetailContainer(
                  icon: "assets/icon/clock.png",
                  value: workout.duration.toENTimeString(),
                  unit: "",
                  contentColor: contentColor)
            ],
          ),
        ),
      ),
      trailing: Column(
        children: [
          const SizedBox(
            height: 5,
          ),
          Text(
            workout.createdAt?.toDateFormatActivities() ?? '',
            style: Theme.of(context)
                .textTheme
                .subtitle2
                ?.copyWith(fontSize: 10, color: contentColor),
          ),
        ],
      ),
    );
  }

  Widget _petAvatar() {
    return SizedBox(
        width: 50,
        child: PetStackedAvatar(
          pets: workout.pets,
          crossDirection: true,
        ));
  }
}

class DetailContainer extends StatelessWidget {
  DetailContainer(
      {Key? key,
      required this.icon,
      required this.value,
      required this.unit,
      required this.contentColor})
      : super(key: key);

  final icon;
  final String value, unit;
  final Color contentColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xffD9D9D9))),
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 4),
      child: Row(
        children: [
          _icon(icon),
          const SizedBox(
            width: 3,
          ),
          FittedBox(
            child: RichText(
              overflow: TextOverflow.ellipsis,
              text: TextSpan(
                text: value,
                style: Theme.of(context)
                    .textTheme
                    .subtitle1
                    ?.copyWith(fontSize: 12, color: contentColor),
                children: [
                  const TextSpan(text: ' '),
                  TextSpan(
                    text: unit,
                    style: Theme.of(context)
                        .textTheme
                        .subtitle2
                        ?.copyWith(fontSize: 12, color: contentColor),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _icon(icon) {
    return icon is! String
        ? Icon(
            icon,
            color: contentColor,
            size: 14,
          )
        : Image.asset(
            icon,
            height: 14,
            width: 14,
            color: contentColor,
          );
  }
}
