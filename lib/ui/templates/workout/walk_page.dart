import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:mybuddy/blocs/googlemap_tracking_provider.dart';
import 'package:mybuddy/ui/components/widget_animated_counter.dart';

import 'widgets/walk_widget.dart';

class WorkoutActivityPage extends StatefulWidget {
  WorkoutActivityPage({Key? key}) : super(key: key);

  @override
  State<WorkoutActivityPage> createState() => _WorkoutActivityPageState();
}

class _WorkoutActivityPageState extends State<WorkoutActivityPage> {
  TrackingProvider trackingProvider = Get.find();

  @override
  void initState() {
    super.initState();
    trackingProvider.init();
  }

  @override
  void dispose() {
    trackingProvider.walkPageDisposed();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        if(trackingProvider.isUploading.value) {
          return Future.value(false);
        }
        return Future.value(true);
      },
      child: Scaffold(
        body: Column(
          children: const <Widget>[
            CounterScreen(),
            Expanded(child: WalkWidget()),
            // BannerAdWidget(),
          ],
        ),
      ),
    );
  }
}

class CounterScreen extends StatefulWidget {
  const CounterScreen({Key? key}) : super(key: key);

  @override
  _CounterScreenState createState() => _CounterScreenState();
}

class _CounterScreenState extends State<CounterScreen> {
  Timer? _timer;
  int counter = 0;

  @override
  void initState() {

    if (TrackingProvider.of(context).isFreshWalk()) {
      counter = 0;
      _timer = Timer.periodic(const Duration(milliseconds: 1000), (timer) {
        if (counter != 0) {
          setState(() {
            counter -= 1;
          });
        } else {
          timer.cancel();
          TrackingProvider.of(context).startWalk();
        }
      });
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // Counter 0, we hide this
    if (counter == 0) return const SizedBox(height: 0, width: 0);

    final ThemeData themeData = Theme.of(context);
    return Container(
      color: Colors.blue,
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: Container(
        color: themeData.colorScheme.secondary,
        child: Center(
            child: WidgetAnimatedCounter(
          duration: const Duration(milliseconds: 1000),
          value: counter,
          textStyle:
              themeData.textTheme.headline1!.copyWith(color: Colors.white),
        )),
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
