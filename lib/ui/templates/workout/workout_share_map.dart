import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:intl/intl.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/ui/templates/workout/widgets/stacked_pet_images_widget.dart';
import 'package:mybuddy/ui/templates/workout/widgets/workout_info_tile.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../../class/data.dart';
import '../../../models/walk_model.dart';
import '../../../models/workout.dart';
import '../../components/mb_stackable_avatar.dart';

class WorkoutShareMapScreen extends StatefulWidget {
  final Workout workout;
  final Uint8List snapshot;

  const WorkoutShareMapScreen(
    this.workout,
    this.snapshot, {
    Key? key,
  }) : super(key: key);

  static GlobalKey previewContainer = GlobalKey();

  @override
  State<WorkoutShareMapScreen> createState() => _WorkoutShareMapScreenState();
}

class _WorkoutShareMapScreenState extends State<WorkoutShareMapScreen> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: RepaintBoundary(
            key: WorkoutShareMapScreen.previewContainer,
            child: Container(
              decoration: BoxDecoration(
                  color: const Color(0xffE8ECF0),
                  borderRadius: BorderRadius.circular(15)),
              child: Column(
                children: [
                  _workoutTile(),
                  Expanded(
                    child: _mapView(),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(
          height: 15,
        ),
        _shareButton(),
        const SizedBox(
          height: 20,
        ),
      ],
    );
  }

  Future<void> _takeScreenshot() async {
    final directory = await getApplicationDocumentsDirectory();
    final imagePath = await File('${directory.path}/image.png').create();

    RenderRepaintBoundary boundary = WorkoutShareMapScreen
        .previewContainer.currentContext!
        .findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: 3.0);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    await imagePath.writeAsBytes(byteData!.buffer.asUint8List());

    await Share.shareFiles([imagePath.path]);
  }

  Widget _mapView() {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          margin: const EdgeInsets.all(8),
          width: double.infinity,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
          child: ClipRRect(
              borderRadius: BorderRadius.circular(15),
              child: Image.memory(
                widget.snapshot,
                fit: BoxFit.fill,
              )),
        ),
        Positioned(
          bottom: 15,
          child: Image.asset(
            "assets/images/wooftrax.png",
            width: 150,
            color: Colors.black,
          ),
        )
      ],
    );
  }

  Widget _shareButton() {
    return Row(
      children: [
        Expanded(
          child: RawMaterialButton(
            onPressed: _takeScreenshot,
            elevation: 10.0,
            fillColor: Theme.of(context).colorScheme.secondary,
            padding: const EdgeInsets.symmetric(vertical: 15.0),
            shape: const StadiumBorder(
              side: BorderSide.none,
            ),
            child: Text(
              'APPLICATION_MOBILE_LABEL_WORKOUT_SHARE_MAP'.tr(),
              style: Theme.of(context).textTheme.headline6?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    color: Colors.white,
                  ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _workoutTile() {
    return Container(
      padding: const EdgeInsets.all(15),
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(15)),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                WorkoutInfoTile(
                  "${widget.workout.distance.toUserLength().toLocalStringAsFixed(2)} ${Data().get().owner.units.toStringDistanceLabel}",
                  'APPLICATION_MOBILE_TITLE_SHARE_DISTANCE'.tr("Distance"),
                  MoodType.values
                      .elementAt(widget.workout.mood != null &&
                              widget.workout.mood != 0
                          ? widget.workout.mood! - 1
                          : 0)
                      .toIcon,
                  verticalAlignment: true,
                ),
                const SizedBox(
                  height: 15,
                ),
                WorkoutInfoTile(
                  widget.workout.duration.toENTimeString(),
                  "APPLICATION_MOBILE_TITLE_SHARE_DURATION".tr("Duration"),
                  "assets/icon/clock.png",
                  verticalAlignment: true,
                ),
              ],
            ),
          ),
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                WorkoutInfoTile(
                  widget.workout.shelter?.name ?? "-",
                  "APPLICATION_MOBILE_TITLE_SHARE_CHARITY".tr("Charity"),
                  "assets/icon/handshake.png",
                  verticalAlignment: true,
                ),
                const SizedBox(
                  height: 15,
                ),
                WorkoutInfoTile(
                  DateFormat.yMMMd('en_US').format(widget.workout.createdAt!),
                  "APPLICATION_MOBILE_TITLE_SHARE_DATE".tr("Date"),
                  "assets/icon/calendar.png",
                  verticalAlignment: true,
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _petAvatar(),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  widget.workout.pets.isEmpty
                      ? "Lexi"
                      : widget.workout.getFilledPets().length > 1
                          ? "${widget.workout.getFilledPets().length} Dogs"
                          : widget.workout.getFilledPets().first.name,
                  style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      color: const Color(0xff474747),
                      fontWeight: FontWeight.w600,
                      fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _petAvatar() {
    return PetStackedAvatar(pets: widget.workout.getFilledPets());
  }
}
