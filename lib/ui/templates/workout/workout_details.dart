import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/workout_details_map_controller.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/models/walk_model.dart';
import 'package:mybuddy/models/workout.dart';
import 'package:mybuddy/ui/templates/workout/widgets/stacked_pet_images_widget.dart';
import 'package:mybuddy/ui/templates/workout/widgets/workout_details_google_map.dart';
import 'package:mybuddy/ui/templates/workout/widgets/workout_info_tile.dart';
import 'package:mybuddy/ui/templates/workout/workout_share.dart';

class WorkoutDetails extends StatefulWidget {
  final Workout workout;

  const WorkoutDetails(
    this.workout, {
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _WorkoutDetailsState();
}

class _WorkoutDetailsState extends State<WorkoutDetails> {
  WorkoutDetailsMapController controller =
      WorkoutDetailsMapController.of();

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    controller.disposeController();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Scaffold(
      body: SizedBox(
        height: size.height,
        child: Stack(
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.height - 300,
              child: FutureBuilder(
                  future: controller.getFullData(widget.workout),
                  builder:
                      (context, AsyncSnapshot<List<List<LatLng>>> snapshot) {
                    if (snapshot.connectionState == ConnectionState.done) {
                      return WorkoutDetailsGoogleMapWidget(
                        latLngList: snapshot.data ?? [],
                      );
                    } else {
                      return const Center(
                        heightFactor: 4,
                        child: CircularProgressIndicator.adaptive(),
                      );
                    }
                  }),
            ),
            Positioned(
              top: 40,
              left: 20,
              child: Container(
                height: 36,
                width: 36,
                decoration: const BoxDecoration(
                    color: Colors.white, shape: BoxShape.circle),
                child: Center(
                  child: IconButton(
                    icon: const Icon(FontAwesomeIcons.chevronLeft),
                    iconSize: 16,
                    color: Colors.black,
                    onPressed: () {
                      Tools().navigatorPop();
                    },
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              child: Container(
                height: 330,
                width: MediaQuery.of(context).size.width,
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(15))),
                child: Column(
                  children: [
                    ListTile(
                      leading: _petAvatar(),
                      title: Text(
                        'APPLICATION_MOBILE_LABEL_DOG'.tr('Dogs'),
                        style: Theme.of(context).textTheme.bodyText1?.copyWith(
                            color: const Color(0xff9A9A9A),
                            fontWeight: FontWeight.w600),
                      ),
                      subtitle: Text(
                        widget.workout.pets.isEmpty
                            ? 'APPLICATION_MOBILE_LABEL_PET_LEXI'.tr('Lexi')
                            : widget.workout.pets.length == 1
                                ? widget.workout.pets.first.name
                                : "${widget.workout.pets.first.name} and ${widget.workout.pets.length - 1} ${widget.workout.pets.length == 2 ? "other" : "others"}",
                        style: Theme.of(context).textTheme.headline6?.copyWith(
                            color: const Color(0xff474747),
                            fontSize: 16,
                            fontWeight: FontWeight.w600),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Divider(
                      color: Colors.grey[400],
                    ),
                    Row(
                      children: [
                        Expanded(
                            child: WorkoutInfoTile(
                          "${widget.workout.distance.toUserLength().toLocalStringAsFixed(2)} ${Data().get().owner.units.toStringDistanceLabel}",
                          'APPLICATION_MOBILE_TITLE_SHARE_DISTANCE'
                              .tr("Distance"),
                          MoodType.values
                              .elementAt(widget.workout.mood != null &&
                                      widget.workout.mood != 0
                                  ? widget.workout.mood! - 1
                                  : 0)
                              .toIcon,
                        )),
                        Expanded(
                            child: WorkoutInfoTile(
                                widget.workout.duration.toENTimeString(),
                                "APPLICATION_MOBILE_TITLE_SHARE_DURATION"
                                    .tr("Duration"),
                                "assets/icon/clock.png")),
                      ],
                    ),
                    Divider(
                      color: Colors.grey[400],
                    ),
                    Row(
                      children: [
                        Expanded(
                            child: WorkoutInfoTile(
                                "${widget.workout.speed.toUserLength().toStringAsFixed(2)} ${Data().get().owner.units.toStringSpeedLabel}",
                                "APPLICATION_MOBILE_TITLE_SHARE_AVG_SPEED"
                                    .tr("Avg. Speed"),
                                "assets/icon/speed_arrow.png")),
                        Expanded(
                            child: WorkoutInfoTile(
                                widget.workout.shelter?.name ?? "-",
                                "APPLICATION_MOBILE_TITLE_SHARE_CHARITY"
                                    .tr("Charity"),
                                "assets/icon/handshake.png")),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    _shareButton(),
                    const SizedBox(
                      height: 10,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _petAvatar() {
    return PetStackedAvatar(pets: widget.workout.getFilledPets());
  }

  Widget _shareButton() {
    return Row(
      children: [
        Expanded(
          child: RawMaterialButton(
            onPressed: () => shareButtonPressed(),
            elevation: 10.0,
            fillColor: Theme.of(context).colorScheme.secondary,
            padding: const EdgeInsets.symmetric(vertical: 15.0),
            shape: const StadiumBorder(
              side: BorderSide.none,
            ),
            child: Text(
              'APPLICATION_MOBILE_LABEL_WORKOUT_SHARE'.tr(),
              style: Theme.of(context).textTheme.headline6?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    color: Colors.white,
                  ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> shareButtonPressed() async {
    Tools().navigatorPush(WorkoutShareScreen(
        widget.workout,
        controller.isTrackNotAvailable
            ? null
            : await controller.mapController?.takeSnapshot()));
  }
}
