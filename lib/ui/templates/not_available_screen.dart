import 'package:flutter/material.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

// tmp widget for on build screens
class NotAvailableScreen extends StatelessWidget {
  const NotAvailableScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Text('APPLICATION_MOBILE_MESSAGE_NOT_AVAILABLE_YET'.tr()),
      ),
    );
  }
}
