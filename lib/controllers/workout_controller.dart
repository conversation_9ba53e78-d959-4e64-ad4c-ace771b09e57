import 'package:mybuddy/models/walk_model.dart';
import 'package:mybuddy/models/workout.dart';

import '../Api/mb_api_workout.dart';
import '../Api/mb_response.dart';
import '../Tools/tools.dart';

class WorkoutController {
  bool error = false;
  int totalCount = 0;

  Future<List<Workout>> _fetch(int page) async {
    try {
      List<int> moodTypes =
          MoodType.values.toList().map((e) => e.toValue).toList();

      MBResponse response = await MbApiWorkout().getWorkoutHistory(
          page: page, startDate: null, endDate: null, moodType: moodTypes);

      if (response.success) {
        WorkoutResponse workoutResponse =
            WorkoutResponse.fromJson(response.body);

        totalCount = workoutResponse.totalCount;

        return workoutResponse.workouts;
      } else {
        error = true;

        return [];
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      error = true;

      return [];
    }
  }

  Future<List<Workout>> pageFetch(int page) async {
    List<Workout> data = await _fetch(page);
    return data;
  }
}
