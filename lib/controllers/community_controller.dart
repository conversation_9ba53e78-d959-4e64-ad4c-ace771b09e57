import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:mybuddy/class/constants.dart';
import 'package:mybuddy/controllers/root_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

enum CommunityTab { mygroups, ranking }

class CommunityController extends GetxController {
  static CommunityController of() {
    try {
      return Get.find<CommunityController>();
    } catch (e) {
      return Get.put(CommunityController());
    }
  }

  Rx<CommunityTab> _activeTab = (CommunityTab.mygroups).obs;

  CommunityTab get activeTab => _activeTab.value;

  List<String> get tabs => <String>[
        'APPLICATION_MOBILE_TITLE_MY_PACKS_COMMUNITY'.tr('My Groups'),
        'APPLICATION_MOBILE_TITLE_RANKING_COMMUNITY'.tr('Ranking'),
      ];

  double get tabWidth => (Get.width - 120) / 2;
  double get tabHeight => 40;

  void switchCommunityTab(CommunityTab tab) {
    _activeTab.value = tab;
    setScreenToCIO();
  }

  void setScreenToCIO() {
    if (_activeTab.value == CommunityTab.ranking) {
      RootController.of.updateScreenOnCIO(cioScreenNameCommunity);
    } else {
      RootController.of.updateScreenOnCIO(cioScreenNameCommunityMyGroups);
    }
  }
}
