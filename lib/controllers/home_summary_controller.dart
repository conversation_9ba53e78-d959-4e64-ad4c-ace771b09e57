import 'dart:async';
import 'package:get/get.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/db/database_service.dart';
import 'package:mybuddy/db/models/home_summary_db.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/models/home_summary.dart';
import '../Api/mb_response.dart';
import '../Tools/tools.dart';

class HomeSummaryController extends GetxController {

  /// This method is used to get the instance of the controller
  static HomeSummaryController of() {
    try {
      return Get.find();
    } catch (e) {
      return Get.put(HomeSummaryController());
    }
  }

  final Rx<HomeSummaryDb> _homeSummary = HomeSummaryDb().obs;
  final RxBool _loading = false.obs;
  final RxInt _userLevel = 0.obs;

  int get userLevel => _userLevel.value;

  int get userNextLevel => _userLevel.value + 1;

  int get userNextLevelRequiredPoints =>
      userNextLevel.toLevelPoints() - homeSummary.totalPoints;

  int get totalPointsBetweenTheLevels =>
      (userNextLevel.toLevelPoints() - userLevel.toLevelPoints());

  double get percentToCompleteNextLevel =>
      (totalPointsBetweenTheLevels - userNextLevelRequiredPoints) /
      totalPointsBetweenTheLevels;

  HomeSummaryDb get homeSummary => _homeSummary.value;

  bool get loading => _loading.value;

  @override
  void onInit() {
    super.onInit();
    fetchHomeSummary();
  }

  HomeSummaryDb homeSummaryDbMapper(HomeSummary homeSummary) => HomeSummaryDb(
        totalPoints: homeSummary.totalPoints,
        totalWalks: homeSummary.totalWalks,
        totalDistance: homeSummary.totalDistance,
        totalMinutes: homeSummary.totalMinutes,
        totalMigratedCharityPoints: homeSummary.totalMigratedCharityPoints,
      );

  Future<void> fetchHomeSummary() async {
    _loading.value = true;
    try {
      final MBResponse response = await MbApiOwner().getHomeSummary();
      if (response.success) {
        HomeSummary networkModel = HomeSummary.fromJson(response.body);
        DatabaseService()
            .homeSummaryDao
            .setHomeSummary(homeSummaryDbMapper(networkModel));
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
    }

    final HomeSummaryDb homeSum =
        await DatabaseService().homeSummaryDao.getAggregatedHomeSummary();

    _homeSummary(homeSum);
    _userLevel.value = homeSum.totalPoints.toLevel()["level"];
    _loading.value = false;
  }
}
