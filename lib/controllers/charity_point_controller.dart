import 'package:get/get.dart';

import '../Api/mb_api_charity_points.dart';
import '../Api/mb_response.dart';
import '../Tools/tools.dart';
import '../models/charity_point_history.dart';
import 'base_pagination_class.dart';

class CharityPointController extends BasePaginationClass<PointsData> {
  RxInt detailMessageIndex = (-1).obs;
  RxInt detailMessageInnerIndex = (-1).obs;

  Rx<CharityPointHistory> history = CharityPointHistory().obs;

  @override
  void onInit() {
    nextPageThreshold = 1;
    super.onInit();
  }

  @override
  void resetData() {
    super.resetData();
    detailMessageIndex.value = -1;
    detailMessageInnerIndex.value = -1;
  }

  @override
  Future<void> initialFetch() async {
    resetData();
    dataList.value = await fetch();
  }

  @override
  Future<void> pageFetch() async {
    dataList.addAll(await fetch());
  }

  @override
  Future<List<PointsData>> fetch() async {
    try {
      MBResponse response = await MbApiCharityPoints().getCharityPointsHistory(
          page: pageNumber.value,
          from: pageNumber.value == 1 ? null : history.value.fromDate);

      if (response.success) {
        history.value = CharityPointHistory.fromJson(response.body);

        hasMore = history.value.data.isNotEmpty;
        pageNumber += 1;

        loading = false;

        return history.value.data;
      } else {
        loading = false;
        error = true;

        return [];
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      loading = false;
      error = true;

      return [];
    }
  }
}
