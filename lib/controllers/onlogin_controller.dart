import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:mybuddy/Tools/tools.dart';

import '../class/data.dart';
import '../models/pet.dart';
import '../models/shelter.dart';

class OnLoginController extends GetxController {
  static OnLoginController of() {
    try {
      return Get.find();
    } catch (e) {
      return Get.put(OnLoginController());
    }
  }

  PageController pageController = PageController();

  RxInt _currentPage = 0.obs;
  RxList<Pet> _ownerPets = <Pet>[].obs;
  Rx<Shelter> _ownerShelter = Shelter.none().obs;

  int get currentPage => _currentPage.value;
  bool get isShelterNotSelected => _ownerShelter.value == Shelter.none();
  bool get isPetsEmpty => _ownerPets.isEmpty;

  List<Pet> get pets {
    assignPets();
    return _ownerPets;
  }

  Shelter get shelter {
    assignShelter();
    return _ownerShelter.value;
  }


  @override
  void onInit() {
    super.onInit();
    assignPets();
    assignShelter();
  }

  Future<void> nextPage() async {
    try {
      await pageController.nextPage(
          duration: const Duration(milliseconds: 500), curve: Curves.ease);
      _currentPage.value = pageController.page!.toInt();
    } catch (e) {
      Tools.debugPrint(e.toString());
    }
  }

  Future<void> previousPage() async {
    try {
      await pageController.previousPage(
          duration: const Duration(milliseconds: 500), curve: Curves.ease);
      _currentPage.value = pageController.page!.toInt();
    }catch (e) {
      Tools.debugPrint(e.toString());
    }
  }

  // assign owners pets to reflect the change in UI
  void assignPets(){
    _ownerPets.assignAll(Data().get().pets);
    _ownerPets.refresh();
  }

  // assign owners shelter to reflect the change in UI
  void assignShelter(){
    Shelter? shelter = Data().get().owner.shelter;
    if(shelter != null){
      _ownerShelter.value = shelter;
    } else {
      _ownerShelter.value = Shelter.none();
    }
    _ownerShelter.refresh();
  }
}
