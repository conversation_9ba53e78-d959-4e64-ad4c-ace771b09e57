import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mybuddy/Api/mb_api_pet.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/dog_form_controller.dart';
import 'package:mybuddy/models/shelter.dart';
import 'package:mybuddy/ui/templates/pet/widgets/state_search_delegate.dart';

import '../models/state.dart';
import 'charity_list_controller.dart';

class DogCharityController extends CharityListController {
  DogCharityController([Shelter? shelter]) : super(shelter);

  static DogCharityController of([Shelter? shelter]) {
    try {
      return Get.find();
    } catch (e) {
      return Get.put(DogCharityController(shelter));
    }
  }

  final formKey = GlobalKey<FormState>();
  TextEditingController stateController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  FocusNode name = FocusNode();
  FocusNode city = FocusNode();
  List<CountryState> stateList = [];

  @override
  void onReady() {
    super.onReady();
    getStatesList();
  }

  void makeShelterEmpty() {
    selectedShelter = Shelter.none();
    nameController.clear();
    cityController.clear();
    stateController.clear();
  }

  @override
  set selectedShelter(Shelter value) {
    super.selectedShelter = value;
    Get.find<DogFormController>().updateShelter(value);
  }

  Future<void> getStatesList() async {
    var response = await MbApiPet().petCharityStates(Get.context!);

    if (response.success) {
      stateList = (response.body["states"] as List)
          .map((element) => CountryState.fromJson(element))
          .toList();
    }
  }

  void selectState() async {
    CountryState? result = await showSearch<CountryState?>(
      context: Get.context!,
      delegate: StateSearchDelegate(states: stateList),
    );

    if (result != null) {
      stateController.text = result.name!;
      selectedShelter.state = result.name!;
    }
  }

  void saveShelter() {
    bool isValid = formKey.currentState?.validate() ?? false;

    if (!isValid) return;

    selectedShelter.state = stateController.text;
    selectedShelter.name = nameController.text;
    selectedShelter.city = cityController.text;
    Tools().navigatorPop(value: selectedShelter);
    Get.find<DogFormController>().updateShelter(selectedShelter);
  }
}
