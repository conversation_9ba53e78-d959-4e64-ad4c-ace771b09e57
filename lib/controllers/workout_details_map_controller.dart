import 'package:archive/archive_io.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gpx/gpx.dart';
import 'package:mybuddy/Api/mb_api_workout.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/class/app_storage.dart';
import 'package:mybuddy/controllers/google_map_display_controller.dart';
import 'package:mybuddy/db/database_service.dart';
import 'package:mybuddy/models/workout.dart';

class WorkoutDetailsMapController extends GoogleMapDisplayController {

  static WorkoutDetailsMapController of(){
    try{
      return Get.find<WorkoutDetailsMapController>();
    }catch(e){
      return Get.put(WorkoutDetailsMapController());
    }
  }

  final RxList<List<LatLng>> _latLongsList = List<List<LatLng>>.empty().obs;

  //getter
  List<List<LatLng>> get latLongsList => _latLongsList.value;
  bool get isTrackNotAvailable => latLongsList.isEmpty;

  //setter
  set latLongsList(List<List<LatLng>> value) => _latLongsList.value = value;

  CameraPosition initialCameraPosition() {
    return CameraPosition(
      target: latLongsList.first.first,
      zoom: 15,
    );
  }

  Future<List<List<LatLng>>> getFullData(Workout workout) async {
    String gpxFileName = workout.filename ?? '';
    String? gpxStr = await AppStorage().readGpx(gpxFileName);
    if (gpxStr == null) {
      // either fileName is empty or file not found in AppStorage
      try {
        String s3Url = '';
        if (workout.s3FilenameUrl?.isNotEmpty ?? false) {
          // this workout object came with s3 url,
          // no need to call Workout/detail to obtain it
          s3Url = workout.s3FilenameUrl!;
          gpxFileName = workout.filename ??
              // unix timestamp
              DateTime.now().millisecondsSinceEpoch.toString();
        } else {
          // query backend for fileUrl and download file
          MBResponse res = await MbApiWorkout().workoutDetails(workout.id);

          if (res.success) {
            Workout workout = Workout.fromJson(res.body["workout"]);
            s3Url = workout.s3FilenameUrl ?? '';
            gpxFileName = workout.filename ??
                // unix timestamp
                DateTime.now().millisecondsSinceEpoch.toString();
          }
        }
        gpxStr = await _downloadFile(s3Url, workout, fileName: gpxFileName);

        // still couldn't get gpx (null or empty)
        if (gpxStr?.isEmpty ?? true) return [];

        // set workout's filename (both local object and DB)
        workout.filename = gpxFileName;
        DatabaseService()
            .getWorkoutDao()
            .updateGpxFileName(workout.id, gpxFileName);
      } catch (e) {
        return [];
      }
    }

    Gpx gpx = GpxReader().fromString(gpxStr!);

    // gpx string was non-empty but not parsed properly by GpxReader
    if (gpx.trks.isEmpty || gpx.trks.first.trksegs.isEmpty) return [];

    List<List<LatLng>> temp = gpx.trks.first.trksegs
        .map((seg) => seg.trkpts.map((pt) => LatLng(pt.lat!, pt.lon!)).toList())
        .toList();

    /// remove empty list
    temp.removeWhere((element) => element.isEmpty);
    return temp;
  }

  Future<String?> _downloadFile(String url, Workout workout,
      {String? fileName}) async {
    HttpClient httpClient = HttpClient();
    try {
      var request = await httpClient.getUrl(Uri.parse(url));
      var response = await request.close();
      if (response.statusCode == 200) {
        var bytes = await consolidateHttpClientResponseBytes(response);
        String gpx = String.fromCharCodes(BZip2Decoder().decodeBytes(bytes));
        await AppStorage().writeGpx(fileName ?? workout.filename ?? '', gpx);
        return gpx;
      }
    } catch (ex) {
      return null;
    }

    return null;
  }

  @override
  void onMapCreated(GoogleMapController controller) async {
    String startMarkerImagePath = Platform.isAndroid
        ? "assets/images/woof_marker_green.png"
        : "assets/images/ic_woof_marker_green.png";

    String finishMarkerImagePath = Platform.isAndroid
        ? "assets/images/woof_marker_red.png"
        : "assets/images/ic_woof_marker_red.png";

    BitmapDescriptor startMarker = await BitmapDescriptor.fromAssetImage(
        ImageConfiguration(
            size: const Size(15, 15),
            devicePixelRatio: MediaQuery.of(Get.context!).devicePixelRatio),
        startMarkerImagePath);

    BitmapDescriptor finishMarker = await BitmapDescriptor.fromAssetImage(
        ImageConfiguration(
            size: const Size(15, 15),
            devicePixelRatio: MediaQuery.of(Get.context!).devicePixelRatio),
        finishMarkerImagePath);

    await Future.delayed(const Duration(milliseconds: 300));
    controller.animateCamera(CameraUpdate.newLatLngBounds(_getLLBounds(), 50));

    clearMap();

    Future.delayed(const Duration(milliseconds: 100), () async {
      /// origin marker
      addMarker(latLongsList.first.first, "origin", startMarker);

      /// destination marker
      addMarker(latLongsList.last.last, "destination", finishMarker);

      addPolyLine(latLongsList);
      var cameraBound = _getBounds();

      controller.animateCamera(
        CameraUpdate.newLatLngBounds(cameraBound, 50),
      );
    });
    super.onMapCreated(controller);
  }

  LatLngBounds _getLLBounds() {
    final LatLng southwest = LatLng(
      min(latLongsList.first.first.latitude, latLongsList.last.last.latitude),
      min(latLongsList.first.first.longitude, latLongsList.last.last.longitude),
    );

    final LatLng northeast = LatLng(
      max(latLongsList.first.first.latitude, latLongsList.last.last.latitude),
      max(latLongsList.first.first.longitude, latLongsList.last.last.longitude),
    );
    LatLngBounds bounds = LatLngBounds(
      southwest: southwest,
      northeast: northeast,
    );

    return bounds;
  }

  LatLngBounds _getBounds() {
    double minLat = min(
            latLongsList.first.first.latitude, latLongsList.last.last.latitude),
        maxLat = max(
            latLongsList.first.first.latitude, latLongsList.last.last.latitude),
        minLng = min(latLongsList.first.first.longitude,
            latLongsList.last.last.longitude),
        maxLng = max(latLongsList.first.first.longitude,
            latLongsList.last.last.longitude);

    for (var i in latLongsList) {
      for (var j in i) {
        minLat = min(minLat, j.latitude);
        maxLat = max(maxLat, j.latitude);
        minLng = min(minLng, j.longitude);
        maxLng = max(maxLng, j.longitude);
      }
    }

    final LatLng southwest = LatLng(
      minLat,
      minLng,
    );

    final LatLng northeast = LatLng(
      maxLat,
      maxLng,
    );
    LatLngBounds bounds = LatLngBounds(
      southwest: southwest,
      northeast: northeast,
    );

    return bounds;
  }
}
