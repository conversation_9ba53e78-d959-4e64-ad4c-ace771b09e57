import 'dart:developer';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mybuddy/Api/mb_api_home_stats.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/workout_sync_controller.dart';
import 'package:mybuddy/db/database_service.dart';
import 'package:mybuddy/db/models/workout_db.dart';
import 'package:mybuddy/models/home_stats.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/user_goal.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class WeeklyStatsController extends GetxController {
  static WeeklyStatsController of() {
    try {
      return Get.find();
    } catch (e) {
      return Get.put(WeeklyStatsController());
    }
  }

  final RxString _selectedDate = 'This Week'.obs;
  final RxBool _loading = true.obs;
  final RxBool _showGoals = true.obs;
  final Rx<HomeStats> _homeStats = HomeStats().obs;
  final Rx<WeeklyUserGoal> _todaysGoals = WeeklyUserGoal().obs;
  final Rx<DateTime> _lastQueriedDate = DateTime.now().obs;
  final RxBool _isGoalSet = false.obs;

  DateTime? previousCustomStartDate;
  PickerDateRange? selectedWeek;
  final DateRangePickerController customDateController =
      DateRangePickerController();

  final List<String> bottomSheetItems = ['This Week', 'Last Week', 'Custom'];

  String get selectedDate => _selectedDate.value;
  DateTime get lastQueriedDate => _lastQueriedDate.value;
  bool get loading => _loading.value;
  bool get showGoals => _showGoals.value;
  HomeStats get homeStats => _homeStats.value;
  WeeklyUserGoal get weeklyGoals => _todaysGoals.value;
  bool get isGoalSet => _isGoalSet.value;

  @override
  void onInit() async {
    once(WorkoutSyncController.of().isDataAvailableToDisplay, (callback) {
      refreshHomeStats();
    });
    await setDateByItem(selectedDate);
    updateThisWeeksGoals();
    _loading.value = false;
    checkIfGoalIsSet();

    super.onInit();
  }

  void updateThisWeeksGoals() {
    Owner _owner = Data().get().owner;
    weeklyGoals.weeklyDistanceGoal = _owner.weeklyWalkingGoalDistance ?? 2;
    weeklyGoals.weeklyWalkGoal = _owner.weeklyWalkingGoalWalks ?? 3;
    if (selectedDate == 'This Week') {
      homeStats.weeklyUserGoal = weeklyGoals;
      Tools.debugPrint('UPDATED TODAYS GOALS');
      inspect(weeklyGoals);
      inspect(_owner);
      _showGoals.value = true;
    }
  }

  Future<void> setDateByItem(String selectedItem) async {
    switch (selectedItem) {
      case 'Last Week':
        final daysFromSunday = DateTime.now().weekday;
        DateTime date =
            DateTime.now().subtract(Duration(days: 7 + daysFromSunday));
        return await setDate(date, selectedItem);
      case 'This Week':
      default: // in case selected item doesn't match
        await handleThisWeeksStats();
        updateThisWeeksGoals();
    }
  }

  Future<void> handleThisWeeksStats() async {
    _loading.value = true;
    WalkStats stats =
        await DatabaseService().getWorkoutDao().getThisWeeksStats();

    Tools.debugPrint('GOT THIS WEEKS STATS:');
    inspect(stats);
    homeStats.userStats.walks = stats.totalWalks;
    homeStats.userStats.coveredDistance = stats.totalDistance;
    _selectedDate.value = 'This Week';
    _loading.value = false;
  }

  Future<void> setDate(DateTime date, String selectedItem) async {
    if (await Tools().common.isOnline(alertContext: Get.context!)) {
      await updateHomeStats(date);
      _selectedDate.value = selectedItem;
    }
  }

  Future<void> updateHomeStats(DateTime date) async {
    String dateString = DateFormat('yyyy-MM-dd').format(date);
    _loading.value = true;
    final MBResponse response = await MbApiHomeStats().getHomeStats(dateString);
    if (response.success) {
      _homeStats.value =
          HomeStats.fromJson(response.body, userGoalType: UserGoalType.weekly);
      _lastQueriedDate.value = date;
      _showGoals.value = homeStats.weeklyUserGoal.id != null;

      var data = Data().get();
      data.textBanners = homeStats.textBanners;
      data.owner.points = homeStats.userStats.totalCharityPoints;
      data.owner.weeklyWalkingGoalWalks =
          homeStats.weeklyUserGoal.weeklyWalkGoal;
      data.owner.weeklyWalkingGoalDistance =
          homeStats.weeklyUserGoal.weeklyDistanceGoal;
      Data().storeData();
      checkIfGoalIsSet();
    }
    _loading.value = false;
  }

  void refreshHomeStats() {
    if (selectedDate == 'Custom') {
      updateHomeStats(lastQueriedDate);
    } else {
      setDateByItem(selectedDate);
    }
  }

  void onCustomDateSelected(
    DateRangePickerSelectionChangedArgs args,
  ) {
    int firstDayOfWeek = DateTime.sunday % 7;
    int lastDayOfWeek = DateTime.saturday % 7;

    PickerDateRange ranges = args.value;
    DateTime date1 = ranges.startDate!;
    DateTime date2 = (ranges.endDate ?? ranges.startDate)!;

    // to avoid recursion, because this function is called whether date is set by user or programmatically
    if (selectedWeek != null && isSameDateRange(selectedWeek!, ranges)) return;

    DateTime selectedDate =
        isSameDate(date1, previousCustomStartDate) ? date2 : date1;
    // below is a hack, because for some reason dart might subtract an extra day if the time is 00:00:00
    // (might be due to Daylight savings time) adding an hour seems to fix this issue
    selectedDate = selectedDate.add(const Duration(hours: 1));

    // construct week range
    int day = selectedDate.weekday % 7;
    DateTime dat1 = selectedDate.subtract(Duration(days: day - firstDayOfWeek));
    DateTime dat2 = selectedDate.add(Duration(days: lastDayOfWeek - day));

    if (dat2.isAfter(DateTime.now())) dat2 = DateTime.now();

    final PickerDateRange weekRange = PickerDateRange(dat1, dat2);

    customDateController.selectedRange = weekRange;
    selectedWeek = weekRange;
    previousCustomStartDate = date1;
  }

  bool isSameDate(DateTime? date1, DateTime? date2) {
    if (date1 == date2) return true;
    if (date1 == null || date2 == null) return false;
    return date1.month == date2.month &&
        date1.year == date2.year &&
        date1.day == date2.day;
  }

  bool isSameDateRange(PickerDateRange range1, PickerDateRange range2) =>
      isSameDate(range1.startDate, range2.startDate) &&
      isSameDate(range1.endDate, range2.endDate);

  void checkIfGoalIsSet() {
    _isGoalSet.value = Data().get().owner.weeklyWalkingGoalWalks != null &&
        Data().get().owner.weeklyWalkingGoalDistance != null;
  }
}
