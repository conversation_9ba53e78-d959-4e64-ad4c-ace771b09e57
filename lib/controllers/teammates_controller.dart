import 'package:get/get.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/controllers/base_pagination_class.dart';
import 'package:mybuddy/models/community.dart';

import '../Api/mb_response.dart';
import '../Tools/tools.dart';

class TeammatesController extends BasePaginationClass<Friend> {
  final RxInt teamId = (-1).obs;
  final RxString searchQuery = ''.obs;

  void setQuery(String s) {
    searchQuery.value = s;
  }

  @override
  void onInit() {
    super.onInit();
    debounce(searchQuery, (_) => initialFetch());
  }

  @override
  Future<List<Friend>> fetch() async {
    if (teamId.value == -1) {
      loading = false;
      return [];
    }
    try {
      final MBResponse response = await MbApiCommunity().getTeammates(
        pageNumber.value,
        teamId.value,
        searchQuery.value,
      );
      if (response.success) {
        Map<String, dynamic> json = response.body;
        pageNumber.value++;
        totalCount.value = json["totalRecords"];
        itemsPerPage.value = json["numItemsPerPage"];
        List<Friend> friendsList = <Friend>[];
        if (json['data'] != null) {
          json['data'].forEach((f) {
            friendsList.add(Friend.fromJson(f));
          });
        }
        loading = false;
        return friendsList;
      } else {
        loading = false;
        error = true;
        return <Friend>[];
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      loading = false;
      error = true;
      return <Friend>[];
    }
  }

  void initTeamId(int? id) {
    if (id != null && teamId.value == -1) {
      teamId.value = id;
      initialFetch();
    }
  }

  void removeMember(int id) {
    dataList.removeWhere((element) => element.ownerId == id);
    dataList.refresh();
  }
}
