import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:mybuddy/Api/kickbox_api.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/ke_email_verification_response.dart';
import 'package:mybuddy/models/ref_settings.dart';

class EmailVerificationController extends GetxController {
  static EmailVerificationController of() {
    try {
      return Get.find<EmailVerificationController>();
    } catch (e) {
      return Get.put(EmailVerificationController());
    }
  }

  final TextEditingController emailController = TextEditingController();

  RxBool _isEmailChecking = false.obs;
  RxBool _isEmailTypingStarted = false.obs;
  RxBool _isButtonDisabled = false.obs;
  RxString _email = "".obs;
  RxString _validationMsg = "".obs;
  RxString _didYouMeanSuggestion = "".obs;
  RxString? _emailErrorText = "".obs;
  RefSettings settings = Data().refSettings;

  bool get isEmailChecking => _isEmailChecking.value;
  set isEmailChecking(bool value) => _isEmailChecking.value = value;

  bool get isEmailTypingStarted => _isEmailTypingStarted.value;
  set isEmailTypingStarted(bool value) => _isEmailTypingStarted.value = value;

  String get email => _email.value;
  set email(String value) {
    _email.value = value;
  }

  String get validationMsg => _validationMsg.value;
  set validationMsg(String value) => _validationMsg.value = value;

  String get didYouMeanSuggestion => _didYouMeanSuggestion.value;
  set didYouMeanSuggestion(String value) => _didYouMeanSuggestion.value = value;

  bool get isButtonDisabled =>
      _isButtonDisabled.value ||
      _isEmailChecking.value ||
      emailErrorText != null ||
      _didYouMeanSuggestion.isNotEmpty;

  String? get emailErrorText {
    // at any time, we can get the text from _controller.value.text
    final text = _email.value;

    // if user haven't started typing, don't show any error
    if (!isEmailTypingStarted || _didYouMeanSuggestion.isNotEmpty) {
      _emailErrorText = null;
      return _emailErrorText?.value;
    }

    // if there is a suggestion, show it
    // if there is a validation message, show it
    // if the text is empty or not an email, show an error

    _emailErrorText = "".obs;

    if (text.isEmpty) {
      _emailErrorText?.value = "Email address is required";
    } else if (validationMsg.isNotEmpty) {
      if (settings.kickboxAllowedResponses.contains(validationMsg)) {
        _emailErrorText = null;
      } else {
        _emailErrorText?.value = settings.kickboxResponses
            .singleWhere((element) => element.type == validationMsg,
                orElse: () => KickboxResponses(
                    type: validationMsg,
                    message: "Invalid email address, please try again"))
            .message!;
      }
    } else if (!text.isEmail) {
      _emailErrorText?.value = "Invalid email address, please try again";
    } else {
      _emailErrorText = null;
    }

    return _emailErrorText?.value;
  }

  @override
  void onInit() {
    super.onInit();
    debounce(_email, (String value) => verifyEmailFromKickBox(value),
        time: const Duration(milliseconds: 700));
    ever(_email, (callback) => disableButtonWhileTyping(),
        condition: (_) => _email.value.isNotEmpty && !_isButtonDisabled.value);
  }

  void init() {
    _isEmailChecking.value = false;
    _isEmailTypingStarted.value = false;
    _email.value = "";
    _validationMsg.value = "";
    _didYouMeanSuggestion.value = "";
    _emailErrorText?.value = "";
    emailController.clear();
  }

  Future<void> verifyEmailFromKickBox(String value) async {
    if (!value.contains("@")) return;

    isEmailChecking = true;
    validationMsg = "";
    didYouMeanSuggestion = "";

    KBEmailVerificationResponse response =
        await KickBoxApi().verifyEmail(value);

    if (response.role == true) {
      validationMsg = "role_email";
    } else if (response.disposable == true) {
      validationMsg = "disposable_email";
    } else {
      validationMsg = response.reason ?? "";
    }
    didYouMeanSuggestion = response.didYouMean ?? "";
    isEmailChecking = false;
  }

  void applySuggestion() {
    email = didYouMeanSuggestion;
    emailController.text = didYouMeanSuggestion;
    didYouMeanSuggestion = "";
  }

  bool emailValidate() {
    isEmailTypingStarted = true;
    if (emailErrorText == null && _didYouMeanSuggestion.isEmpty) {
      return true;
    } else {
      return false;
    }
  }

  void disableButtonWhileTyping() {
    if (!_isButtonDisabled.value) {
      // Disable the button
      _isButtonDisabled.value = true;

      Future.delayed(const Duration(milliseconds: 700), () {
        // Enable the button after debounce time
        _isButtonDisabled.value = false;
      });
    }
  }

  @override
  void onClose() {
    emailController.dispose();
    super.onClose();
  }
}
