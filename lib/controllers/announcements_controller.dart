import 'package:mybuddy/Api/mb_api_announcements.dart';
import 'package:mybuddy/controllers/base_pagination_class.dart';
import 'package:mybuddy/models/announcement.dart';

import '../Api/mb_response.dart';
import '../Tools/tools.dart';

class AnnouncementsController extends BasePaginationClass<Announcement> {
  @override
  void onInit() {
    //leave it empty because the cycle of announcement is controlled by stateful widget life cycle
  }

  @override
  Future<List<Announcement>> fetch() async {
    try {
      final MBResponse response = await MbApiAnnouncements().getAnnouncements();
      if (response.success) {
        Map<String, dynamic> json = response.body;
        List<Announcement> announcements = [];
        if (json['announcements'] != null) {
          for (var a in json['announcements']) {
            announcements.add(Announcement.fromJson(a));
          }
        }

        hasMore = false;
        loading = false;
        error = false;

        return announcements;
      } else {
        loading = false;
        error = true;

        return [];
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      loading = false;
      error = true;

      return [];
    }
  }
}
