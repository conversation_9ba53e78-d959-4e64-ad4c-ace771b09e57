import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/controllers/weekly_stats_controller.dart';

import '../class/data.dart';
import '../models/owner.dart';

class SetWeeklyGoalsController extends GetxController {
  static SetWeeklyGoalsController of() {
    try {
      return Get.find();
    } catch (e) {
      return Get.put(SetWeeklyGoalsController());
    }
  }

  RxBool isLoading = false.obs;
  RxList<int> walks = <int>[].obs;
  RxList<int> distance = <int>[].obs;
  RxInt walkSelected = 3.obs;
  RxInt distanceSelected = 2.obs;
  late Owner owner;

  bool get loading => isLoading.value;
  List<int> get walkList => walks.value;
  List<int> get distanceList => distance.value;

  int get walkSelectedValue => walkSelected.value;
  set walkSelectedValue(int value) => walkSelected.value = value;

  int get distanceSelectedValue => distanceSelected.value;
  set distanceSelectedValue(int value) => distanceSelected.value = value;

  @override
  void onInit() {
    super.onInit();
    owner = Data().get().owner;
    initializeWalkAndDuration();
    assignWalkAndDuration();
  }

  void initializeWalkAndDuration() {
    for (int i = 1; i <= 50; i++) {
      distance.add(i);
    }
    for (int i = 1; i <= 30; i++) {
      walks.add(i);
    }
  }

  void assignWalkAndDuration() {
    if (owner.weeklyWalkingGoalDistance != null) {
      distanceSelected.value = (owner.weeklyWalkingGoalDistance! != 0
              ? owner.weeklyWalkingGoalDistance!
              : 2.0)
          .toInt();
    }
    if (owner.weeklyWalkingGoalWalks != null) {
      walkSelected.value = owner.weeklyWalkingGoalWalks! != 0
          ? owner.weeklyWalkingGoalWalks!
          : 3;
    }
  }

  Future<void> updateUser() async {
    isLoading.value = true;

    owner.weeklyWalkingGoalDistance = distanceSelected.value.toDouble();
    owner.weeklyWalkingGoalWalks = walkSelected.value;

    MBResponse response = await MbApiOwner().updateUserRequest(
      Get.context!,
      owner: owner,
    );

    isLoading.value = false;

    if (!response.success) {
      return;
    }

    Data().get().owner = owner;
    Data().storeData();
    WeeklyStatsController.of().checkIfGoalIsSet();

    Tools().common.showMessage(
          Get.context!,
          "Goals Updated!",
          Theme.of(Get.context!).colorScheme.secondary,
        );

    Tools().navigatorPop(value: true);
  }
}
