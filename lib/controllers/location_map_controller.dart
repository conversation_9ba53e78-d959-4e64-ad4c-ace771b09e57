import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mybuddy/Tools/tools_location.dart';
import 'package:mybuddy/class/location_manager.dart';

import 'google_map_display_controller.dart';
import 'gps_indication_controller.dart';

class LocationMapController extends GoogleMapDisplayController implements LocationListener {
  static const String TAG_WALK_TAB_LISTENER = "Walk_Tab";

  RxBool _locationDisabled = false.obs;
  Rx<UserLocation> _userLocation = LocationManager().userLocation.obs;

  BitmapDescriptor? currentLocationMarker;

  //getter
  bool get locationDisabled => _locationDisabled.value;

  LatLng get userLocation =>
      LatLng(_userLocation.value.latitude!, _userLocation.value.longitude!);

  CameraPosition initialCameraPosition() {
    return CameraPosition(
      target: userLocation,
      zoom: 17,
    );
  }

  @override
  void dispose() {
    LocationManager().unregisterListener(TAG_WALK_TAB_LISTENER);
    super.dispose();
  }

  @override
  void onMapCreated(GoogleMapController controller) async {
    await initLocationListener();

    super.onMapCreated(controller);
  }

  Future<void> initLocationListener() async {
    await LocationTools().getLocationPermissions(
      onGranted: () async {
        _locationDisabled.value = false;
        clearMap();
        LocationManager().registerListener(TAG_WALK_TAB_LISTENER, this);
        return true;
      },
      onDenied: () async {
        clearMap();
        _locationDisabled.value = true;
        return true;
      },
    );
  }

  Future<void> _markCurrentLocation() async {
    _locationDisabled.value = false;

    clearMap();

    currentLocationMarker ??= await BitmapDescriptor.fromAssetImage(
        ImageConfiguration(
            size: const Size(15, 15),
            devicePixelRatio: MediaQuery.of(Get.context!).devicePixelRatio),
        Platform.isAndroid
            ? 'assets/icon/current_location.png'
            : 'assets/icon/ic_current_location.png');

    /// current location marker
    addMarker(userLocation, "currentLocation", currentLocationMarker!,
        rotation: _userLocation.value.bearing ?? 0);

    Future.delayed(const Duration(milliseconds: 300), () {
      animateToCurrentPosition();
    });
  }

  void animateToCurrentPosition() {
    mapController?.animateCamera(CameraUpdate.newLatLng(userLocation));
  }

  @override
  void onUserLocationError(err) {}

  @override
  void onUserLocationUpdated(UserLocation location) {
    _userLocation.value = location;
    _markCurrentLocation();
  }
}
