import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum GpsSignal { none, level_1, level_2, level_3, level_4 }

class GpsIndicationController extends GetxController {

  static GpsIndicationController of(){
    try{
      return Get.find<GpsIndicationController>();
    }catch(e){
      return Get.put(GpsIndicationController());
    }
  }

  RxDouble gpsSignalValue = (0.0).obs;

  RxString gpsSignalName = "N/A".obs;

  Rx<Color> gpsSignalColor = (const Color(0xff979797)).obs;

  RxDouble accuracy = (0.0).obs;

  GpsSignal _getGpsSignal() {
    if (accuracy <= 0) {
      // No Signal
      return GpsSignal.none;
    }
    if (accuracy > 35) {
      // No Signal
      return GpsSignal.level_1;
    }
    if (accuracy > 25) {
      // Poor Signal
      return GpsSignal.level_2;
    }
    if (accuracy > 15) {
      // Average Signal
      return GpsSignal.level_3;
    }
    // Full Signal
    return GpsSignal.level_4;
  }

  void _setSignalValue() {
    switch (_getGpsSignal()) {
      case GpsSignal.level_1:
        gpsSignalValue.value = 0.1;
        gpsSignalName.value = "Poor";
        gpsSignalColor.value = const Color(0xffD20000);
        break;
      case GpsSignal.level_2:
        gpsSignalValue.value = 0.26;
        gpsSignalName.value = "Weak";
        gpsSignalColor.value = const Color(0xffFF9839);
        break;
      case GpsSignal.level_3:
        gpsSignalValue.value = 0.51;
        gpsSignalName.value = "Good";
        gpsSignalColor.value = const Color(0xff419563);
        break;
      case GpsSignal.level_4:
        gpsSignalValue.value = 1.0;
        gpsSignalName.value = "Excellent";
        gpsSignalColor.value = const Color(0xff419563);
        break;
      case GpsSignal.none:
      default:
        gpsSignalValue.value = 0.0;
        gpsSignalName.value = "N/A";
        gpsSignalColor.value = const Color(0xff979797);
        break;
    }
  }

  void updateAccuracy(double? newAccuracy) {
    accuracy.value = newAccuracy??0.0;
    _setSignalValue();
  }
}