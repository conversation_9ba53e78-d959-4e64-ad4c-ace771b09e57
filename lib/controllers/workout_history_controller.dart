import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/base_pagination_class.dart';
import 'package:mybuddy/controllers/workout_sync_controller.dart';
import 'package:mybuddy/db/dao/workout_dao.dart';
import 'package:mybuddy/db/dao/workout_dog_dao.dart';
import 'package:mybuddy/db/database_service.dart';
import 'package:mybuddy/db/models/workout_db.dart';
import 'package:mybuddy/db/models/workout_dogs_db.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/workout.dart';

import '../Tools/tools.dart';
import '../class/workout_filter.dart';
import 'internet_controller.dart';

/// This class is responsible for fetching the user's workout in a paginated
/// way from the local database, so that user sees only selected records.
class WorkoutHistoryController extends BasePaginationClass<Workout> {

  static WorkoutHistoryController of() {
    try {
      return Get.find();
    } catch (e) {
      return Get.put(WorkoutHistoryController());
    }
  }

  int pageSize = 20;
  late WorkoutDao workoutDao;
  late WorkoutDogDao workoutDogDao;
  WorkoutSyncController syncController = WorkoutSyncController.of();
  Rx<WorkoutFilter> _filter = WorkoutFilter.defaultFilter().obs;

  RxDouble _totalMiles = (0.0).obs, _totalMinutes = (0.0).obs;
  RxInt _totalPoints = 0.obs, _totalWalks = 0.obs;

  String get totalMiles =>
      _totalMiles.value.toUserLength().toStringAsFixed(1);

  String get totalMinutes => _totalMinutes.value.toENTimeString();

  String get totalPoints => _totalPoints.value.toPointsLong();

  int get totalPointsAsNumber => _totalPoints.value;

  int get totalWalks => _totalWalks.value;

  bool get shouldShowNoInternetWidget => shouldShowProgressWidget &&
      shouldShowEmptyWidget && !InternetController.of().isConnected;

  bool get shouldShowProgressWidget =>
      syncController.isSyncNotCompleted && totalCount.value == dataList.length;

  WorkoutFilter get filter => _filter.value;

  set filter(WorkoutFilter value) {
    _filter.value = value;
  }

  @override
  bool get shouldShowEmptyWidget =>
      super.shouldShowEmptyWidget && !syncController.isSyncNotCompleted;

  @override
  int get itemCount =>
      dataList.length + (hasMore || shouldShowProgressWidget ? 1 : 0);

  @override
  void onInit() {
    workoutDao = DatabaseService().getWorkoutDao();
    workoutDogDao = DatabaseService().getWorkoutDogDao();

    once(syncController.isDataAvailableToDisplay, (callback) {
      initialFetch();
    });

    ever(syncController.syncProgress, (callback) {
      if (!syncController.isSyncNotCompleted) {
        calculateTotalStats();
      }
    });

    super.onInit();
  }

  @override
  Future<List<Workout>> fetch() async {
    try {
      String? startDate = filter.startDateString + "T00:00:00";
      String? endDate = filter.endDateString + "T23:59:59";

      if (filter.dateType == DateSelectionType.allTime) {
        startDate = "1970-01-01T00:00:00";
        endDate = DateTime.now().toIso8601String();
      }

      String moodTypes = "1, 2, 3";
      if (filter.moodTypeList != null) {
        moodTypes = filter.moodTypeList!.join(", ");
      }

      String dogIds = filter.isDogFilterApplied()
          ? filter.pets.map((e) => e.id).join(", ")
          : "";

      int offset = pageSize * pageNumber.value;
      List<WorkoutDb> workoutsDb =
          await workoutDao.getWorkoutsWithLimitAndOffset(
              startDate, endDate, moodTypes, dogIds, pageSize, offset);

      Tools.debugPrint("Fetching page $pageNumber with offset $offset");

      itemsPerPage.value = workoutsDb.length;
      if (workoutsDb.isNotEmpty) {
        pageNumber += 1;
      }

      // Calculating total stats
      await calculateTotalStats();

      List<Workout> workouts = [];
      List<Pet> allPets = [...Data().get().pets, ...Data().get().deletedPets];
      Workout workout;

      for (var work in workoutsDb) {
        workout = Workout.fromWorkoutDB(work);
        List<WorkoutDogDb> wdDbs = await workoutDogDao.getWorkoutDogs(work);
        if (wdDbs.isNotEmpty) {
          List<int> petIds = wdDbs.map((e) => e.dogId).toList();
          List<Pet> workoutPets =
              allPets.where((element) => petIds.contains(element.id)).toList();
          workout.pets = workoutPets;
        }
        workouts.add(workout);
      }
      loading = false;
      return workouts;
    } catch (e) {
      Tools.debugPrint(e.toString());
      loading = false;
      error = true;

      return [];
    }
  }

  @override
  void resetData() {
    super.resetData();
    pageNumber.value = 0;
  }

  void applyFilter(WorkoutFilter filter) {
    this.filter = filter;
    initialFetch();
  }

  /// Calculate total stats to be shown in History summary section
  Future<void> calculateTotalStats() async {
    String? startDate = filter.startDateString + "T00:00:00";
    String? endDate = filter.endDateString + "T23:59:59";

    if (filter.dateType == DateSelectionType.allTime) {
      startDate = "1970-01-01T00:00:00";
      endDate = DateTime.now().toIso8601String();
    }

    String moodTypes = "1, 2, 3";
    if (filter.moodTypeList != null) {
      moodTypes = filter.moodTypeList!.join(", ");
    }

    String dogIds =
        filter.isDogFilterApplied() ? filter.pets.map((e) => e.id).join(", ") : "";

    // Calculating total stats
    Map<String, dynamic> totalStats = await workoutDao
        .getWorkoutsSummaryWithFilters(startDate, endDate, moodTypes, dogIds);

    _totalMinutes.value = totalStats['duration'];
    _totalMiles.value = totalStats['distance'];
    _totalPoints.value = totalStats['points'];
    _totalWalks.value = totalStats['walks'];
    totalCount.value = totalStats['walks'];

    Tools.debugPrint("total count ${totalCount.value}");
  }

  void updateFilterPetsObject(){
    for(var pet in Data().get().pets) {
      if(filter.pets.any((element) => element.id == pet.id)){
        filter.pets[filter.pets.indexWhere((element) => element.id == pet.id)] = pet;
      }
    }
  }
}
