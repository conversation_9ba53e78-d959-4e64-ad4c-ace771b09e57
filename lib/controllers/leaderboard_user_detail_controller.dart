import 'package:get/get.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/models/group_leaderboard.dart';

class LeaderboardUserDetailController extends GetxController {
  Rx<GroupLeaderboardMember>? _user;
  Team team;
  final RxBool _loading = true.obs;

  LeaderboardUserDetailController(this._user, this.team);

  bool get loading => _loading.value;

  GroupLeaderboardMember get user => _user?.value ?? GroupLeaderboardMember();

  String get walks => loading ? "-" : "${user.workouts ?? 0}";

  String get distance => loading ? "-" : user.commaSeperatedDistance;

  String get position => loading ? "-" : "${user.position ?? 0}";

  @override
  void onInit() {
    super.onInit();
    getDetails();
  }

  Future<void> getDetails() async {
    _loading.value = true;
    try {
      final MBResponse response = await MbApiCommunity()
          .getLeaderboardUserDetails(user.ownerId!, team.id!);
      if (response.success) {
        _user?.value = GroupLeaderboardMember.fromJson(response.body);
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
    }
    _loading.value = false;
  }
}
