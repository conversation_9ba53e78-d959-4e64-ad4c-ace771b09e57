import 'package:mybuddy/controllers/base_pagination_class.dart';

import '../Api/mb_api_pet_activity.dart';
import '../Api/mb_response.dart';
import '../Tools/tools.dart';
import '../models/pet_activity.dart';

class PetGalleryController extends BasePaginationClass<PetActivity> {
  PetGalleryController({required this.petId});

  int petId = 0;

  @override
  Future<List<PetActivity>> fetch() async {
    try {
      MBResponse response = await MbApiPetActivity()
          .getPetActivity(page: pageNumber.value, petId: petId, type: 5);

      if (response.success) {
        PetActivityResponse workoutResponse =
            PetActivityResponse.fromJson(response.body);

        pageNumber += 1;
        itemsPerPage.value = workoutResponse.numItemsPerPage;
        totalCount.value = workoutResponse.totalRecords;

        loading = false;

        return workoutResponse.data;
      } else {
        loading = false;
        error = true;

        return [];
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      loading = false;
      error = true;

      return [];
    }
  }

  void removeImage(PetActivity petActivity) {
    dataList.remove(petActivity);
    dataList.refresh();
  }

  void addImage(PetActivity petActivity) {
    dataList.add(petActivity);
    dataList.refresh();
  }
}
