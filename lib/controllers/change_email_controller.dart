import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../Api/mb_api_owner.dart';
import '../Tools/tools.dart';
import '../class/data.dart';
import '../ui/templates/account/email_verification.dart';

class ChangeEmailController extends GetxController {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController password = TextEditingController();
  TextEditingController newEmail = TextEditingController();
  RxString _email = ''.obs;
  RxBool _hidePassword = true.obs;

  bool get hidePassword => _hidePassword.value;

  GlobalKey<FormState> get formKey => _formKey;

  String get email => _email.value;

  @override
  void onInit() {
    // assign the owner current email
    _email.value = Data().get().owner.email;
    super.onInit();
  }

  void changeEmail() {
    FormState? form = _formKey.currentState;
    if (form == null || !form.validate()) {
      return;
    }
    form.save();

    MbApiOwner()
        .changeEmailRequest(
      Get.context,
      newEmail.text,
      password.text,
    )
        .then((response) {
      if (response.success) {
        Tools().navigatorReplacement(EmailVerification.changeEmail(
          email: newEmail.text,
          type: EmailVerificationType.changeEmail,
          oldEmail: email,
        ));
      }
    });
  }

  void togglePasswordVisibility() {
    _hidePassword.value = !_hidePassword.value;
  }
}
