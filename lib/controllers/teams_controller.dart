import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/state_manager.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/base_pagination_class.dart';
import 'package:mybuddy/models/community.dart';

import '../Api/mb_response.dart';
import '../Tools/tools.dart';

class TeamsController extends BasePaginationClass<Team> {
  final RxInt _expandedIndex = (-1).obs;
  final RxInt _currentTab = (0).obs;
  final RxString searchQuery = ''.obs;

  int get expandedIndex => _expandedIndex.value;

  set expandedIndex(int i) => _expandedIndex.value = i;

  int get currentTab => _currentTab.value;

  set currentTab(int i) {
    _currentTab.value = i;
    initialFetch();
  }

  static TeamsController of() {
    try {
      return Get.find<TeamsController>();
    } catch (e) {
      return Get.put(TeamsController());
    }
  }

  @override
  void onInit() {
    super.onInit();
    debounce(searchQuery, (_) => initialFetch());
  }

  @override
  Future<List<Team>> fetch() async {
    try {
      final MBResponse response;

      if (_currentTab == 0) {
        response = await MbApiCommunity().getTeams(
          pageNumber.value,
        );
      } else{
        response = await MbApiCommunity().getPublicTeams(
          pageNumber.value,
          searchQuery.value,
        );
      }
      if (response.success) {
        Map<String, dynamic> json = response.body;
        pageNumber.value++;
        totalCount.value = json["totalRecords"];
        List<Team> teamList = <Team>[];
        if (json['data'] != null) {
          teamList = <Team>[];
          json['data'].forEach((t) {
            teamList.add(Team.fromJson(t));
          });
        }
        loading = false;
        error = false;
        return teamList;
      } else {
        loading = false;
        error = true;

        return <Team>[];
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      loading = false;
      error = true;

      return <Team>[];
    }
  }

  void increaseTeamMemberCount(int teamId) {
    for (var element in dataList) {
      if (element.id == teamId) {
        element.teamMembersCount = element.teamMembersCount! + 1;
      }
    }
    dataList.refresh();
  }

  void decreaseTeamMemberCount(int teamId) {
    for (var element in dataList) {
      if (element.id == teamId) {
        element.teamMembersCount = element.teamMembersCount! - 1;
      }
    }
    dataList.refresh();
  }

  void replaceTeam(Team team) {
    for (var element in dataList) {
      if (element.id == team.id) {
        element = team;
      }
    }
    dataList.refresh();
  }

  void removeTeam(int teamId) {
    dataList.removeWhere((element) => element.id == teamId);
    // Removing from local teams also
    Data().get().teams.removeWhere((element) => element.id == teamId);
    dataList.refresh();
  }

  //get first five records of team list
  List<Team> getRecordsForHomepage() {
    if (dataList.isEmpty) {
      return Data().get().teams;
    } else if (dataList.length < 6) {
      return dataList;
    }
    return dataList.getRange(0, 5).toList();
  }

  void setQuery(String s) {
    searchQuery.value = s;
  }
}
