import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:move_to_background/move_to_background.dart';
import 'package:mybuddy/Tools/tools.cio.dart';
import 'package:mybuddy/Tools/tools_location.dart';
import 'package:mybuddy/class/constants.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/location_manager.dart';
import 'package:mybuddy/controllers/challenges_controller.dart';
import 'package:mybuddy/controllers/community_controller.dart';
import 'package:mybuddy/controllers/internet_controller.dart';
import 'package:mybuddy/controllers/offline_workout_sync_controller.dart';
import 'package:mybuddy/controllers/root_controller.dart';
import 'package:mybuddy/controllers/teams_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/owner.dart';

import '../Api/mb_api_owner.dart';
import '../Tools/tools.dart';
import '../blocs/googlemap_tracking_provider.dart';
import '../blocs/walk_provider.dart';
import '../class/ip_address.dart';
import 'location_map_controller.dart';

enum HomeTab { home, community, walk, challenges, history }

class HomeTabController extends GetxController {
  static HomeTabController of() {
    try {
      return Get.find<HomeTabController>();
    } catch (e) {
      return Get.put(HomeTabController());
    }
  }

  final _selectedIndex = 0.obs;
  late PageController pageController;

  int get selectedIndex => _selectedIndex.value;

  bool get isWalkTab => selectedIndex == 2;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    pageController = PageController(initialPage: selectedIndex);
    _initData();
  }

  void tabSwitcher(HomeTab tab) {
    _switchTab(tab.index);
  }

  Future<void> _switchTab(int index) async {
    // Triggering sync of walks on every tab switch call
    OfflineWorkoutSyncController.of()
        .saveLocalOfflineWalksToServer(showSnackbar: false);

    if (index == selectedIndex) {
      return;
    }

    final WalkProvider walkProvider = WalkProvider.of();
    InternetController.of().checkInternet();
    final rootController = RootController.of;

    if (index == 0) {
      rootController.updateScreenOnCIO(cioScreenNameHome);
    } else if (index == 1) {
      if (Get.isRegistered<TeamsController>()) {
        Get.find<TeamsController>().initialFetch();
      }
      if (Get.isRegistered<CommunityController>()) {
        final controller = Get.find<CommunityController>();
        controller.setScreenToCIO();
      } else {
        rootController.updateScreenOnCIO(cioScreenNameCommunity);
      }
    } else if (index == 2) {
      if (walkProvider.walkOngoing.value) {
        await Tools().location.openWorkoutPage(Get.context!);
        return;
      } else {
        if (Get.isRegistered<LocationMapController>()) {
          Get.find<LocationMapController>().initLocationListener();
        }
      }
    } else if (index == 3) {
      if (Get.isRegistered<ChallengesController>()) {
        final controller = Get.find<ChallengesController>();
        if (controller.error ||
            controller.dataList.isEmpty ||
            controller.currentLocation == null) {
          controller.initialFetch();
        }
        controller.setScreenToCIO();
      } else {
        rootController.updateScreenOnCIO(cioScreenNameChallengesAvailable);
      }
    } else if (index == 4) {
      rootController.updateScreenOnCIO(cioScreenNameHistory);
    }

    _selectedIndex.value = index;
    pageController.jumpToPage(_selectedIndex.value);
  }

  Future<void> _initData() async {
    await Future.wait([MbApiOwner().refreshLoginRequest()]);
    bool notificationPermission =
        await Tools().isNotificationPermissionGranted();
    await LocationTools().getLocationPermissions(
      onGranted: () async {
        await ChallengesController.of().initialFetch();
      },
      onDenied: () async {
        await ChallengesController.of().initialFetch();
      },
    );
    await CIOTools().identifyCIOUser();
    Tools().initNotifications();

    OfflineWorkoutSyncController.of()
        .saveLocalOfflineWalksToServer(showSnackbar: false);
    _updateCIOAppStartupAttributes(notificationPermission);
  }

  Future<void> _updateCIOAppStartupAttributes(bool notifcationStatus) async {
    Owner _owner = Data().get().owner.copy();
    _owner.notificationsAllowed = notifcationStatus;
    _owner.notificationStatusDate = DateTime.now();
    _owner.ipAddress = await IPAddress.getIP;

    try {
      LocationManager locationManager = LocationManager();
      await locationManager.getCurrentLocation();

      UserLocation userLocation = locationManager.userLocation;

      if ((userLocation.latitude != null && userLocation.longitude != null) &&
          (userLocation.latitude != DEFAULT_LOCATION_LATITUDE &&
              userLocation.longitude != DEFAULT_LOCATION_LONGITUDE)) {
        _owner.lastKnownLatitude = userLocation.latitude;
        _owner.lastKnownLongitude = userLocation.longitude;
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
    }

    await MbApiOwner().updateUserRequest(null, owner: _owner);
  }

  Future<bool> onWillPop(BuildContext context) async {
    if (selectedIndex != 0) {
      _selectedIndex.value = 0;
      pageController.jumpToPage(selectedIndex);
      return false;
    }

    bool? result = await Tools().common.showValidDialog(
          context,
          text: 'APPLICATION_MOBILE_MESSAGE_CONFIRM_APPLICATION_EXIT'.tr(),
        );

    if (result == true) {
      // Checking if walk is in progress, then we simply move the activity to
      // background using the MoveToBackground.moveTaskToBack().
      // Otherwise we pop the complete app
      TrackingProvider trackingProvider = TrackingProvider.of(context);
      if (trackingProvider.isWalking.value) {
        MoveToBackground.moveTaskToBack();
        return false;
      } else {
        SystemChannels.platform.invokeMethod('SystemNavigator.pop');
      }
    }
    return result ?? false;
  }
}
