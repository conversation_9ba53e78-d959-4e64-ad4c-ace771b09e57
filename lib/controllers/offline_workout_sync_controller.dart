import 'package:get/get.dart';
import 'package:mybuddy/Tools/tools.cio.dart';
import 'package:mybuddy/controllers/home_summary_controller.dart';
import 'package:mybuddy/controllers/workout_history_controller.dart';
import 'package:mybuddy/db/database_service.dart';
import 'package:mybuddy/db/models/workout_dogs_db.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/models/walk_model.dart';

import '../Api/mb_api_workout.dart';
import '../Api/mb_response.dart';
import '../Tools/tools.dart';
import '../class/app_storage.dart';
import '../class/data.dart';
import '../db/dao/workout_dao.dart';
import '../db/dao/workout_dog_dao.dart';
import '../db/models/workout_db.dart';
import '../models/global_constants.dart';
import '../models/owner.dart';
import '../models/unit.dart';

enum OfflineWalkSyncStatus { notAvailable, notStarted, inProgress, complete }

class OfflineWorkoutSyncController extends GetxController {
  static OfflineWorkoutSyncController of() {
    try {
      return Get.find<OfflineWorkoutSyncController>();
    } catch (e) {
      return Get.put(OfflineWorkoutSyncController());
    }
  }

  late WorkoutDao _workoutDao;
  late WorkoutDogDao _workoutDogDao;

  Rx<OfflineWalkSyncStatus> _syncStatus =
      OfflineWalkSyncStatus.notAvailable.obs;

  bool get isUnSyncWalksAvailable =>
      _syncStatus.value != OfflineWalkSyncStatus.notAvailable;

  bool get isSyncingInProgress =>
      _syncStatus.value == OfflineWalkSyncStatus.inProgress;

  @override
  void onInit() {
    super.onInit();
    _workoutDao = DatabaseService().getWorkoutDao();
    _workoutDogDao = DatabaseService().getWorkoutDogDao();
    checkUnSyncWalksStatus();
  }

  Future<WorkoutDb?> saveOfflineWalkInDB(Walk walk) async {
    try {
      //save gpx file to local storage
      await AppStorage().writeGpx(walk.trackName, walk.gpx);

      CIOTools().logDebugEvent(
          Event.WRITE_GPX, {'trackName': walk.trackName, 'gpx': walk.gpx});

      //covert walk object to the local storage workout map
      Map<String, dynamic> _body = walk.toOfflineWalkMap();

      WorkoutDb workoutDB = WorkoutDb.fromMap(_body);

      //reward points to the workout
      workoutDB.points = pointCalculation(walk);

      if (workoutDB.workoutId != null) {
        //check if the data exist in local database
        WorkoutDb? workoutDBData =
            await _workoutDao.getWorkoutByID(workoutDB.workoutId!);

        if (workoutDBData != null) {
          // if data exist in the database then compare the stats and set workoutId to null

          CIOTools().logDebugEvent(Event.DUPLICATE_WORKOUT, {
            'oldWorkout': workoutDBData.toMap(),
            'newWorkout': workoutDB.toMap()
          });

          if ((workoutDBData.distance != workoutDB.distance ||
                  workoutDBData.duration != workoutDB.duration) &&
              workoutDBData.createdAt != workoutDB.createdAt) {
            workoutDB.workoutId = null;
            Tools.debugPrint("Workout ID is already exist but data is changed");
          } else {
            // if it's the duplicate data then return without saving
            Tools.debugPrint("Duplicate Workout exist");
            return workoutDB;
          }
        }
      }
      CIOTools().logDebugEvent(Event.INSERT_WORKOUT, workoutDB.toMap());
      int? workoutId = await _workoutDao.insertWorkout(workoutDB);

      if (workoutId == null) return workoutDB;

      workoutDB.id = workoutId;

      //get the dogs list of the workout to store it in workout dogs table
      List<WorkoutDogDb> workoutDogs = walk
          .toWalkDogsMap(workoutId)
          .map((e) => WorkoutDogDb.fromMap(e))
          .toList();

      CIOTools().logDebugEvent(Event.INSERT_WORKOUT_DOG,
          {'workoutDogs': walk.toWalkDogsMap(workoutId)});
      await _workoutDogDao.insertWorkoutDogs(workoutDogs);

      // Adding distance covered in the pets total miles that were
      // selected for the walk
      double petMiles = walk.distance.toUserLength(src: LengthSource.meters);
      Data().get().pets.forEach((pet) {
        if (walk.pets.any((workoutPet) => pet.id == workoutPet.id)) {
          pet.totalMiles = pet.totalMiles! + petMiles;
          pet.totalWalks = pet.totalWalks! + 1;
        }
      });

      Owner owner = Data().get().owner;
      int dailyPoints = owner.dailyCharityPointsGained;
      dailyPoints = dailyPoints + workoutDB.points;
      owner.points = owner.points + workoutDB.points;

      Data().storeData();

      return workoutDB;
    } catch (e, trace) {
      Tools.sendErrorToCrashlytics(e, trace);
      return null;
    }
  }

  Future<bool> _syncSpecificWalkToServer(
      Map<String, dynamic> body, WorkoutDb workoutDB) async {
    CIOTools().logDebugEvent(Event.UPLOAD_WALK, body);
    MBResponse response = await MbApiWorkout().add(null, Status.saved, body);

    if (response.success) {
      workoutDB.isUploaded = 1;
      workoutDB.workoutId = response.body['workout']['id'];
      _workoutDao.updateWorkout(workoutDB);

      CIOTools().logDebugEvent(Event.UPLOAD_WALK_SUCCESS, workoutDB.toMap());
      return true;
    }
    return false;
  }

  Future<void> saveLocalOfflineWalksToServer({bool showSnackbar = true}) async {
    if (!isUnSyncWalksAvailable || isSyncingInProgress) {
      return;
    }

    _syncStatus.value = OfflineWalkSyncStatus.inProgress;

    if (!(await Tools()
        .common
        .isOnline(alertContext: showSnackbar ? Get.context : null))) {
      _syncStatus.value = OfflineWalkSyncStatus.notStarted;
      return;
    }

    List<WorkoutDb> workouts = await _workoutDao.getUnSyncedWorkouts();

    for (var workout in workouts) {
      List<WorkoutDogDb> workoutDogs =
          await _workoutDogDao.getWorkoutDogs(workout);

      Map<String, dynamic> body = await workout.toWalkJson();
      body['petIds'] = workoutDogs.map((p) => p.dogId).toList().join(',');

      bool success = await _syncSpecificWalkToServer(body, workout);

      if (!success) {
        break;
      }
    }

    // Refreshing the home summary stats as soon as the walks are synced
    HomeSummaryController.of().fetchHomeSummary();

    // refresh history
    if (Get.isRegistered<WorkoutHistoryController>()) {
      WorkoutHistoryController.of().initialFetch();
    }
    // update sync status
    await checkUnSyncWalksStatus();
  }

  Future<void> checkUnSyncWalksStatus() async {
    _syncStatus.value = (await _workoutDao.getUnSyncedWorkouts()).isEmpty
        ? OfflineWalkSyncStatus.notAvailable
        : OfflineWalkSyncStatus.notStarted;
  }

  int pointCalculation(Walk walk) {
    GlobalConstants globalConstants = Data().get().globalConstants;

    int points = 0;
    double userSpeed = walk.speed.toUserSpeed();
    double maxSpeedLimit =
        globalConstants.maxSpeedLimitToEarnPoints?.toDouble() ?? 0;
    double userDistance = walk.distance.toUserLength(src: LengthSource.meters);
    double minDistanceLimit = globalConstants
            .minimumDistanceInMilesLimitToEarnCharityPoints
            ?.toDouble() ??
        0;

    if (userDistance >= minDistanceLimit && userSpeed <= maxSpeedLimit) {
      points = globalConstants.charityPointsPerWorkout ?? 0;
    }

    return points;
  }
}
