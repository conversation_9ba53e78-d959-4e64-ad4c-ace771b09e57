import 'package:get/get.dart';
import 'package:mybuddy/controllers/base_pagination_class.dart';

import '../Api/mb_api_shelter.dart';
import '../Tools/tools.dart';
import '../models/shelter.dart';

class CharityListController extends BasePaginationClass {
  static CharityListController of([Shelter? shelter]) {
    try {
      return Get.find();
    } catch (e) {
      return Get.put(CharityListController(shelter));
    }
  }

  CharityListController(Shelter? shelter) {
    if (shelter != null) {
      _selectedShelter.value = shelter;
    }
  }

  Rx<Shelter> _selectedShelter = Shelter.none().obs;
  RxString _searchText = ''.obs;

  Shelter get selectedShelter => _selectedShelter.value;

  String get searchText => _searchText.value;

  bool get isShelterChanged => selectedShelter.id != Shelter.none().id;

  set selectedShelter(Shelter value) {
    _selectedShelter.value = value;
  }

  set searchText(String value) => _searchText.value = value;

  @override
  void onInit() {
    super.onInit();
    debounce(_searchText, (_) => initialFetch());
  }

  @override
  Future<void> initialFetch() async {
    resetData();
    itemsPerPage.value = 50;
    dataList.value = await fetch();
  }

  @override
  Future<void> pageFetch() async {
    dataList.addAll(await fetch());
  }

  @override
  Future<List<Shelter>> fetch() async {
    try {
      var response = await MbApiShelter().getShelters(
        page: pageNumber.value,
        perPageCount: itemsPerPage.value,
        terms: searchText,
      );

      if (response.success) {
        List<Shelter> fetchedShelters =
            Shelter.parseShelters(response.body['shelters']);

        pageNumber += 1;
        hasMore = fetchedShelters.length == itemsPerPage.value;

        loading = false;

        return fetchedShelters;
      } else {
        loading = false;
        error = true;

        return [];
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      loading = false;
      error = true;

      return [];
    }
  }

  // check if shelter is selected to show the selection on UI
  bool isShelterSelected(int id) {
    return selectedShelter.id == id;
  }
}
