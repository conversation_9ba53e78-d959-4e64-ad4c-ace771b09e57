import 'package:get/get_instance/src/extension_instance.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import '../Tools/tools.dart';

enum SocialErrorType { fbEmailError }

class SocialLoginController extends GetxController {
  static SocialLoginController of() {
    try {
      return Get.find<SocialLoginController>();
    } catch (e) {
      return Get.put(SocialLoginController());
    }
  }

  RxBool _shouldShowError = false.obs;
  RxString _errorMessage = "".obs;
  RxString _errorIcon = "".obs;

  bool get isError => _shouldShowError.value;

  String get errorMessage => _errorMessage.value;

  String get errorIcon => _errorIcon.value;

  void _showError(SocialErrorType errorType) {
    if (errorType == SocialErrorType.fbEmailError) {
      _errorMessage.value =
          'APPLICATION_MOBILE_TEXT_FB_EMAIL_ERROR_MESSAGE'.tr();
      _errorIcon.value = 'assets/icon/fb_error.png';
    }
    _shouldShowError(true);
  }

  void _hideError() {
    _shouldShowError(false);
    _errorMessage.value = "";
    _errorIcon.value = "";
  }

  void fbLogin() async {
    _hideError();
    await Tools().facebook.login(Get.context!, onEmailNotFoundError: () {
      _showError(SocialErrorType.fbEmailError);
    });
  }

  void googleLogin() async {
    _hideError();
    await Tools().google.login(Get.context!);
  }

  void appleLogin() async {
    _hideError();
    await Tools().apple.login(Get.context!);
  }

  void hideErrorAndPerformSpecificAction(Function onTap) {
    _hideError();
    onTap();
  }
}
