import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

abstract class GoogleMapDisplayController extends GetxController {
  RxMap<MarkerId, Marker> _markers = <MarkerId, Marker>{}.obs;
  RxMap<PolylineId, Polyline> _polylines = <PolylineId, Polyline>{}.obs;

  GoogleMapController? _mapController;

  Map<MarkerId, Marker> get markers => _markers.value;

  Map<PolylineId, Polyline> get polylines => _polylines.value;

  GoogleMapController? get mapController => _mapController;

  void onMapCreated(GoogleMapController controller) async {
    _mapController = controller;
    update();
  }

  void addMarker(LatLng position, String id, BitmapDescriptor descriptor,
      {double rotation = 0}) {
    MarkerId markerId = MarkerId(id);
    Marker marker =
    Marker(markerId: markerId, icon: descriptor, position: position,
        rotation: rotation);
    markers[markerId] = marker;
    _markers.refresh();
  }

  void addPolyLine(List<List<LatLng>> latLongsList) {
    PolylineId id = const PolylineId("polyline");
    for (int i = 0; i < latLongsList.length; i++) {
      id = PolylineId("route${i + 1}");
      Polyline polyline = Polyline(
          polylineId: id,
          color: const Color(0xfffdc635),
          width: 8,
          points: latLongsList[i]);
      polylines[id] = polyline;
      _polylines.refresh();
    }
  }

  void clearMap() {
    _markers.clear();
    _polylines.clear();
  }

  void disposeController() {
    _mapController?.dispose();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    clearMap();
    disposeController();
  }
}
