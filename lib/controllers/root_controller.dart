import 'dart:async';

import 'package:get/get.dart';
import 'package:mybuddy/Tools/tools.cio.dart';
import 'package:mybuddy/class/constants.dart';
import 'package:mybuddy/controllers/set_weekly_goals_controller.dart';
import 'package:mybuddy/controllers/teams_controller.dart';
import 'package:mybuddy/controllers/weekly_stats_controller.dart';
import 'package:mybuddy/controllers/workout_history_controller.dart';
import 'package:mybuddy/controllers/workout_sync_controller.dart';
import 'package:mybuddy/db/database_service.dart';
import 'package:mybuddy/ui/templates/account/owner_set_weekly_goals.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../Api/mb_api_owner.dart';
import '../Api/mb_api_ref.dart';
import '../Tools/tools.dart';
import '../Tools/tools_migration.dart';
import '../Tools/tools_native_splash.dart';
import '../class/data.dart';
import '../class/location_manager.dart';
import '../class/petcarepack.dart';
import '../class/ref.dart';
import '../class/settings_delegate.dart';
import '../class/walk_lock_screen_widget_ios.dart';
import '../models/login_data.dart';
import '../models/owner.dart';
import '../ui/templates/notification/notification_permission_screen.dart';
import '../ui/templates/onboarding/onlogin_page.dart';
import 'home_stats_controller.dart';
import 'home_summary_controller.dart';
import 'home_tab_controller.dart';
import 'offline_workout_sync_controller.dart';

enum AuthStatus { notDetermined, onBoarding, notSignedIn, signedIn }

class RootController extends GetxController {
  static RootController get of {
    try {
      return Get.find();
    } catch (e) {
      return Get.put(RootController());
    }
  }

  RxBool refRequestDone = false.obs;
  String? _data;
  Rx<AuthStatus> authStatus = AuthStatus.notDetermined.obs;

  @override
  void onInit() {
    super.onInit();
    init();
  }

  Future<void> init() async {
    Tools()
        .appsFlyerTools
        .initSdk(appsFlyerDevKey, appsFlyerAppIdIos, appsFlyerAppIdAndroid);

    if (refRequestDone.isTrue) return;
    refRequestDone(true);

    ///init fallback refs
    await Ref().initFallBackRef();

    ///init petcarepack
    PetCarePackDelegate()
        .setPacks(SettingsDelegate().prefs.getString('homecare'));

    /// refresh ref
    if (await Tools().common.isOnline()) {
      await MbApiRef().refRequest(null);
      await MbApiRef().refSettingsRequest(null);
    }

    /// init all platform and version information
    await initPlatformAndVersion();

    ///init data if available
    if (Data().data == null) {
      _data = SettingsDelegate().prefs.getString('data');
      Data().setData(_data);
    }

    ///init migration
    await migrateXamarinToFlutter();

    DatabaseService db = DatabaseService();
    await db.database;

    /// refresh view
    if (_data == null) {
      SettingsDelegate().prefs.containsKey('login') != true
          ? authStatus(AuthStatus.onBoarding)
          : authStatus(AuthStatus.notSignedIn);
    } else {
      LoginData data = Data().get();

      if ((data.pets.isEmpty && shouldShowFirstLoginScreens) ||
          data.owner.shelter == null) {
        Tools()
            .navigatorPush(const OnLoginPage())
            .then((value) => initiateSignedInState());
      } else {
        initiateSignedInState();
      }
    }

    await CIOTools().initialize();
    NativeSplashTools.hideSplashScreen();

    Tools().firebaseDynamicLinkTools.initDynamicLinks();
  }

  Future<void> initPlatformAndVersion() async {
    String? oldBuild = SettingsDelegate().prefs.getString('version');
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String _appName = packageInfo.appName;
    String _packageName = packageInfo.packageName;
    String version = packageInfo.version;
    String _buildNumber = packageInfo.buildNumber;
    Tools.debugPrint('info:  $_appName $_packageName $version $_buildNumber');
    unawaited(SettingsDelegate().prefs.setString('version', _buildNumber));
    unawaited(SettingsDelegate()
        .prefs
        .setString('version-update-time', DateTime.now().toIso8601String()));
    Tools().isUpdate = (oldBuild != null && _buildNumber != oldBuild);
    if (Tools().isUpdate) {
      Tools.debugPrint(
          'NEW VERSION   newBuild: $_buildNumber oldBuild: $oldBuild');
      // await MbApiOwner().refreshLoginRequest();
    }
    Tools().appVersion = version;
    Tools().packageName = _packageName;
    Tools().buildNumber = _buildNumber;
  }

  Future<void> migrateXamarinToFlutter() async {
    MigrationXamarinToFlutter obj = MigrationXamarinToFlutter();
    await obj.start();
  }

  void initiateNotSignedInState() {
    authStatus(AuthStatus.notSignedIn);
    LocationManager().stopLocationUpdatesAppWide();
    LockScreenWalkWidget.dismissWidget();
    _clearControllers();
  }

  Future<void> checkNotificationPermission() async {
    bool shouldAskPermission =
        await Tools().shouldNotificationPermissionScreenVisible();
    if (shouldAskPermission) {
      await Tools().navigatorPush(NotificationPermissionScreen());
    }
  }

  Future<void> showSetGoalsScreen() async {
    if (shouldShowFirstLoginScreens) {
      await Tools()
          .navigatorPush(SetWeeklyGoalsScreen(fromSetting: false))
          .then((value) {
        Get.delete<SetWeeklyGoalsController>();
        SettingsDelegate().prefs.setBool(isFirstLogin, false);
      });
    }
  }

  Future<void> initiateSignedInState() async {
    await checkNotificationPermission();
    authStatus(AuthStatus.signedIn);
    updateScreenOnCIO(cioScreenNameHome);
  }

  void initiateOnBoardingState() {
    authStatus(AuthStatus.onBoarding);
  }

  Future<void> updateScreenOnCIO(String screen) async {
    Tools().cioTools.setScreenEvent(screen);
    Owner _owner = Data().get().owner.copy();
    _owner.screen = screen;

    try {
      await MbApiOwner().updateUserRequest(null, owner: _owner);
    } catch (e) {
      Tools.debugPrint(e.toString());
    }
  }

  //Function to delete all the controllers of the HomePage on logout
  void _clearControllers() {
    Get.delete<HomeStatsController>();
    Get.delete<WeeklyStatsController>();
    Get.delete<HomeSummaryController>();
    Get.delete<HomeTabController>();
    Get.delete<TeamsController>();

    if (Get.isRegistered<WorkoutSyncController>()) {
      WorkoutSyncController syncController = WorkoutSyncController.of();
      syncController.clearSyncedData();
    }

    Get.delete<WorkoutHistoryController>();
    Get.delete<WorkoutSyncController>();
    Get.delete<OfflineWorkoutSyncController>();
  }

  bool get shouldShowFirstLoginScreens =>
      SettingsDelegate().prefs.getBool(isFirstLogin) ?? false;
}
