import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/ref_settings.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/ui/templates/account/email_verification.dart';
import 'package:url_launcher/url_launcher.dart';

import 'email_verification_controller.dart';

class RegisterUserController extends EmailVerificationController {
  static RegisterUserController of() {
    try {
      return Get.find<RegisterUserController>();
    } catch (e) {
      return Get.put(RegisterUserController());
    }
  }

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();
  RxBool _hidePassword = true.obs;
  RxBool _hideConfirmPassword = true.obs;
  RxBool _agreementsAccepted = true.obs;
  RxBool _isLoading = false.obs;

  final Owner owner = Owner();
  Country? country;
  RefSettings settings = Data().refSettings;

  bool get hidePassword => _hidePassword.value;
  set hidePassword(bool value) => _hidePassword.value = value;

  bool get hideConfirmPassword => _hideConfirmPassword.value;
  set hideConfirmPassword(bool value) => _hideConfirmPassword.value = value;

  bool get agreementsAccepted => _agreementsAccepted.value;
  set agreementsAccepted(bool value) => _agreementsAccepted.value = value;

  bool get isLoading => _isLoading.value;
  set isLoading(bool value) => _isLoading.value = value;

  @override
  set email(String value) {
    super.email = value;
    owner.email = value;
  }

  Future<void> submitForm(BuildContext context) async {
    final FormState? form = formKey.currentState;
    bool isEmailValid = !emailValidate();
    if ((form == null || !form.validate()) || isEmailValid) {
      Tools().common.showValidationErrorMessage(context);
      return;
    }

    if (!agreementsAccepted) {
      Tools().common.showValidationErrorMessage(context);
      return;
    }

    form.save();
    isLoading = true;

    MBResponse response = await MbApiOwner().registerRequest(
      context,
      owner,
      passwordController.text,
    );

    isLoading = false;

    if (response.success) {
      Tools().navigatorPop();
      Tools().navigatorPush(EmailVerification(
        email: owner.email,
        type: EmailVerificationType.signup,
      ));
    }
  }

  Future<void> openPrivacyPolicy() async {
    await launchUrl(Uri.parse("https://www.wooftrax.com/privacy"));
  }

  Future<void> openTermsAndConditions() async {
    await launchUrl(Uri.parse("https://www.wooftrax.com/tc"));
  }

  @override
  void dispose() {
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.dispose();
  }
}
