import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../Tools/tools.dart';

class InternetController extends GetxController {
  static InternetController of() {
    try{
      return Get.find<InternetController>();
    } catch (e) {
      Tools.debugPrint(e.toString());
      return Get.put(InternetController());
    }
  }

  RxBool _isInternetConnected = true.obs;

  bool get isConnected => _isInternetConnected.value;

   Future<bool> checkInternet({BuildContext? context}) async {
    _isInternetConnected(await Tools().common.isOnline(alertContext: context));
    return _isInternetConnected.value;
  }

}