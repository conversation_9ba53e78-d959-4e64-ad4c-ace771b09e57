import 'package:get/get.dart';

abstract class BasePaginationClass<T> extends Getx<PERSON>ontroller {
  int nextPageThreshold = 5;
  RxInt pageNumber = 1.obs;
  RxInt totalCount = 0.obs;
  RxInt itemsPerPage = 0.obs;

  final RxBool _hasMore = true.obs;
  final RxBool _error = false.obs;
  final RxBool _loading = true.obs;

  RxList<T> dataList = List<T>.empty().obs;

  bool get hasMore => _hasMore.value;

  bool get error => _error.value;

  bool get loading => _loading.value;

  bool get shouldShowEmptyWidget => dataList.isEmpty && !loading && !error;

  int get itemCount => dataList.length + (hasMore ? 1 : 0);

  set hasMore(bool b) => _hasMore.value = b;

  set error(bool b) => _error.value = b;

  set loading(bool b) => _loading.value = b;

  void resetData() {
    pageNumber.value = 1;
    totalCount.value = 0;
    itemsPerPage.value = 0;
    _loading.value = true;
    dataList.value = [];
  }

  @override
  void onInit() {
    super.onInit();
    dataList.value = [];
    initialFetch();
  }

  Future<void> initialFetch() async {
    resetData();
    dataList.value = await fetch();
    hasMore = totalCount > dataList.length;
  }

  Future<void> pageFetch() async {
    dataList.addAll(await fetch());
    hasMore = totalCount > dataList.length;
  }

  Future<List<T>> fetch();

  bool shouldNextPageFetch(index) {
    return hasMore == true && index == dataList.length - nextPageThreshold;
  }

  List<T> errorResponse() {
    loading = false;
    error = true;

    return [];
  }
}
