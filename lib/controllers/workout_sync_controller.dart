import 'package:get/get.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/constants.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/controllers/workout_controller.dart';
import 'package:mybuddy/db/dao/workout_dao.dart';
import 'package:mybuddy/db/dao/workout_dog_dao.dart';
import 'package:mybuddy/db/database_service.dart';
import 'package:mybuddy/db/models/workout_db.dart';
import 'package:mybuddy/db/models/workout_dogs_db.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/workout.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum SyncStatus { needSyncing, started, stopped, complete, alreadyDone }

/// This controller is responsible for Syncing the workout history of the user
class WorkoutSyncController extends GetxController {

  /// This is used to get the instance of the controller from anywhere
  static WorkoutSyncController of() {
    try {
      return Get.find();
    } catch (e) {
      return Get.put(WorkoutSyncController());
    }
  }

  RxDouble syncProgress = (0.0).obs;
  final Rx<SyncStatus> _syncStatus = SyncStatus.needSyncing.obs;
  late WorkoutDao _workoutDao;
  late WorkoutDogDao _workoutDogDao;
  late SharedPreferences _prefs;
  int _pageToBeFetched = 1;
  int _totalRecordsFetched = 0;
  int _totalCount = 1;
  RxBool isDataAvailableToDisplay = false.obs;

  @override
  void onInit() {
    super.onInit();
    _workoutDao = DatabaseService().getWorkoutDao();
    _workoutDogDao = DatabaseService().getWorkoutDogDao();
    _prefs = SettingsDelegate().prefs;
  }

  @override
  void onClose() {
    _stopSyncProcess();
    super.onClose();
  }

  /// This kicks off the workout history sync process
  Future<void> startSyncProcess() async {
    _checkIfSyncDone();

    // Check if the sync is already done we do nothing
    // Checking if already not running, then return simply
    if (_syncStatus.value == SyncStatus.alreadyDone ||
        _syncStatus.value == SyncStatus.complete ||
        _syncStatus.value == SyncStatus.started) {
      return;
    }
    _syncStatus.value = SyncStatus.started;

    _loadSyncState();

    WorkoutController controller = WorkoutController();
    syncProgress.value = _totalRecordsFetched / _totalCount * 100;
    List<Workout> dataList = [];

    try {
      Tools.debugPrint(
          "pagenumber : ${_pageToBeFetched} total count: ${_totalCount} total records fetched ${_totalRecordsFetched}");

      int pageRecords = 0;
      while (_totalRecordsFetched < _totalCount) {
        // Breaking the while loop forcefully on stop sync process
        if (_syncStatus.value == SyncStatus.stopped) break;

        dataList = await controller.pageFetch(_pageToBeFetched);
        if (controller.error) {
          _syncStatus.value = SyncStatus.stopped;
          break;
        }

        pageRecords = dataList.length;

        // Breaking the while loop forcefully on stop sync process
        if (_syncStatus.value == SyncStatus.stopped) break;

        await _savePageInDb(dataList);

        // Setting values to be saved in prefs for resuming the sync page wise
        _totalCount = controller.totalCount;
        _totalRecordsFetched += pageRecords;
        _pageToBeFetched++;

        await _prefs.setInt(pageNumberSynced, _pageToBeFetched);
        await _prefs.setInt(totalRecordsSynced, _totalRecordsFetched);
        await _prefs.setInt(totalRecordsCount, _totalCount);

        syncProgress.value = _totalRecordsFetched / _totalCount * 100;
      }

      if (_syncStatus.value == SyncStatus.stopped) return;

      // Marking sync done only if the records fetched and total count is equal,
      // Otherwise the sync has not completed
      if (_totalRecordsFetched == _totalCount) {
        await _prefs.setBool(freshSyncDone, true);

        syncProgress.value = 100;
        _syncStatus.value = SyncStatus.complete;
      }
    } catch (e) {
      Tools.debugPrint("Error inserting records $e");
    }
  }

  void _stopSyncProcess() {
    if (syncInProgress()) {
      _syncStatus.value = SyncStatus.stopped;
    }
  }

  /// This methods tell whether the sync process is already done by checking
  /// the sharedpreferences
  void _checkIfSyncDone() {
    bool? isSyncDone = _prefs.getBool(freshSyncDone);
    if (isSyncDone != null && isSyncDone) {
      _syncStatus.value = SyncStatus.alreadyDone;
      syncProgress.value = 100;
    }
  }

  void _loadSyncState() {
    int? pageNumber = _prefs.getInt(pageNumberSynced);
    _pageToBeFetched = pageNumber ?? 1;

    int? totalRecords = _prefs.getInt(totalRecordsSynced);
    _totalRecordsFetched = totalRecords ?? 0;

    int? totalCount = _prefs.getInt(totalRecordsCount);
    _totalCount = totalCount ?? 1;
  }

  Future<void> _savePageInDb(List<Workout> workouts) async {
    List<WorkoutDb> workoutsDb =
    workouts.map((e) => _workoutMapper(e)).toList();
    // Tools.debugPrint("Before Inserting records $workoutsDb");
    await _workoutDao.insertWorkouts(workoutsDb);

    List<WorkoutDogDb> workoutsDogDb = [];
    Pet pet;
    WorkoutDogDb dogDb;
    for (var i = 0; i < workouts.length; i++) {
      for (var j = 0; j < workouts[i].pets.length; j++) {
        pet = workouts[i].pets[j];
        dogDb = _workoutDogMapper(pet, workoutsDb[i]);
        workoutsDogDb.add(dogDb);
      }
    }

    if (workoutsDogDb.isNotEmpty) {
      await _workoutDogDao.insertWorkoutDogs(workoutsDogDb);
    }

    isDataAvailableToDisplay.value = true;
  }

  bool syncInProgress() {
    return _syncStatus.value == SyncStatus.needSyncing ||
        _syncStatus.value == SyncStatus.started;
  }

  bool isSyncStopped() {
    return _syncStatus.value == SyncStatus.stopped;
  }

  /// This method clears the synced workout history, if user logsout from the app
  Future<void> clearSyncedData() async {
    await _workoutDogDao.clear();
    await _workoutDao.clear();
    await _prefs.remove(freshSyncDone);
    await _prefs.remove(pageNumberSynced);
    await _prefs.remove(totalRecordsSynced);
    await _prefs.remove(totalRecordsCount);
  }

  WorkoutDb _workoutMapper(Workout workout) {
    return WorkoutDb(
        workoutId: workout.id,
        ownerId: workout.ownerId ?? 0,
        status: 1,
        distance: workout.distance,
        speed: workout.speed,
        charityId: workout.shelter!.id,
        charityName: workout.shelter!.name,
        duration: workout.duration,
        moodType: workout.mood!,
        points: workout.points ?? 0,
        gpxFileName: workout.filename ?? "",
        startLat: workout.startLat ?? 0.0,
        startLong: workout.startLong ?? 0.0,
        endLat: workout.endLat ?? 0.0,
        endLong: workout.endLong ?? 0.0,
        isUploaded: 1,
        createdAt: workout.createdAt!,
        updatedAt: workout.updatedAt!);
  }

  WorkoutDogDb _workoutDogMapper(Pet pet, WorkoutDb workoutDb) {
    return WorkoutDogDb(dogId: pet.id, workoutId: workoutDb.id!);
  }

  bool get isSyncNotCompleted => syncProgress.value < 100;

  int get syncProgressRoundedValue => syncProgress.value.round();
}
