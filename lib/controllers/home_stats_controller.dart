import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mybuddy/Api/mb_api_home_stats.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/workout_sync_controller.dart';
import 'package:mybuddy/db/database_service.dart';
import 'package:mybuddy/db/models/workout_db.dart';
import 'package:mybuddy/models/home_stats.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/user_goal.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class HomeStatsController extends GetxController {
  static HomeStatsController of() {
    try {
      return Get.find();
    } catch (e) {
      return Get.put(HomeStatsController());
    }
  }

  final RxString _selectedDate = 'Today'.obs;
  final RxBool _loading = true.obs;
  final RxBool _showGoals = true.obs;
  final Rx<HomeStats> _homeStats = HomeStats().obs;
  final Rx<DailyUserGoal> _todaysGoals = DailyUserGoal().obs;
  final Rx<DateTime> _lastQueriedDate = DateTime.now().obs;

  DateTime? previousCustomStartDate;
  PickerDateRange? selectedWeek;
  final DateRangePickerController customDateController =
      DateRangePickerController();

  final List<String> bottomSheetItems = ['Today', 'Yesterday', 'Custom'];

  String get selectedDate => _selectedDate.value;
  DateTime get lastQueriedDate => _lastQueriedDate.value;
  bool get loading => _loading.value;
  bool get showGoals => _showGoals.value;
  HomeStats get homeStats => _homeStats.value;
  DailyUserGoal get todaysGoals => _todaysGoals.value;

  @override
  void onInit() async {
    once(WorkoutSyncController.of().isDataAvailableToDisplay, (callback) {
      refreshHomeStats();
    });
    await setDateByItem(selectedDate);
    updateTodaysGoals();
    _loading.value = false;

    super.onInit();
  }

  void updateTodaysGoals() {
    Owner _owner = Data().get().owner;
    todaysGoals.dailyDistanceGoal = _owner.dailyWalkingGoalDistance ?? 2;
    todaysGoals.dailyWalkGoal = _owner.dailyWalkingGoalWalks ?? 3;
    if (selectedDate == 'Today') {
      homeStats.dailyUserGoal = todaysGoals;
      _showGoals.value = true;
    }
  }

  Future<void> setDateByItem(String selectedItem) async {
    switch (selectedItem) {
      case 'Yesterday':
        DateTime date = DateTime.now().subtract(const Duration(days: 1));
        return await setDate(date, selectedItem);
      case 'Today':
      default: // in case selected item doesn't match
        await handleTodaysStats();
        updateTodaysGoals();
    }
  }

  Future<void> handleTodaysStats() async {
    _loading.value = true;
    WalkStats stats = await DatabaseService().getWorkoutDao().getTodaysStats();
    homeStats.userStats.walks = stats.totalWalks;
    homeStats.userStats.coveredDistance = stats.totalDistance;
    _selectedDate.value = 'Today';
    _loading.value = false;
  }

  Future<void> setDate(DateTime date, String selectedItem) async {
    if (await Tools().common.isOnline(alertContext: Get.context!)) {
      await updateHomeStats(date);
      _selectedDate.value = selectedItem;
    }
  }

  Future<void> updateHomeStats(DateTime date) async {
    String dateString = DateFormat('yyyy-MM-dd').format(date);
    _loading.value = true;
    try {
      final MBResponse response =
          await MbApiHomeStats().getHomeStats(dateString);
      if (response.success) {
        _homeStats.value = HomeStats.fromJson(response.body);
        _lastQueriedDate.value = date;
        _showGoals.value = homeStats.dailyUserGoal.id != null;
        Data().get().textBanners = homeStats.textBanners;
        Data().get().owner.points = homeStats.userStats.totalCharityPoints;
        Data().storeData();
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
    }
    _loading.value = false;
  }

  void refreshHomeStats() {
    if (selectedDate == 'Custom') {
      updateHomeStats(lastQueriedDate);
    } else {
      setDateByItem(selectedDate);
    }
  }

  bool isSameDate(DateTime? date1, DateTime? date2) {
    if (date1 == date2) return true;
    if (date1 == null || date2 == null) return false;
    return date1.month == date2.month &&
        date1.year == date2.year &&
        date1.day == date2.day;
  }

  bool isSameDateRange(PickerDateRange range1, PickerDateRange range2) =>
      isSameDate(range1.startDate, range2.startDate) &&
      isSameDate(range1.endDate, range2.endDate);
}
