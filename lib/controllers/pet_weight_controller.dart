import 'package:get/state_manager.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/controllers/base_pagination_class.dart';
import '../Api/mb_api_pet_activity.dart';
import '../Api/mb_response.dart';
import '../Tools/tools.dart';
import '../models/pet.dart';
import '../models/pet_activity.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class PetWeightController extends BasePaginationClass<PetActivity> {
  PetWeightController({required this.petId});

  int petId = 0;
  RxInt totalWalks = 0.obs;
  RxDouble totalMiles = (0.0).obs;

  @override
  void resetData() {
    super.resetData();
    totalWalks(0);
    totalMiles(0);
  }

  @override
  Future<List<PetActivity>> fetch() async {
    try {
      MBResponse response = await MbApiPetActivity()
          .getPetActivity(page: pageNumber.value, petId: petId, type: 6);

      if (response.success) {
        PetActivityResponse workoutResponse =
            PetActivityResponse.fromJson(response.body);

        pageNumber += 1;
        itemsPerPage.value = workoutResponse.numItemsPerPage;
        totalCount.value = workoutResponse.totalRecords;
        totalMiles.value = workoutResponse.totalMiles;
        totalWalks.value = workoutResponse.totalWalks;

        loading = false;

        return workoutResponse.data;
      } else {
        loading = false;
        error = true;

        return [];
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      loading = false;
      error = true;

      return [];
    }
  }

  List<PetActivity> getFilteredPetActivities(
      {ChronoFilter chronoFilter = ChronoFilter.none,
      int? typeFilter = 0,
      LivingState livingState = LivingState.both}) {
    /// get allowed pet id list
    List<int?> petIds = [];
    petIds.add(petId);

    List<PetActivity> list = [];
    var now = DateTime.now();
    for (PetActivity pa in dataList) {
      /// filter if pet indicated or pet filtered
      if (petId != null || livingState != LivingState.both) {
        bool found = false;
        for (int? _petId in petIds) {
          found |= pa.forThisPet(petId: _petId);
        }

        /// remove if not for these pets
        if (!found) continue;
      }

      /// specific past/coming
      if (chronoFilter == ChronoFilter.past) {
        if (pa.activityId == 2 && pa.nextOccurrence != null) continue;
        if (pa.activityId != 2 && pa.dateStart!.isAfter(now)) continue;
      } else if (chronoFilter == ChronoFilter.future) {
        if (pa.activityId == 2 && pa.nextOccurrence == null) continue;
        if (pa.activityId != 2 && pa.dateStart!.isBefore(now)) continue;
      }

      /// remove ignored activity (images, etc...)
      if (![3, 4, 5].contains(typeFilter) &&
          [3, 4, 5].contains(pa.activityId)) {
        continue;
      }

      /// filter
      if (typeFilter == 999) {
        if (pa.serviceId == null) continue;
      } else if (typeFilter != 0) {
        if (typeFilter != pa.activityId) continue;
      }
      list.add(pa);
    }

    /// sort list
    if (chronoFilter == ChronoFilter.past ||
        chronoFilter == ChronoFilter.none) {
      list.sort((a, b) {
        if (a.dateStart == null) {
          return 1;
        } else if (b.dateStart == null) {
          return -1;
        } else {
          return b.dateStart!.compareTo(a.dateStart!);
        }
      });
    } else if (chronoFilter == ChronoFilter.future) {
      list.sort((a, b) {
        DateTime? aDate = a.activityId == 2 ? a.nextOccurrence : a.dateStart;
        DateTime? bDate = b.activityId == 2 ? b.nextOccurrence : b.dateStart;
        if (aDate == null) {
          return 1;
        } else if (bDate == null) {
          return -1;
        } else {
          return aDate.compareTo(bDate);
        }
      });
    }

    return list;
  }

  String getWeightForPet() {
    List<PetActivity> petActivities = getFilteredPetActivities(
      chronoFilter: ChronoFilter.past,
    );
    for (PetActivity pa in petActivities) {
      if (((pa.activityId == 6 && pa.typeId == 2) || pa.activityId == 8) &&
          pa.weight != null) {
        return Data().convertWeightToUserUnitStr(pa.weight!);
      }
    }
    return 'APPLICATION_MOBILE_LABEL_WEIGHT_ADD_WEIGHT'.tr();
  }

  Future<bool> submitWeight(context, petActivity) async {
    MBResponse response =
        await MbApiPetActivity().addPetActivityRequest(context, petActivity);

    if (response.success) {
      PetActivity pa = PetActivity.fromJson(response.body['petActivity']);
      dataList.add(pa);
      dataList.refresh();
      return true;
    }
    return false;
  }

  void updatePetId(int id) {
    petId = id;
    initialFetch();
  }

  void updateActivity(PetActivity pa) {
    int index = dataList.indexWhere((element) => element.id == pa.id);
    if (index != -1) {
      dataList.removeWhere((element) => element.id == pa.id);
      dataList.add(pa);
      dataList.refresh();
    }
  }

  void deleteActivity(int id) {
    dataList.removeWhere((element) => element.id == id);
    dataList.refresh();
  }
}
