import 'package:get/get.dart';
import 'package:mybuddy/class/data.dart';

import '../Api/mb_api_owner.dart';
import '../Api/mb_response.dart';
import '../Tools/tools.dart';
import '../blocs/walk_provider.dart';
import '../models/owner.dart';
import '../models/shelter.dart';
import 'charity_list_controller.dart';

class CharitySelectionController extends CharityListController {
  CharitySelectionController([Shelter? shelter]) : super(shelter);

  static CharitySelectionController of() {
    try {
      return Get.find();
    } catch (e) {
      return Get.put(CharitySelectionController());
    }
  }

  Shelter? ownerShelter;

  @override
  bool get isShelterChanged =>
      selectedShelter.id != Data().data!.owner.shelter?.id &&
      selectedShelter.id != Shelter.none().id;

  @override
  set selectedShelter(Shelter value) {
    super.selectedShelter = value;
    WalkProvider.of().shelterChanged();
  }

  @override
  void onInit() {
    super.onInit();
    setOwnerShelter();
  }

  @override
  Future<void> initialFetch() async {
    await super.initialFetch();
    makeOwnerShelterAsSelected();
  }

  @override
  Future<void> pageFetch() async {
    await super.pageFetch();
    makeOwnerShelterAsSelected();
  }

  // assign owner shelter to selected shelter to reflect the change in UI
  void makeOwnerShelterAsSelected() {
    // if owner shelter is already selected then return
    if (selectedShelter.id == Data().data?.owner.shelter?.id) {
      return;
    }

    Shelter? ownerShelter = Data().data?.owner.shelter;

    // if owner shelter is not null and it is in the fetched data list then assign it as selected shelter
    if (ownerShelter != null && dataList.contains(ownerShelter)) {
      selectedShelter =
          dataList.firstWhere((element) => element.id == ownerShelter.id);
    }
  }

  Future<void> updateShelter() async {
    Owner _owner = Data().data!.owner;
    _owner.shelter = selectedShelter;
    MBResponse response = await MbApiOwner().updateUserRequest(
      Get.context,
      owner: _owner,
    );

    if (response.success) {
      WalkProvider.of().shelterChanged();
      setOwnerShelter();
    } else if (response.errorMessage != "NOT ONLINE") {
      return;
    }
  }

  // get owner shelter from data class
  void setOwnerShelter() {
    ownerShelter = Data().data?.owner.shelter;
  }
}
