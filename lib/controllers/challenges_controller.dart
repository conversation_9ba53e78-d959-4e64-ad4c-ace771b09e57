import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:mybuddy/class/constants.dart';
import 'package:mybuddy/controllers/base_pagination_class.dart';
import 'package:mybuddy/controllers/root_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

import '../Api/mb_api_challenge.dart';
import '../Api/mb_response.dart';
import '../Tools/tools.dart';
import '../class/data.dart';
import '../class/location_manager.dart';
import '../models/challenge.dart';
import '../ui/templates/challenge/challenge_details_page.dart';

enum ChallengeTab { available, joined }

class ChallengesController extends BasePaginationClass<Challenge> {
  static ChallengesController of() {
    try {
      return Get.find<ChallengesController>();
    } catch (e) {
      return Get.put(ChallengesController());
    }
  }

  Rx<ChallengesResponse> _challengesResponse = ChallengesResponse().obs;

  UserLocation? currentLocation;

  Rx<ChallengeTab> _activeTab = (ChallengeTab.available).obs;

  ChallengeTab get activeTab => _activeTab.value;

  String get emptyTitle => _activeTab.value == ChallengeTab.joined
      ? 'APPLICATION_MOBILE_TEXT_NO_CHALLENGE_JOINED'.tr()
      : 'APPLICATION_MOBILE_TEXT_NO_CHALLENGE_AVAILABLE'.tr();

  String get emptySubTitle => _activeTab.value == ChallengeTab.joined
      ? 'APPLICATION_MOBILE_TEXT_NO_CHALLENGE_JOINED_SUBTITLE'.tr()
      : 'APPLICATION_MOBILE_TEXT_NO_CHALLENGE_AVAILABLE_SUBTITLE'.tr();

  List<String> get tabs => <String>[
        'APPLICATION_MOBILE_TITLE_CHALLENGE_TAB_AVAILABLE'.tr(),
        'APPLICATION_MOBILE_TITLE_CHALLENGE_TAB_JOINED'.tr(),
      ];

  ChallengesResponse get challengesResponse => _challengesResponse.value;

  Future<void> switchChallengeList(ChallengeTab tab) async {
    _activeTab.value = tab;
    if (error) {
      await initialFetch();
    }
    tab == ChallengeTab.available
        ? dataList.value = challengesResponse.available
        : dataList.value = challengesResponse.joined;
    dataList.refresh();

    if (_activeTab.value == tab) return;
    setScreenToCIO();
  }

  void setScreenToCIO() {
    if (_activeTab.value == ChallengeTab.available) {
      RootController.of.updateScreenOnCIO(cioScreenNameChallengesAvailable);
    } else {
      RootController.of.updateScreenOnCIO(cioScreenNameChallengesJoined);
    }
  }

  bool get shouldShowAvailableChallengeIndicator {
    return challengesResponse.available.isNotEmpty;
  }

  @override
  Future<List<Challenge>> fetch() async {
    try {
      try {
        currentLocation = await LocationManager().getCurrentLocation();
      } catch (e) {
        currentLocation = null;
      }
      final MBResponse response = await MbApiChallenge()
          .getChallenges(currentLocation?.latitude, currentLocation?.longitude);
      if (response.success) {
        Map<String, dynamic> json = response.body;
        _challengesResponse.value = ChallengesResponse.fromJson(json);
        error = false;
        hasMore = false;
        loading = false;

        await Future.delayed(Duration.zero, () {
          switchChallengeList(_activeTab.value);
        });

        return dataList.value;
      } else {
        loading = false;
        error = true;

        return [];
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      loading = false;
      error = true;

      return [];
    }
  }

  void _modifyChallengeList(Challenge challenge, bool join) {
    if (join) {
      challengesResponse.joined.add(challenge);
      challengesResponse.available.remove(challenge);
    } else {
      challengesResponse.joined.remove(challenge);
      challengesResponse.available.add(challenge);
    }
    switchChallengeList(_activeTab.value);
    _challengesResponse.refresh();
  }

  Future<void> joinChallenge(Challenge challenge) async {
    MBResponse response = await MbApiChallenge().join(challenge, Get.context!);
    if (response.success) {
      List results = response.body['challengers'];
      for (var element in results) {
        Data().get().challengers.add(Challenger.fromJson(element));
      }
      if (challenge.challengerCount != null) {
        challenge.challengerCount = challenge.challengerCount! + 1;
        _modifyChallengeList(challenge, true);
      }
      Data().storeData();
      Tools().common.showMessage(
          Get.context!,
          'APPLICATION_MOBILE_MESSAGE_CHALLENGE_JOIN'
              .tr('Challenge Joined Successfully!'));
      await Tools().navigatorPush(ChallengeDetailsPage(
        challenge: challenge,
      ));
    }
  }

  Future<void> leaveChallenge(Challenge challenge) async {
    MBResponse response = await MbApiChallenge().leave(challenge);
    if (response.success) {
      Data().get().challengers.removeWhere(
          (element) => element.ownerChallengeId.toString() == challenge.id);

      if (challenge.challengerCount != null) {
        challenge.challengerCount = challenge.challengerCount! - 1;
        _modifyChallengeList(challenge, false);
      }
      Data().storeData();
      Tools().common.showMessage(Get.context!,
          'APPLICATION_MOBILE_MESSAGE_CHALLENGE_LEAVE'.tr('Challenge Left'));
    }
  }
}
