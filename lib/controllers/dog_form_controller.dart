// ignore_for_file: curly_braces_in_flow_control_structures

import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:jiffy/jiffy.dart';
import 'package:mybuddy/controllers/dog_charity_controller.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/shelter.dart';
import 'package:mybuddy/ui/templates/pet/pet_add_charity.dart';
import 'package:mybuddy/ui/templates/pet/pet_adoption_date.dart';
import 'package:mybuddy/ui/templates/pet/pet_birthdate_age.dart';
import 'package:mybuddy/ui/templates/pet/pet_charity_selection.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../Api/mb_api_pet.dart';
import '../Api/mb_response.dart';
import '../Tools/tools.dart';
import '../blocs/walk_provider.dart';
import '../class/data.dart';
import '../class/ref.dart';
import '../models/pet.dart';
import '../models/refs_data.dart';
import '../ui/templates/pet/pet_pageview.dart';
import '../ui/templates/pet/widgets/breed_search_delegate.dart';

enum BirthDateType { age, date }

class DogFormController extends GetxController {
  Pet? pet;

  DogFormController({this.pet});

  static DogFormController of() {
    try {
      return Get.find<DogFormController>();
    } catch (e) {
      return Get.put(DogFormController());
    }
  }

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  final TextEditingController birthDateController = TextEditingController();
  final TextEditingController adoptionDateController = TextEditingController();
  final TextEditingController adoptedCharityController =
      TextEditingController();

  final TextEditingController breedController = TextEditingController();
  final TextEditingController secondaryBreedController =
      TextEditingController();
  final TextEditingController weightController = TextEditingController();

  final DateRangePickerController birthDatePickerController =
      DateRangePickerController();
  final DateRangePickerController adoptionDatePickerController =
      DateRangePickerController();

  final RegExp textRegex = RegExp('[a-zA-Z\\ \\-\\.]');
  final RegExp weightRegex = RegExp(r'^(\d+)?\.?\d{0,2}');

  late bool _isNew;
  RxBool _unknownBirthday = false.obs;
  RxBool _adoptedFromCharity = true.obs;
  RxBool _adoptedFromSelectedCharity = false.obs;
  RxBool _autoSelect = true.obs;
  RxBool _newWalk = false.obs;
  Species? _species;
  Breed? _breed, _secondaryBreed;
  Rx<Gender?> _gender = Rx<Gender?>(null);
  late Pet _addEditPet;
  Rx<File?> _avatar = Rx<File?>(null);
  RxBool _isSubmitting = false.obs;
  RxBool _rainbowBridge = false.obs;
  Rx<bool?> _pureBreed = Rx<bool?>(null);
  late Shelter userWoofTraxShelter;
  Rx<int?> _ageMonths = Rx<int?>(null);
  Rx<int?> _ageYears = Rx<int?>(null);
  Rx<BirthDateType> _birthDateType = BirthDateType.age.obs;
  bool isSaveDatePressed = false;
  Rx<DateTime> _birthDate = DateTime.now().obs;
  Rx<DateTime> _adoptionDate = DateTime.now().obs;

  Pet get addEditPet => _addEditPet;

  set addEditPet(Pet value) {
    _addEditPet = value;
  }

  bool get isSubmitting => _isSubmitting.value;

  bool get unknownBirthday => _unknownBirthday.value;
  set unknownBirthday(bool value) {
    _unknownBirthday.value = value;
    if (value) {
      birthDateController.clear();
      addEditPet.birthDate = null;
    }
  }

  bool get adoptedFromCharity => _adoptedFromCharity.value;
  set adoptedFromCharity(bool value) {
    _adoptedFromCharity.value = value;
    if (!value) {
      addEditPet.shelter = null;
      addEditPet.petShelter = null;
      adoptedCharityController.clear();
      DogCharityController.of().makeShelterEmpty();
      adoptedFromSelectedCharity = false;
    } else {
      addEditPet.otherThanShelter = null;
    }
  }

  bool get adoptedFromSelectedCharity => _adoptedFromSelectedCharity.value;
  set adoptedFromSelectedCharity(bool value) {
    if (value) {
      adoptedCharityController.text = getShelterText(userWoofTraxShelter);
      DogCharityController.of().selectedShelter = userWoofTraxShelter;
      addEditPet.shelter = userWoofTraxShelter;
      addEditPet.petShelter = Shelter.none();
    } else {
      addEditPet.shelter = null;
      adoptedCharityController.clear();
      DogCharityController.of().makeShelterEmpty();
    }
    _adoptedFromSelectedCharity.value = value;
  }

  bool get autoSelect => _autoSelect.value;

  set autoSelect(bool value) {
    _autoSelect.value = value;
    if (value) {
      addEditPet.activityStatus = ActivityWalkingStatus.selected;
    } else {
      addEditPet.activityStatus = ActivityWalkingStatus.notSelectable;
      newWalk = false;
    }
  }

  bool get newWalk => _newWalk.value;

  set newWalk(bool value) {
    _newWalk.value = value;
    if (value) {
      addEditPet.activityStatus = ActivityWalkingStatus.selectable;
    } else {
      addEditPet.activityStatus = ActivityWalkingStatus.notSelectable;
    }
  }

  File? get avatar => _avatar.value;

  set avatar(File? value) => _avatar.value = value;

  Gender? get gender => _gender.value;

  set gender(Gender? value) => _gender.value = value;

  Species? get species => _species;

  Breed? get breed => _breed;

  set breed(Breed? value) {
    _breed = value;
    addEditPet.breedId = value?.id;
    breedController.text = value?.name ?? "";
  }

  Breed? get secondaryBreed => _secondaryBreed;

  set secondaryBreed(Breed? value) {
    _secondaryBreed = value;
    addEditPet.secondaryBreedId = value?.id;
    secondaryBreedController.text = value?.name ?? "";
  }

  bool get rainbowBridge => _rainbowBridge.value;

  set rainbowBridge(bool value) {
    _rainbowBridge.value = value;
    addEditPet.deceased = value;
    formKey.currentState?.validate();
  }

  bool? get pureBreed => _pureBreed.value;

  set pureBreed(bool? value) {
    if (value != null) {
      _pureBreed.value = value;
      addEditPet.crossed = !value;

      if (value) {
        secondaryBreed = null;
      }
    }
  }

  int? get ageMonths => _ageMonths.value;
  set ageMonths(int? value) => _ageMonths.value = value;

  int? get ageYears => _ageYears.value;
  set ageYears(int? value) => _ageYears.value = value;

  DateTime get birthDate => _birthDate.value;
  set birthDate(DateTime value) => _birthDate.value = value;

  DateTime get adoptionDate => _adoptionDate.value;
  set adoptionDate(DateTime value) => _adoptionDate.value = value;

  BirthDateType get birthDateType => _birthDateType.value;
  set birthDateType(BirthDateType value) => _birthDateType.value = value;

  final RxString _petNameForBirthDateSheet = "Pet".obs;
  String get petNameForBirthDateSheet => _petNameForBirthDateSheet.value;
  set petNameForBirthDateSheet(String value) =>
      _petNameForBirthDateSheet.value = value;

  bool get isNew => _isNew;

  set isNew(bool value) {
    _isNew = value;
    if (value) {
      addEditPet = Pet();
      addEditPet.weight = 0;
      weightController.text = '0';
    } else {
      addEditPet = pet!.copy(); //need to copy especially if we cancel
      breed = addEditPet.primaryBreed;
      gender = addEditPet.gender;
      pureBreed = !addEditPet.crossed;
      rainbowBridge = addEditPet.deceased;
      birthDateType = BirthDateType.age;
      weightController.text = addEditPet.weight?.toStringAsFixed(2) ?? "0";
      if (addEditPet.name.isNotEmpty)
        petNameForBirthDateSheet = addEditPet.name;
      if (addEditPet.birthDate == null)
        _unknownBirthday.value = true;
      else {
        birthDateController.text =
            Data().dateTimeToUserDateStr(addEditPet.birthDate);
        updateAgeUsingDate(addEditPet.birthDate!);
        birthDate = addEditPet.birthDate!;
      }

      if (addEditPet.adoptionDate != null) {
        adoptionDateController.text =
            Data().dateTimeToUserDateStr(addEditPet.adoptionDate);
        adoptionDate = addEditPet.adoptionDate!;
      }

      if (addEditPet.petShelter != null) {
        adoptedCharityController.text = getShelterText(addEditPet.petShelter!);
      } else if (addEditPet.shelter?.id != null) {
        if (addEditPet.shelter?.id == userWoofTraxShelter.id) {
          _adoptedFromSelectedCharity.value = true;
          adoptedCharityController.text = getShelterText(userWoofTraxShelter);
        } else
          adoptedCharityController.text = addEditPet.shelter == null
              ? ""
              : getShelterText(addEditPet.shelter!);
      } else {
        _adoptedFromCharity.value = false;
        _adoptedFromSelectedCharity.value = false;
      }
      _autoSelect.value =
          addEditPet.activityStatus == ActivityWalkingStatus.selected;
      _newWalk.value =
          addEditPet.activityStatus == ActivityWalkingStatus.selectable;
      if (pureBreed == false) {
        secondaryBreed = addEditPet.secondaryBreed;
      }
    }
  }

  @override
  void onInit() {
    super.onInit();
    //assign user wooftrax shelter
    userWoofTraxShelter = Data().data!.owner.shelter ?? Shelter.none();

    isNew = pet == null;
    _species = Ref().get().getDogSpecies();
    addEditPet.speciesId = _species?.id;

    // empty checkbox if birth date is selected
    birthDateController.addListener(() {
      if (birthDateController.text.isNotEmpty) unknownBirthday = false;
    });
  }

  bool isValidOldDate(String birthDate) {
    if (birthDate.isEmpty) return false;
    var d = Data().dateTimeFromUserDateStr(birthDate);

    return d != null && d.isBefore(DateTime.now());
  }

  bool isValidNewDate(String date) {
    if (unknownBirthday || addEditPet.birthDate == null) return true;

    if (date.isEmpty) return false;

    var dateString = Data().dateTimeFromUserDateStr(date);

    return dateString != null &&
        (dateString.isAtSameMomentAs(addEditPet.birthDate!) ||
            dateString.isAfter(addEditPet.birthDate!));
  }

  void updateBirthDateusingAge() {
    DateTime today = DateTime.now();
    DateTime date = DateTime(
      today.year - (ageYears ?? 0),
      today.month - (ageMonths ?? 0) - (birthDate.day > today.day ? 1 : 0),
      birthDate.day,
    );

    birthDate = date;
  }

  void updateAgeUsingDate(DateTime date) {
    DateTime? lastDate;

    if (addEditPet.diedAt != null) {
      lastDate = addEditPet.diedAt;
    }

    var jiffy = Jiffy(lastDate);

    ageMonths = jiffy.diff(Jiffy(date), Units.MONTH).toInt() % 12;
    ageYears = jiffy.diff(Jiffy(date), Units.YEAR).toInt();
  }

  void saveBirthDate(BirthDateType birthDateType) {
    late DateTime dateToSave;
    switch (birthDateType) {
      case BirthDateType.age:
        DateTime today = DateTime.now();
        DateTime date = DateTime(
          today.year - (ageYears ?? 0),
          today.month - (ageMonths ?? 0) - (birthDate.day > today.day ? 1 : 0),
          birthDate.day,
        );
        dateToSave = date;
        addEditPet.birthDateType = "age";
        break;
      case BirthDateType.date:
        dateToSave = birthDate;
        addEditPet.birthDateType = "date";
        break;
    }
    isSaveDatePressed = true;
    addEditPet.birthDate = dateToSave;
    birthDateController.text = Data().dateTimeToUserDateStr(dateToSave);
    Tools().navigatorPop();
  }

  void savedAdoptionDate() {
    addEditPet.adoptionDate = adoptionDate;
    adoptionDateController.text = Data().dateTimeToUserDateStr(adoptionDate);
    Tools().navigatorPop();
  }

  void chooseAdoptionDate(BuildContext context) async {
    DateTime now = DateTime.now();
    DateTime initialDate = addEditPet.adoptionDate ?? now;
    DateTime firstDate =
        addEditPet.birthDate != null ? addEditPet.birthDate! : DateTime(1970);
    initialDate =
        (initialDate.year >= firstDate.year && initialDate.isBefore(now)
            ? initialDate
            : now);

    Size size = MediaQuery.of(context).size;

    showDialog(
        context: context,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async {
              Tools().navigatorPop();

              adoptionDatePickerController.view = DateRangePickerView.month;
              adoptionDatePickerController.displayDate =
                  addEditPet.adoptionDate;

              return true;
            },
            child: AlertDialog(
              elevation: 0,
              backgroundColor: Colors.white,
              contentPadding: const EdgeInsets.all(10),
              content: SizedBox(
                height: size.height / 2,
                width: size.width,
                child: SfDateRangePicker(
                  selectionColor: const Color(0xff419563),
                  startRangeSelectionColor: const Color(0xff419563),
                  endRangeSelectionColor: const Color(0xff419563),
                  todayHighlightColor: const Color(0xff419563),
                  rangeSelectionColor: const Color(0x20419563),
                  minDate: firstDate,
                  maxDate: now,
                  yearCellStyle: const DateRangePickerYearCellStyle(
                    disabledDatesTextStyle: TextStyle(color: Colors.white),
                    todayTextStyle: TextStyle(color: Color(0xff419563)),
                  ),
                  monthCellStyle: const DateRangePickerMonthCellStyle(
                    todayTextStyle: TextStyle(color: Color(0xff419563)),
                  ),
                  showNavigationArrow: true,
                  initialSelectedDate: initialDate,
                  initialDisplayDate: initialDate,
                  showActionButtons: true,
                  onCancel: () {
                    Tools().navigatorPop();

                    adoptionDatePickerController.view =
                        DateRangePickerView.month;
                    adoptionDatePickerController.displayDate =
                        addEditPet.adoptionDate ?? DateTime.now();
                  },
                  onSubmit: (Object? obj) {
                    final DateTime? selectedDate =
                        adoptionDatePickerController.selectedDate;

                    if (selectedDate == null) {
                      Tools().common.showMessage(
                            context,
                            "Please select a date of the month",
                          );
                    } else {
                      addEditPet.adoptionDate = selectedDate;
                      adoptionDateController.text =
                          Data().dateTimeToUserDateStr(selectedDate);
                      Tools().navigatorPop();
                    }
                  },
                  controller: adoptionDatePickerController,
                  view: DateRangePickerView.month,
                  onViewChanged: (dateRangePickerViewChangedArgs) {
                    if (dateRangePickerViewChangedArgs.view !=
                        DateRangePickerView.month) {
                      adoptionDatePickerController.selectedDate = null;
                    }
                  },
                  selectionMode: DateRangePickerSelectionMode.single,
                  monthViewSettings: const DateRangePickerMonthViewSettings(
                    enableSwipeSelection: false,
                  ),
                ),
              ),
            ),
          );
        });
  }

  void chooseAdoptionDateSheet(BuildContext context) async {
    await showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.0),
          topRight: Radius.circular(15.0),
        ),
      ),
      // isScrollControlled: true,
      builder: (BuildContext context) {
        return const PetAdoptionDate();
      },
    );
    return;
  }

  void chooseBirthDate(BuildContext context) async {
    await showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.0),
          topRight: Radius.circular(15.0),
        ),
      ),
      // isScrollControlled: true,
      builder: (BuildContext context) {
        return const PetBirthDateAge();
      },
    ).whenComplete(() {
      if (!isSaveDatePressed) {
        // exiting without saving date, reset values
        if (addEditPet.birthDate == null) {
          ageMonths = 0;
          ageYears = 0;
        } else
          updateAgeUsingDate(addEditPet.birthDate!);
      } else
        isSaveDatePressed = false;
    });
    return;
  }

  Future<void> submitForm(bool onLogin) async {
    final FormState? form = formKey.currentState;

    if (form == null || !form.validate()) {
      return;
    }

    _isSubmitting.value = true;

    form.save();

    MBResponse response;
    if (isNew) {
      response = await MbApiPet().addPetRequest(
          scaffoldKey.currentContext, addEditPet,
          avatar: avatar);
    } else {
      response = await MbApiPet().editPetRequest(
        scaffoldKey.currentContext,
        addEditPet,
      );
    }

    if (response.success) {
      /// Sending the updated pet to the WalkProvider object so that we get
      /// this pet added or updated in the initialized walk
      WalkProvider.of(Get.context!).refreshDogs();

      Tools().navigatorPop();
      if (isNew && response.entity != null && !onLogin) {
        unawaited(
            Tools().navigatorPush(PetPageView(pet: response.entity as Pet)));
      }
      if (onLogin) Tools().navigatorPop(value: true);
    }

    _isSubmitting.value = false;
  }

  void weightIncrement() {
    if (weightController.text.isEmpty) {
      weightController.text = "0";
    }
    weightController.text =
        (double.parse(weightController.text) + 1).toString();
    weightController.selection = TextSelection.fromPosition(
        TextPosition(offset: weightController.text.length));
  }

  void weightDecrement() {
    if (weightController.text.isEmpty) {
      weightController.text = "0";
    } else if (double.parse(weightController.text) > 0) {
      weightController.text =
          (double.parse(weightController.text) - 1).toString();
    }
    weightController.selection = TextSelection.fromPosition(
        TextPosition(offset: weightController.text.length));
  }

  String getInvalidCharacterString(String val) {
    // TODO LOW trad
    String _mbLastNameError =
        'APPLICATION_MOBILE_MESSAGE_USER_FORM_LASTNAME_ERROR'.tr();
    String subString = _mbLastNameError.substring(
      _mbLastNameError.indexOf('cont'),
      _mbLastNameError.length,
    );

    return "$val $subString";
  }

  String? nameValidator(String? val) {
    if (val == null || val.isEmpty || val.trim() == '') {
      return 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_NAME'.tr();
    }
    if (!textRegex.hasMatch(val)) {
      return getInvalidCharacterString(val);
    }
    return null;
  }

  String? genderValidator(Gender? val) {
    if (val != null) {
      return null;
    } else {
      return 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_GENDER'.tr();
    }
  }

  String? birthDateValidator(val) {
    if (val != null && isValidOldDate(val)) {
      return null;
    } else if (val.trim().toString().isEmpty) {
      return unknownBirthday
          ? null
          : 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_BIRTH_DATE'.tr();
    } else {
      return 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_BIRTHDATE_BEFORE_NOW'.tr();
    }
  }

  String? adoptionDateValidator(val) {
    if (val == null || val.trim().toString().isEmpty) {
      return rainbowBridge
          ? null
          : 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_ADOPTION_DATE'.tr();
    } else if (!isValidOldDate(val)) {
      return 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_ADOPTION_DATE_BEFORE_NOW'
          .tr();
    } else if (!isValidNewDate(val)) {
      return 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_ADOPTION_DATE_BEFORE_BIRTH'
          .tr();
    } else {
      return null;
    }
  }

  String? adoptedCharityValidator(String? val) {
    if (adoptedFromSelectedCharity ||
        !adoptedFromCharity ||
        rainbowBridge ||
        val != null && val.trim().isNotEmpty) {
      return null;
    } else {
      return 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_ADOPTED_CHARITY'
          .tr();
    }
  }

  String? breedValidator(String? val) {
    if (val != null && val.trim().isNotEmpty) {
      return null;
    } else {
      return 'APPLICATION_MOBILE_MESSAGE_PET_ERROR_PRESENCE_BREED'.tr();
    }
  }

  void selectBreed({bool primary = true}) async {
    Breed? result = await showSearch<Breed?>(
      context: Get.context!,
      delegate:
          BreedSearchDelegate(species?.id, breedId: primary ? null : breed?.id),
    );

    if (result != null) {
      if (primary) {
        breed = result;
      } else {
        secondaryBreed = result;
      }
    }
  }

  void selectCharity(BuildContext context) async {
    final selectedShelter = DogCharityController.of().selectedShelter;
    await showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.0),
          topRight: Radius.circular(15.0),
        ),
      ),
      isScrollControlled: true,
      builder: (BuildContext context) {
        if (selectedShelter.id == -666 && selectedShelter.name.isNotEmpty) {
          return PetAddCharity();
        } else
          return PetCharitySelection();
      },
    );
  }

  void updateShelter(Shelter selectedShelter) {
    adoptedCharityController.text = getShelterText(selectedShelter);
    if (selectedShelter.name.isNotEmpty) {
      _adoptedFromSelectedCharity.value = false;
      if (selectedShelter.id == -666) {
        // manually added
        addEditPet.shelter = null;
        addEditPet.petShelter = selectedShelter;
      } else {
        // selected from list
        addEditPet.petShelter = null;
        addEditPet.shelter = selectedShelter;
        if (selectedShelter.id == userWoofTraxShelter.id)
          _adoptedFromSelectedCharity.value = true;
      }
    }
  }

  String getShelterText(Shelter shelter) {
    if (shelter.name.isEmpty) return "";

    return "${shelter.name}, ${shelter.city}, ${shelter.state}";
  }
}
