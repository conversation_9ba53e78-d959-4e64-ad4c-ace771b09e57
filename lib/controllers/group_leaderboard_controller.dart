import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:intl/intl.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/controllers/base_pagination_class.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

import '../Api/mb_response.dart';
import '../Tools/tools.dart';
import '../class/data.dart';
import '../models/group_leaderboard.dart';

enum LeaderBoardFilter { month, allTime, custom }

/// This class is responsible for fetching the group's leaderboard data in a paginated list with the ability to filter by month and year
class GroupLeaderboardController
    extends BasePaginationClass<GroupLeaderboardMember> {
  final int _groupId;
  final DateTime _groupCreatedDate;
  RxInt _userPosition = 0.obs;
  DateTime? _startDate, _endDate;
  LeaderBoardFilter? _selectedFilter;
  Rx<GroupLeaderboardStats> _groupLeaderboardStats =
      GroupLeaderboardStats().obs;

  GroupLeaderboardController(this._groupId, this._groupCreatedDate);

  static GroupLeaderboardController of(int groupId, DateTime groupCreatedDate) {
    try {
      return Get.find();
    } catch (e) {
      return Get.put(GroupLeaderboardController(groupId, groupCreatedDate));
    }
  }

  int get userPosition => _userPosition.value;

  DateTime get startDate => _startDate ?? DateTime.now();

  DateTime get endDate =>
      _endDate ?? DateTime.now().add(const Duration(minutes: 1));

  DateTimeRange? get initialDateRange {
    if (_startDate == null || _endDate == null) return null;

    if (_startDate!.isBefore(_groupCreatedDate)) _startDate = _groupCreatedDate;

    if (_endDate!.isAfter(DateTime.now())) _endDate = DateTime.now();

    if (_startDate!.isAfter(_endDate!)) return null;

    if (_startDate!.isAtSameMomentAs(_endDate!)) return null;

    return DateTimeRange(start: startDate, end: endDate);
  }

  LeaderBoardFilter get selectedFilter =>
      _selectedFilter ?? LeaderBoardFilter.month;

  GroupLeaderboardStats get stats => _groupLeaderboardStats.value;

  /// This Map is used to populate the bottom sheet filtration options
  final Map filterSheetItems = {
    LeaderBoardFilter.month:
        'APPLICATION_MOBILE_LABEL_DATE_SELECTION_SELECT_MONTH'.tr(),
    LeaderBoardFilter.allTime:
        'APPLICATION_MOBILE_LABEL_WORKOUT_DATE_SELECTION_ALL_TIME'.tr(),
    LeaderBoardFilter.custom:
        'APPLICATION_MOBILE_LABEL_WORKOUT_DATE_SELECTION_CUSTOM'.tr()
  };

  @override
  void onInit() {
    setCurrentMonthAndYear();
  }

  @override
  Future<void> initialFetch() {
    assignEmptyStats();
    return super.initialFetch();
  }

  @override
  Future<List<GroupLeaderboardMember>> fetch() async {
    try {
      String? start;
      String? end;

      if (selectedFilter != LeaderBoardFilter.allTime) {
        start = _startDate.toString().split(" ").first;
        end = _endDate.toString().split(" ").first;
      }

      MBResponse response = await MbApiCommunity()
          .getTeamLeaderboard(_groupId, start, end, pageNumber.value);

      if (response.success) {
        List<GroupLeaderboardMember> groupLeaderboardList =
            <GroupLeaderboardMember>[];

        Map<String, dynamic> json = response.body;

        _groupLeaderboardStats.value =
            GroupLeaderboardStats.fromJson(json['stats']);

        for (var element in json['data']) {
          groupLeaderboardList.add(GroupLeaderboardMember.fromJson(element));
        }

        pageNumber.value++;
        totalCount.value = json["totalRecords"];
        itemsPerPage.value = json["numItemsPerPage"];
        _userPosition.value = json["position"] ?? 0;

        loading = false;

        return groupLeaderboardList;
      } else {
        assignEmptyStats();
        return errorResponse();
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      assignEmptyStats();
      return errorResponse();
    }
  }

  DateTime getFirstDayOfTheMonth(int month, int year) {
    DateTime firstDay = DateTime(year, month, 1);

    // Compare the group's created date with the current month and year
    // If the group's created date is greater than the current month and year then set the start date to the group's created date

    if (_groupCreatedDate.isAfter(firstDay)) {
      return _groupCreatedDate;
    }

    return firstDay;
  }

  DateTime getLastDayOfTheMonth(int month, int year, DateTime startDate) {
    // Calculate the last day of the current month
    DateTime lastDay = DateTime(year, month + 1, 0);

    if (_groupCreatedDate.isAfter(lastDay)) {
      return _groupCreatedDate;
    }

    if (startDate.isAfter(lastDay)) {
      return startDate;
    }

    if (lastDay.isAfter(DateTime.now())) {
      return DateTime.now();
    }

    return lastDay;
  }

  void setCurrentMonthAndYear() {
    DateTime now = DateTime.now();
    fetchSelectedMonth(now.month, now.year);
  }

  /// This method is used to fetch data for a selected month
  void fetchSelectedMonth(int month, int year) {
    DateTime firstDay = getFirstDayOfTheMonth(month, year);
    DateTime lastDay = getLastDayOfTheMonth(month, year, firstDay);

    setDatesAndFetchData(firstDay, lastDay, LeaderBoardFilter.month);
  }

  /// This method is used to fetch all time data
  void fetchAllTime() {
    setDatesAndFetchData(null, null, LeaderBoardFilter.allTime);
  }

  /// This method is used to fetch custom range data
  void fetchCustomRange(DateTimeRange range) {
    setDatesAndFetchData(range.start, range.end, LeaderBoardFilter.custom);
  }

  void setDatesAndFetchData(
      DateTime? start, DateTime? end, LeaderBoardFilter filter) {
    _startDate = start;
    _endDate = end;
    _selectedFilter = filter;
    initialFetch();
    update();
  }

  /// This method is used to check the user's rank to highlight the row
  bool isMyRank(int index) {
    return dataList[index].ownerId == Data().get().owner.id;
  }

  /// This method is used to get the selected month and year in a readable format
  String getSelectedMonthAndYear() {
    if (_selectedFilter == LeaderBoardFilter.allTime) return 'All Time';

    if (_selectedFilter == LeaderBoardFilter.custom) return 'Custom Range';

    //get month name from month number
    String _month = DateFormat.MMM().format(_startDate!);

    //get year from date
    String _year = DateFormat.y().format(_startDate!);

    return '$_month $_year';
  }

  void assignEmptyStats() {
    _groupLeaderboardStats.value = GroupLeaderboardStats(
      totalDistance: 0,
      totalMembers: 0,
      totalTime: 0,
      totalWalks: 0,
    );
  }
}
