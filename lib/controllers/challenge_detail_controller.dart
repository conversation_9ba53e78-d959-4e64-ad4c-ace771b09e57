import 'package:get/get.dart';
import 'package:mybuddy/controllers/challenges_controller.dart';

import '../Api/mb_api_challenge.dart';
import '../class/data.dart';
import '../models/challenge.dart';

class ChallengeDetailController extends GetxController {
  Rx<Challenge> _challenge = Challenge().obs;
  Rx<Challenge> _challengeDetail = Challenge().obs;
  RxBool _loading = false.obs;
  RxBool _isSubmitting = false.obs;
  RxBool _subscribed = false.obs;

  Challenge get challenge => _challengeDetail.value;

  bool get loading => _loading.value;

  bool get isSubmitting => _isSubmitting.value;

  bool get subscribed => _subscribed.value;

  void setChallenge(Challenge challenge) {
    _challenge.value = challenge;
  }

  @override
  void onInit() {
    super.onInit();
    _loading(true);
    Future.delayed(const Duration(milliseconds: 200), () {
      _fetchDetails();
    });
  }

  Future<void> _fetchDetails() async {
    _assignSubscription();
    Challenge? response =
        await MbApiChallenge().challengeDetail(_challenge.value.id!);

    if (response != null) {
      _challengeDetail.value = response;
    }else{
      _challengeDetail.value = _challenge.value;
    }

    _loading(false);
  }

  Future<void> joinChallenge() async {
    _isSubmitting(true);

    await Get.find<ChallengesController>().joinChallenge(challenge);

    _assignSubscription();
    _isSubmitting(false);
  }

  Future<void> leaveChallenge() async {
    await Get.find<ChallengesController>().leaveChallenge(challenge);

    _assignSubscription();
  }

  void _assignSubscription() {
    _subscribed(Data().get().challengeSubscribed(int.parse(_challenge.value.id!)));
  }
}
