import 'package:mybuddy/controllers/base_pagination_class.dart';
import 'package:mybuddy/models/workout.dart';

import '../Api/mb_api_pet_activity.dart';
import '../Api/mb_response.dart';
import '../Tools/tools.dart';

class PetWorkoutController extends BasePaginationClass<Workout> {
  PetWorkoutController({required this.petId});

  int petId = 0;

  @override
  Future<List<Workout>> fetch() async {
    try {
      MBResponse response = await MbApiPetActivity()
          .getPetActivity(page: pageNumber.value, petId: petId, type: 1);

      if (response.success) {
        WorkoutResponse workoutResponse =
            WorkoutResponse.fromJson(response.body);

        pageNumber += 1;
        itemsPerPage.value = workoutResponse.numItemsPerPage;
        totalCount.value = workoutResponse.totalWalks!;

        loading = false;

        return workoutResponse.workouts;
      } else {
        loading = false;
        error = true;

        return [];
      }
    } catch (e) {
      Tools.debugPrint(e.toString());
      loading = false;
      error = true;

      return [];
    }
  }

  void updatePetId(int id) {
    petId = id;
    initialFetch();
  }
}
