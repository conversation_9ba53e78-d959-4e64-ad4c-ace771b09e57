import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

const OutlineInputBorder outlineInputBorder = OutlineInputBorder(
  borderRadius: BorderRadius.all(Radius.circular(8.0)),
);
const IconThemeData iconThemeData = IconThemeData(size: 27.0);

/// Material Design typography scheme
TextTheme globalTextTheme({Color? primary, Color? secondary}) {
  return TextTheme(
    /// size: 96.0, weight: light, spacing: -1.5
    headline1: TextStyle(color: primary, fontWeight: FontWeight.w900),

    /// size: 60.0, weight: light, spacing: -0.5
    headline2: TextStyle(color: primary, fontWeight: FontWeight.w900),

    /// size: 48.0, weight: regular, spacing: 0.0
    headline3: TextStyle(color: primary, fontWeight: FontWeight.w700),

    /// size: 34.0, weight: regular, spacing: 0.25
    headline4: TextStyle(color: primary, fontWeight: FontWeight.w700),

    /// size: 24.0, weight: regular, spacing: 0.0
    headline5: TextStyle(color: primary, fontWeight: FontWeight.w700),

    /// size: 20.0, weight: medium, spacing: 0.15
    headline6: TextStyle(color: primary, fontWeight: FontWeight.w700),

    /// size: 16.0, weight: regular, spacing: 0.5
    bodyText1: TextStyle(color: primary, fontWeight: FontWeight.w700),

    /// size: 14.0, weight: regular, spacing: 0.25
    bodyText2: const TextStyle(color: Colors.black87),

    /// size: 16.0, weight: regular, spacing: 0.15
    subtitle1: TextStyle(color: primary, fontSize: 14.0),

    /// size: 14.0, weight: medium, spacing: 0.1
    subtitle2: TextStyle(color: secondary, fontSize: 12.0),

    /// size: 14.0, weight: medium, spacing: 1.25
    button: const TextStyle(color: Colors.white),

    /// size: 12.0, weight: regular, spacing: 0.4
    caption: TextStyle(color: secondary),

    /// size: 10.0, weight: regular, spacing: 1.5
    overline: TextStyle(color: secondary),

    labelMedium: TextStyle(
      color: primary,
      fontWeight: FontWeight.w500,
      fontSize: 18.0,
    ),
  );
}

class AppTheme {
  static ThemeData get myBuddyTheme {
    const Color _primaryColor =
        kReleaseMode ? Color(0xFF55647d) : Colors.deepOrange;
    const Color _primaryVariantColor = Color.fromRGBO(230, 229, 227, 1.0);
    const Color _secondaryColor = Color.fromRGBO(75, 142, 203, 1.0);
    const Color _secondaryVariantColor = Color.fromRGBO(234, 240, 247, 1.0);
    const Color _disabledColor = Color.fromRGBO(159, 179, 200, 1.0);
    const Color _errorColor = Colors.redAccent;
    final TextTheme _textTheme = globalTextTheme(
      primary: _primaryColor,
      secondary: _disabledColor,
    );

    return ThemeData(
      primaryColor: _primaryColor,
      primaryColorLight: _primaryVariantColor,
      errorColor: _errorColor,
      fontFamily: 'Montserrat',
      backgroundColor: _secondaryVariantColor,
      scaffoldBackgroundColor: Colors.white,
      canvasColor: Colors.white,
      disabledColor: _disabledColor,
      highlightColor: _primaryVariantColor,
      hintColor: _primaryColor,
      indicatorColor: _secondaryColor,
      appBarTheme: AppBarTheme(
        centerTitle: true,
        color: Colors.white,
        iconTheme: iconThemeData.copyWith(color: _primaryColor),
        actionsIconTheme: iconThemeData.copyWith(color: _primaryColor),
        elevation: 1,
        titleTextStyle: _textTheme.headline6,
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        elevation: 4,
        selectedIconTheme: iconThemeData.copyWith(
          color: _secondaryColor,
        ),
        unselectedIconTheme: iconThemeData.copyWith(
          color: _disabledColor,
        ),
      ),
      bottomSheetTheme: const BottomSheetThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(15.0)),
        ),
      ),
      cardTheme: const CardTheme(
        margin: EdgeInsets.all(5.0),
        elevation: 0,
        shape: RoundedRectangleBorder(
          side: BorderSide(color: _primaryVariantColor, width: 1),
          borderRadius: BorderRadius.all(Radius.circular(8.0)),
        ),
      ),
      tabBarTheme: TabBarTheme(
        labelColor: _secondaryColor,
        unselectedLabelColor: _disabledColor,
        labelStyle: _textTheme.headline6?.copyWith(fontSize: 18.0),
        unselectedLabelStyle: _textTheme.headline6?.copyWith(fontSize: 18.0),
      ),
      snackBarTheme: const SnackBarThemeData(
        backgroundColor: _secondaryVariantColor,
        contentTextStyle: TextStyle(fontSize: 18.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(15.0)),
        ),
      ),
      iconTheme: iconThemeData.copyWith(color: _primaryColor),
      inputDecorationTheme: InputDecorationTheme(
        errorMaxLines: 2,
        isDense: true,
        hintStyle: _textTheme.bodyText2,
        border: outlineInputBorder,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          primary: _secondaryColor,
          // onPrimary: Colors.white,
          textStyle: _textTheme.button!.copyWith(fontSize: 18.0),
          padding: const EdgeInsets.all(15.0),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0)),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          primary: _primaryColor,
          side: const BorderSide(color: _primaryColor),
          textStyle: _textTheme.bodyText1!.copyWith(fontSize: 18.0),
          padding: const EdgeInsets.all(15.0),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0)),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          primary: _secondaryColor,
          textStyle: _textTheme.bodyText1,
          padding: const EdgeInsets.all(15.0),
        ),
      ),
      textTheme: _textTheme,
      colorScheme: ColorScheme.fromSwatch().copyWith(
        secondary: _secondaryColor,
      ),
    );
  }

  static ThemeData get woofTraxTheme {
    const Color _primaryColor =
        kReleaseMode ? Color.fromRGBO(16, 42, 67, 1.0) : Colors.deepOrange;
    const Color _primaryVariantColor = Color.fromRGBO(217, 226, 236, 1.0);
    const Color _secondaryColor = Color(0xff419563);
    const Color _secondaryVariantColor = Color(0xffF8F7F3);
    const Color _disabledColor = Color.fromRGBO(159, 179, 200, 1.0);
    const Color _errorColor = Colors.redAccent;
    final TextTheme _textTheme = globalTextTheme(
      primary: _primaryColor,
      secondary: _disabledColor,
    );

    return ThemeData(
      primaryColor: _primaryColor,
      primaryColorLight: _primaryVariantColor,
      errorColor: _errorColor,
      fontFamily: 'Montserrat',
      backgroundColor: _secondaryVariantColor,
      scaffoldBackgroundColor: Colors.white,
      canvasColor: Colors.white,
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: _secondaryColor,
        selectionColor: _secondaryVariantColor,
        selectionHandleColor: _secondaryColor,
      ),
      disabledColor: _disabledColor,
      highlightColor: _primaryVariantColor,
      hintColor: _disabledColor,
      indicatorColor: _secondaryColor,
      appBarTheme: AppBarTheme(
        centerTitle: true,
        color: Colors.white,
        iconTheme: iconThemeData.copyWith(color: _primaryColor),
        actionsIconTheme: iconThemeData.copyWith(color: _primaryColor),
        elevation: 1,
        titleTextStyle: _textTheme.headline6,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        elevation: 4,
        showSelectedLabels: true,
        showUnselectedLabels: true,
        selectedItemColor: _secondaryColor,
        unselectedItemColor: _disabledColor,
      ),
      bottomSheetTheme: const BottomSheetThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(15.0)),
        ),
      ),
      cardTheme: const CardTheme(
        margin: EdgeInsets.symmetric(horizontal: 15.0, vertical: 6.0),
        elevation: 0,
        shape: RoundedRectangleBorder(
          side: BorderSide(color: _primaryVariantColor, width: 1),
          borderRadius: BorderRadius.all(Radius.circular(8.0)),
        ),
      ),
      dialogTheme: DialogTheme(
        titleTextStyle: const TextStyle(
          fontSize: 18.0,
          fontWeight: FontWeight.bold,
          color: _secondaryColor,
        ),
        contentTextStyle: _textTheme.bodyText2,
        backgroundColor: _secondaryVariantColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(25.0),
        ),
      ),
      tabBarTheme: TabBarTheme(
        labelColor: _secondaryColor,
        labelStyle: _textTheme.headline6?.copyWith(fontSize: 18.0),
        unselectedLabelColor: _disabledColor,
        unselectedLabelStyle: _textTheme.headline6?.copyWith(fontSize: 18.0),
      ),
      snackBarTheme: const SnackBarThemeData(
        backgroundColor: _secondaryVariantColor,
        contentTextStyle: TextStyle(fontSize: 18.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(15.0)),
        ),
      ),
      iconTheme: iconThemeData.copyWith(color: _primaryColor),
      inputDecorationTheme: InputDecorationTheme(
        errorMaxLines: 2,
        isDense: true,
        labelStyle: _textTheme.subtitle1,
        hintStyle: _textTheme.subtitle1!.copyWith(fontSize: 16.0),
        border: outlineInputBorder,
        focusedBorder: outlineInputBorder.copyWith(
          borderSide: const BorderSide(color: _secondaryColor),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          primary: _secondaryColor,
          textStyle: _textTheme.button!.copyWith(fontSize: 18.0),
          padding: const EdgeInsets.all(15.0),
          shape: const StadiumBorder(),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          primary: _primaryColor,
          side: const BorderSide(color: _primaryColor),
          textStyle: _textTheme.bodyText1,
          padding: const EdgeInsets.all(15.0),
          shape: const StadiumBorder(),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          primary: _secondaryColor,
          textStyle: _textTheme.bodyText1,
          padding: const EdgeInsets.all(15.0),
        ),
      ),
      textTheme: _textTheme,
      colorScheme: ColorScheme.fromSwatch().copyWith(
        secondary: _secondaryColor,
      ),
    );
  }
}
