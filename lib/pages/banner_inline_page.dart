import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/ad_manager.dart';

class BannerInlinePage extends StatefulWidget {
  final double? height;

  const BannerInlinePage({Key? key, this.height}) : super(key: key);

  @override
  _BannerInlinePageState createState() => _BannerInlinePageState();
}

class _BannerInlinePageState extends State<BannerInlinePage> {
  BannerAd? _ad;
  bool _isAdLoaded = false;

  double? _height;

  @override
  void initState() {
    super.initState();
    _height = widget.height ?? 50.0;

    /// Create a BannerAd instance
    _ad = BannerAd(
      adUnitId: AdManager.bannerAdUnitId,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        // Called when an ad is successfully received.
        onAdLoaded: (_) {
          if (mounted) {
            setState(() {
              _isAdLoaded = true;
            });
          }
        },
        // Called when an ad request failed.
        onAdFailedToLoad: (ad, error) {
          // Releases an ad resource when it fails to load
          ad.dispose();

          Tools.debugPrint(
              'Ad load failed (code=${error.code} message=${error.message})');
        },
        // Called when an ad opens an overlay that covers the screen.
        onAdOpened: (Ad ad) => Tools.debugPrint('Ad opened.'),
        // Called when an ad removes an overlay that covers the screen.
        onAdClosed: (Ad ad) => Tools.debugPrint('Ad closed.'),
        // Called when an ad is in the process of leaving the application.
        onAdWillDismissScreen: (Ad ad) => Tools.debugPrint('Left application.'),
      ),
    );

    if (_ad != null) {
      _ad!.load();
    }
  }

  @override
  void dispose() {
    _ad?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSize(
      duration: const Duration(milliseconds: 250),
      child: _isAdLoaded
          ? SizedBox(
              width: _ad?.size.width.toDouble() ??
                  MediaQuery.of(context).size.width,
              height: _ad?.size.height.toDouble() ?? _height,
              child: AdWidget(ad: _ad!),
            )
          : const SizedBox.shrink(),
    );
  }
}
