import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/ad_manager.dart';
// import 'package:mybuddy/class/ad_manager.dart';

class BannerAdWidget extends StatefulWidget {
  /// google mobile ad banner 2nde version
  const BannerAdWidget({Key? key}) : super(key: key);

  @override
  _BannerAdWidgetState createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends State<BannerAdWidget> {
  BannerAd? _myBanner;
  bool loading = true;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadAd();
  }

  Future<void> _loadAd() async {
    // Get an AnchoredAdaptiveBannerAdSize before loading the ad.
    final AnchoredAdaptiveBannerAdSize? size =
        await AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(
            MediaQuery.of(context).size.width.truncate());

    if (size == null) {
      Tools.debugPrint('Unable to get height of anchored banner.');
      return;
    }

    _myBanner = BannerAd(
      adUnitId: AdManager.bannerAdUnitId,
      size: size,
      request: const AdManagerAdRequest(),
      listener: AdManagerBannerAdListener(
        // Called when an ad is successfully received.
        onAdLoaded: (Ad ad) => Tools.debugPrint('AdManagerBannerAd loaded.'),
        // Called when an ad request failed.
        onAdFailedToLoad: (Ad ad, LoadAdError error) {
          // Dispose the ad here to free resources.
          ad.dispose();
          Tools.debugPrint(
              'AdManagerBannerAd failed to load: ${error.message}');
        },
        // Called when an ad opens an overlay that covers the screen.
        onAdOpened: (Ad ad) => Tools.debugPrint('AdManagerBannerAd opened.'),
        // Called when an ad removes an overlay that covers the screen.
        onAdClosed: (Ad ad) => Tools.debugPrint('AdManagerBannerAd closed.'),
        // Called when an impression occurs on the ad.
        onAdImpression: (Ad ad) =>
            Tools.debugPrint('AdManagerBannerAd impression.'),
      ),
    );
    loading = false;
    setState(() {});
    _myBanner?.load();
  }

  @override
  void dispose() {
    super.dispose();
    _myBanner?.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (loading == false && _myBanner != null) {
      return Container(
        alignment: Alignment.center,
        child: AdWidget(ad: _myBanner!),
        width: _myBanner!.size.width.toDouble(),
        height: _myBanner!.size.height.toDouble(),
        margin: const EdgeInsets.symmetric(vertical: 2.0),
      );
    } else {
      return const SizedBox();
    }
  }
}
