// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBinrpWGRtkmAxy0hBIrFN-PmMTXPorTWk',
    appId: '1:348729777085:android:c2ec3b6008324910',
    messagingSenderId: '348729777085',
    projectId: 'wooftrax-87ae2',
    databaseURL: 'https://wooftrax-87ae2.firebaseio.com',
    storageBucket: 'wooftrax-87ae2.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC_iSB9po5uXZO5Qq667HszEvkXfGnR69s',
    appId: '1:348729777085:ios:34805bf67fc46462',
    messagingSenderId: '348729777085',
    projectId: 'wooftrax-87ae2',
    databaseURL: 'https://wooftrax-87ae2.firebaseio.com',
    storageBucket: 'wooftrax-87ae2.appspot.com',
    iosClientId: '348729777085-q6lvc1jhdm15384ovk0qe33tvs8meed7.apps.googleusercontent.com',
    iosBundleId: 'com.wooftrax.wfad',
  );
}
