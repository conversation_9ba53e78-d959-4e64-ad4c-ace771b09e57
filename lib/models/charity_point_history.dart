class CharityPointHistory {
  late bool success;
  late String token;
  late String currentLang;
  late int totalWalks;
  late double totalDistance;
  late int totalPoints;
  late String fromDate;
  late String toDate;
  late List<PointsData> data;

  CharityPointHistory();

  CharityPointHistory.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    token = json['token'];
    currentLang = json['currentLang'];
    totalWalks = json['totalWalks'];
    totalDistance = json['totalDistance'].toDouble();
    totalPoints = json['totalPoints'];
    toDate = json['to_date'];
    fromDate = json['from_date'];
    if (json['data'] != null) {
      data = <PointsData>[];
      json['data'].forEach((v) {
        data.add(PointsData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = this.success;
    data['token'] = this.token;
    data['currentLang'] = this.currentLang;
    data['totalWalks'] = this.totalWalks;
    data['totalDistance'] = this.totalDistance.toDouble();
    data['totalPoints'] = this.totalPoints;
    data['from_date'] = this.fromDate;
    data['to_date'] = this.toDate;
    data['data'] = this.data.map((v) => v.toJson()).toList();
    return data;
  }
}

class PointsData {
  late List<Days> days=[];
  late String title;
  late String from;
  late String to;
  late int weekWalks;
  late int weekPoints;
  late double weekDistance;

  PointsData();

  PointsData.fromJson(Map<String, dynamic> json) {
    if (json['days'] != null) {
      days = <Days>[];
      json['days'].forEach((v) {
        days.add(Days.fromJson(v));
      });
    }
    title = json['title'];
    from = json['from'];
    to = json['to'];
    weekWalks = json['weekWalks'];
    weekPoints = json['weekPoints'];
    weekDistance = json['weekDistance'].toDouble();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['days'] = this.days.map((v) => v.toJson()).toList();
    data['title'] = this.title;
    data['from'] = this.from;
    data['to'] = this.to;
    data['weekWalks'] = this.weekWalks;
    data['weekPoints'] = this.weekPoints;
    data['weekDistance'] = this.weekDistance;
    return data;
  }
}

class Days {
  late String day;
  late String date;
  late int walks;
  late int charityPoints;
  late double distance;
  late bool goalAcheived;

  Days();

  Days.fromJson(Map<String, dynamic> json) {
    day = json['day'];
    date = json['date'];
    walks = json['walks'];
    charityPoints = int.parse(json['charity_points'].toString());
    distance = double.parse(json['distance'].toString());
    goalAcheived = json['goalAcheived'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['day'] = this.day;
    data['date'] = this.date;
    data['walks'] = this.walks;
    data['charity_points'] = this.charityPoints;
    data['distance'] = this.distance;
    data['goalAcheived'] = this.goalAcheived;
    return data;
  }
}