import 'package:mybuddy/extensions/string_extensions.dart';


enum LengthSource { meters, kilometers}

class Unit {
  late int unitLengthId;
  late int unitWeightId;

  Unit({this.unitLengthId = 2, this.unitWeightId = 1});

  Unit.fromJson(Map<String, dynamic> parsedJson) {
    unitLengthId = parsedJson['unitLengthId'] ?? 2;
    unitWeightId = parsedJson['unitWeightId'] ?? 1;
  }

  Map<String, dynamic> toJson() {
    return {
      'unitLengthId': unitLengthId,
      'unitWeightId': unitWeightId,
    };
  }

  String get toStringDistanceLabel => unitLengthId == 2 ? 'APPLICATION_MOBILE_LABEL_MILES'.tr('miles') : 'APPLICATION_MOBILE_LABEL_KILOMETERS'.tr('km');
  String get toStringDistanceShortLabel => unitLengthId == 2 ? 'APPLICATION_MOBILE_SHORT_LABEL_MILES'.tr('mi') : 'APPLICATION_MOBILE_LABEL_KILOMETERS'.tr('km');

  /// distance from kilometers to miles/kilometers
  //todo move this parameter in api
  double get fromKilometersFactor => unitLengthId == 2 ? 1.609 : 1.0;

  String get toStringSpeedLabel => unitLengthId == 2 ? 'mph' : 'km/h';
}

enum RenderingDistance {numeric, toStringRounded, toStringAsFixed}