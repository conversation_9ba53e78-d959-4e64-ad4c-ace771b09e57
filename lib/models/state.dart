import 'dart:convert';

CountryState stateFromJson(String str) =>
    CountryState.fromJson(json.decode(str));
String stateToJson(CountryState data) => json.encode(data.toJson());

class CountryState {
  CountryState({
    int? id,
    String? name,
  }) {
    _id = id;
    _name = name;
  }

  CountryState.fromJson(dynamic json) {
    _id = json['id'];
    _name = json['name'];
  }
  int? _id;
  String? _name;
  CountryState copyWith({
    int? id,
    String? name,
  }) =>
      CountryState(
        id: id ?? _id,
        name: name ?? _name,
      );
  int? get id => _id;
  String? get name => _name;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['name'] = _name;
    return map;
  }
}
