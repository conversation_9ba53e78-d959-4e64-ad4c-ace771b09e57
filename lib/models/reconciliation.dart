import 'package:mybuddy/class/data.dart';

class ReconcilablePet {
  int? petId;
  late final String pimsPetId;
  late final int serviceId;
  ReconcilablePetData? data;

  ReconcilablePet.fromJson(Map<String, dynamic> json) {
    petId = json['petId'];
    pimsPetId = json['pimsPetId'];
    serviceId = json['serviceId'];
    data = json['data'] != null ? ReconcilablePetData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['petId'] = petId;
    data['pimsPetId'] = pimsPetId;
    data['serviceId'] = serviceId;
    data['data'] = this.data?.toJson();

    return data;
  }
}

class ReconcilablePetData {
  late final String name;
  int? genderId;
  String? breedName;
  int? speciesId;
  String? speciesName;
  int? breedId;
  late final bool crossed;
  DateTime? birthDate;
  String? color;

  ReconcilablePetData.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    genderId = json['genderId'];
    breedName = json['breedName'];
    speciesId = json['speciesId'];
    speciesName = json['speciesName'];
    breedId = json['breedId'];
    crossed = json['crossed'];
    birthDate = Data.getDate(json['birthDate'], format: 'y-MM-dd');
    color = json['color'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['name'] = name;
    data['genderId'] = genderId;
    data['breedName'] = breedName;
    data['speciesId'] = speciesId;
    data['speciesName'] = speciesName;
    data['breedId'] = breedId;
    data['crossed'] = crossed;
    data['birthDate'] = birthDate;
    data['color'] = color;
    return data;
  }
}
