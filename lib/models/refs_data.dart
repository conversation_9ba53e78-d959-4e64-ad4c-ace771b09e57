import 'package:mybuddy/models/reward.dart';
import 'package:mybuddy/models/treatment_type.dart';

import 'package:collection/collection.dart';


class AppointmentRequestCause {
  late final int id;
  late final String label;

  AppointmentRequestCause.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    label = json['label'];
  }
}

class Breed {
  late final int id;
  late final String name;
  late final int speciesId;

  Breed.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    speciesId = json['speciesId'];
  }
}

class Country {
  late final int id;
  late final String shortLangLabel;
  late final String longLangLabel;
  late final String name;
  late final String dateFormat;
  late final String timeFormat;
  late final String datePlaceholder;
  late final bool isCurrent;
  late final bool isActive;
  late final bool showLof;
  late final bool showEuPassport;

  Country.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    shortLangLabel = json['shortLangLabel'];
    longLangLabel = json['longLangLabel'];
    name = json['name'];
    dateFormat = json['dateFormat'];
    timeFormat = json['timeFormat'];
    datePlaceholder = json['datePlaceholder'];
    isCurrent = json['isCurrent'];
    showEuPassport = json['showEuPassport'];
    showLof = json['showLof'];
    isActive = true;
  }

  // ignore: non_constant_identifier_names
  Country.USA() {
    id = 2;
    shortLangLabel = 'US';
    longLangLabel = 'American English';
    name = 'U.S.A.';
    dateFormat = 'm/d/Y';
    timeFormat = 'h:i A';
    datePlaceholder = 'MM/DD/YY';
    isCurrent = true;
    showLof = false;
    showEuPassport = false;
    isActive = true;
  }

  int getUnitLengthId() {
    //TODO LOW ADD IN API
    return id == 2 ? 2 : 1;
  }

  int getUnitWeightId() {
    //TODO LOW ADD IN API
    return id == 2 ? 2 : 1;
  }
}

class Gender {
  late final int id;
  late final String name;

  Gender.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }
}

class GuiText {
  final String code;
  final String value;

  GuiText(this.code, this.value);
}

class NoteType {
  late final int id;
  late final String label;

  NoteType.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    label = json['label'];
  }
}

class OrderStatus {
  late final int id;
  late final String label;

  OrderStatus.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    label = json['label'];
  }
}

class PetSmileLevel {
  late final int id;
  late final String label;

  PetSmileLevel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    label = json['label'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['label'] = label;
    return data;
  }
}

/// all References class
class Refs {
  late final String currentLang;
  late final List<PetSmileLevel> petSmileLevels;
  late final List<Species> speciess;
  late final List<Breed> breeds;
  late final List<Gender> genders;
  late final List<NoteType> petNoteTypes;
  late final List<ReminderType> reminderTypes;
  late final List<OrderStatus> orderStatus;
  late final List<UnitLength> unitLengths;
  late final List<UnitWeight> unitWeights;
  late final List<Country> countries;
  late final List<Treatment> treatments;
  late final List<TreatmentType> treatmentTypes;
  late final List<AppointmentRequestCause> appointmentRequestCauses;
  late final List<Reward> rewards;

  Refs.fromJson(Map<String, dynamic> json) {
    currentLang = json['currentLang'];

    petSmileLevels = [];
    if (json['petSmileLevels'] != null) {
      json['petSmileLevels'].forEach((v) {
        petSmileLevels.add(PetSmileLevel.fromJson(v));
      });
    }

    speciess = [];
    if (json['speciess'] != null) {
      json['speciess'].forEach((v) {
        speciess.add(Species.fromJson(v));
      });
      speciess.sort((a, b) {
        return a.name.toLowerCase().compareTo(b.name.toLowerCase());
      });
    }

    breeds = [];
    if (json['breeds'] != null) {
      json['breeds'].forEach((v) {
        breeds.add(Breed.fromJson(v));
      });
      breeds.sort((a, b) {
        return a.name.toLowerCase().compareTo(b.name.toLowerCase());
      });
    }

    genders = [];
    if (json['genders'] != null) {
      json['genders'].forEach((v) {
        genders.add(Gender.fromJson(v));
      });
    }

    petNoteTypes = [];
    if (json['petNoteTypes'] != null) {
      json['petNoteTypes'].forEach((v) {
        petNoteTypes.add(NoteType.fromJson(v));
      });
    }

    reminderTypes = [];
    if (json['reminderTypes'] != null) {
      json['reminderTypes'].forEach((v) {
        reminderTypes.add(ReminderType.fromJson(v));
      });
    }

    orderStatus = [];
    if (json['orderStatus'] != null) {
      json['orderStatus'].forEach((v) {
        orderStatus.add(OrderStatus.fromJson(v));
      });
    }

    unitLengths = [];
    if (json['unitLengths'] != null) {
      json['unitLengths'].forEach((v) {
        unitLengths.add(UnitLength.fromJson(v));
      });
    }

    unitWeights = [];
    if (json['unitWeights'] != null) {
      json['unitWeights'].forEach((v) {
        unitWeights.add(UnitWeight.fromJson(v));
      });
    }

    countries = [];
    if (json['countries'] != null) {
      json['countries'].forEach((v) {
        countries.add(Country.fromJson(v));
      });
    }

    treatments = [];
    if (json['treatments'] != null) {
      json['treatments'].forEach((v) {
        treatments.add(Treatment.fromJson(v));
      });
    }

    appointmentRequestCauses = [];
    if (json['appointmentRequestCauses'] != null) {
      json['appointmentRequestCauses'].forEach((v) {
        appointmentRequestCauses.add(AppointmentRequestCause.fromJson(v));
      });
    }

    rewards = <Reward>[];
    if(json.containsKey('rewards')) {
      json['rewards'].forEach((a) {
        rewards.add(Reward.fromJson(a));
      });
    }

    populateTreatmentTypes();
  }

  String getAppointmentStatusLabel(int? causeId) {
    if (causeId == null) {
      return '';
    }
    AppointmentRequestCause? cause =
        appointmentRequestCauses.singleWhereOrNull((f) => f.id == causeId);
    return cause == null ? '' : cause.label;
  }

  Breed? getBreed(int? breedId) {
    if (breedId == null) {
      return null;
    }
    return breeds.singleWhereOrNull((f) => f.id == breedId);
  }

  Country? getCountry(int? countryId) {
    if (countryId == null) {
      return null;
    }
    return countries.singleWhereOrNull((f) => f.id == countryId);
  }

  Country getCountryByShortLabel(String? shortLabel) {
    if (shortLabel == null) return Country.USA();
    return countries.singleWhere(
        (f) => (f.shortLangLabel.toLowerCase() == shortLabel.trim().toLowerCase()), orElse: () {
      return countries.singleWhere((c) => (c.id == 2), orElse: () {
        return Country.USA();
      });
    });
  }

  List<Breed> getFilteredBreeds(int? speciesId) {
    if (speciesId == null) {
      return <Breed>[];
    }
    //filter breed without translation
    return breeds.where((f) => f.speciesId == speciesId && f.name.trim() != '').toList();
  }

  Gender? getGender(int? genderId) {
    if (genderId == null) {
      return null;
    }
    return genders.singleWhereOrNull((f) => f.id == genderId);
  }

  NoteType? getNoteType(int? typeId) {
    if (typeId == null) {
      return null;
    }

    return petNoteTypes.singleWhereOrNull((f) => f.id == typeId);
  }

  String getOrderStatusLabel(int? statusId) {
    if (statusId == null) {
      return '';
    }
    OrderStatus? status = orderStatus.singleWhereOrNull((f) => f.id == statusId);

    return status == null ? '' : status.label;
  }

  PetSmileLevel getPetSmileLevel(int? pslId) {
    if (pslId == null) {
      return petSmileLevels.first;
    }
    return petSmileLevels.singleWhere((f) => (f.id == pslId), orElse: () {
      return petSmileLevels.first;
    });
  }

  ReminderType? getReminderType(int? reminderTypeId) {
    if (reminderTypeId == null) {
      return null;
    }
    return reminderTypes.singleWhereOrNull((f) => f.id == reminderTypeId);
  }

  Species? getDogSpecies() {
    return getSpecies(2); // 2 is the id specific to dog
  }

  Species? getSpecies(int? speciesId) {
    if (speciesId == null) {
      return null;
    }
    return speciess.singleWhereOrNull((f) => f.id == speciesId);
  }

  Treatment? getTreatment(int? treatmentId) {
    if (treatmentId == null) {
      return null;
    }
    return treatments.singleWhereOrNull((f) => f.id == treatmentId);
  }

  TreatmentType? getTreatmentType(int? typeId) {
    if (typeId == null) {
      return null;
    }
    return treatmentTypes.singleWhereOrNull((f) => f.id == typeId);
  }

  UnitLength getUnitLength(int? unitId) {
    if (unitId == null) {
      return unitLengths.first;
    }
    return unitLengths.singleWhere((f) => (f.id == unitId), orElse: () {
      return unitLengths.first;
    });
  }

  UnitWeight getUnitWeight(int? unitId) {
    if (unitId == null) {
      return unitWeights.first;
    }
    return unitWeights.singleWhere((f) => (f.id == unitId), orElse: () {
      return unitWeights.first;
    });
  }

  void populateTreatmentTypes() {
    treatmentTypes = [
      TreatmentType(0, 'APPLICATION_MOBILE_LABEL_HEALTHBOOK_VACCINES'),
      TreatmentType(1, 'APPLICATION_MOBILE_LABEL_HEALTHBOOK_ANTI_INTERNAL'),
      TreatmentType(2, 'APPLICATION_MOBILE_LABEL_HEALTHBOOK_ANTI_EXTERNAL'),
      TreatmentType(3, 'APPLICATION_MOBILE_LABEL_HEALTHBOOK_STERILISATION'),
    ];
  }
}

class ReminderType {
  late final int id;
  late final String label;

  ReminderType.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    label = json['label'];
  }
}

class Species {
  late final int id;
  late final String name;

  Species.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }
}

class Treatment {
  late final int id;
  int? speciesId;
  int? type;
  String? acronym;
  String? valence;

  Treatment.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    acronym = json['acronym'];
    valence = json['valence'];
    speciesId = json['speciesId'];
    type = json['type'];
  }

  Map<String, dynamic> toApiJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    return data;
  }
}

class UnitLength {
  late final int id;
  late final String name;
  late final String shortName;
  late final double toMeters;

  UnitLength.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    shortName = json['shortName'];
    toMeters = json['toMeters'].toDouble();
  }
}

class UnitWeight {
  late final int id;
  late final String name;
  late final String shortName;
  late final double toKilos;

  UnitWeight.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    shortName = json['shortName'];
    toKilos = json['toKilos'].toDouble();
  }
}
