// ignore_for_file: curly_braces_in_flow_control_structures

import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/models/text_banners.dart';
import 'package:mybuddy/models/user_goal.dart';
import 'package:mybuddy/models/user_stats.dart';

class HomeStats {
  late bool success = false;
  late String token = '';
  late String currentLang = '';
  UserStats userStats = UserStats();
  DailyUserGoal dailyUserGoal = DailyUserGoal();
  WeeklyUserGoal weeklyUserGoal = WeeklyUserGoal();
  TextBanners textBanners = TextBanners();

  HomeStats();

  HomeStats.fromJson(Map<String, dynamic> parsedJson,
      {UserGoalType userGoalType = UserGoalType.daily}) {
    success = parsedJson['success'];
    token = parsedJson['token'];
    currentLang = parsedJson['currentLang'];
    try {
      userStats = UserStats.fromJson(parsedJson['stats']);
    } catch (e) {
      Tools.debugPrint('error with user stats on home page');
      userStats = UserStats();
    }
    try {
      if (parsedJson['ownerGoal'] != null) {
        if (userGoalType == UserGoalType.daily)
          dailyUserGoal = DailyUserGoal.fromJson(parsedJson['ownerGoal']);
        else
          weeklyUserGoal = WeeklyUserGoal.fromJson(parsedJson['ownerGoal']);
      }
    } catch (e) {
      Tools.debugPrint('error with user goals on home page');
    }
    try {
      if (parsedJson['textBanners'] != null)
        textBanners = TextBanners.fromJson(parsedJson['textBanners']);
    } catch (e) {
      Tools.debugPrint('error with Text Banners on home page');
      textBanners = TextBanners();
    }
  }
}
