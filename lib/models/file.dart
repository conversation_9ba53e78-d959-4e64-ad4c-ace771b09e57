import 'package:mybuddy/class/data.dart';

class MBFile {
  late final int id;
  late final int? serviceId;
  late final String filename;
  late final DateTime? timestamp;

  MBFile.fromJson(Map<String, dynamic> parsedJson) {
    id = parsedJson['id'];
    serviceId = parsedJson['serviceId'];
    filename = parsedJson['filename'];
    timestamp = Data.getDate(parsedJson['timestamp']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['filename'] = filename;
    data['serviceId'] = serviceId;
    data['timestamp'] = Data.dateTimeToApiDateTimeStr(timestamp);
    return data;
  }
}
