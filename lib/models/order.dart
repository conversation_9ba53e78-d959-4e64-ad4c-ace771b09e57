import 'package:mybuddy/class/data.dart';

class Order {
  DateTime? availableDate;
  String? description;
  String? comment;
  DateTime? endDate;
  late final int id;
  int? imageId;
  String? imageUrl;
  int? ownerId;
  DateTime? requestDate;
  int? serviceId;
  DateTime? startDate;
  late int status = 0;

  Order();

  Order.fromJson(Map<String, dynamic> parsedJson) {
    availableDate = Data.getDate(parsedJson['availableDate']);
    description = parsedJson['description'];
    comment = parsedJson['comment'];
    endDate = Data.getDate(parsedJson['endDate']);
    id = parsedJson['id'];
    imageId = parsedJson['imageId'];
    imageUrl = parsedJson['imageUrl'];
    ownerId = parsedJson['ownerId'];
    requestDate = Data.getDate(parsedJson['requestDate']);
    serviceId = parsedJson['serviceId'];
    startDate = Data.getDate(parsedJson['startDate']);
    status = parsedJson['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['serviceId'] = serviceId;
    data['ownerId'] = ownerId;
    data['description'] = description;
    data['imageId'] = imageId;
    data['imageUrl'] = imageUrl;
    data['status'] = status;
    data['requestDate'] = Data.dateTimeToApiDateTimeStr(requestDate);
    data['startDate'] = Data.dateTimeToApiDateTimeStr(startDate);
    data['availableDate'] = Data.dateTimeToApiDateTimeStr(availableDate);
    data['endDate'] = Data.dateTimeToApiDateTimeStr(endDate);
    data['comment'] = comment;
    return data;
  }
}
