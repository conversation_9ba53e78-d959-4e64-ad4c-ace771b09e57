class Emergency {
  late final bool holidays;
  late final bool nights;
  late final bool sundays;

  Emergency.fromJson(Map<String, dynamic> parsedJson) {
    holidays = parsedJson['holidays'];
    nights = parsedJson['nights'];
    sundays = parsedJson['sundays'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['nights'] = nights;
    data['sundays'] = sundays;
    data['holidays'] = holidays;
    return data;
  }
}
