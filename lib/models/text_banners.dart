class TextBanners {
  late String homeScreenTextBanner = "";

  TextBanners();

  TextBanners.fromJson(Map<String, dynamic> parsedJson) {
    if (parsedJson['homeScreenTextBanner'] != null) {
      homeScreenTextBanner = parsedJson['homeScreenTextBanner'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['homeScreenTextBanner'] = homeScreenTextBanner;
    return data;
  }
}
