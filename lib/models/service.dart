import 'package:collection/collection.dart' show IterableExtension;
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

import 'emergency.dart';
import 'image.dart';
import 'opening.dart';
import 'staff.dart';

class MBService implements Comparable {
  late bool acceptAppointment = false;
  late bool acceptOrder = false;
  late bool acceptMessage = false;
  String? address;
  String? city;
  String? code;
  late int countryId = 1;
  String? externalAptUrl;
  String? externalStoreUrl;
  String? description;
  String? email;
  Emergency? emergency;
  String? fax;
  int? pimsId;
  late final int id;
  int? imageId;
  String? imageUrl;
  late List<MBImage> images = <MBImage>[];
  int? langId;
  String? name;
  late bool official = false;
  List<Opening>? openings;
  bool? ownerReconciliated;
  String? phone;
  String? phoneEmergency;
  int? serviceCategoryId;
  List<MBService>? services;
  late int serviceTypeId = 2;
  late List<Staff> staffs = <Staff>[];
  late String googlePlaceId = '';
  String? website;
  String? zipCode;
  int? parentServiceId;
  String? captainvetId;

  MBService();

  /// may be null unsafe without initializing non nullable values
  MBService.unknown() {
    name = 'APPLICATION_MOBILE_MESSAGE_ERROR_NOT_RIGHT_CLINIC'.tr();
    countryId = 1;
    acceptAppointment = false;
    acceptMessage = false;
    acceptOrder = false;
    official = false;
    ownerReconciliated = false;
    googlePlaceId = '';
  }

  MBService.fromJson(Map<String, dynamic> parsedJson, {int? serviceId}) {
    acceptAppointment = parsedJson['acceptAppointment'];
    acceptOrder = parsedJson['acceptOrder'];
    acceptMessage = parsedJson['acceptMessage'];
    address = parsedJson['address'];
    if (parsedJson['official'] != null) {
      official = parsedJson['official'];
    }
    city = parsedJson['city'];
    code = parsedJson['code'];
    countryId = parsedJson['countryId'];
    description = parsedJson['description'];
    googlePlaceId = parsedJson['googlePlaceId'];
    email = parsedJson['email'];
    if (parsedJson['emergency'] != null) {
      emergency = Emergency.fromJson(parsedJson['emergency']);
    }
    fax = parsedJson['fax'];
    pimsId = parsedJson['pimsId'];
    id = parsedJson['id'];
    imageId = parsedJson['imageId'];
    imageUrl = parsedJson['imageUrl'];
    images = parseImages(parsedJson['images']);
    langId = parsedJson['langId'];
    name = parsedJson['name'] ?? '';
    if (parsedJson['openings'] != null) {
      openings = parseOpenings(parsedJson['openings']);
    }
    ownerReconciliated = parsedJson['ownerReconciliated'];
    phone = parsedJson['phone'];
    phoneEmergency = parsedJson['phoneEmergency'];
    serviceCategoryId = parsedJson['serviceCategoryId'];
    if (parsedJson['services'] != null) {
      services = parseServices(parsedJson['services'], serviceId: id);
    }
    serviceTypeId = parsedJson['serviceTypeId'];
    if (parsedJson.containsKey('staffs')) {
      staffs = parseStaffs(parsedJson['staffs']);
    }
    website = parsedJson['website'];
    externalAptUrl = parsedJson['externalAptUrl'];
    externalStoreUrl = parsedJson['externalStoreUrl'];
    zipCode = parsedJson['zipCode'];
    parentServiceId = serviceId;
    captainvetId = parsedJson['captainvetId'];
  }

  Map<String, dynamic> toJson({bool updated = true}) {
    final Map<String, dynamic> data = {};
    if (updated) {
      data['id'] = id;
    }
    data['serviceTypeId'] = serviceTypeId;
    data['serviceCategoryId'] = serviceCategoryId;
    data['name'] = name;
    data['langId'] = langId;
    data['countryId'] = countryId;
    data['captainvetId'] = captainvetId;
    data['address'] = address;
    data['zipCode'] = zipCode;
    data['city'] = city;
    data['phone'] = phone;
    data['phoneEmergency'] = phoneEmergency;
    data['googlePlaceId'] = googlePlaceId;
    data['fax'] = fax;
    data['email'] = email;
    data['website'] = website;
    data['acceptAppointment'] = acceptAppointment;
    data['acceptOrder'] = acceptOrder;
    data['acceptMessage'] = acceptMessage;
    data['official'] = official;
    data['description'] = description;
    data['imageId'] = imageId;
    data['imageUrl'] = imageUrl;
    data['code'] = code;
    data['customStoreSettings'] =
        externalStoreUrl != null ? {'url': externalStoreUrl} : null;
    data['customAptSettings'] =
        externalAptUrl != null ? {'url': externalAptUrl} : null;
    data['externalStoreUrl'] = externalStoreUrl;
    data['externalAptUrl'] = externalAptUrl;
    data['emergency'] = emergency?.toJson();
    data['pimsId'] = pimsId;
    data['ownerReconciliated'] = ownerReconciliated;
    data['images'] = images.map((v) => v.toLightJson()).toList();
    data['openings'] = openings?.map((v) => v.toJson()).toList();
    data['staffs'] = staffs.map((v) => v.toJson()).toList();
    data['services'] = services?.map((v) => v.toJson()).toList();

    return data;
  }

  MBService.fromLightJson(Map<String, dynamic> parsedJson) {
    id = parsedJson['id'];
  }

  Map<String, dynamic> toLightJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    return data;
  }

  bool allowMessage() {
    return official && acceptMessage;
  }

  bool canMakeAppointment() {
    return official &&
        (acceptAppointment ||
            getExternalAppointmentUrl() != null ||
            captainvetId != null);
  }

  bool canMakeOrder() {
    return official && (acceptOrder || getExternalOrderUrl() != null);
  }

  @override
  int compareTo(other) {
    return name!.toLowerCase().compareTo(other.name.toLowerCase());
  }

  @override
  bool operator ==(other) {
    return (other is MBService) && other.id == id;
  }

  @override
  int get hashCode => id.hashCode ^ countryId.hashCode;

  MBService copy() {
    return MBService.fromJson(toJson());
  }

  Staff? findStaff(int? staffId) {
    if (staffId == null || staffs.isEmpty) return null;
    return staffs.singleWhereOrNull((s) => s.id == staffId);
  }

  List<int> getAllImages() {
    List<int> allImages = <int>[];
    if (imageId != null) allImages.add(imageId!);
    for (MBImage img in images) {
      if (!allImages.contains(img.id)) allImages.add(img.id);
    }
    return allImages;
  }

  List<MBImage> getAllMBImages() {
    List<MBImage> allImages = [];
    if (imageId != null)
      allImages.add(MBImage.fromJson({'id': imageId, 'url': imageUrl}));
    for (MBImage img in images) {
      if (!allImages.contains(img)) allImages.add(img);
    }
    return allImages;
  }

  String? getExternalAppointmentUrl() {
    if (externalAptUrl == null || externalAptUrl!.trim() == '') {
      return null;
    }
    return externalAptUrl;
  }

  String? getExternalOrderUrl() {
    if (externalStoreUrl == null || externalStoreUrl!.trim() == '') {
      return null;
    }
    return externalStoreUrl;
  }

  String getFullAddress() {
    List<String> addressList = <String>[];
    if (address != null && address!.trim() != '') addressList.add(address!);
    if (zipCode != null && zipCode!.trim() != '') addressList.add(zipCode!);
    if (city != null && city!.trim() != '') addressList.add(city!);
    var serviceCountry = Ref().get().getCountry(countryId);
    if (serviceCountry != null) {
      addressList.add(serviceCountry.name);
    }
    return addressList.join(', ');
  }

  int? getImageId() {
    if (imageId != null) return imageId;
    if (images.isNotEmpty) return images[0].id;
    return null;
  }

  bool hasImage() {
    return imageId != null || images.isNotEmpty;
  }

  String? getFavoriteImageUrl() {
    if (imageId != null) return imageUrl ?? imageId!.toImageUrl();
    if (images.isNotEmpty) {
      MBImage image = images[0];
      return image.url ?? image.id.toImageUrl();
    }
    // TODO LOW could implement an asset image
    return null;
  }

  List<Staff> getStaffForAppointment() {
    List<Staff> aptStaffs =
        staffs.where((s) => s.acceptAppointment == true).toList();
    aptStaffs.sort((a, b) {
      return a
          .getFullName()
          .toLowerCase()
          .compareTo(b.getFullName().toLowerCase());
    });

    Staff _staff = Staff.indifferent();
    aptStaffs.insert(0, _staff);
    return aptStaffs;
  }

  static List<MBImage> parseImages(jsonList) {
    List? list = jsonList as List?;
    if (jsonList == null) return [];
    List<MBImage> objectList =
        list!.map((data) => MBImage.fromJson(data)).toList();
    return objectList;
  }

  static List<Opening> parseOpenings(jsonList) {
    List list = jsonList as List;
    List<Opening> objectList =
        list.map((data) => Opening.fromJson(data)).toList();
    return objectList;
  }

  static List<MBService> parseServices(jsonList, {int? serviceId}) {
    List list = jsonList as List;
    List<MBService> objectList = list
        .map((data) => MBService.fromJson(data, serviceId: serviceId))
        .toList();
    return objectList;
  }

  static List<Staff> parseStaffs(jsonList) {
    List list = jsonList as List;
    List<Staff> objectList = list.map((data) => Staff.fromJson(data)).toList();
    return objectList;
  }

  String get mainImageUrl {
    String? url = imageUrl;
    url ??= imageId?.toImageUrl();
    return url ?? '';
  }
}
