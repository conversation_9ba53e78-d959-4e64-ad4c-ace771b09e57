import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:device_info/device_info.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:ios_utsname_ext/extension.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/location_manager.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/shelter.dart';
import 'package:mybuddy/models/unit.dart';
import 'package:mybuddy/models/workout.dart';

import 'pet.dart';

enum MoodType { walking, running, cycling }

extension MoodExtension on MoodType {
  // TODO check back-end int values
  Map<String, dynamic> get toMap {
    switch (this) {
      case MoodType.walking:
        return {
          'name': 'APPLICATION_MOBILE_LABEL_WORKOUT_MOOD_TYPE_WALKING'
              .tr('Walking'),
          'value': 1,
          'iconData': FontAwesomeIcons.walking
        };
      case MoodType.running:
        return {
          'name': 'APPLICATION_MOBILE_LABEL_WORKOUT_MOOD_TYPE_RUNNING'
              .tr('Running'),
          'value': 2,
          'iconData': FontAwesomeIcons.running
        };
      case MoodType.cycling:
        return {
          'name': 'APPLICATION_MOBILE_LABEL_WORKOUT_MOOD_TYPE_CYCLING'
              .tr('Cycling'),
          'value': 3,
          'iconData': Icons.directions_bike_outlined
        };
      default:
        return {
          'name': 'APPLICATION_MOBILE_ERROR_DEFAULT_UNKNOWN'.tr('Unknown'),
          'value': null,
          'iconData': Icons.do_not_disturb_on
        };
    }
  }

  String get toLowercaseName {
    return toMap['name'].toString().toLowerCase();
  }

  String get toName {
    return toMap['name'].toString();
  }

  IconData get toIcon {
    return toMap['iconData'];
  }

  int get toValue {
    return toMap["value"];
  }
}

enum AutoEndReason {
  normal,
  noMovement,
  outOfMemory,
  speedLimit,
  gpsLost,
  noAutoEnd,
  pauseSpeedLimit,
  pauseAppStart,
}

enum Status { unknown, started, saved, stopped }

class Walk {
  late String gpx;
  AutoEndReason? autoEndReason;
  late MoodType type = MoodType.walking;
  late List<Pet> pets = <Pet>[];
  Shelter? shelter;
  Status status = Status.unknown;
  Workout? workout;
  List<UserLocation> positions = <UserLocation>[];
  String currentTrackNumber = "0";
  Map<String, List<UserLocation>> positionsWithTracks = {};
  double speed = 0.0;
  int duration = 0; // In milliseconds
  double distance = 0.0;
  String trackName = "";
  int ownerId = 0;
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();

  Walk();

  Future<Map<String, dynamic>> toJsonApi() async {
    final Map<String, dynamic> data = {};

    int? reason;
    switch (autoEndReason) {
      case AutoEndReason.normal:
        reason = 0;
        break;
      case AutoEndReason.noMovement:
        reason = 1;
        break;
      case AutoEndReason.outOfMemory:
        reason = 2;
        break;
      case AutoEndReason.speedLimit:
        reason = 3;
        break;
      case AutoEndReason.gpsLost:
        reason = 4;
        break;
      case AutoEndReason.noAutoEnd:
        reason = 5;
        break;
      case AutoEndReason.pauseSpeedLimit:
        reason = 6;
        break;
      case AutoEndReason.pauseAppStart:
        reason = 7;
        break;
      default:
    }
    data['autoEndReason'] = reason;
    data['type'] = type.toMap['value'];
    data['petIds'] = pets.map((p) => p.id).toList().join(',');
    data['shelter'] = shelter?.toJson();
    data['api'] = SettingsDelegate().prefs.getString('version');
    data['phone_os'] = Platform.operatingSystemVersion;
    int phoneType = 0;
    String phoneModel = '';
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      phoneType = 1;
      AndroidDeviceInfo phone = await deviceInfo.androidInfo;
      phoneModel = phone.model;
      data['phone_os'] = phone.version.release;
    } else if (Platform.isIOS) {
      phoneType = 3;
      IosDeviceInfo phone = await deviceInfo.iosInfo;
      phoneModel = phone.utsname.machine.iOSProductName;
    }
    data['phone_model'] = phoneModel;
    data['phone_type'] = phoneType;

    int statusNumber = 0;
    switch (status) {
      case Status.saved:
        statusNumber = 1;
        break;
      case Status.started:
        statusNumber = 2;
        break;
      case Status.stopped:
        statusNumber = 3;
        break;
      default:
    }

    data['status'] = statusNumber;
    if (workout != null) {
      data['id'] = workout!.id;
    }
    data['app_version'] = Tools().appVersion;
    data['duration'] = duration;
    data['gpx'] = base64Encode(utf8.encode(gpx));
    // Converting distance in miles from meters
    data['distance'] =
        distance.toUserLength(src: LengthSource.meters).toLocalStringAsFixed();
    data['ownerId'] = ownerId;
    data['averageSpeed'] = double.parse(speed.toKPHSpeed().toStringAsFixed(10));
    data['createdAt'] = Data.dateTimeToApiDateTimeStr(createdAt);
    data['updatedAt'] = Data.dateTimeToApiDateTimeStr(updatedAt);

    return data;
  }

  factory Walk.fromJson(Map<String, dynamic> jsonData) {
    Walk walk = Walk();

    Status status = Status.unknown;
    switch (jsonData['status']) {
      case 1:
        status = Status.saved;
        break;
      case 2:
        status = Status.started;
        break;
      case 3:
        status = Status.stopped;
        break;
      default:
    }
    walk.status = status;

    AutoEndReason autoEndReason = AutoEndReason.noAutoEnd;
    switch (jsonData['reason']) {
      case 0:
        autoEndReason = AutoEndReason.normal;
        break;
      case 1:
        autoEndReason = AutoEndReason.noMovement;
        break;
      case 2:
        autoEndReason = AutoEndReason.outOfMemory;
        break;
      case 3:
        autoEndReason = AutoEndReason.speedLimit;
        break;
      case 4:
        autoEndReason = AutoEndReason.gpsLost;
        break;
      case 5:
        autoEndReason = AutoEndReason.noAutoEnd;
        break;
      case 6:
        autoEndReason = AutoEndReason.pauseSpeedLimit;
        break;
      case 7:
        autoEndReason = AutoEndReason.pauseAppStart;
        break;
      default:
    }

    walk.autoEndReason = autoEndReason;
    walk.shelter = Shelter.fromJson(jsonData['shelter']);
    if (jsonData.containsKey('workout')) {
      walk.workout = Workout.fromJson(jsonData['workout']);
    }
    walk.pets = (jsonData['pets'] as List<dynamic>)
        .map<Pet>((item) => Pet.fromJson(item))
        .toList();

    MoodType moodType = MoodType.walking;
    switch (jsonData['type']) {
      case 1:
        moodType = MoodType.walking;
        break;
      case 2:
        moodType = MoodType.running;
        break;
      case 3:
        moodType = MoodType.cycling;
        break;
      default:
    }
    walk.type = moodType;
    walk.duration = jsonData['duration'];
    walk.distance = jsonData['distance'];
    walk.speed = jsonData['speed'];
    walk.trackName = jsonData['track_name'];
    walk.positionsWithTracks = jsonData['positions_with_tracks'] == null
        ? {}
        : UserLocation.decodeLocationMap(jsonData['positions_with_tracks']);
    // populating the last track positions
    walk.positions = walk.positionsWithTracks.isEmpty
        ? []
        : walk.positionsWithTracks.values.last;
    walk.currentTrackNumber = jsonData['current_track_number'] ?? "0";
    walk.ownerId = jsonData['ownerId'] ?? 0;
    walk.createdAt = DateTime.parse(jsonData['createdAt'] ?? DateTime.now().toString());
    walk.updatedAt = DateTime.parse(jsonData['updatedAt'] ?? DateTime.now().toString());

    return walk;
  }

  static Map<String, dynamic> toMap(Walk walk) {
    final Map<String, dynamic> data = {};

    int? reason;
    switch (walk.autoEndReason) {
      case AutoEndReason.normal:
        reason = 0;
        break;
      case AutoEndReason.noMovement:
        reason = 1;
        break;
      case AutoEndReason.outOfMemory:
        reason = 2;
        break;
      case AutoEndReason.speedLimit:
        reason = 3;
        break;
      case AutoEndReason.gpsLost:
        reason = 4;
        break;
      case AutoEndReason.noAutoEnd:
        reason = 5;
        break;
      case AutoEndReason.pauseSpeedLimit:
        reason = 6;
        break;
      case AutoEndReason.pauseAppStart:
        reason = 7;
        break;
      default:
    }
    data['autoEndReason'] = reason;
    data['type'] = walk.type.toMap['value'];
    data['pets'] = walk.pets.map((p) => p.toJson()).toList();
    data['shelter'] = walk.shelter?.toJson();

    int statusNumber = 0;
    switch (walk.status) {
      case Status.saved:
        statusNumber = 1;
        break;
      case Status.started:
        statusNumber = 2;
        break;
      case Status.stopped:
        statusNumber = 3;
        break;
      default:
    }

    data['status'] = statusNumber;
    if (walk.workout != null) {
      data['workout'] = walk.workout!.toJson();
    }
    data['duration'] = walk.duration;
    data['distance'] = walk.distance;
    data['speed'] = walk.speed;
    data['track_name'] = walk.trackName;
    data['positions_with_tracks'] =
        UserLocation.encodeLocationMap(walk.positionsWithTracks);
    data['current_track_number'] = walk.currentTrackNumber;
    data['ownerId'] = walk.ownerId;
    data['createdAt'] = Data.dateTimeToApiDateTimeStr(walk.createdAt);
    data['updatedAt'] = Data.dateTimeToApiDateTimeStr(walk.updatedAt);

    return data;
  }

  static String encode(Walk walk) => jsonEncode(Walk.toMap(walk));

  static Walk decode(String walkJson) => Walk.fromJson(jsonDecode(walkJson));

  Map<String, dynamic> toOfflineWalkMap() {
    int statusNumber = 0;
    switch (status) {
      case Status.saved:
        statusNumber = 1;
        break;
      case Status.started:
        statusNumber = 2;
        break;
      case Status.stopped:
        statusNumber = 3;
        break;
      default:
    }

    double startLat = 0;
    double startLong = 0;
    double endLat = 0;
    double endLong = 0;

    if (positionsWithTracks.values.isNotEmpty &&
        positionsWithTracks.values.first.isNotEmpty) {
      startLat = positionsWithTracks.values.first.first.latitude ?? 0;
      startLong = positionsWithTracks.values.first.first.longitude ?? 0;
    }

    if (positionsWithTracks.values.isNotEmpty &&
        positionsWithTracks.values.last.isNotEmpty) {
      endLat = positionsWithTracks.values.last.last.latitude ?? 0;
      endLong = positionsWithTracks.values.last.last.longitude ?? 0;
    }

    return {
      'workoutId': workout != null ? workout!.id : null,
      'ownerId': ownerId,
      'status': statusNumber,
      'distance': double.parse(distance.toLocalStringAsFixed()) / 1000,
      'points': 0,
      'duration': duration.millisecondToMinute(),
      'speed': double.parse(speed.toKPHSpeed().toStringAsFixed(10)),
      'moodType': type.toMap['value'],
      'charityId': shelter?.id,
      'charityName': shelter?.name,
      'startLat': startLat,
      'startLong': startLong,
      'endLat': endLat,
      'endLong': endLong,
      'gpxFileName': trackName,
      'isUploaded': 0,
      'autoEndReason': autoEndReason?.index,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  //toDogWalkMap
  List<Map<String, int?>> toWalkDogsMap(int workoutId) {
    return pets
        .map((e) => {"id": null, "dogId": e.id, 'workoutId': workoutId})
        .toList();
  }
}
