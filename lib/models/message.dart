import 'package:collection/collection.dart' show IterableExtension;
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/file.dart';
import 'package:mybuddy/models/pet.dart';

class MBMessage implements Comparable {
  late final int id;
  int? serviceId;
  int? ownerId;
  late String title = '';
  late bool archived = false;
  MessageUser? messageUser;
  late List<MessageUser> messageUsers = <MessageUser>[];
  late List<MessageItem> items = <MessageItem>[];
  late List<Pet> pets = <Pet>[];
  DateTime? timestamp;

  MBMessage();

  MBMessage.light(messageId) {
    id = messageId;
    items = [];
  }

  MBMessage.fromJson(Map<String, dynamic> parsedJson) {
    id = parsedJson['id'];
    serviceId = parsedJson['serviceId'];
    ownerId = parsedJson['ownerId'];
    title = parsedJson['title'];
    archived = parsedJson['archived'];
    timestamp = Data.getDate(parsedJson['timestamp']);
    if (parsedJson['items'] != null) {
      items = [];
      parsedJson['items'].forEach((v) {
        items.add(MessageItem.fromJson(v));
      });
    }
    if (parsedJson['pets'] != null) {
      pets = parsePets(parsedJson['pets']);
    }
    if(parsedJson.containsKey('messageUser') && parsedJson['messageUser'] != null) {
      messageUser = MessageUser.fromJson(parsedJson['messageUser']);
    }
    if (parsedJson.containsKey('messageUsers') && parsedJson['messageUsers'] != null) {
      messageUsers = <MessageUser>[];
      parsedJson['messageUsers'].forEach((mu) {
        messageUsers.add(MessageUser.fromJson(mu));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['serviceId'] = serviceId;
    data['ownerId'] = ownerId;
    data['title'] = title;
    data['archived'] = archived;
    data['items'] = items.map((v) => v.toJson()).toList();
    data['timestamp'] = Data.dateTimeToApiDateTimeStr(timestamp);
    data['pets'] = pets.map((v) => v.toLightJson()).toList();
    data['messageUser'] = messageUser?.toJson();
    data['messageUsers'] = messageUsers.map((mu) => mu.toJson()).toList();

    return data;
  }

  @override
  int compareTo(other) {
    DateTime a = items.isNotEmpty ? items.last.timestamp : timestamp!;
    DateTime b = other.items.isNotEmpty ? other.items.last.timestamp : other.timestamp;
    return a.compareTo(b);
  }

  bool read() {
    if(messageUser != null && messageUser!.messageReadAt != null) {
      return !items.any((i) => i.timestamp.isAfter(messageUser!.messageReadAt!));
    } else {
      bool read = true;
      for (MessageItem item in items) {
        read &= item.readByOwner;
      }
      return read;
    }
  }

  bool isFromOwner(int? userId) {
    return messageUser != null && messageUser!.userId == userId;
  }

  MessageUser? getItemSender(int? userId) {
    if(userId == null) {
      return null;
    }
    return messageUsers.singleWhereOrNull((mu) => mu.userId == userId);
  }

  static List<Pet> parsePets(jsonList) {
    List list = jsonList as List;
    List<Pet> objectList = list.map((data) => Pet.fromLightJson(data)).toList();
    return objectList;
  }
}

class MessageItem {
  late final int id;
  int? userId;
  MBFile? file;
  int? petActivity;
  late String content;
  late bool readByOwner;
  late bool readByService;
  late DateTime timestamp;
  int? staffId;
  int? imageId;
  String? imageUrl;
  bool fromOwner = false;

  MessageItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    if(json.containsKey('userId')) {
      userId = json['userId'];
    }
    file = json['file'] != null ? MBFile.fromJson(json['file']) : null;
    petActivity = json['petActivity'];
    content = json['content'];
    readByOwner = json['readByOwner'];
    readByService = json['readByService'];
    fromOwner = json['isFromOwner'];
    timestamp = Data.getDate(json['timestamp'], utc:true)!;
    staffId = json['staffId'];
    imageId = json['imageId'];
    imageUrl = json['imageUrl'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['userId'] = userId;
    data['staffId'] = staffId;
    data['file'] = file;
    data['petActivity'] = petActivity;
    data['imageId'] = imageId;
    data['imageUrl'] = imageUrl;
    data['content'] = content;
    data['readByOwner'] = readByOwner;
    data['readByService'] = readByService;
    data['isFromOwner'] = fromOwner;
    data['timestamp'] = Data.dateTimeToApiDateTimeStr(timestamp);
    return data;
  }
}

class MessageUser {
  late final int id;
  late int userId;
  late bool userFromService;
  String? userFirstName;
  String? userLastName;
  int? userImageId;
  String? userImageUrl;
  late bool hasArchivedMessage;
  DateTime? messageReadAt;

  MessageUser.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['userId'];
    userFromService = json['userFromService'];
    userFirstName = json['userFirstName'];
    userLastName = json['userLastName'];
    userImageId = json['userImageId'];
    userImageUrl = json['userImageUrl'];
    hasArchivedMessage = json['hasArchivedMessage'];
    messageReadAt = Data.getDate(json['messageReadAt'], utc:true);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['userId'] = userId;
    data['userFromService'] = userFromService;
    data['userFirstName'] = userFirstName;
    data['userLastName'] = userLastName;
    data['userImageId'] = userImageId;
    data['userImageUrl'] = userImageUrl;
    data['hasArchivedMessage'] = hasArchivedMessage;
    data['messageReadAt'] = Data.dateTimeToApiDateTimeStr(messageReadAt);
    return data;
  }

  String get initials {
    String f = userFirstName != null && userFirstName!.isNotEmpty ? userFirstName!.substring(0, 1).toUpperCase() : '';
    String l = userLastName != null && userLastName!.isNotEmpty ? userLastName!.substring(0, 1).toUpperCase(): '';

    return f+l;
  }
}