class UserStats {
  late int earnedCharityPoints = 0;
  late int walks = 0;
  late double coveredDistance = 0;
  late int oldPoints = 0;
  late int totalPoints = 0;
  late int totalCharityPoints = 0;
  late String date = '';

  UserStats();

  UserStats.fromJson(Map<String, dynamic> parsedJson) {
    earnedCharityPoints = parsedJson['charity_points'];
    walks = parsedJson['walks'];
    coveredDistance = parsedJson['covered_distance'].toDouble();
    oldPoints = parsedJson['old_points'] ?? 0;
    totalPoints = parsedJson['total_points'] ?? 0;
    totalCharityPoints = parsedJson['total_charity_points'] ?? 0;
    date = parsedJson['startDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['earned_charity_points'] = earnedCharityPoints;
    data['walks'] = walks;
    data['covered_distance'] = coveredDistance;
    data['old_points'] = oldPoints;
    data['total_points'] = totalPoints;
    data['total_charity_points'] = totalCharityPoints;
    data['date'] = date;
    return data;
  }
}
