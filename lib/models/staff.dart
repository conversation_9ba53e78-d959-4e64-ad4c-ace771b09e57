import 'package:mybuddy/models/emergency.dart';
import 'package:mybuddy/models/opening.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class Staff {
  late bool acceptAppointment = true;
  String? description;
  String? email;
  Emergency? emergency;
  String? firstName;
  late final int id;
  int? imageId;
  String? imageUrl;
  String? jobTitle;
  String? lastName;
  String? phone;
  late List<Opening> presences = <Opening>[];
  String? speciality;
  late String type = '0';

  Staff();

  Staff.fromJson(Map<String, dynamic> parsedJson) {
    acceptAppointment = parsedJson['acceptAppointment'];
    description = parsedJson['description'];
    email = parsedJson['email'];
    emergency = parsedJson['emergency'] != null ? Emergency.fromJson(parsedJson['emergency']) : null;
    firstName = parsedJson['firstName'];
    id = parsedJson['id'];
    imageId = parsedJson['imageId'];
    imageUrl = parsedJson['imageUrl'];
    jobTitle = parsedJson['jobTitle'];
    lastName = parsedJson['lastName'];
    phone = parsedJson['phone'];
    presences = parsePresences(parsedJson['presences']);
    speciality = parsedJson['speciality'];
    type = parsedJson['type'];
  }

  Staff.indifferent() {
    id = 0;
    lastName = 'APPLICATION_MOBILE_TEXT_CHOICE_VET_INDIFFERENT'.tr();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['lastName'] = lastName;
    data['firstName'] = firstName;
    data['email'] = email;
    data['type'] = type;
    data['jobTitle'] = jobTitle;
    data['phone'] = phone;
    data['acceptAppointment'] = acceptAppointment;
    data['description'] = description;
    data['speciality'] = speciality;
    data['imageId'] = imageId;
    data['imageUrl'] = imageUrl;
    data['emergency'] = emergency?.toJson();
    data['presences'] = presences.map((v) => v.toJson()).toList();
    return data;
  }

  String getFullName({bool firstNameFirst = true}) {
    String str;
    String f = firstName ?? '';
    String l = lastName ?? '';
    str = firstNameFirst ? '$f $l' : str = '$l $f';

    return str.trim();
  }

  static List<Opening> parsePresences(jsonList) {
    List list = jsonList as List;
    List<Opening> objectList = list.map((data) => Opening.fromJson(data)).toList();
    return objectList;
  }
}
