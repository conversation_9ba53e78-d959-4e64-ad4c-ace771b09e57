// ignore_for_file: curly_braces_in_flow_control_structures

import 'dart:ui';

import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/models/shelter.dart';

import '../class/ref.dart';

enum LivingState { alive, dead, both }

enum ActivityWalkingStatus { selected, selectable, notSelectable }

class Pet implements Comparable {
  // TODO: fetch/udpate this field from API payload
  DateTime? birthDate;
  DateTime? adoptionDate;
  DateTime? diedAt;
  String? birthDateType;
  int? breedId;
  int? secondaryBreedId;
  String? breedName;
  String? chipNumber;
  String? frLof;
  String? euPassportNumber;
  String? color;
  late bool crossed = false;
  int? genderId;
  late bool hospitalized = false;
  late final int id;
  int? imageId;
  String? imageUrl;
  late String name = '';
  int? speciesId;
  late bool sterilized = false;
  late bool deceased = false;
  DateTime? deathDate;
  late List<MBService> pimsServices = <MBService>[];
  String? captainvetId;
  late ActivityWalkingStatus activityStatus = ActivityWalkingStatus.selected;
  double? totalMiles;
  int? totalWalks;
  double? weight;
  String? otherThanShelter;
  Shelter? shelter;
  Shelter? petShelter;

  Pet();

  Pet.fromJson(Map<String, dynamic> parsedJson) {
    if (parsedJson.containsKey('birthDate') &&
        parsedJson['birthDate'].length > 0) {
      birthDate = Data.getDate(parsedJson['birthDate'], format: 'y-MM-dd');
    }
    if (parsedJson.containsKey('adoptionDate')) {
      adoptionDate =
          Data.getDate(parsedJson['adoptionDate'], format: 'y-MM-dd');
    }
    if (parsedJson.containsKey('diedAt') &&
        parsedJson['diedAt'] != null &&
        parsedJson['diedAt'].length > 0) {
      diedAt = Data.getDate(parsedJson['diedAt'], format: 'y-MM-dd');
    }
    breedId = parsedJson['breedId'];
    birthDateType = parsedJson['birthDateType'];
    secondaryBreedId = parsedJson['secondaryBreedId'];
    breedName = parsedJson['breedName'];
    chipNumber = parsedJson['chipNumber'];
    frLof = parsedJson['frLof'];
    euPassportNumber = parsedJson['euPassportNumber'];
    color = parsedJson['color'];
    crossed = parsedJson['crossed'];
    genderId = parsedJson['genderId'];
    hospitalized = parsedJson['hospitalized'];
    id = parsedJson['id'];
    imageId = parsedJson['imageId'];
    imageUrl = parsedJson['imageUrl'];
    name = parsedJson['name'];
    speciesId = parsedJson['speciesId'];
    sterilized = parsedJson['sterilized'];
    otherThanShelter = parsedJson['otherThanShelter'];
    if (parsedJson.containsKey('shelter') && parsedJson['shelter'] != null) {
      shelter = Shelter.fromJson(parsedJson['shelter']);
    }
    if (parsedJson.containsKey('petShelter') &&
        parsedJson['petShelter'] != null) {
      petShelter = Shelter.fromJson(parsedJson['petShelter']);
    }
    if (parsedJson['deceased'] == '0' || parsedJson['deceased'] == null) {
      deceased = false;
    } else if (parsedJson['deceased'] == '1') {
      deceased = true;
    } else {
      deceased = true;
      deathDate = Data.getDate(
          parsedJson['deceased'].toString().substring(0, 10),
          format: 'y-MM-dd');
    }
    pimsServices =
        parsePimsServices(parsedJson['reconciliated']); //todo change in api
    captainvetId = parsedJson['captainvetId'];
    totalMiles = double.parse(parsedJson['totalMiles']?.toString() ?? "0.0");
    totalWalks = int.parse(parsedJson['totalWalks']?.toString() ?? "0");
    weight = double.parse(parsedJson['weight']?.toString() ?? "0.0");
    if (parsedJson.containsKey('activityStatus')) {
      switch (parsedJson['activityStatus']) {
        case 0:
          activityStatus = ActivityWalkingStatus.selected;
          break;
        case 1:
          activityStatus = ActivityWalkingStatus.selectable;
          break;
        case 2:
          activityStatus = ActivityWalkingStatus.notSelectable;
          break;
      }
    }
  }

  Pet.fromBaseEntityJson(Map<String, dynamic> parsedJson) {
    id = parsedJson['id'];
    name = parsedJson['name'];
    imageId = parsedJson['imageId'];
    imageUrl = parsedJson['imageUrl'];
    if (parsedJson.containsKey('diedAt') && parsedJson['diedAt'].length > 0) {
      diedAt = Data.getDate(parsedJson['diedAt'], format: 'y-MM-dd');
    }
    if (parsedJson['deceased'] == '0' || parsedJson['deceased'] == null) {
      deceased = false;
    } else if (parsedJson['deceased'] == '1') {
      deceased = true;
    } else {
      deceased = true;
      deathDate = Data.getDate(
          parsedJson['deceased'].toString().substring(0, 10),
          format: 'y-MM-dd');
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['imageId'] = imageId;
    data['imageUrl'] = imageUrl;
    data['chipNumber'] = chipNumber;
    data['frLof'] = frLof;
    data['euPassportNumber'] = euPassportNumber;
    data['genderId'] = genderId;
    data['breedId'] = breedId;
    data['secondaryBreedId'] = secondaryBreedId;
    data['breedName'] = breedName;
    data['speciesId'] = speciesId;
    data['crossed'] = crossed;
    data['color'] = color;
    data['sterilized'] = sterilized;
    data['totalMiles'] = totalMiles;
    data['totalWalks'] = totalWalks;
    data['weight'] = weight;
    data['birthDate'] = Data.dateTimeToApiDateStr(birthDate);
    data['diedAt'] = Data.dateTimeToApiDateStr(diedAt);
    data['birthDateType'] = birthDateType;
    data['adoptionDate'] = Data.dateTimeToApiDateStr(adoptionDate);
    data['otherThanShelter'] = otherThanShelter;
    data['shelterId'] = shelter?.id;
    data['shelter'] = shelter?.toJson();
    data['petShelter'] = petShelter?.toJson();
    if (deceased && deathDate == null) {
      data['deceased'] = '1';
    } else if (deceased && deathDate != null) {
      data['deceased'] = Data.dateTimeToApiDateStr(deathDate);
    } else {
      data['deceased'] = null;
    }
    data['reconciliated'] = pimsServices.map((v) => v.toLightJson()).toList();
    data['hospitalized'] = hospitalized;
    data['captainvetId'] = captainvetId;
    switch (activityStatus) {
      case ActivityWalkingStatus.selected:
        data['activityStatus'] = 0;
        break;
      case ActivityWalkingStatus.selectable:
        data['activityStatus'] = 1;
        break;
      case ActivityWalkingStatus.notSelectable:
        data['activityStatus'] = 2;
        break;
    }

    return data;
  }

  Map<String, dynamic> toJsonApi({bool updated = true}) {
    final Map<String, dynamic> data = {};
    if (updated) {
      data['id'] = id;
    }
    data['name'] = name;
    data['breedId'] = breedId;
    data['secondaryBreedId'] = secondaryBreedId;
    data['genderId'] = genderId;
    data['crossed'] = crossed;
    data['birthDate'] = birthDate;
    data['diedAt'] = diedAt;
    data['birthDateType'] = birthDateType;
    data['adoptionDate'] = adoptionDate;
    data['otherThanShelter'] = otherThanShelter;
    data['shelterId'] = shelter?.id;
    if (shelter == null && petShelter != null)
      data['petShelter'] = petShelter?.toJsonPetShelter();
    data['chipNumber'] = chipNumber;
    data['color'] = color;
    data['sterilized'] = sterilized;
    data['deceased'] = deceased ? '1' : '0';
    data['frLof'] = frLof;
    data['euPassportNumber'] = euPassportNumber;
    data['weight'] = weight;
    data['releaseDate'] =
        Data.dateTimeToApiDateTimeStr(DateTime.now(), toUtc: true);
    if (updated) {
      data['imageId'] = imageId;
      data['captainvetId'] = captainvetId;
    }

    return data;
  }

  Pet.fromLightJson(Map<String, dynamic> parsedJson) {
    id = parsedJson['id'];
  }

  Map<String, dynamic> toLightJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    return data;
  }

  @override
  bool operator ==(other) {
    return (other is Pet) &&
        other.id == id &&
        other.speciesId == speciesId &&
        other.breedId == breedId;
  }

  @override
  int get hashCode => hashValues(id, name);

  @override
  int compareTo(other) {
    return name.toLowerCase().compareTo(other.name.toLowerCase());
  }

  Pet copy() {
    return Pet.fromJson(toJson());
  }

  bool isBirthdayToday() {
    DateTime now = DateTime.now();

    /// todo control birthDate | local crash
    if (birthDate != null &&
        (now.month == birthDate!.month && now.day == birthDate!.day)) {
      return true;
    }
    return false;
  }

  DateTime? nextBirthday() {
    if (birthDate == null) {
      return null;
    }
    DateTime now = DateTime.now();

    if (isBirthdayToday()) {
      return now;
    }

    DateTime birthDay = DateTime(now.year, birthDate!.month, birthDate!.day);
    if (now.isAfter(birthDay)) {
      birthDay = DateTime(now.year + 1, birthDate!.month, birthDate!.day);
    }

    return birthDay;
  }

  @override
  String toString() {
    return 'Id: $id, Name: $name,Breed: $breedName,Chip: $chipNumber,'
        'Birthdate: $birthDate,Color: $color,'
        'Deceased: ${deceased.toString()},Sterilize: ${sterilized.toString()},';
  }

  static List<MBService> parsePimsServices(jsonList) {
    if (jsonList == null) return [];
    List list = jsonList as List;
    try {
      List<MBService> objectList =
          list.map((data) => MBService.fromLightJson(data)).toList();
      return objectList;
    } catch (exception) {
      return [];
    }
  }

  String getCaptainVetSpeciesId() {
    switch (speciesId) {
      case 1:
        return '0841a78c-8f0f-11e7-bf6b-f40f242f80fc';
      case 2:
        return '0840a058-8f0f-11e7-92bf-f40f242f80fc';
      case 27:
        return '0841c1ea-8f0f-11e7-8528-f40f242f80fc';
      case 29:
        return '084235ee-8f0f-11e7-bd17-f40f242f80fc';
      case 37:
        return '0841da90-8f0f-11e7-a021-f40f242f80fc';
      case 38:
        return 'e125e28-109b-484b-b230-1093dd6085a4';
      case 39:
        return '084235ee-8f0f-11e7-bd17-f40f242f80fc';
      case 40:
        return '0841f232-8f0f-11e7-ac81-f40f242f80fc';
      case 41:
        return '08424b74-8f0f-11e7-9e0f-f40f242f80fc'; //autre
      case 42:
        return '721f36af-8915-4e9a-b3ed-cd923843d0f3';
      case 43:
        return '721f36af-8915-4e9a-b3ed-cd923843d0f3'; //ane => cheval
      case 46:
        return '721f36af-8915-4e9a-b3ed-cd923843d0f3'; //poney => cheval
      case 47:
        return '08424b74-8f0f-11e7-9e0f-f40f242f80fc'; //poisson => autre
      case 48:
        return '706b8e36-ff2d-48f6-b9fd-d8c7548f2c52'; //cochon => basse-cour
      // return 'a4af0654-c5c9-4ff8-91c8-7e20585c2db9'; //cochon => porc-pro
      case 49:
        return '08421fe6-8f0f-11e7-93a1-f40f242f80fc';
      case 50:
        return '0c7af6e7-429b-43de-992a-5575caaf6c4b';
      case 51:
        return 'e8b32061-ec79-405a-b28a-7dd0a00d3448';
      case 52:
        return '08424b74-8f0f-11e7-9e0f-f40f242f80fc'; //vache => autre
      default:
        return '08424b74-8f0f-11e7-9e0f-f40f242f80fc'; // => autre
    }
  }

  String? get avatarUrl {
    String? url = imageUrl;
    return url;
  }

  String autoSelected() {
    switch (activityStatus) {
      case ActivityWalkingStatus.selected:
        return "Yes";
      case ActivityWalkingStatus.selectable:
        return "No";
      case ActivityWalkingStatus.notSelectable:
        return "No";
    }
  }

  String enableForNewWalks() {
    switch (activityStatus) {
      case ActivityWalkingStatus.selected:
        return "-";
      case ActivityWalkingStatus.selectable:
        return "Yes";
      case ActivityWalkingStatus.notSelectable:
        return "No";
    }
  }

  String get deceaseStatus => deceased ? "Deceased" : "Alive";
  String get pureBreed => crossed ? "No" : "Yes";
  String get getWeightWithUnit =>
      "${weight?.toStringAsFixed(2) ?? "0.0"} ${Data().getUnitWeight().shortName}";
  String get getWeight => weight?.toStringAsFixed(2) ?? "0.0";
  String get birthDay => birthDate?.toDateFormatActivities() ?? "N/A";
  String get adoptionDay => adoptionDate?.toDateFormatActivities() ?? "N/A";
  String get adoptedFrom => shelter != null
      ? shelter!.name
      : petShelter != null
          ? petShelter!.name
          : "N/A";
  Gender? get gender => Ref().get().getGender(genderId);
  String get genderName => gender?.name.tr() ?? "N/A";
  Breed? get primaryBreed => Ref().get().getBreed(breedId);
  String get primaryBreedName => primaryBreed?.name.tr() ?? "N/A";
  Breed? get secondaryBreed => Ref().get().getBreed(secondaryBreedId);
  String get secondaryBreedName => secondaryBreed?.name.tr() ?? "N/A";
  String get totalMileWithTwoDecimal => totalMiles?.toStringAsFixed(2) ?? "0.0";
}
