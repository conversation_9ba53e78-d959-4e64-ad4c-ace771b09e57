class PetCarePack implements Comparable {
  late final int id;
  late String title = '';
  int? breedId;
  int? speciesId;
  int? langId;
  int? buddyCreditPrice;
  int? appStoreIdGoogle;
  int? appStoreIdApple;
  int? imageId;
  String? imageUrl;
  String? introduction;
  bool? online;
  late List<PetCarePackItem> items = <PetCarePackItem>[];

  PetCarePack.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    breedId = json['breedId'];
    speciesId = json['speciesId'];
    langId = json['langId'];
    buddyCreditPrice = json['buddyCreditPrice'];
//    appStoreIdGoogle = json['appStoreIdGoogle'];
//    appStoreIdApple = json['appStoreIdApple'];
    imageId = json['imageId'];
    imageUrl = json['imageUrl'];
    introduction = json['introduction'];
    online = json['online'];
    if (json['items'] != null) {
      items = <PetCarePackItem>[];
      json['items'].forEach((v) {
        items.add(PetCarePackItem.fromJson(v));
      });
    }
  }

  @override
  int compareTo(other) {
    return title.compareTo(other.title);
  }
}

class PetCarePackItem {
  late final int id;
  int? imageId;
  String? imageUrl;
  String? title;
  String? summary;
  String? introduction;
  late List<PetCarePackItem> items = <PetCarePackItem>[];
  String? data;

  PetCarePackItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    imageId = json['imageId'];
    imageUrl = json['imageUrl'];
    title = json['title'];
    summary = json['summary'];
    introduction = json['introduction'];
    if (json['items'] != null) {
      items = <PetCarePackItem>[];
      json['items'].forEach((v) {
        items.add(PetCarePackItem.fromJson(v));
      });
    }
    data = json['data'];
  }
}

class PetCarePacks {
  late List<PetCarePack> homeCarePacks = <PetCarePack>[];
  late List<PetCarePack> petCarePacks = <PetCarePack>[];
  PetCarePackItem? sourcesItem;

  PetCarePacks();

  PetCarePacks.fromJson(Map<String, dynamic> json) {
    if (json['petCarePacks'] != null) {
      petCarePacks = <PetCarePack>[];
      json['petCarePacks'].forEach((v) {
        petCarePacks.add(PetCarePack.fromJson(v));
      });
    }
    if (json['homeCare'] != null) {
      homeCarePacks = <PetCarePack>[];
      json['homeCare'].forEach((v) {
        homeCarePacks.add(PetCarePack.fromJson(v));
      });
    }

    if (json['sources'] != null) {
      sourcesItem = PetCarePackItem.fromJson(json['sources']);
    }
  }
}
