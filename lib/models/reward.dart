import 'dart:ui';

import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

class Reward {
  late int id;
  late String code;
  String? taskCode;
  late int count;
  late int type;
  late int reachPoints;
  int? ownerRewardId;
  DateTime? ownerReleaseDate;
  int? rewardImageId;
  String? rewardImageUrl;

  Reward.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    code = json['code'];
    taskCode = json['taskCode'];
    count = json['count'];
    type = json['type'];
    reachPoints = json['reachPoints'];
    if(json.containsKey('ownerRewardId')) {
      ownerRewardId = json['ownerRewardId'];
    }
    if(json.containsKey('ownerReleaseDate')) {
      ownerReleaseDate = Data.getDate(json['ownerReleaseDate']);
    }
    if (json.containsKey('rewardImageId')) {
      rewardImageId = json['rewardImageId'];
    }
    if (json.containsKey('rewardImageUrl')) {
      rewardImageUrl = json['rewardImageUrl'];
    }
  }

  Reward.unknown({int rewardType = 0}) {
    id = 0;
    type = rewardType;
    code = rewardType == 0 ? 'unknown' : 'wt-avatar-0';
    count = 0;
    reachPoints = 0;
  }

  static List<Reward> parse(json) {
    List list = json as List;
    return list.map((e) => Reward.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['id'] = id;
    _data['code'] = code;
    _data['taskCode'] = taskCode;
    _data['count'] = count;
    _data['type'] = type;
    _data['reachPoints'] = reachPoints;
    _data['ownerRewardId'] = ownerRewardId;
    _data['ownerReleaseDate'] = Data.dateTimeToApiDateTimeStr(ownerReleaseDate);
    _data['rewardImageId'] = rewardImageId;
    _data['rewardImageUrl'] = rewardImageUrl;

    return _data;
  }

  @override
  bool operator == (other) {
    return (other is Reward) && other.id == id && other.code == code;
  }

  @override
  int get hashCode => hashValues(id, code);

  String get title {
    if (id == 0) {
      return 'APPLICATION_MOBILE_TITLE_ERROR_REWARD'.tr('unknown reward');
    }
    String formatCode;
    if(type == 1) {
      List<String> explodeCode = code.split('-');
      formatCode = explodeCode.map((c) => c.toUpperCase()).join('_');
    } else {
      formatCode = code;
    }
    String baseText = type == 1 ? 'APPLICATION_MOBILE_TITLE_' : 'APPLICATION_MOBILE_TITLE_BADGE_';
    String title = baseText + formatCode;

    return title.tr();
  }

  String get desc {
    if (id == 0) {
      return '';
    }
    String baseText = type == 1 ? 'APPLICATION_MOBILE_LABEL_AVATAR_REACH_LEVEL_COUNT'.tr() : 'APPLICATION_MOBILE_SUBTITLE_BADGE_$code'.tr();
    String desc = type == 1 ? baseText.replaceAll('%l', count.toString()) : baseText;

    return desc;
  }
}

enum RewardState { locked, inProgress, earned }
