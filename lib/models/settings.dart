class Settings {
  late bool forcePetView;
  late int petProfileBirthDatePreferredMode;
  late String? lang;

  Settings() {
    forcePetView = false;
    petProfileBirthDatePreferredMode = 0;
    lang = null;
  }

  Settings.fromJson(Map<String, dynamic> parsedJson) {
    forcePetView = parsedJson['forcePetView'] ?? false;
    petProfileBirthDatePreferredMode = parsedJson['petProfileBirthDatePreferredMode'] ?? 0;
    lang = parsedJson['lang'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['petProfileBirthDatePreferredMode'] = petProfileBirthDatePreferredMode;
    data['forcePetView'] = forcePetView;
    data['lang'] = lang;
    return data;
  }
}
