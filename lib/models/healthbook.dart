import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/models/refs_data.dart';

class HealthBook {
  final Pet? pet;
  late List<TreatmentActivity> treatments;

  HealthBook(this.pet) {
    Ref refs = Ref();
    treatments = refs
        .get()
        .treatments
        .where((tr) => (tr.speciesId == pet?.speciesId && tr.valence != ''))

        /// a vaccine is not available in some countries
        .map<TreatmentActivity>((tr) => TreatmentActivity(tr))
        .toList();
  }
}

class TreatmentActivity implements Comparable {
  late final Treatment treatment;
  PetActivity? petActivity;

  TreatmentActivity(this.treatment);

  @override
  int compareTo(other) {
    if(treatment.valence == null) {
      return 1;
    } else if (other.treatment.valence == null) {
      return -1;
    } else {
      return treatment.valence!.compareTo(other.treatment.valence);
    }
  }
}
