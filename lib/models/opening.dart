import 'package:flutter/material.dart';

class Opening {
  late bool allDay;
  late final int dayIndex;
  late final int id;
  late TimeOfDay? openAt;
  late TimeOfDay? pauseStart;
  late TimeOfDay? pauseEnd;
  late TimeOfDay? closeAt;

  Opening.fromJson(Map<String, dynamic> parsedJson) {
    allDay = parsedJson['allDay'];
    closeAt = parseTimeOfDay(parsedJson['closeAt']);
    dayIndex = parsedJson['dayIndex'];
    id = parsedJson['id'];
    openAt = parseTimeOfDay(parsedJson['openAt']);
    pauseEnd = parseTimeOfDay(parsedJson['pauseEnd']);
    pauseStart = parseTimeOfDay(parsedJson['pauseStart']);
    _checkTimeTable();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['dayIndex'] = dayIndex;
    data['openAt'] = timeOFDayToString(openAt);
    data['closeAt'] = timeOFDayToString(closeAt);
    data['pauseStart'] = timeOFDayToString(pauseStart);
    data['pauseEnd'] = timeOFDayToString(pauseEnd);
    data['allDay'] = allDay;
    return data;
  }

  String format(TimeOfDay? time, BuildContext context) {
    if (time == null) return '';
    return time.format(context);
  }

  String getDayCode() {
    switch (dayIndex) {
      case 0:
        return 'APPLICATION_MOBILE_LABEL_DAY_SUNDAY_FIX';
      case 1:
        return 'APPLICATION_MOBILE_LABEL_DAY_MONDAY';
      case 2:
        return 'APPLICATION_MOBILE_LABEL_DAY_TUESDAY';
      case 3:
        return 'APPLICATION_MOBILE_LABEL_DAY_WEDNESDAY';
      case 4:
        return 'APPLICATION_MOBILE_LABEL_DAY_THURSDAY';
      case 5:
        return 'APPLICATION_MOBILE_LABEL_DAY_FRIDAY';
      case 6:
        return 'APPLICATION_MOBILE_LABEL_DAY_SATURDAY';
      default:
        return '';
    }
  }

  bool isNull(TimeOfDay? time) {
    return time == null || (time.hour == 0 && time.minute == 0);
  }

  TimeOfDay? parseTimeOfDay(String? timeStr) {
    if (timeStr == null || timeStr == '') return null;
    return TimeOfDay(
        hour: int.parse(timeStr.split(':')[0]), minute: int.parse(timeStr.split(':')[1]));
  }

  String timeOFDayToString(TimeOfDay? tod) {
    if (tod == null) {
      return '';
    }
    return '${tod.hour < 10 ? '0' : ''}${tod.hour}:${tod.minute < 10 ? '0' : ''}${tod.minute}:00';
  }

  String toFormattedTimeString(BuildContext context) {
    //todo LOW format time
    if (isNull(openAt) && isNull(pauseStart) && isNull(pauseEnd) && isNull(closeAt)) {
      return '-';
    }  else if (allDay && isNull(pauseStart) && isNull(pauseEnd)) {
      return '${format(openAt, context)}-${format(closeAt, context)}';
    } else if (allDay && isNull(closeAt)) {
      return '${format(openAt, context)}-${format(pauseStart, context)}';
    } else {
      return '${format(openAt, context)}-${format(pauseStart, context)}, ${format(pauseEnd, context)}-${format(closeAt, context)}';
    }
  }

  void _checkTimeTable() {
    if (!isNull(openAt) && isNull(pauseStart) && isNull(pauseEnd) && !isNull(closeAt)) {
      allDay = true;
    } else if (!isNull(openAt) && !isNull(pauseStart) && isNull(pauseEnd) && isNull(closeAt)) {
      closeAt = pauseStart;
      pauseStart = null;
      pauseEnd = null;
      allDay = true;
    } else if (isNull(openAt) && isNull(pauseStart) && !isNull(pauseEnd) && !isNull(closeAt)) {
      openAt = pauseEnd;
      pauseStart = null;
      pauseEnd = null;
      allDay = true;
    }
  }
}
