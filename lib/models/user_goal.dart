enum UserGoalType { daily, weekly }

class UserGoal {}

class DailyUserGoal extends UserGoal {
  late String? id;
  late int dailyDistanceGoal = 0;
  late int dailyWalkGoal = 0;
  late String createdDate = '';

  DailyUserGoal();

  DailyUserGoal.fromJson(Map<String, dynamic> parsedJson) {
    id = parsedJson['id'];
    if (id != null) {
      dailyDistanceGoal = parsedJson['dailyDistanceGoal'];
      dailyWalkGoal = parsedJson['dailyWalkGoal'];
      createdDate = parsedJson['createdDate'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['dailyDistanceGoal'] = dailyDistanceGoal;
    data['dailyWalkGoal'] = dailyWalkGoal;
    data['createdDate'] = createdDate;
    return data;
  }
}

class WeeklyUserGoal extends UserGoal {
  String? id = null;
  late double weeklyDistanceGoal = 0;
  late int weeklyWalkGoal = 0;
  late String createdDate = '';

  WeeklyUserGoal();

  WeeklyUserGoal.fromJson(Map<String, dynamic> parsedJson) {
    id = parsedJson['id'];
    weeklyDistanceGoal = parsedJson['weeklyDistanceGoal'].toDouble();
    weeklyWalkGoal = parsedJson['weeklyWalkGoal'];
    createdDate = parsedJson['createdDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['weeklyDistanceGoal'] = weeklyDistanceGoal;
    data['weeklyWalkGoal'] = weeklyWalkGoal;
    data['createdDate'] = createdDate;
    return data;
  }
}
