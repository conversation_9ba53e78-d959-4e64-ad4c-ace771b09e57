import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:mybuddy/models/reward.dart';
import 'package:mybuddy/models/shelter.dart';

import 'petsmile.dart';
import 'unit.dart';

const Map<String, double> mpDefaultPosition = {
  'latitude': 39.833333, // Random location
  'longitude': -98.583333,
};

class Owner {
  late String address = '';
  late String city = '';

  // List<String> contacts;
  dynamic contacts; //todo
  late int countryId = 1;
  late String email = '';
  String? facebookUserId;
  String? appleUserId;
  String? googleUserId;
  int? favoriteClinicId;
  late String firstName = '';
  late final int id;
  String? objectId;
  int? langId;
  late String lastName = '';
  bool? passwordNeedReset;
  late PetSmile petSmiles = PetSmile();
  late String phone = '';
  late String phone2 = '';
  late bool serviceNotificationAccept = true;
  late Unit units = Unit();
  late String zipCode = '';
  String? captainvetToken;
  int? weeklyWalkingGoal;
  double? lastKnownLatitude;
  double? lastKnownLongitude;
  Shelter? shelter;
  int? imageId;
  String? imageUrl;
  int? publicAvatarId;
  late int points = 0;
  late final String encryptedId;
  int? dailyWalkingGoalWalks;
  int? dailyWalkingGoalDistance;
  int? weeklyWalkingGoalWalks;
  double? weeklyWalkingGoalDistance;
  late int dailyCharityPointsGained = 0;
  bool isActiveWalker = false;
  bool? notificationsAllowed;
  String? screen;
  String? ipAddress;
  String? firstLoginDate;
  DateTime? notificationStatusDate;

  Owner() {
    address = '';
    city = '';
    email = '';
    firstName = '';
    lastName = '';
    phone = '';
    zipCode = '';
    units = Unit();
  }

  Owner.fromJson(Map<String, dynamic> parsedJson) {
    address = parsedJson['address'];
    city = parsedJson['city'];
    contacts = parsedJson['contacts']; //todo
    countryId = parsedJson['countryId'];
    email = parsedJson['email'];
    facebookUserId = parsedJson['facebookUserId'];
    appleUserId = parsedJson['appleUserId'];
    googleUserId = parsedJson['googleUserId'];
    favoriteClinicId = parsedJson['favoriteClinicId'];
    firstName = parsedJson['firstName'] ?? '';
    id = parsedJson['id'];
    objectId = parsedJson['objectId'];
    langId = parsedJson['langId'];
    lastName = parsedJson['lastName'] ?? '';
    passwordNeedReset = parsedJson['passwordNeedReset'];
    petSmiles = parsedJson['petSmiles'] != null
        ? PetSmile.fromJson(parsedJson['petSmiles'])
        : PetSmile();
    phone = parsedJson['phone'];
    phone2 = parsedJson['phone2'];
    serviceNotificationAccept = parsedJson['serviceNotificationAccept'];
    units = Unit.fromJson(parsedJson['units']);
    zipCode = parsedJson['zipCode'];
    captainvetToken = parsedJson['captainvetToken'];
    weeklyWalkingGoal = parsedJson['weeklyWalkingGoal'];
    lastKnownLatitude = double.parse(
        (parsedJson['lastKnownLatitude'] ?? mpDefaultPosition['latitude'])
            .toString());
    lastKnownLongitude = double.parse(
        (parsedJson['lastKnownLongitude'] ?? mpDefaultPosition['longitude'])
            .toString());
    shelter = parsedJson['shelter'] != null
        ? Shelter.fromJson(parsedJson['shelter'])
        : null;
    imageId = parsedJson['imageId'];
    imageUrl = parsedJson['imageUrl'];
    if (parsedJson.containsKey('publicAvatarId')) {
      publicAvatarId = parsedJson['publicAvatarId'];
    }
    points = parsedJson.containsKey('points') ? parsedJson['points'] : 0;
    encryptedId = parsedJson['encryptedId'];
    dailyWalkingGoalWalks = parsedJson['dailyWalkingGoalWalks'];
    dailyWalkingGoalDistance =
        int.parse(parsedJson['dailyWalkingGoalDistance']?.toString() ?? "0");
    weeklyWalkingGoalWalks = parsedJson['weeklyWalkingGoalWalks'];
    weeklyWalkingGoalDistance = parsedJson['weeklyWalkingGoalDistance'] == null
        ? null
        : double.parse(
            parsedJson['weeklyWalkingGoalDistance']?.toString() ?? "0");
    dailyCharityPointsGained = parsedJson['dailyCharityPointsGained'] ?? 0;
    isActiveWalker = parsedJson['isActiveWalker'] ?? false;
    screen = parsedJson['screen'] ?? "";
    firstLoginDate = parsedJson['firstLoginDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['objectId'] = objectId;
    data['langId'] = langId;
    data['countryId'] = countryId;
    data['lastName'] = lastName;
    data['petSmiles'] = petSmiles.toJson();
    data['firstName'] = firstName;
    data['email'] = email;
    data['phone'] = phone;
    data['phone2'] = phone2;
    data['address'] = address;
    data['zipCode'] = zipCode;
    data['city'] = city;
    data['contacts'] = contacts;
    data['serviceNotificationAccept'] = serviceNotificationAccept;
    data['passwordNeedReset'] = passwordNeedReset;
    data['favoriteClinicId'] = favoriteClinicId;
    data['facebookUserId'] = facebookUserId;
    data['appleUserId'] = appleUserId;
    data['googleUserId'] = googleUserId;
    data['units'] = units.toJson();
    data['captainvetToken'] = captainvetToken;
    data['weeklyWalkingGoal'] = weeklyWalkingGoal;
    data['lastKnownLatitude'] = lastKnownLatitude;
    data['lastKnownLongitude'] = lastKnownLongitude;
    data['shelter'] = shelter?.toJson();
    data['imageId'] = imageId;
    data['imageUrl'] = imageUrl;
    data['publicAvatarId'] = publicAvatarId;
    data['points'] = points;
    data['encryptedId'] = encryptedId;
    data['dailyWalkingGoalWalks'] = dailyWalkingGoalWalks;
    data['dailyWalkingGoalDistance'] = dailyWalkingGoalDistance;
    data['dailyCharityPointsGained'] = dailyCharityPointsGained;
    data['weeklyWalkingGoalWalks'] = weeklyWalkingGoalWalks;
    data['weeklyWalkingGoalDistance'] = weeklyWalkingGoalDistance;
    data['isActiveWalker'] = isActiveWalker;
    data['screen'] = screen;

    return data;
  }

  Owner copy() {
    return Owner.fromJson(toJson());
  }

  String getFullAddress() {
    List<String?> addressList = [];
    if (address.trim() != '') addressList.add(address);
    if (zipCode.trim() != '') addressList.add(zipCode);
    if (city.trim() != '') addressList.add(city);
    addressList.add(Ref().get().getCountry(countryId)?.name);

    return addressList.join(', ');
  }

  String getFullName({bool firstNameFirst = true}) {
    return firstNameFirst ? '$firstName $lastName' : '$lastName $firstName';
  }

  Owner setCountry(Country country) {
    langId = country.id;
    countryId = country.id;
    units.unitLengthId = country.getUnitLengthId();
    units.unitWeightId = country.getUnitWeightId();
    return this;
  }

  Map<String, dynamic> toApiRegisterJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': phone,
      'address': address,
      'zipCode': zipCode,
      'city': city,
      'langId': langId,
      'countryId': countryId,
      'unitLengthId': units.unitLengthId,
      'unitWeightId': units.unitWeightId,
      'facebookUserId': facebookUserId,
      'appleUserId': appleUserId,
      'googleUserId': googleUserId,
    };
  }

  Map<String, dynamic> toApiUpdateJson() {
    Map<String, dynamic> data = {
      'user[firstName]': firstName,
      'user[lastName]': lastName,
      'user[email]': email,
      'phone': phone,
      'phone2': phone2,
      'address': address,
      'zipCode': zipCode,
      'city': city,
      'user[country]': countryId,
      'user[lang]': langId,

      /// not in form  if no facebook user
      'user[unitLength]': units.unitLengthId,
      'user[unitWeight]': units.unitWeightId,
      'contacts': contacts ?? '',
      'favoriteClinicId': favoriteClinicId,
      'captainvetToken': captainvetToken,
      'weeklyWalkingGoal': weeklyWalkingGoal,
      'shelterId': shelter?.id,
      'publicAvatar': publicAvatarId,
      'dailyWalkingGoalWalks': dailyWalkingGoalWalks,
      'dailyWalkingGoalDistance': dailyWalkingGoalDistance
    };

    if (weeklyWalkingGoalWalks != null) {
      data["weeklyWalkingGoalWalks"] = weeklyWalkingGoalWalks;
    }

    if (weeklyWalkingGoalDistance != null) {
      data["weeklyWalkingGoalDistance"] = weeklyWalkingGoalDistance;
    }
    if (firstLoginDate != null) {
      data['firstLoginDate'] = firstLoginDate;
    }

    if (notificationStatusDate != null) {
      data['NotificationStatusDate'] =
          notificationStatusDate!.toIso8601String();
    }

    if (notificationsAllowed != null) {
      data['NotificationsAllowed'] = notificationsAllowed;
    }

    if (lastKnownLatitude != null) {
      data['lastKnownLatitude'] = lastKnownLatitude;
    }

    if (lastKnownLongitude != null) {
      data['lastKnownLongitude'] = lastKnownLongitude;
    }

    if (ipAddress != null) {
      data['IPAddress'] = ipAddress;
    }

    if (screen != null) {
      data['screen'] = screen;
    }

    return data;
  }

  String get initials {
    String f =
        firstName.isNotEmpty ? firstName.substring(0, 1).toUpperCase() : '';
    String l =
        lastName.isNotEmpty ? lastName.substring(0, 1).toUpperCase() : '';

    return f + l;
  }

  Reward get avatar => Ref().referenceAvatars.singleWhere(
        (a) {
          return a.id == publicAvatarId;
        },
        orElse: () => Reward.unknown(rewardType: 1),
      );

  String? get privateAvatarUrl {
    String? url = imageUrl;
    return url;
  }
}
