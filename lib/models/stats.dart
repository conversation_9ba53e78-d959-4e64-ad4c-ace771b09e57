class Stats {
  late int workoutCount = 0;
  late double workoutDistance = 0;
  late int charityPointsPerWalk = 0;
  late int dailyWalkingCharityPointsLimit = 0;

  Stats();

  Stats.fromJson(Map<String, dynamic> parsedJson) {
    if (parsedJson['total_workout_distance'] != null) {
      workoutDistance = parsedJson['total_workout_distance'];
    }
    if (parsedJson['total_workout_count'] != null) {
      workoutCount = parsedJson['total_workout_count'];
    }
    charityPointsPerWalk = parsedJson['charityPointsPerWalk'];
    dailyWalkingCharityPointsLimit =
        parsedJson['dailyWalkingCharityPointsLimit'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['total_workout_count'] = workoutCount;
    data['total_workout_distance'] = workoutDistance;
    data['charityPointsPerWalk'] = charityPointsPerWalk;
    data['dailyWalkingCharityPointsLimit'] = dailyWalkingCharityPointsLimit;
    return data;
  }
}
