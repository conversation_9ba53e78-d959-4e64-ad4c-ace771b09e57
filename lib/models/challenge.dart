import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/reward.dart';
import 'package:mybuddy/models/task.dart';

class ChallengesResponse {
  ChallengesResponse();

  ChallengesResponse.fromJson(dynamic json) {
    success = json['success'];
    token = json['token'];
    currentLang = json['currentLang'];
    totalPoints = json['totalPoints'];
    if (json['joined'] != null) {
      joined = [];
      json['joined'].forEach((v) {
        joined.add(Challenge.fromJson(v));
      });
    }
    if (json['available'] != null) {
      available = [];
      json['available'].forEach((v) {
        available.add(Challenge.fromJson(v));
      });
    }
  }
  late bool success;
  late String token;
  late String currentLang;
  late int totalPoints;
  List<Challenge> joined = [];
  List<Challenge> available = [];

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = success;
    map['token'] = token;
    map['currentLang'] = currentLang;
    map['totalPoints'] = totalPoints;
    map['joined'] = joined.map((v) => v.toJson()).toList();
    map['available'] = available.map((v) => v.toJson()).toList();
    return map;
  }
}

class Challenge {
  String? id;
  String? encryptedId;
  String? title;
  String? summary;
  String? description;
  String? rules;
  ChallengeType? type;
  int? imageId;
  String? imageUrl;
  int? imageDetailId;
  String? imageDetailUrl;
  int? sponsorId;
  String? sponsorName;
  String? sponsorImageUrl;
  int? challengerCount;
  int? maxChallengers;
  DateTime? dateStart;
  DateTime? dateEnd;
  List<Reward>? rewards;
  List<Task>? tasksToDo;
  int totalWalks = 0;
  int yourWalks = 0;
  double totalDistance = 0.0;
  double yourDistance = 0.0;
  String? completedTaskCode;
  bool? isRegistered;

  Challenge();

  Challenge.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    encryptedId = json['encryptedId'];
    title = json['title'];
    summary = json['summary'];
    description = json['description'];
    rules = json['rules'];
    switch (json['type']) {
      case 0:
        type = ChallengeType.classic;
        break;
      case 1:
        type = ChallengeType.walk;
        break;
    }
    imageId = json['imageId'];
    imageUrl = json['imageUrl'];
    imageDetailId = json['imageDetailId'];
    imageDetailUrl = json['imageDetailUrl'];
    sponsorId = json['sponsorId'];
    sponsorName = json['sponsorName'];
    sponsorImageUrl = json['sponsorImageUrl'];
    challengerCount = json['challengerCount'] ?? 0;
    maxChallengers = json['maxChallengers'] ?? 0;
    dateStart = Data.getDate(json['dateStart'])!;
    dateEnd = Data.getDate(json['dateEnd']);
    if (json.containsKey('rewards') && json['rewards'] != null) {
      rewards = Reward.parse(json['rewards']);
    }
    if (json.containsKey('tasksToDo') && json['tasksToDo'] != null) {
      tasksToDo = Task.parse(json['tasksToDo']);
    }
    totalWalks = json.containsKey('allTotalWalks') ? json['allTotalWalks'] : 0;
    totalDistance = json.containsKey('allTotalDistance')
        ? json['allTotalDistance'].toDouble()
        : 0.0;
    yourWalks = json.containsKey('yourTotalWalks') ? json['yourTotalWalks'] : 0;
    yourDistance = json.containsKey('yourTotalDistance')
        ? json['yourTotalDistance'].toDouble()
        : 0.0;
    isRegistered = json['isRegistered'];
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['id'] = id;
    _data['title'] = title;
    _data['summary'] = summary;
    _data['description'] = description;
    _data['rules'] = rules;

    int? dataType;
    switch (type) {
      case ChallengeType.classic:
        dataType = 0;
        break;
      case ChallengeType.walk:
        dataType = 1;
        break;
      default:
        break;
    }

    _data['type'] = dataType;
    _data['imageId'] = imageId;
    _data['imageUrl'] = imageUrl;
    _data['imageDetailId'] = imageDetailId;
    _data['imageDetailUrl'] = imageDetailUrl;
    _data['sponsorId'] = sponsorId;
    _data['sponsorName'] = sponsorName;
    _data['sponsorImageUrl'] = sponsorImageUrl;
    _data['challengerCount'] = challengerCount;
    _data['maxChallengers'] = maxChallengers;
    _data['dateStart'] = Data.dateTimeToApiDateTimeStr(dateStart);
    _data['dateEnd'] = Data.dateTimeToApiDateTimeStr(dateEnd);
    _data['rewards'] = rewards!.map((r) => r.toJson()).toList();
    _data['tasksToDo'] = tasksToDo!.map((t) => t.toJson()).toList();
    _data['totalWalks'] = totalWalks;
    _data['totalDistance'] = totalDistance;
    _data['yourTotalWalks'] = yourWalks;
    _data['yourTotalDistance'] = yourDistance;

    return _data;
  }

  static List<Challenge> parse(json) {
    List l = json as List;
    return l.map((e) => Challenge.fromJson(e)).toList();
  }

  String get completedTask =>
      rewards!.isNotEmpty ? rewards!.first.taskCode! : 'OWNER_CHALLENGE_$id';

  @override
  bool operator ==(other) {
    return (other is Challenge) && other.id == id && other.type == type;
  }

  @override
  int get hashCode => id.hashCode ^ type.hashCode;

  bool get isWalk => type == ChallengeType.walk;
  bool get isClassic => type == ChallengeType.classic;
}

enum ChallengeType { classic, walk }

class Challenger {
  late final int ownerChallengeId;
  late final DateTime? subscribedAt;

  Challenger.fromJson(Map<String, dynamic> json) {
    ownerChallengeId = json['ownerChallengeId'];
    subscribedAt = Data.getDate(json['subscribedAt']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ownerChallengeId'] = ownerChallengeId;
    data['subscribedAt'] = Data.dateTimeToApiDateTimeStr(subscribedAt);

    return data;
  }

  static List<Challenger> parse(json) {
    if (json == null) {
      return <Challenger>[];
    }
    List l = json as List;
    return l.map((e) => Challenger.fromJson(e)).toList();
  }
}
