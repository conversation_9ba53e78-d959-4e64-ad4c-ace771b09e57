import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/refs_data.dart';

import 'image.dart';
import 'pet.dart';

enum ChronoFilter { none, past, future }

class PetActivityResponse {
  late bool success;
  late String token;
  late String currentLang;
  late int totalPoints;
  late int currentPageNumber;
  late int numItemsPerPage;
  late int totalWalks;
  late double totalMiles;
  late int totalRecords;
  late List<PetActivity> data;

  PetActivityResponse();

  PetActivityResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    token = json['token'];
    currentLang = json['currentLang'];
    totalPoints = json['totalPoints'];
    currentPageNumber = json['currentPageNumber'];
    numItemsPerPage = json['numItemsPerPage'];
    totalWalks = json['totalWalks'];
    totalMiles = double.parse(json['totalMiles']?.toString()??"0.0");;
    totalRecords = json['totalRecords'];
    if (json['data'] != null) {
      data = <PetActivity>[];
      json['data'].forEach((v) {
        data.add(PetActivity.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = this.success;
    data['token'] = this.token;
    data['currentLang'] = this.currentLang;
    data['totalPoints'] = this.totalPoints;
    data['currentPageNumber'] = this.currentPageNumber;
    data['numItemsPerPage'] = this.numItemsPerPage;
    data['totalWalks'] = this.totalWalks;
    data['totalMiles'] = this.totalMiles;
    data['totalRecords'] = this.totalRecords;
    data['data'] = this.data.map((v) => v.toJson()).toList();
    return data;
  }
}

class PetActivity {
  late int activityId;
  String? anomalyReported;
  String? cause;
  String? comment;
  String? company;
  String? contractNumber;
  DateTime? dateEnd;
  DateTime? dateStart;
  String? diagnostic;
  double? distance;
  DateTime? nextOccurrence;
  int? duration;
  DateTime? endRecurrence;
  String? externalPestControl;
  int? file;
  String? foodName;
  int? generalCondition;
  int? id;
  int? imageId;
  String? imageUrl;
  late List<MBImage> images = <MBImage>[];
  int? inProgress;
  String? internPestControl;
  int? intervalRecurrence;
  int? intervalRecurrenceUnit;
  int? intervalRecurrenceValue;
  String? lotNumber;
  String? medicalTreatment;
  late List<Pet> pets = <Pet>[];
  int? reminderDelay;
  int? reminderId;
  String? response;
  String? sentence;
  int? serviceId;
  int? staffId;
  int? status;
  String? suggestions;
  String? surgeryTreatment;
  String? testPerformed;
  String? title;
  String? treatmentName;
  int? typeId;
  int? vaccination;
  double? weight;
  List<Treatment>? treatments;

  PetActivity();

  PetActivity.fromAddImageRequest(Map<String, dynamic> parsedJson, Pet pet) {
    pets.add(pet);
    id = parsedJson['id'];
    imageId = parsedJson['imageId']['id'];
    imageUrl = parsedJson['petActivity']['imageUrl'];
    dateStart = DateTime.now();
    activityId = 5;
  }

  PetActivity.fromLightJson(Map<String, dynamic> parsedJson) {
    id = parsedJson['id'];
  }

  PetActivity.fromJson(Map<String, dynamic> parsedJson) {
    id = parsedJson['id'];
    activityId = parsedJson['activityId'];
    anomalyReported = parsedJson['anomalyReported'];
    cause = parsedJson['cause'];
    comment = parsedJson['comment'];
    company = parsedJson['company'];
    contractNumber = parsedJson['contractNumber'];
    dateEnd = Data.getDate(parsedJson['dateEnd']);
    serviceId = parsedJson['serviceId'];
    switch (activityId) {
      case 3:

        /// health plan / insurance UTC if created by a clinic
        dateStart =
            Data.getDate(parsedJson['dateStart'], utc: serviceId != null);
        break;
      case 7:
      case 8:

        /// hospitalization or health note
        dateStart = Data.getDate(parsedJson['dateStart'], utc: true);
        break;
      default:
        dateStart = Data.getDate(parsedJson['dateStart']);
    }

    diagnostic = parsedJson['diagnostic'];

    if (parsedJson['distance'] != null) {
      distance = parsedJson['distance'].toDouble();
    }
    if (parsedJson['duration'] != null) {
      duration = parsedJson['duration'];
    }
    endRecurrence = Data.getDate(parsedJson['endRecurrence']);
    externalPestControl = parsedJson['externPestControl'];
    file = parsedJson['file'];
    foodName = parsedJson['foodName'];
    generalCondition = parsedJson['generalCondition'];
    imageId = parsedJson['imageId'];
    imageUrl = parsedJson['imageUrl'];
    images = parseImages(parsedJson['images']);
    inProgress = parsedJson['inProgress'];
    internPestControl = parsedJson['internPestControl'];
    intervalRecurrence = parsedJson['intervalRecurrence'];
    intervalRecurrenceUnit = parsedJson['intervalRecurrenceUnit'];
    intervalRecurrenceValue = parsedJson['intervalRecurrenceValue'];
    lotNumber = parsedJson['lotNumber'];
    medicalTreatment = parsedJson['medicalTreatment'];
    pets = parsePets(parsedJson['pets']);
    reminderDelay = parsedJson['reminderDelay'];
    reminderId = parsedJson['reminderId'];
    if (reminderId == 0) reminderId = null;
    response = parsedJson['response'];
    sentence = parsedJson['sentence'];
    staffId = parsedJson['staffId'];
    status = parsedJson['status'];
    suggestions = parsedJson['suggestions'];
    surgeryTreatment = parsedJson['surgeryTreatment'];
    testPerformed = parsedJson['testPerformed'];
    title = parsedJson['title'];
    treatmentName = parsedJson['treatmentName'];
    typeId = parsedJson['type'];
    vaccination = parsedJson['vaccination'];
    if (parsedJson['weight'] != null) {
      weight = parsedJson['weight'].toDouble();
    }
    if (parsedJson['treatments'] != null) {
      treatments = parseTreatments(parsedJson['treatments']);
    }
    nextOccurrence = getReminderNextOccurence();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['activityId'] = activityId;
    data['anomalyReported'] = anomalyReported;
    data['cause'] = cause;
    data['comment'] = comment;
    data['company'] = company;
    data['contractNumber'] = contractNumber;
    data['dateEnd'] = Data.dateTimeToApiDateTimeStr(dateEnd);
    data['serviceId'] = serviceId;
    switch (activityId) {
      case 3:

        /// health plan / insurance UTC if created by a clinic
        data['dateStart'] =
            Data.dateTimeToApiDateTimeStr(dateStart, toUtc: serviceId != null);
        break;
      case 7:
      case 8:

        /// hospitalization or health note
        data['dateStart'] =
            Data.dateTimeToApiDateTimeStr(dateStart, toUtc: true);
        break;
      default:
        data['dateStart'] = Data.dateTimeToApiDateTimeStr(dateStart);
    }
    data['diagnostic'] = diagnostic;
    data['distance'] = distance;
    data['duration'] = duration;
    data['endRecurrence'] = Data.dateTimeToApiDateTimeStr(endRecurrence);
    data['externPestControl'] = externalPestControl;
    data['file'] = file;
    data['foodName'] = foodName;
    data['generalCondition'] = generalCondition;
    data['imageId'] = imageId;
    data['imageUrl'] = imageUrl;
    data['images'] = images.map((v) => v.toJson()).toList();
    data['inProgress'] = inProgress;
    data['internPestControl'] = internPestControl;
    data['intervalRecurrence'] = intervalRecurrence;
    data['intervalRecurrenceValue'] = intervalRecurrenceValue;
    data['intervalRecurrenceUnit'] = intervalRecurrenceUnit;
    data['lotNumber'] = lotNumber;
    data['medicalTreatment'] = medicalTreatment;
    data['pets'] = pets.map((v) => v.toLightJson()).toList();
    data['reminderDelay'] = reminderDelay;
    data['reminderId'] = reminderId;
    data['response'] = response;
    data['sentence'] = sentence;
    data['staffId'] = staffId;
    data['status'] = status;
    data['suggestions'] = suggestions;
    data['surgeryTreatment'] = surgeryTreatment;
    data['testPerformed'] = testPerformed;
    data['title'] = title;
    data['treatmentName'] = treatmentName;
    data['type'] = typeId;
    data['vaccination'] = vaccination;
    data['weight'] = weight;
    data['treatments'] = treatments?.map((v) => v.toApiJson()).toList() ?? [];

    return data;
  }

  DateTime? addReminderOccurenceDuration(DateTime? oldOccurrence) {
    if (oldOccurrence == null || intervalRecurrenceValue == null) return null;
    DateTime newDateTime;
    switch (intervalRecurrenceUnit) {
      case 1:
        newDateTime =
            oldOccurrence.add(Duration(days: intervalRecurrenceValue!));
        break;
      case 2:
        newDateTime =
            oldOccurrence.add(Duration(days: intervalRecurrenceValue! * 7));
        break;
      case 3: //todo LOW TEST with 29,30,31
        int months = oldOccurrence.month + intervalRecurrenceValue!;
        int years = months ~/ 12;
        newDateTime = DateTime(
            oldOccurrence.year + years, months % 12, oldOccurrence.day);
        break;
      case 4:
        newDateTime = DateTime(oldOccurrence.year + intervalRecurrenceValue!,
            oldOccurrence.month, oldOccurrence.day); //todo test with feb 29
        break;
      default:
        return null;
    }
    return setDateToFixedHour(newDateTime);
  }

  void cleanReminderDates() {
    if (dateStart == null) return;

    ///set dateStart to
    dateStart = setDateToFixedHour(dateStart!);

    ///set endRecurrence like dateStart Time of day
    if (endRecurrence == null) return;
    endRecurrence = DateTime(
      endRecurrence!.year,
      endRecurrence!.month,
      endRecurrence!.day,
      dateStart!.hour,
      dateStart!.minute,
      dateStart!.second,
    );
  }

  PetActivity copy() {
    List<Pet> _pets = <Pet>[];
    for (var pet in pets) {
      Pet? p = Data().get().getPet(pet.id);
      if (p != null) {
        _pets.add(p);
      }
    }
    List<Treatment> _treatments = <Treatment>[];
    if (treatments != null) {
      for (var treatment in treatments!) {
        Treatment? t = Ref().get().getTreatment(treatment.id);
        if (t != null) {
          _treatments.add(t);
        }
      }
    }
    PetActivity newPetActivity = PetActivity.fromJson(toJson());
    newPetActivity.pets = _pets;
    newPetActivity.treatments = _treatments;
    return newPetActivity;
  }

  bool forThisPet({int? petId, Pet? pet}) {
    return Tools.isIdInList(pets, id: petId, thisObject: pet);
  }

  String? getFullRecurrenceDetails() {
    if (intervalRecurrenceUnit == null || intervalRecurrenceValue == null) {
      return null;
    }
    Data data = Data();
    String text = 'APPLICATION_MOBILE_FIELD_LABEL_RECURRENCE_TYPE'.tr();
    text += ' $intervalRecurrenceValue  ';
    text += '${getReminderUnitLabelCode().tr()} ';
    if (endRecurrence != null) {
      text += '\n';
      text += 'APPLICATION_MOBILE_FIELD_LABEL_RECURRENCE_END'.tr();
      text += ' ${data.dateTimeToUserDateStr(endRecurrence)}';
    }
    return text;
  }

  String getPetListString() {
    String petNames = '';
    for (var p in pets) {
      if (petNames != '') {
        petNames += ', ';
      }
      petNames += Data().get().getPet(p.id)?.name ?? '';
    }
    return petNames;
  }

  int getRemainingOccurrenceCount() {
    /// case with only one reminder
    if (intervalRecurrenceUnit == null && intervalRecurrenceValue == null) {
      return dateEnd == null ? 1 : 0;
    }

    /// case with recurrence and not finished yet
    if (intervalRecurrenceUnit != null && intervalRecurrenceValue != null) {
      ///no limit
      if (endRecurrence == null) {
        return 999;
      }

      /// ended
      if (dateEnd == endRecurrence ||
          (dateEnd != null && dateEnd!.isAfter(endRecurrence!))) {
        return 0;
      }

      //TODO NS MID -> safe return but not sure 0 is the best value
      if (dateStart == null) {
        return 0;
      }
      DateTime _next = dateStart!;
      int i = 0;
      do {
        if (dateEnd == null || _next.isAfter(dateEnd!)) {
          i++;
        }

        _next = addReminderOccurenceDuration(_next)!;
      } while (i < 1000 && !_next.isAfter(endRecurrence!));
      return i;
    }

    /// case where one of unit and value is null and not the other -> PROBLEM
    return 0;
  }

  DateTime? getReminderNextOccurence() {
    if (activityId != 2) return null;

    /// first we need to clean endRecurrence
    cleanReminderDates();

    /// case with only one reminder
    if (intervalRecurrenceUnit == null && intervalRecurrenceValue == null) {
      return dateEnd == null ? dateStart : null;
    }

    /// case with recurrence and not finished yet
    if (intervalRecurrenceUnit != null && intervalRecurrenceValue != null) {
      /// not started
      if (dateEnd == null) return dateStart;

      /// ended
      if (endRecurrence != null &&
          (dateEnd == endRecurrence || dateEnd!.isAfter(endRecurrence!))) {
        return null;
      }

      //TODO NS MID -> safe return but not sure null is the best value
      if (dateStart == null) {
        return null;
      }

      ///need to start from the beginning to get a more precise datetime
      DateTime _next = dateStart!;
      int i = 0;
      do {
        _next = addReminderOccurenceDuration(_next)!;
        i++;
      } while (i < 1000 && !_next.isAfter(dateEnd!));
      return _next;
    }

    /// case where one of unit and value is null and not the other -> PROBLEM
    return null;
  }

  /// ********** REMINDER ************

  String getReminderUnitLabelCode() {
    return getReminderUnitLabelCodeForId(intervalRecurrenceUnit);
  }

  bool hasThisTreatment({int? treatmentId, Treatment? treatment}) {
    return Tools.isIdInList(treatments, id: treatmentId, thisObject: treatment);
  }

  DateTime setDateToFixedHour(DateTime dateTime,
      {int hour = 8, int minute = 0, int second = 0}) {
    return DateTime(
        dateTime.year, dateTime.month, dateTime.day, hour, minute, second);
  }

  Map<String, dynamic> toApiJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['activityId'] = activityId;

    List<String> petIds = <String>[];
    for (var p in pets) {
      petIds.add(p.id.toString());
    }
    data['petIds'] = petIds.join(',');

    data['dateStart'] = Data.dateTimeToApiDateTimeStr(dateStart);
    data['dateEnd'] = Data.dateTimeToApiDateTimeStr(dateEnd);
    data['company'] = company;
    data['contractNumber'] = contractNumber;
    data['comment'] = comment;
    data['weight'] = weight;
    data['foodName'] = foodName;
    data['distance'] = distance;
    data['duration'] = duration;
    data['type'] = typeId;
    data['treatmentName'] = treatmentName;
    data['lotNumber'] = lotNumber;
    data['title'] = title;
    data['endRecurrence'] = Data.dateTimeToApiDateTimeStr(endRecurrence);
    data['intervalRecurrenceValue'] = intervalRecurrenceValue;
    data['intervalRecurrenceUnit'] = intervalRecurrenceUnit;
    data['reminderId'] = reminderId;

    if (treatments != null) {
      data['treatments'] = treatments!.map((v) => v.toApiJson()).toList();
    }
    return data;
  }

  Map<String, dynamic> toLightJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    return data;
  }

  @override
  String toString() {
    String result =
        'id: $id,imageId: $imageId, activity: $activityId, dateStart: $dateStart type:$typeId pets:';
    for (var element in pets) {
      result += ' ${element.id}';
    }
    return result;
  }

  static String getReminderUnitLabelCodeForId(int? recurrenceUnitId) {
    if (![1, 2, 3, 4].contains(recurrenceUnitId)) return '';
    return ('APPLICATION_MOBILE_FIELD_TEXT_00${recurrenceUnitId}_RECURRENCE_TYPE');
  }

  static List<MBImage> parseImages(jsonList) {
    List list = jsonList as List;
    List<MBImage> objectList =
        list.map((data) => MBImage.fromJson(data)).toList();
    return objectList;
  }

  static List<Pet> parsePets(jsonList) {
    List list = jsonList as List;
    List<Pet> objectList = list.map((data) => Pet.fromLightJson(data)).toList();
    return objectList;
  }

  static List<Treatment> parseTreatments(jsonList) {
    if (jsonList == null) return [];
    List list = jsonList as List;
    List<Treatment> objectList =
        list.map((data) => Treatment.fromJson(data)).toList();
    return objectList;
  }
}
