class KBEmailVerificationResponse {
  KBEmailVerificationResponse({
    String? result,
    String? reason,
    bool? role,
    bool? free,
    bool? disposable,
    bool? acceptAll,
    String? didYouMean,
    double? sendex,
    String? email,
    dynamic user,
    String? domain,
    bool? success,
    String? message,
  }) {
    _result = result;
    _reason = reason;
    _role = role;
    _free = free;
    _disposable = disposable;
    _acceptAll = acceptAll;
    _didYouMean = didYouMean;
    _sendex = sendex;
    _email = email;
    _user = user;
    _domain = domain;
    _success = success;
    _message = message;
  }

  KBEmailVerificationResponse.fromJson(dynamic json) {
    _result = json['result'];
    _reason = json['reason'];
    _role = json['role'];
    _free = json['free'];
    _disposable = json['disposable'];
    _acceptAll = json['accept_all'];
    _didYouMean = json['did_you_mean'];
    _sendex = json['sendex'] is int
        ? json['sendex'].toDouble()
        : json['sendex'] is double
            ? json['sendex']
            : 0.0;
    _email = json['email'];
    _user = json['user'];
    _domain = json['domain'];
    _success = json['success'];
    _message = json['message'];
  }
  String? _result;
  String? _reason;
  bool? _role;
  bool? _free;
  bool? _disposable;
  bool? _acceptAll;
  String? _didYouMean;
  double? _sendex;
  String? _email;
  dynamic _user;
  String? _domain;
  bool? _success;
  String? _message;
  KBEmailVerificationResponse copyWith({
    String? result,
    String? reason,
    bool? role,
    bool? free,
    bool? disposable,
    bool? acceptAll,
    String? didYouMean,
    double? sendex,
    String? email,
    dynamic user,
    String? domain,
    bool? success,
    String? message,
  }) =>
      KBEmailVerificationResponse(
        result: result ?? _result,
        reason: reason ?? _reason,
        role: role ?? _role,
        free: free ?? _free,
        disposable: disposable ?? _disposable,
        acceptAll: acceptAll ?? _acceptAll,
        didYouMean: didYouMean ?? _didYouMean,
        sendex: sendex ?? _sendex,
        email: email ?? _email,
        user: user ?? _user,
        domain: domain ?? _domain,
        success: success ?? _success,
        message: message ?? _message,
      );
  String? get result => _result;
  String? get reason => _reason;
  bool? get role => _role;
  bool? get free => _free;
  bool? get disposable => _disposable;
  bool? get acceptAll => _acceptAll;
  String? get didYouMean => _didYouMean;
  double? get sendex => _sendex;
  String? get email => _email;
  dynamic get user => _user;
  String? get domain => _domain;
  bool? get success => _success;
  String? get message => _message;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['result'] = _result;
    map['reason'] = _reason;
    map['role'] = _role;
    map['free'] = _free;
    map['disposable'] = _disposable;
    map['accept_all'] = _acceptAll;
    map['did_you_mean'] = _didYouMean;
    map['sendex'] = _sendex;
    map['email'] = _email;
    map['user'] = _user;
    map['domain'] = _domain;
    map['success'] = _success;
    map['message'] = _message;
    return map;
  }
}
