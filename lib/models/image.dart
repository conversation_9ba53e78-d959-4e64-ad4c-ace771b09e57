class MBImage {
  late final int id;
  late final String? url;
  late final bool isVideo;

  MBImage.fromJson(Map<String, dynamic> parsedJson) {
    id = parsedJson['id'];
    url = parsedJson['url'];
    isVideo = parsedJson['isVideo'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['url'] = url;
    data['isVideo'] = isVideo;
    return data;
  }

  Map<String, dynamic> toLightJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    return data;
  }
}
