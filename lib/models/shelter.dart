import 'dart:ui';

class Shelter {
  late final int id;
  late String name = '';
  late String state = '';
  late String city = '';
  String? zipCode;
  // double latitude;
  // double longitude;

  Shelter();

  Shelter.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    name = json['name'] ?? '';
    state = json['state'] ?? '';
    city = json['city'] ?? '';
    zipCode = json['zipCode'] != null ? json['zipCode'].toString() : "0";
    // latitude = json['latitude'];
    // longitude = json['longitude'];
  }

  Shelter.none() {
    id = -666;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['state'] = state;
    data['city'] = city;
    data['zipCode'] = zipCode;
    // data['latitude'] = latitude;
    // data['longitude'] = longitude;

    return data;
  }

  Map<String, dynamic> toJsonPetShelter() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['state'] = state;
    data['city'] = city;
    return data;
  }

  @override
  bool operator ==(other) {
    return (other is Shelter) &&
        other.id == id &&
        other.name == name &&
        other.state == state &&
        other.city == city &&
        other.zipCode == zipCode;
  }

  @override
  int get hashCode => hashValues(id, name, city, state);

  static List<Shelter> parseShelters(jsonList) {
    List list = jsonList as List;
    return list.map((data) => Shelter.fromJson(data)).toList();
  }

  // static Map<String, List<Shelter>> parseResults(Map<String, dynamic> json) {
  //   Map<String, List<Shelter>> results = <String, List<Shelter>>{};
  //   json.forEach((key, value) => results[key] = value.map((v) => Shelter.fromJson(v)).toList());
  //
  //   return results;
  // }
}
