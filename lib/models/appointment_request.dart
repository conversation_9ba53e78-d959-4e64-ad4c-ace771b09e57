import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/models/pet_activity.dart';

import 'cause.dart';
import 'image.dart';
import 'period.dart';
import 'pet.dart';

class AppointmentRequest implements Comparable {
  late List<Cause> causes = <Cause>[];
  late final int id;
  late List<MBImage> images = <MBImage>[];
  late List<PetActivity> notes = <PetActivity>[];
  late List<Period> periods = <Period>[];
  int? petActivityId;
  late List<Pet> pets = <Pet>[];
  int? serviceId;
  int? staffId;
  int? status;
  late String message = '';
  late String comment = '';
  String? captainvetId;

  AppointmentRequest();

  AppointmentRequest.fromJson(Map<String, dynamic> parsedJson) {
    causes = parsedJson['causes'] != null ? parseCauses(parsedJson['causes']) : <Cause>[];
    id = parsedJson['id'];
    images = parsedJson['imagesId'] != null ? parseImages(parsedJson['imagesId']) : <MBImage>[];
    notes = parsedJson['notesId'] != null ? parseNotes(parsedJson['notesId']) : <PetActivity>[];
    periods = parsedJson['periods'] != null ? parsePeriods(parsedJson['periods']) : <Period>[];
    petActivityId = parsedJson['petActivityId'];
    pets = parsedJson['pets'] != null ? parsePets(parsedJson['pets']) : <Pet>[];
    serviceId = parsedJson['serviceId'];
    staffId = parsedJson['staffId'];
    status = parsedJson['status'];
    message = parsedJson['message'];
    comment = parsedJson['comment'];
    captainvetId = parsedJson['captainvetId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['serviceId'] = serviceId;
    data['staffId'] = staffId;
    data['pets'] = pets.map((v) => v.toLightJson()).toList();
    data['causes'] = causes.map((v) => v.toLightJson()).toList();
    data['comment'] = comment;
    data['message'] = message;
    data['petActivityId'] = petActivityId;
    data['status'] = status;
    data['periods'] = periods.map((v) => v.toJson()).toList();
    data['notesId'] = notes.map((v) => v.toLightJson()).toList();
    data['imagesId'] = images.map((v) => v.toLightJson()).toList();
    data['captainvetId'] = captainvetId;
    return data;
  }

  @override
  int compareTo(other) {
    return id.compareTo(other.id);
  }

  List<String> getListIds(var list) {
    //TODO MOVE TO TOOLS or to base model
    List<String> stringIdList = [];
    try {
      for (var object in list) {
        if (object.id != null) stringIdList.add(object.id.toString());
      }
    } catch (exception) {
      // print('Exception in getListIds');
    }
    return stringIdList;
  }

  String getPetListString() {
    String petNames = '';
    for (Pet p in pets) {
      if (petNames != '') {
        petNames += ', ';
      }
      petNames += Data().get().getPet(p.id)?.name ?? '';
    }
    return petNames;
  }

  Map<String, dynamic> toApiJson() {
    final Map<String, dynamic> data = {};
    data['causeId'] = getListIds(causes);
    data['serviceId'] = serviceId;
    data['message'] = message;
    data['staffId'] = staffId;
    data['petIds'] = getListIds(pets);
    List<String> dateStartList = [];
    List<String> dateEndList = [];
    for (Period period in periods) {
      if (period.dateStart != null) {
        dateStartList.add(Data.dateTimeToApiDateTimeStr(period.dateStart));
      }
      if (period.dateEnd != null) {
        dateEndList.add(Data.dateTimeToApiDateTimeStr(period.dateEnd));
      }
    }
    data['dateStart'] = dateStartList;
    data['dateEnd'] = dateEndList;
    data['notesId'] = getListIds(notes);

    return data;
  }

  static String getTextCodeForStatus(int? status) {
    switch (status) {
      case 0:
        return 'APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_REQUESTED';
      case 1:
        return 'APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_REFUSED';
      case 3:
        return 'APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_ACCEPTED';
      case 6:
        return 'APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_CANCELED';
      case 4:
        return 'APPLICATION_MOBILE_TEXT_APPOINTMENT_STATUS_DONE';
    }
    return '';
  }

  static List<Cause> parseCauses(jsonList) {
    List list = jsonList as List;
    List<Cause> objectList = list.map((data) => Cause.fromJson(data)).toList();
    return objectList;
  }

  static List<MBImage> parseImages(jsonList) {
    List list = jsonList as List;
    List<MBImage> objectList = list.map((data) => MBImage.fromJson(data)).toList();
    return objectList;
  }

  static List<PetActivity> parseNotes(jsonList) {
    List list = jsonList as List;
    List<PetActivity> objectList = list.map((data) => PetActivity.fromLightJson(data)).toList();
    return objectList;
  }

  static List<Period> parsePeriods(jsonList) {
    List list = jsonList as List;
    List<Period> objectList = list.map((data) => Period.fromJson(data)).toList();
    return objectList;
  }

  static List<Pet> parsePets(jsonList) {
    List list = jsonList as List;
    List<Pet> objectList = list.map((data) => Pet.fromLightJson(data)).toList();
    return objectList;
  }
}
