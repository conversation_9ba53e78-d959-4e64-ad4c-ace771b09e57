import 'package:mybuddy/extensions/mybuddy_extensions.dart';
import 'package:mybuddy/models/pet.dart';

class GroupLeaderboardMember {
  GroupLeaderboardMember(
      {int? position,
      int? ownerId,
      double? distance,
      int? workouts,
      int? userId,
      String? firstName,
      String? lastName,
      String? userImage,
      String? email,
      List<Pet>? pets,
      bool? isAdmin,
      String? shelter}) {
    _position = position;
    _ownerId = ownerId;
    _distance = distance;
    _workouts = workouts;
    _userId = userId;
    _firstName = firstName;
    _lastName = lastName;
    _userImage = userImage;
    _email = email;
    _shelter = shelter;
    _pets = pets;
    _isAdmin = isAdmin;
  }

  GroupLeaderboardMember.fromJson(dynamic json) {
    _position = json['position'];
    _ownerId = json['owner_id'];
    _distance = json['distance'] is double
        ? json['distance']
        : double.tryParse(json['distance'].toString());
    _workouts = json['workouts'];
    _userId = json['user_id'];
    _firstName = json['firstName'];
    _lastName = json['lastName'];
    _userImage = json['user_image'];
    _email = json['email'];
    if (json['pets'] != null) {
      _pets = [];
      json['pets'].forEach((v) {
        _pets?.add(Pet.fromJson(v));
      });
    }
    _shelter = json['shelter'];
    _isAdmin = json['isAdmin'];
  }

  int? _position;
  int? _ownerId;
  double? _distance;
  int? _workouts;
  int? _userId;
  String? _firstName;
  String? _lastName;
  String? _userImage;
  String? _email;
  List<Pet>? _pets;
  String? _shelter;
  bool? _isAdmin;

  int? get position => _position;

  int? get ownerId => _ownerId;

  double? get distance => _distance;

  String get commaSeperatedDistanceInt =>
      int.parse((distance)?.roundOrNull()?.toString() ?? "0.0").toPointsLong();

  String get commaSeperatedDistance =>
      double.parse((distance)?.toStringAsFixed(2) ?? "0.0").toPointsLong();

  int? get workouts => _workouts;

  int? get userId => _userId;

  String? get firstName => _firstName;

  String? get lastName => _lastName;

  String get fullName =>
      (firstName ?? "") + (lastName != null ? " $lastName" : "");

  String? get email => _email;

  String? get userImage => _userImage;

  List<Pet> get pets => _pets ?? [];

  String get shelter => _shelter ?? "";

  bool get isAdmin => _isAdmin ?? false;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['position'] = _position;
    map['owner_id'] = _ownerId;
    map['distance'] = _distance;
    map['workouts'] = _workouts;
    map['user_id'] = _userId;
    map['firstName'] = _firstName;
    map['lastName'] = _lastName;
    map['user_image'] = _userImage;
    map['email'] = _email;
    map['shelter'] = _shelter;
    map['isAdmin'] = _isAdmin;
    if (_pets != null) {
      map['pets'] = _pets?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class GroupLeaderboardStats {
  int? totalWalks;
  double? totalDistance;
  double? totalTime;
  int? totalMembers;

  GroupLeaderboardStats(
      {this.totalWalks = 0,
      this.totalDistance = 0,
      this.totalTime = 0,
      this.totalMembers = 0});

  GroupLeaderboardStats.fromJson(dynamic json) {
    totalWalks = json['totalWalks'];
    totalDistance = double.parse(json['totalDistance'].toString());
    totalTime = double.parse(json['totalTime'].toString());
    totalMembers = json['members'];
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['totalWalks'] = totalWalks;
    map['totalDistance'] = totalDistance;
    map['totalTime'] = totalTime;
    map['members'] = totalMembers;
    return map;
  }
}
