import 'package:mybuddy/class/data.dart';

/*  TYPE from BO SRC
    public const PET_OWNER_MISC = 0;
    public const PET_OWNER_APPOINTMENT_REQUEST = 1;
    public const PET_OWNER_APPOINTMENT = 2;
    public const PET_OWNER_ANNOUNCEMENT = 3;
    public const PET_OWNER_ORDER = 4;
    public const PET_OWNER_HOSPITALIZATION = 5;
    public const PET_OWNER_REMINDER = 6;
    public const PET_OWNER_PETSMILES = 7;
    public const PET_OWNER_RESTART_AFTER_RECONCILIATION = 8;
    public const PET_OWNER_NOTE = 9;
    public const PET_OWNER_MESSAGE = 10;

    public const BO_USER_MISC = 100;
    public const BO_USER_NEW_CLIENT = 101;
    public const BO_USER_NEW_ORDER = 102;
    public const BO_USER_NEW_APT_REQUEST = 103;
    public const BO_USER_NEW_CLIENT_MESSAGE = 104;
    public const BO_USER_NEW_CLIENT_MESSAGE_2 = 105;
 */
class MBNotification {
  late final int id;
  late final int type;
  int? serviceId;
  int? petActivityId;
  late final String message;
  late bool read;
  DateTime? timestamp;

  MBNotification.fromJson(Map<String, dynamic> parsedJson) {
    id = parsedJson['id'];
    type = parsedJson['type'];
    serviceId = parsedJson['serviceId'];
    petActivityId = parsedJson['petActivityId'];
    message = parsedJson['message'];
    read = parsedJson['read'];
    timestamp = Data.getDate(parsedJson['timestamp']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['type'] = type;
    data['serviceId'] = serviceId;
    data['petActivityId'] = petActivityId;
    data['message'] = message;
    data['read'] = read;
    data['timestamp'] = Data.dateTimeToApiDateTimeStr(timestamp);
    return data;
  }

  MBNotification.fromPwJson(Map<dynamic, dynamic> custom, String? msg) {
    id = 0;
    type = custom['notificationType'];
    petActivityId = custom['notificationId'];
    message = msg ?? '';
    read = false;
    timestamp = Data.getDate(custom['timestamp']);
  }

  @override
  bool operator ==(Object other) {
    return (other is MBNotification) &&
        other.timestamp == timestamp &&
        other.message == message &&
        other.type == type &&
        other.petActivityId == petActivityId;
  }

  @override
  int get hashCode =>
      type.hashCode ^
      petActivityId.hashCode ^
      message.hashCode ^
      timestamp.hashCode;

  @override
  String toString() {
    return 'id: $id, message: $message, type: $type, activity: $petActivityId, timestamp $timestamp ,is read? ${read ? 'yes' : 'no'} ';
  }
}
