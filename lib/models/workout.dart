import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/db/models/workout_db.dart';
import 'package:mybuddy/models/shelter.dart';

import 'pet.dart';

class WorkoutResponse {
  late bool success;
  late String token;
  late String currentLang;
  late int totalPoints;
  late List<Workout> workouts;
  late int totalCount;
  late int currentPageNumber;
  late int numItemsPerPage;
  int? totalWalks;
  double? totalMiles;
  double? totalMinutes;

  WorkoutResponse();

  WorkoutResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    token = json['token'];
    currentLang = json['currentLang'];
    totalPoints = json['totalPoints'];
    if (json['workouts'] != null) {
      workouts = <Workout>[];
      json['workouts'].forEach((v) {
        workouts.add(Workout.fromJson(v));
      });
    } else if (json['data'] != null) {
      workouts = <Workout>[];
      json['data'].forEach((v) {
        workouts.add(Workout.fromJson(v));
      });
    }
    totalCount = json['totalCount'] ?? json["totalRecords"] ?? 0;
    currentPageNumber = json['currentPageNumber'];
    numItemsPerPage = json['numItemsPerPage'];
    totalWalks = json['totalWalks'];
    totalMiles = double.parse(json['totalMiles']?.toString() ?? "0.0");
    totalMinutes = double.parse(json['totalMinutes']?.toString() ?? "0.0");
  }
}

class Workout {
  late final int id;
  int? ownerId;
  String? ownerFirstName;
  String? ownerLastName;
  late double speed = 0.0;
  late double duration = 0.0;
  late double distance = 0.0;
  late List<Pet> pets = <Pet>[];
  String? s3FilenameUrl;
  String? filename;
  DateTime? createdAt;
  DateTime? updatedAt;
  Shelter? shelter;
  int? mood;
  int? points;
  bool winningWalk = false;
  bool isUploaded = true;
  double? startLat;
  double? startLong;
  double? endLat;
  double? endLong;

  Workout();

  Workout.fromJson(Map<String, dynamic> parsedJson) {
    id = parsedJson['id'];
    ownerId = parsedJson.containsKey('ownerId') ? parsedJson['ownerId'] : null;
    ownerFirstName = parsedJson.containsKey('ownerFirstName')
        ? parsedJson['ownerFirstName']
        : null;
    ownerLastName = parsedJson.containsKey('ownerLastName')
        ? parsedJson['ownerLastName']
        : null;
    speed = (parsedJson['speed'])?.toDouble() ?? 0.0;
    duration = (parsedJson['duration'])?.toDouble() ?? 0.0;
    distance = (parsedJson['distance'])?.toDouble() ?? 0.0;
    if (parsedJson.containsKey('pets')) {
      pets = parsePets(parsedJson['pets']);
    }
    s3FilenameUrl = parsedJson['s3FilenameUrl'];
    filename = parsedJson['filename'];
    createdAt = Data.getDate(parsedJson['createdAt']);
    updatedAt = parsedJson['UpdatedAt'] != null
        ? Data.getDate(parsedJson['UpdatedAt'])
        : createdAt;
    if (parsedJson['shelter'] != null) {
      shelter = Shelter.fromJson(parsedJson['shelter']);
    } else {
      shelter = Data().get().owner.shelter;
    }
    mood = parsedJson['mood'];
    points = parsedJson['points'] ?? 0;
    startLat = double.parse(parsedJson['startLat']?.toString() ?? "0.0");
    startLong = double.parse(parsedJson['startLong']?.toString() ?? "0.0");
    endLat = double.parse(parsedJson['endLat']?.toString() ?? "0.0");
    endLong = double.parse(parsedJson['endLong']?.toString() ?? "0.0");
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    data['id'] = id;
    data['speed'] = speed;
    data['duration'] = duration;
    data['distance'] = distance;
    data['pets'] = pets.map((v) => v.toJson()).toList();
    data['s3FilenameUrl'] = s3FilenameUrl;
    data['filename'] = filename;
    data['createdAt'] = Data.dateTimeToApiDateTimeStr(createdAt);
    data['UpdatedAt'] = Data.dateTimeToApiDateTimeStr(updatedAt);
    data['mood'] = mood;
    data['points'] = points;
    data['shelter'] = shelter?.toJson();
    data['startLat'] = startLat;
    data['startLong'] = startLong;
    data['endLat'] = endLat;
    data['endLong'] = endLong;

    return data;
  }

  Workout.fromWorkoutDB(WorkoutDb workoutDb) {
    id = workoutDb.workoutId ?? 0;
    ownerId = null;
    ownerFirstName = null;
    ownerLastName = null;
    speed = workoutDb.speed;
    duration = workoutDb.duration;
    distance = workoutDb.distance;
    pets = []; // Will be populated after wards
    filename = workoutDb.gpxFileName;
    createdAt = workoutDb.createdAt;
    updatedAt = workoutDb.updatedAt;
    shelter = Shelter.fromJson({'name': workoutDb.charityName});
    mood = workoutDb.moodType;
    points = workoutDb.points;
    isUploaded = workoutDb.isUploaded == 1;
    startLat = workoutDb.startLat;
    startLong = workoutDb.startLong;
    endLat = workoutDb.endLat;
    endLong = workoutDb.endLong;
  }

  List<Pet> getFilledPets() {
    return pets;
  }

  static List<Workout> parseWorkouts(jsonList) {
    List list = jsonList as List;
    List<Workout> objectList =
        list.map((data) => Workout.fromJson(data)).toList();
    return objectList;
  }

  static List<Pet> parsePets(jsonList) {
    List list = jsonList as List;
    List<Pet> objectList =
        list.map((data) => Pet.fromBaseEntityJson(data)).toList();
    return objectList;
  }
}
