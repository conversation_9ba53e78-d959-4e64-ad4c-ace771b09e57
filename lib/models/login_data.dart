import 'package:collection/collection.dart' show IterableExtension;
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/models/file.dart';
import 'package:mybuddy/models/message.dart';
import 'package:mybuddy/models/notification.dart';
import 'package:mybuddy/models/stats.dart';
import 'package:mybuddy/models/task.dart';
import 'package:mybuddy/models/text_banners.dart';

import 'announcement.dart';
import 'appointment_request.dart';
import 'challenge.dart';
import 'global_constants.dart';
import 'order.dart';
import 'owner.dart';
import 'pet.dart';
import 'pet_activity.dart';
import 'reward.dart';
import 'service.dart';
import 'workout.dart';

class LoginData {

  /// This field will be removed in Future because it is belongs to myBuddy
  late List<AppointmentRequest> appointmentRequests = <AppointmentRequest>[];
  late String currentLang = 'us';

  late Owner owner = Owner();

  /// This field will be removed in Future, from now it is implemented with separate controllers (PetGalleryController, <PERSON><PERSON><PERSON>ght<PERSON>ontroller, PetWorkoutController)
  late List<PetActivity> petActivities = <PetActivity>[];
  late List<Pet> pets = <Pet>[];
  late List<Pet> deletedPets = <Pet>[];

  /// This field will be removed in Future because it is belongs to myBuddy
  late List<MBService> services = <MBService>[];
  late List<MBNotification> notifications = <MBNotification>[];

  /// This field will be removed in Future because it is belongs to myBuddy
  late List<MBMessage> messages = <MBMessage>[];
  late bool success = false;
  late List<Workout> workouts = <Workout>[];
  late List<Team> teams = <Team>[];
  late List<Task> tasks = <Task>[];
  late List<Challenger> challengers = <Challenger>[];
  late String token = '';
  late Stats stats = Stats();
  late TextBanners textBanners = TextBanners();
  late GlobalConstants globalConstants = GlobalConstants();

  LoginData();

  LoginData.fromJson(Map<String, dynamic> parsedJson) {

    appointmentRequests = <AppointmentRequest>[];

    try {
      currentLang = parsedJson['currentLang'];
    } catch (e) {
      Tools.debugPrint('problem with lang');
      currentLang = 'us';
    }

    try {
      owner = Owner.fromJson(parsedJson['owner']);
    } catch (e) {
      Tools.debugPrint('problem with owner');
      owner = Owner();
    }

    petActivities = <PetActivity>[];

    try {
      pets = parsePets(parsedJson['pets']);
    } catch (e) {
      Tools.debugPrint('problem with pets');
      pets = <Pet>[];
    }

    try {
      deletedPets = parsePets(parsedJson['deletedPets']);
    } catch (e) {
      Tools.debugPrint('problem with deleted pets');
      deletedPets = <Pet>[];
    }

    services = <MBService>[];

    notifications = <MBNotification>[];

    success = parsedJson['success'];

    messages = <MBMessage>[];

//    timeDebug = parsedJson['time_debug'];
    try {
      token = parsedJson['token'];
    } catch (e) {
      Tools.debugPrint('problem with token');
      token = '';
    }

    try {
      workouts = parseWorkouts(parsedJson['workouts']);
    } catch (e) {
      Tools.debugPrint('problem with workouts');
      workouts = <Workout>[];
    }

    try {
      teams = parseTeams(parsedJson['teams']);
    } catch (e) {
      Tools.debugPrint('problem with teams');
      teams = <Team>[];
    }

    tasks = <Task>[];

    try {
      challengers = Challenger.parse(parsedJson['challengers']);
    } catch (e) {
      Tools.debugPrint('problem with challengers');
      challengers = <Challenger>[];
    }

    try {
      stats = Stats.fromJson(parsedJson['stats']);
    } catch (e) {
      Tools.debugPrint('problem with stats');
      stats = Stats();
    }

    try {
      textBanners = TextBanners.fromJson(parsedJson['textBanners']);
    } catch (e) {
      Tools.debugPrint('problem with textBanners');
      textBanners = TextBanners();
    }

    try {
      globalConstants = GlobalConstants.fromJson(parsedJson['globalConstants']);
    } catch (e) {
      Tools.debugPrint('problem with globalConstants');
      globalConstants = GlobalConstants();
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['success'] = success;
    data['token'] = token;
    data['currentLang'] = currentLang;
    data['owner'] = owner.toJson();
    data['pets'] = pets.map((v) => v.toJson()).toList();
    data['deletedPets'] = deletedPets.map((v) => v.toJson()).toList();
    data['petActivities'] = petActivities.map((v) => v.toJson()).toList();
    data['services'] = services.map((v) => v.toJson()).toList();
    data['appointmentRequests'] =
        appointmentRequests.map((v) => v.toJson()).toList();
    data['notifications'] = notifications.map((v) => v.toJson()).toList();
    data['messages'] = messages.map((v) => v.toJson()).toList();
    data['workouts'] = workouts.map((w) => w.toJson()).toList();
    data['teams'] = teams.map((t) => t.toJson()).toList();
    data['tasks'] = tasks.map((t) => t.toJson()).toList();
    data['challengers'] = challengers.map((c) => c.toJson()).toList();
    data['stats'] = stats.toJson();
    data['textBanner'] = textBanners.toJson();
    data['globalConstants'] = globalConstants.toJson();
    return data;
  }

  AppointmentRequest? getAppointmentRequest(int? aptRequestId) {
    if (aptRequestId == null) {
      return null;
    }
    return appointmentRequests.singleWhereOrNull((f) => f.id == aptRequestId);
  }

  MBService? getService(int? serviceId) {
    if (serviceId == null) {
      return null;
    }
    MBService? service =
        services.singleWhereOrNull((service) => service.id == serviceId);
    if (service == null) {
      for (MBService s in services) {
        if (s.services != null && s.services!.isNotEmpty) {
          for (MBService element in s.services!) {
            if (serviceId == element.id) {
              service = element;
            }
          }
        }
      }
    }
    return service;
  }

  MBMessage? getMessage(int? id) {
    if (id == null) {
      return null;
    }
    return messages.singleWhereOrNull((f) => f.id == id);
  }

  Pet? getPet(int? petId) {
    if (petId == null) {
      return null;
    }
    return pets.singleWhereOrNull((f) => f.id == petId);
  }

  PetActivity? getPetActivity(int? paId) {
    if (paId == null) {
      return null;
    }
    return petActivities.singleWhereOrNull((f) => f.id == paId);
  }

  Team? getTeam(int? teamId) {
    if (teamId == null) {
      return null;
    }
    return teams.singleWhereOrNull((t) => t.id == teamId);
  }

  List<MBMessage> getMessagesGrouped({MBService? service}) {
    List<MBMessage> fromClinic = <MBMessage>[];
    List<MBMessage> fromTeam = <MBMessage>[];
    List<MBMessage> fromWhere = <MBMessage>[];

    ///if message is from nowhere

    for (MBMessage m in messages) {
      ///only message from specific service
      if (service != null) {
        if (m.serviceId == service.id) {
          fromClinic.add(m);
        }
      }

      ///all messages
      else if (m.serviceId != null) {
        fromClinic.add(m);
      } else if (teams.any((t) => t.messageId == m.id)) {
        fromTeam.add(m);
      } else {
        fromWhere.add(m);
      }
    }
    fromClinic.sort();
    fromTeam.sort();

    return <MBMessage>[
      ...fromClinic.reversed.toList(),
      ...fromTeam.reversed.toList(),
      ...fromWhere
    ];
  }

  bool challengeSubscribed(int? id) {
    if (id == null || challengers.isEmpty) {
      return false;
    }

    return challengers.any((challenger) => challenger.ownerChallengeId == id);
  }

  bool challengeCompleted(String? code) {
    if (code == null) {
      return false;
    }

    return tasks.any((t) => t.code == code);
  }

  List<Reward> ownedRewards() {
    List<Reward> results = [];
    for (Task t in tasks) {
      if (t.owned.isNotEmpty) {
        results.addAll(t.owned);
      }
    }

    return results;
  }

  List<Pet> get hospitalizedPets =>
      pets.where((pet) => pet.hospitalized).toList();

  static List<Announcement> parseAnnouncements(jsonList) {
    List list = jsonList as List;
    List<Announcement> objectList =
        list.map((data) => Announcement.fromJson(data)).toList();
    return objectList;
  }

  static List<AppointmentRequest> parseAptRequest(jsonList) {
    List list = jsonList as List;
    List<AppointmentRequest> objectList =
        list.map((data) => AppointmentRequest.fromJson(data)).toList();
    return objectList;
  }

  static List<MBFile> parseFiles(jsonList) {
    List list = jsonList as List;
    List<MBFile> objectList =
        list.map((data) => MBFile.fromJson(data)).toList();
    return objectList;
  }

  static List<MBMessage> parseMessages(jsonList) {
    List list = jsonList as List;
    List<MBMessage> objectList =
        list.map((data) => MBMessage.fromJson(data)).toList();
    return objectList;
  }

  static List<MBNotification> parseNotifications(jsonList) {
    List list = jsonList as List;
    List<MBNotification> objectList =
        list.map((data) => MBNotification.fromJson(data)).toList();
    return objectList;
  }

  static List<Order> parseOrders(jsonList) {
    List list = jsonList as List;
    List<Order> objectList = list.map((data) => Order.fromJson(data)).toList();
    return objectList;
  }

  static List<PetActivity> parsePetActivities(jsonList) {
    List list = jsonList as List;
    List<PetActivity> objectList =
        list.map((data) => PetActivity.fromJson(data)).toList();
    return objectList;
  }

  static List<Pet> parsePets(jsonList) {
    List list = jsonList as List;
    List<Pet> objectList = list.map((data) => Pet.fromJson(data)).toList();
    return objectList;
  }

  static List<MBService> parseServices(jsonList) {
    List list = jsonList as List;
    List<MBService> objectList =
        list.map((data) => MBService.fromJson(data)).toList();
    return objectList;
  }

  static List<Workout> parseWorkouts(jsonList) {
    List list = jsonList as List;
    List<Workout> objectList =
        list.map((data) => Workout.fromJson(data)).toList();
    return objectList;
  }

  static List<Team> parseTeams(jsonList) {
    List list = jsonList as List;
    List<Team> objectList = list.map((data) => Team.fromJson(data)).toList();
    return objectList;
  }

  static List<Task> parseTasks(jsonList) {
    List list = jsonList as List;

    return list.map((data) => Task.fromJson(data)).toList();
  }

//  useless use list and count
//  bool canOrder(){
//      return services.where((f) => (f.acceptOrder == true && f.official).isNotEmpty;
//  }
}
