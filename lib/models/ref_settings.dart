import 'dart:convert';

RefSettings referenceSettingsFromJson(String str) =>
    RefSettings.fromJson(json.decode(str));
String referenceSettingsToJson(RefSettings data) => json.encode(data.toJson());

class RefSettings {
  bool? success;
  String? token;
  String? currentLang;
  List<String> kickboxAllowedResponses = [];
  List<KickboxResponses> kickboxResponses = [];
  String? deleteAccountPageContent;

  RefSettings(
      {this.success,
      this.token,
      this.currentLang,
      this.kickboxAllowedResponses = const [],
      this.deleteAccountPageContent,
      this.kickboxResponses = const []});

  RefSettings.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    token = json['token'];
    currentLang = json['currentLang'];
    kickboxAllowedResponses = json['kickboxAllowedResponses'].cast<String>();
    if (json['kickboxResponses'] != null) {
      kickboxResponses = <KickboxResponses>[];
      json['kickboxResponses'].forEach((v) {
        kickboxResponses.add(new KickboxResponses.fromJson(v));
      });
    }
    deleteAccountPageContent = json['deleteAccountPageContent'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['token'] = this.token;
    data['currentLang'] = this.currentLang;
    data['kickboxAllowedResponses'] = this.kickboxAllowedResponses;
    if (this.kickboxResponses != null) {
      data['kickboxResponses'] =
          this.kickboxResponses.map((v) => v.toJson()).toList();
    }
    data['deleteAccountPageContent'] = this.deleteAccountPageContent;
    return data;
  }
}

class KickboxResponses {
  String? type;
  String? message;

  KickboxResponses({this.type, this.message});

  KickboxResponses.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    data['message'] = this.message;
    return data;
  }
}
