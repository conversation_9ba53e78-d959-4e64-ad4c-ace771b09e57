class GlobalConstants {
  int? charityPointsPerWorkout;
  int? charityPointsLimitPerDay;
  int? maxSpeedLimitToEarnPoints;
  double? minimumDistanceInMilesLimitToEarnCharityPoints;
  int? dailyWalkGoalDefault;
  int? dailyDistanceMilesGoalDefault;
  int? itemsPerPage;
  String? dateFormat;
  String? timeFormat;
  String? dateTimeFormat;

  GlobalConstants(
      {this.charityPointsPerWorkout,
        this.charityPointsLimitPerDay,
        this.maxSpeedLimitToEarnPoints,
        this.minimumDistanceInMilesLimitToEarnCharityPoints,
        this.dailyWalkGoalDefault,
        this.dailyDistanceMilesGoalDefault,
        this.itemsPerPage,
        this.dateFormat,
        this.timeFormat,
        this.dateTimeFormat});

  GlobalConstants.fromJson(Map<String, dynamic> json) {
    charityPointsPerWorkout = json['CHARITY_POINTS_PER_WORKOUT'];
    charityPointsLimitPerDay = json['CHARITY_POINTS_LIMIT_PER_DAY'];
    maxSpeedLimitToEarnPoints = json['MAX_SPEED_LIMIT_TO_EARN_POINTS'];
    minimumDistanceInMilesLimitToEarnCharityPoints =
    json['MINIMUM_DISTANCE_IN_MILES_LIMIT_TO_EARN_CHARITY_POINTS'];
    dailyWalkGoalDefault = json['DAILY_WALK_GOAL_DEFAULT'];
    dailyDistanceMilesGoalDefault = json['DAILY_DISTANCE_MILES_GOAL_DEFAULT'];
    itemsPerPage = json['ITEMS_PER_PAGE'];
    dateFormat = json['DATE_FORMAT'];
    timeFormat = json['TIME_FORMAT'];
    dateTimeFormat = json['DATE_TIME_FORMAT'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['CHARITY_POINTS_PER_WORKOUT'] = charityPointsPerWorkout;
    data['CHARITY_POINTS_LIMIT_PER_DAY'] = charityPointsLimitPerDay;
    data['MAX_SPEED_LIMIT_TO_EARN_POINTS'] = maxSpeedLimitToEarnPoints;
    data['MINIMUM_DISTANCE_IN_MILES_LIMIT_TO_EARN_CHARITY_POINTS'] =
        minimumDistanceInMilesLimitToEarnCharityPoints;
    data['DAILY_WALK_GOAL_DEFAULT'] = dailyWalkGoalDefault;
    data['DAILY_DISTANCE_MILES_GOAL_DEFAULT'] =
        dailyDistanceMilesGoalDefault;
    data['ITEMS_PER_PAGE'] = itemsPerPage;
    data['DATE_FORMAT'] = dateFormat;
    data['TIME_FORMAT'] = timeFormat;
    data['DATE_TIME_FORMAT'] = dateTimeFormat;
    return data;
  }
}
