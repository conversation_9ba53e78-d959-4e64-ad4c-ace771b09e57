import 'package:mybuddy/class/data.dart';

class Period {
  DateTime? dateEnd;
  DateTime? dateStart;

  Period();

  Period.fromJson(Map<String, dynamic> parsedJson) {
    dateEnd = Data.getDate(parsedJson['dateEnd']);
    dateStart = Data.getDate(parsedJson['dateStart']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['dateStart'] = Data.dateTimeToApiDateTimeStr(dateStart);
    data['dateEnd'] = Data.dateTimeToApiDateTimeStr(dateEnd);
    return data;
  }
}
