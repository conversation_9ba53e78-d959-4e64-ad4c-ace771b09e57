import 'package:mybuddy/extensions/mybuddy_extensions.dart';

class HomeSummary {
  const HomeSummary({
    required this.success,
    required this.token,
    required this.currentLang,
    required this.totalPoints,
    required this.totalWalks,
    required this.totalDistance,
    required this.totalMinutes,
    required this.totalMigratedCharityPoints,
  });

  final bool success;
  final String token;
  final String currentLang;
  final int totalPoints;
  final int totalWalks;
  final double totalDistance;
  final double totalMinutes;
  final int totalMigratedCharityPoints;

  factory HomeSummary.fromJson(dynamic json) => HomeSummary(
        success: json['success'],
        token: json['token'],
        currentLang: json['currentLang'],
        totalWalks: json['totalWalks'],
        totalDistance: double.parse(json['totalDistance'].toString()),
        totalPoints: json['totalPoints'],
        totalMinutes: double.parse(json['totalMinutes'].toString()),
        totalMigratedCharityPoints: json['totalMigratedCharityPoints'],
      );

  Map<String, dynamic> toDbJson() => <String, dynamic>{
        'id': 1,
        'totalPoints': totalPoints,
        'totalWalks': totalWalks,
        'totalDistance': totalDistance,
        'totalMinutes': totalMinutes,
        'totalMigratedCharityPoints': totalMigratedCharityPoints
      };

  String get totalDistanceWithOneDecimal => totalDistance.toStringAsFixed(1);
  String get totalPointsInUSFormat => totalPoints.toPointsLong();
}
