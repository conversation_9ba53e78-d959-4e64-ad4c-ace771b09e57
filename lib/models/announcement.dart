import 'package:mybuddy/extensions/number_extensions.dart';

class Announcement implements Comparable {
  late final String? description;
  late final int forced;
  late final int? gender;
  late final int id;
  late final int? imageId;
  late final String? imageUrl;
  late final int? maxAge;
  late final int? maxWeight;
  late final int? minAge;
  late final int? minWeight;
  late final String publishEndDate;
  late final String publishStartDate;
  late final int? serviceId;
  late final int status;
  late final int? sterilized;
  late final String? summary;
  late final int? summaryThumbnailImageId;
  late final String? summaryThumbnailImageUrl;
  late final int targetOwner;
  late final int targetService;
  late final String title;
  late final String? url;

  Announcement.fromJson(Map<String, dynamic> parsedJson) {
    description = parsedJson['description'];
    forced = parsedJson['forced'];
    gender = parsedJson['gender'];
    id = parsedJson['id'];
    imageId = parsedJson['imageId'];
    imageUrl = parsedJson['imageUrl'];
    maxAge = parsedJson['maxAge'];
    maxWeight = parsedJson['maxWeight'];
    minAge = parsedJson['minAge'];
    minWeight = parsedJson['minWeight'];
    publishEndDate = parsedJson['publishEndDate'];
    publishStartDate = parsedJson['publishStartDate'];
    serviceId = parsedJson['serviceId'];
    status = parsedJson['status'];
    sterilized = parsedJson['sterilized'];
    summary = parsedJson['summary'];
    summaryThumbnailImageId = parsedJson['summaryThumbnailImageId'];
    summaryThumbnailImageUrl = parsedJson['summaryThumbnailImageUrl'];
    targetOwner = parsedJson['targetOwner'];
    targetService = parsedJson['targetService'];
    title = parsedJson['title'];
    url = parsedJson['url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['serviceId'] = serviceId;
    data['status'] = status;
    data['forced'] = forced;
    data['title'] = title;
    data['summary'] = summary;
    data['description'] = description;
    data['url'] = url;
    data['imageId'] = imageId;
    data['imageUrl'] = imageUrl;
    data['summaryThumbnailImageId'] = summaryThumbnailImageId;
    data['summaryThumbnailImageUrl'] = summaryThumbnailImageUrl;
    data['gender'] = gender;
    data['minAge'] = minAge;
    data['maxAge'] = maxAge;
    data['minWeight'] = minWeight;
    data['maxWeight'] = maxWeight;
    data['sterilized'] = sterilized;
    data['targetOwner'] = targetOwner;
    data['targetService'] = targetService;
    data['publishStartDate'] = publishStartDate;
    data['publishEndDate'] = publishEndDate;
    return data;
  }

  @override
  int compareTo(other) {
    return other.publishStartDate.compareTo(publishStartDate);
  }

  String get mainImageUrl {
    String? url = imageUrl;
    return url ?? '';
  }

  String get thumbnailImageUrl {
    String? url = summaryThumbnailImageUrl;
    return url ?? '';
  }
}
