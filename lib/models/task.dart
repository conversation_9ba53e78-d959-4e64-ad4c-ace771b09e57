import 'dart:ui';

import 'package:mybuddy/models/reward.dart';


class Task {
  late String code;
  late int points;
  late int maxCount;
  late int frequency;
  late int ownerTaskCount;
  late int ownerTaskPoints;
  late List<Reward> owned;

  Task.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    points = json['points'] ?? 0;
    maxCount = json['maxCount'] ?? 0;
    frequency = json['frequency'] ?? 0;
    ownerTaskCount = json['ownerTaskCount'] ?? 0;
    ownerTaskPoints = json['ownerTaskPoints'] ?? 0;
    owned = <Reward>[];
    if (json.containsKey('owned') && json['owned'] != null) {
      json['owned'].forEach((earnedReward) {
        owned.add(Reward.fromJson(earnedReward));
      });
    }
  }

  static List<Task> parse(json) {
    List list = json as List;
    return list.map((e) => Task.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['code'] = code;
    _data['points'] = points;
    _data['maxCount'] = maxCount;
    _data['frequency'] = frequency;
    _data['ownerTaskCount'] = ownerTaskCount;
    _data['ownerTaskPoints'] = ownerTaskPoints;
    _data['owned'] = owned.map((r) => r.toJson()).toList();

    return _data;
  }

  @override
  bool operator == (other) {
    return (other is Task) &&
        other.code == code &&
        other.ownerTaskCount == ownerTaskCount &&
        other.ownerTaskPoints == ownerTaskPoints &&
        other.owned.length == owned.length;
  }

  @override
  int get hashCode => hashValues(code, points);
}
