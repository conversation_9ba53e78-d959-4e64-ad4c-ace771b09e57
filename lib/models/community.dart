import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';

import 'owner.dart';

class Team {
  int? id;
  late String name;
  String? adminFirstName;
  String? adminLastName;
  String? description;
  int? imageId;
  String? imageUrl;
  late bool everyoneCanInvite;
  late bool isAdmin;
  late bool canInvite;
  String? link;
  int? messageId;
  int? teamMembersCount;
  int? totalWalks;
  double? totalMiles;
  DateTime? createdAt;
  late bool accessibility;

  Team() {
    name = '';
    everyoneCanInvite = false;
    isAdmin = true;
    canInvite = true;
    accessibility = false;
  }

  Team.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    imageId = json['imageId'];
    imageUrl = json['imageUrl'];
    everyoneCanInvite = json['everyoneCanInvite'];
    isAdmin = json['isAdmin'];
    canInvite = json['canInvite'];
    link = json['link'];
    if (json.containsKey('messageId')) {
      messageId = json['messageId'];
    }
    teamMembersCount = json['teammates'] ?? 0;
    totalWalks = json['totalWalks'] ?? 0;
    totalMiles = json['totalMiles'].toDouble() ?? 0.0;
    adminFirstName = json['adminFirstName'];
    adminLastName = json['adminLastName'];
    createdAt = json['groupCreatedAt'] != null
        ? DateTime.parse(json['groupCreatedAt'])
        : null;
    accessibility = json['accessibility'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['imageId'] = imageId;
    data['imageUrl'] = imageUrl;
    data['everyoneCanInvite'] = everyoneCanInvite;
    data['isAdmin'] = isAdmin;
    data['canInvite'] = canInvite;
    data['link'] = link;
    data['messageId'] = messageId;
    if (createdAt != null) {
      data['groupCreatedAt'] = Data.dateTimeToApiDateTimeStr(createdAt);
    }
    if (adminLastName != null) {
      data['adminLastName'] = adminLastName;
    }
    if (adminFirstName != null) {
      data['adminFirstName'] = adminFirstName;
    }
    data['accessibility'] = accessibility;

    return data;
  }

  Map<String, dynamic> toJsonApi() {
    final Map<String, dynamic> data = <String, dynamic>{};

    data['name'] = name;
    data['description'] = description;
    data['everyoneCanInvite'] = everyoneCanInvite ? '1' : '0';

    return data;
  }
}

class Friend {
  int? ownerId;
  String? ownerLastName;
  String? ownerFirstName;
  String? ownerEmail;
  int? ownerTotalPoints;
  int? shelterId;
  String? shelterName;
  int? ownerImageId;
  String? ownerImageUrl;
  int? position;
  bool isAdmin = false;
  bool isTeamMember =
      false; // this is used to show the WoofTrax user status that it is already added in the pack or not while fetching the contact list
  String avatarName = 'wt-avatar-0';
  bool isActiveWalker = true;

  Friend({this.ownerEmail});

  Friend.fromJson(Map<String, dynamic> json) {
    ownerId = json['ownerId'];
    ownerLastName = json['ownerLastName'] ?? '';
    ownerFirstName = json['ownerFirstName'] ?? '';
    ownerEmail = json['ownerEmail'];
    ownerTotalPoints = json['ownerTotalPoints'];
    shelterId = json['shelterId'];
    shelterName = json['shelterName'] ?? '';
    ownerImageId = json['ownerImageId'];
    ownerImageUrl = json['ownerImageUrl'];
    position = json['position'];
    isAdmin = json['isAdmin'];
    isTeamMember = json['isTeamMember'];
    avatarName = json['avatarName'] ?? 'wt-avatar-0';
    isActiveWalker = json['isActiveWalker'] ?? false;
  }

  Friend.fromConnectedOwner() {
    Owner owner = Data().get().owner;

    ownerId = owner.id;
    ownerLastName = owner.lastName;
    ownerFirstName = owner.firstName;
    ownerEmail = owner.email;
    ownerTotalPoints = 0; //todo implement points
    shelterId = owner.shelter?.id;
    shelterName = owner.shelter?.name;
    ownerImageId = owner.imageId;
    ownerImageUrl = owner.imageUrl;
    avatarName = owner.avatar.code;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ownerId'] = ownerId;
    data['ownerLastName'] = ownerLastName;
    data['ownerFirstName'] = ownerFirstName;
    data['ownerEmail'] = ownerEmail;
    data['ownerTotalPoints'] = ownerTotalPoints;
    data['shelterId'] = shelterId;
    data['shelterName'] = shelterName;
    data['ownerImageId'] = ownerImageId;
    data['ownerImageUrl'] = ownerImageUrl;
    data['position'] = position;
    data['isAdmin'] = isAdmin;
    data['isTeamMember'] = isTeamMember;
    data['avatarName'] = avatarName;
    data['isActiveWalker'] = isActiveWalker;
    return data;
  }

  static List<Friend> parseFriends(jsonList) {
    List list = jsonList as List;
    return list.map((data) => Friend.fromJson(data)).toList();
  }

  static Map<String, dynamic> unregisteredToMapList(Friend friend) {
    Map<String, dynamic> data = <String, dynamic>{};
    data['givenName'] = friend.ownerFirstName;
    data['familyName'] = friend.ownerLastName;
    data['email'] = friend.ownerEmail;

    return data;
  }

  String get initials {
    String f = ownerFirstName != null && ownerFirstName!.isNotEmpty
        ? ownerFirstName!.substring(0, 1).toUpperCase()
        : '';
    String l = ownerLastName != null && ownerLastName!.isNotEmpty
        ? ownerLastName!.substring(0, 1).toUpperCase()
        : '';

    return f + l;
  }

  String get fullName {
    String f = ownerFirstName != null ? ownerFirstName!.capitalize() : '';
    String l = ownerLastName != null ? ownerLastName!.capitalize() : '';

    return '$f $l'.trim();
  }

  @override
  bool operator ==(Object other) {
    return super == other && other is Friend && other.ownerEmail == ownerEmail;
  }

  @override
  int get hashCode => ownerEmail.hashCode;
}

class InvitedUser {
  String? email;
  String? firstName;
  String? lastName;
  LastInvite? lastInvite;
  String? userExist;

  InvitedUser(
      {this.email,
      this.firstName,
      this.lastName,
      this.lastInvite,
      this.userExist});

  InvitedUser.fromJson(Map<String, dynamic> json) {
    email = json['email'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    lastInvite = json['lastInvite'] != null
        ? LastInvite.fromJson(json['lastInvite'])
        : null;
    userExist = json['userExist'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['email'] = email;
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    if (lastInvite != null) {
      data['lastInvite'] = lastInvite!.toJson();
    }
    data['userExist'] = userExist;
    return data;
  }

  static List<InvitedUser> parseInvitedUsers(jsonList) {
    List list = jsonList as List;
    return list.map((data) => InvitedUser.fromJson(data)).toList();
  }
}

class LastInvite {
  String? date;
  int? timezoneType;
  String? timezone;

  LastInvite({this.date, this.timezoneType, this.timezone});

  LastInvite.fromJson(Map<String, dynamic> json) {
    date = json['date'];
    timezoneType = json['timezone_type'];
    timezone = json['timezone'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['date'] = date;
    data['timezone_type'] = timezoneType;
    data['timezone'] = timezone;
    return data;
  }
}

class InvitationStatus {
  bool success;
  String message;

  InvitationStatus(this.success, this.message);
}
