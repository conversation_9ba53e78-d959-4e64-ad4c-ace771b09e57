import 'dart:async';

import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_com.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/announcement.dart';
import 'package:mybuddy/models/appointment_request.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/models/message.dart';
import 'package:mybuddy/models/notification.dart';
import 'package:mybuddy/models/order.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/models/reward.dart';
import 'package:mybuddy/ui/components/earning_points_footer_component.dart';
import 'package:mybuddy/ui/components/mb_asset_badge.dart';
import 'package:mybuddy/ui/templates/account/owner_profile_page.dart';
import 'package:mybuddy/ui/templates/announcement/announcement_page.dart';
import 'package:mybuddy/ui/templates/apt/appointment_page.dart';
import 'package:mybuddy/ui/templates/community/community_team_screen.dart';
import 'package:mybuddy/ui/templates/message/message_page.dart';
import 'package:mybuddy/ui/templates/pet/diary/petactivity_page.dart';
import 'package:collection/collection.dart' show IterableExtension;

class PushNotificationTools extends CommonTools {
  StreamSubscription? receivedSubscription;
  StreamSubscription? acceptedSubscription;
  bool isInitialized = false; //to avoid first launch crash

  PushNotificationTools();

  Future<void> handleNotification(MBNotification notification) async {
    if (await showPushPopup(notification, Tools().homeContext)) {
      if (!notification.read) {
        await MbApiCom().setReadNotificationRequest(null, notification);
      }
      Widget? page = await _openNotification(notification);
      if (page != null) {
        await Tools().navigatorPush(page);
      }
    }
  }

//  void _debugToken() async {
//    var token = await Pushwoosh.getInstance.getPushToken;
//    String tokenStr = token.toString();
//    Tools.debugPrint("token: $tokenStr");
//  }

  void showNotFound() {
    showMessage(Tools().homeContext, 'APPLICATION_MOBILE_TEXT_EMPTY_DATA'.tr());
  }

  Future<bool> showPushPopup(MBNotification notification, context) async {
    bool? result = await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext ctx) {
        return AlertDialog(
          title: Text('APPLICATION_MOBILE_LABEL_NOTIFICATION'.tr()),
          content: _pushPopupContent(context, notification),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Tools().navigatorPop(removeLast: false, value: false);
              },
              child: Text('APPLICATION_MOBILE_BUTTON_LABEL_CLOSE'.tr()),
            ),
            if (notification.petActivityId != null)
              TextButton(
                onPressed: () {
                  Tools().navigatorPop(removeLast: false, value: true);
                },
                child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SEE'.tr()),
              ),

            ///endif
          ],
        );
      },
    );

    return result ?? false;
  }

  Future<void> _onNotification(var event, String from) async {
    try {
      await MbApiOwner().refreshLoginRequest();
      MBNotification _notification = MBNotification.fromPwJson(
        event.pushwooshMessage.payload['userdata']['custom'],
        event.pushwooshMessage.title,
      );
      Data().addNotificationFromPush(_notification);

      if (_notification.type == 10 &&

                  /// already in good view (messaging)
                  Tools().routes.isNotEmpty &&
                  Tools().routes.last ==
                      'MessagePage-${_notification.petActivityId}' ||
              _notification.message.trim() == ''

          /// or silent notification
          ) {
        return;
      }
      await handleNotification(_notification);
    } catch (e) {
      Tools.debugPrint('error in push message');
    }
//    Tools.debugPrint('----------------------------------------------------------------------------$from --------------------------------------------');
//    Tools.debugPrint('$from push received ! '+event.pushwooshMessage.title);
//    event.pushwooshMessage.payload.forEach((key,value){
//      Tools.debugPrint('$from payload $key => $value');
//    });
//    Tools.debugPrint('-----------------------');
//    Tools.debugPrint(_notification);
//    Tools.debugPrint('--------------------------------------------------------------------------------------------------------------------------');
  }

  Future<Widget?> _openNotification(MBNotification notification) async {
    switch (notification.type) {
      case 1: //apt request
        AppointmentRequest? aptRequest =
            Data().get().getAppointmentRequest(notification.petActivityId);
        if (aptRequest == null) {
          showNotFound();
          break;
        }
        return AppointmentPage(appointment: aptRequest);
      case 2: // apt
        AppointmentRequest? aptRequest =
            Data().getAppointmentWithPetActivity(notification.petActivityId);
        if (aptRequest == null) {
          showNotFound();
          break;
        }
        return AppointmentPage(appointment: aptRequest);
      case 3: // announcement
        Announcement? announcement;
        if (announcement == null) {
          showNotFound();
          break;
        }
        return AnnouncementPage(announcement: announcement);
      case 5: //hospi
      case 6: //reminder
      case 9: //
        PetActivity? petActivity =
            Data().get().getPetActivity(notification.petActivityId);
        if (petActivity == null) {
          showNotFound();
          break;
        }
        return PetActivityPage(petActivity: petActivity);
      case 10:
        MBMessage? message =
            Data().get().getMessage(notification.petActivityId);
        if (message == null) {
          showNotFound();
          break;
        }
        return MessagePage(message: message);
      case 11:

        ///new notifications type
        Team? team =
            await Tools().team.showJoiningTeamDialog(notification.petActivityId);
        if (team == null) {
          break;
        }
        return TeamScreen(team: team);
      case 12:
      case 13: //TODO determine which page sending winning challenge
      case 14:
        ///new notifications type Earn badge
        /// 1 if only 3 tabs otherwise 2
        return const OwnerProfilePage(selectedTab: 1);
      case 15:
        /// team invitation has been accepted: notify all teammates and update
        Team? team = await Tools().team.update(notification.petActivityId);
        if (team == null) {
          break;
        }
        return TeamScreen(team: team);
    }
    return null;
  }

  Widget _pushPopupContent(BuildContext context, MBNotification notification) {
    /// different dialog for notifications type earned badges
    if (notification.type == 12 && notification.petActivityId != null) {
      Reward referenceReward = Data().get().ownedRewards().singleWhere(
            (ownedReward) => ownedReward.id == notification.petActivityId,
            orElse: () => Reward.unknown(),
          );

      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            notification.message,
            style: Theme.of(context).textTheme.bodyText1,
            textAlign: TextAlign.center,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: AssetReward(reward: referenceReward, size: 87.0),
          ),
          Text(
            referenceReward.title,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyText1,
          ),
          Text(
            referenceReward.desc,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.subtitle2,
          ),
          if (referenceReward.reachPoints != 0)
            EarningPointsWidget(
              points: referenceReward.reachPoints,
              size: 12.0,
            ),

          ///endif
        ],
      );
    }

    return Text(notification.message);
  }
}
