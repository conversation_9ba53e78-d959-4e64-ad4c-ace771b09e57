import 'dart:convert';
import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:device_info/device_info.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_native_timezone/flutter_native_timezone.dart';
import 'package:get/get.dart';
import 'package:mybuddy/Tools/tools.cio.dart';
import 'package:mybuddy/Tools/tools_admob.dart';
import 'package:mybuddy/Tools/tools_apple.dart';
import 'package:mybuddy/Tools/tools_appointment.dart';
import 'package:mybuddy/Tools/tools_appsflyer.dart';
import 'package:mybuddy/Tools/tools_clinic.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/Tools/tools_dynamic_links.dart';
import 'package:mybuddy/Tools/tools_facebook.dart';
import 'package:mybuddy/Tools/tools_google.dart';
import 'package:mybuddy/Tools/tools_image.dart';
import 'package:mybuddy/Tools/tools_local_notifications.dart';
import 'package:mybuddy/Tools/tools_location.dart';
import 'package:mybuddy/Tools/tools_message.dart';
import 'package:mybuddy/Tools/tools_order.dart';
import 'package:mybuddy/Tools/tools_owner.dart';
import 'package:mybuddy/Tools/tools_pet.dart';
import 'package:mybuddy/Tools/tools_push_notification.dart';
import 'package:mybuddy/Tools/tools_team.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/controllers/root_controller.dart';
import 'package:mybuddy/models/login_data.dart';
import 'package:mybuddy/ui/templates/home/<USER>';
import 'package:mybuddy/ui/templates/onboarding/landing_page.dart';
import 'package:mybuddy/ui/templates/onboarding/onlogin_page.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

import '../class/constants.dart';
import '../class/data.dart';
import 'tools_fcm.dart';

class Tools {
  static final Tools _singleton = Tools._internal();

  factory Tools() {
    return _singleton;
  }

  Tools._internal();

  final TeamTools team = TeamTools();
  final FirebaseDynamicLinkTools firebaseDynamicLinkTools =
      FirebaseDynamicLinkTools();
  final PushNotificationTools pushNotification = PushNotificationTools();
  final LocalNotificationsTools localNotification = LocalNotificationsTools();
  final OrderTools order = OrderTools();
  final ClinicTools clinic = ClinicTools();
  final AppointmentTools appointment = AppointmentTools();
  final ImageTools image = ImageTools();
  final CommonTools common = CommonTools();
  final PetTools pet = PetTools();
  final FacebookTools facebook = FacebookTools();
  final OwnerTools owner = OwnerTools();
  final MessageTools message = MessageTools();
  final AppleTools apple = AppleTools();
  final GoogleTools google = GoogleTools();
  final LocationTools location = LocationTools();
  tz.Location? currentTimezone;
  final AdMobTools adMobTools = AdMobTools();
  final AppsFlyerTools appsFlyerTools = AppsFlyerTools();
  final CIOTools cioTools = CIOTools();
  final FCMTools fcmTools = FCMTools();

  final List<String?> routes = ['/'];
  late BuildContext homeContext;

  String appVersion = '';
  String packageName = '';
  String buildNumber = '';
  bool isUpdate = false;

  Future<tz.Location> getLocation() async {
    if (currentTimezone == null) {
      tz.initializeTimeZones();
      String localeString = await FlutterNativeTimezone.getLocalTimezone();
      debugPrint(localeString);
      currentTimezone = tz.getLocation(localeString);
    }
    return currentTimezone!;
  }

  void setContext(BuildContext context, String from) {
    homeContext = context;
  }

  static void debugPrint(String? string) {
    if (kDebugMode) {
      // ignore: avoid_print
      String log = string != null
          ? "${DateTime.now().toString()} $string"
          : "~:-( print debug variable is null";
      print(log);
    }
  }

  void debugRoutes() {
    debugPrint(
        '≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈ /${routes.join('/')}');
  }

  Future<Object?> navigatorPush(widget, {bool preventDuplicates = true}) async {
    try {
      routes.add(widget.routeName);
    } catch (e) {
      routes.add(widget.runtimeType.toString());
    }
    debugRoutes();
    return await Get.to(widget, preventDuplicates: preventDuplicates);
  }

  Future<Object?> navigatorToHome(
      {BuildContext? context, bool onLogin = true}) async {
    Tools().navigatorPopAll();
    routes.clear();
    if (onLogin) {
      /// need to show on boarding first
      LoginData data = Data().get();
      bool? shouldShowPetOption =
          SettingsDelegate().prefs.getBool(isFirstLogin) ?? false;

      if ((data.pets.isEmpty && shouldShowPetOption) ||
          data.owner.shelter == null) {
        await Tools().navigatorPush(const OnLoginPage());
      }
    }
    await RootController.of.showSetGoalsScreen();
    await RootController.of.checkNotificationPermission();
    RootController.of.updateScreenOnCIO(cioScreenNameHome);
    return await Tools().navigatorPush(const HomePage());
  }

  Future<Object?> navigatorReplacement(widget) async {
    if (routes.isNotEmpty) {
      routes.removeLast();
    }
    try {
      routes.add(widget.routeName);
    } catch (e) {
      routes.add(widget.runtimeType.toString());
    }
    debugRoutes();
    return await Get.off(widget);
  }

  void navigatorPopAll() {
    routes.clear();
    routes.add('/');
    Get.until((Route route) {
      return route.settings.name == ('/');
    });
    debugRoutes();
  }

  void navigatorPopAllUntilHome() {
    routes.clear();
    routes.add('/');
    Get.until((Route route) {
      return route.settings.name == ('/HomePage') ||
          route.settings.name == ('/');
    });
    debugRoutes();
  }

  void navigatorPopUntilLandingPage(Widget page) {
    routes.clear();
    routes.add("/LandingPage");
    routes.add(page.runtimeType.toString());
    Get.offUntil(GetPageRoute(page: () => page), (Route route) {
      return route.settings.name == "/LandingPage" ||
          route.settings.name == '/';
    });
    debugRoutes();
  }

  void navigatorPop({value, bool removeLast = true}) {
    if (removeLast && routes.isNotEmpty) {
      routes.removeLast();
    }
    if (Get.currentRoute.isEmpty) {
      Get.off(const LandingPage());
    } else {
      Get.back(result: value);
    }
    debugRoutes();
  }

  void initNotifications() {
    debugPrint('init notifications local and push');
    localNotification.initNotification();
    fcmTools.getDeviceToken();
    fcmTools.initializeNotifications();
  }

  static String generateMd5(String input) {
    return md5.convert(utf8.encode(input)).toString();
  }

  static bool isIdInList(List? list, {int? id, var thisObject}) {
    assert((id != null && thisObject == null) ||
        (id == null && thisObject != null));
    if (thisObject != null) id = thisObject.id;

    if (list == null) return false;
    bool _inList = false;
    for (var object in list) {
      if (object.id == id) {
        _inList = true;
      }
    }
    return _inList;
  }

  static Future<Map<String, dynamic>> deviceDataForCIO() async {
    Map<String, dynamic> data = {};
    try {
      data['app_version'] = Tools().appVersion;
      data['operating_system'] = Platform.operatingSystem;
      if (Platform.isIOS) {
        var iosInfo = await DeviceInfoPlugin().iosInfo;
        data['version'] = iosInfo.systemVersion;
        data['phone'] = iosInfo.name;
      } else if (Platform.isAndroid) {
        var androidInfo = await DeviceInfoPlugin().androidInfo;
        data['version'] = androidInfo.version.release;
        data['phone'] = androidInfo.model;
      }
      return data;
    } catch (e) {
      return data;
    }
  }

  //create a function to ask notifications permission
  Future<bool> askNotificationsPermissionAndRegisterDevice(
      {Function? onDenied, Function? onGranted}) async {
    bool isPermissionGiven = await fcmTools.requestPermission(onDenied: () {
      if (onDenied != null) onDenied();
    });
    if (isPermissionGiven) {
      fcmTools.getDeviceToken();
      fcmTools.initializeNotifications();
      _showLoading(const Duration(milliseconds: 500));
      await Future.delayed(const Duration(milliseconds: 500));
      _dismissLoading();
      if (onGranted != null) onGranted();
    }

    return isPermissionGiven;
  }

  //create a function to check notifications permission status is granted or not
  Future<bool> isNotificationPermissionGranted() async {
    bool isPermissionGiven = await Permission.notification.isGranted;

    return isPermissionGiven;
  }

  Future<bool> shouldNotificationPermissionScreenVisible() async {
    PermissionStatus status = await Permission.notification.status;

    bool checkAskStatus =
        SettingsDelegate().prefs.getBool(notificationPermissionAsked) ?? false;

    return !status.isGranted && !status.isPermanentlyDenied && !checkAskStatus;
  }

  void _showLoading(Duration duration) {
    final _messenger = ScaffoldMessenger.of(Get.context!);

    _messenger.showSnackBar(
      SnackBar(
        duration: duration,
        content: Row(
          children: [
            CircularProgressIndicator(
              color: Theme.of(Get.context!).textSelectionTheme.cursorColor,
            ),
          ],
        ),
      ),
    );
  }

  void _dismissLoading() {
    final _messenger = ScaffoldMessenger.of(Get.context!);
    _messenger.hideCurrentSnackBar();
  }

  static void sendErrorToCrashlytics(dynamic exception, [StackTrace? stack]) {
    FirebaseCrashlytics.instance.recordError(exception, stack, fatal: true);
  }
}
