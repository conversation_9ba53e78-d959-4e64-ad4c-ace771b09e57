import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/refs_data.dart';

import '../controllers/internet_controller.dart';

class FacebookTools extends CommonTools {
  FacebookTools();

  Future<void> login(BuildContext context, {
    Function? onEmailNotFoundError,
  }) async {
    try {
      Tools().common.showDialogWithLoading();

      if (!(await InternetController.of().checkInternet(context: context))) {
        throw Exception("No Internet Connection");
      }

      LoginResult result = await FacebookAuth.instance.login();

      if (result.status == LoginStatus.success) {
        AccessToken? accessToken = result.accessToken;
        if (accessToken == null) {
          return;
        }
        Tools.debugPrint(" <PERSON><PERSON> khatri ${accessToken.toJson().toString()}");
        // get the user data
        final userData =
            await FacebookAuth.instance.getUserData(fields: 'name, email');

        if (userData["email"] == null) {
          // This pops the loading dialog
          Tools().navigatorPop();
          if (onEmailNotFoundError != null) {
            onEmailNotFoundError();
          }
          return;
        }

        Owner _owner = Owner();
        Country country = Ref().get().getCountryByShortLabel("US");
        _owner.setCountry(country);

        if(userData['name'] != null) {
          List<String> nameParts = userData['name'].split(' ');
          _owner.lastName = nameParts.length > 1 ? nameParts.elementAt(1) : '';
          _owner.firstName = nameParts.elementAt(0);
        }

        _owner.email = userData['email'] ?? '';
        _owner.phone = '';
        _owner.facebookUserId = accessToken.userId;

        // Tools.debugPrint('''
        //  Logged in!
        //
        //  Token: ${accessToken.token}
        //  User id: ${accessToken.userId}
        //  Expires: ${accessToken.expires}
        //  Permissions: ${accessToken.grantedPermissions}
        //  Declined permissions: ${accessToken.declinedPermissions}
        //  ''');

        MBResponse response = await MbApiOwner().facebookConnectRequest(
            context, _owner, accessToken.token,
            refresh: false);
        if (response.success) {
          await Tools().navigatorToHome();
        } else {
          // This pops the loading dialog
          Tools().navigatorPop();
        }
      } else {
        // This pops the loading dialog
        Tools().navigatorPop();
      }
    } catch (error) {
      Tools.debugPrint('Error in Facebook Sign in: ' + error.toString());
      // This pops the loading dialog
      Tools().navigatorPop();
    }
  }

  Future<void> logout() async {
    if (await FacebookAuth.instance.accessToken != null) {
      await FacebookAuth.instance.logOut();
    }
  }
}
