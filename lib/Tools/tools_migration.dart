import 'dart:io';

import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_sqlite.dart';
import 'package:mybuddy/class/constants.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// This class is responsible for migrating the data of the legacy Xamarin based
/// app to the new flutter one. This class tries to locate the db file created
/// by the legacy app and read the logged in user's info like email, first and last
/// name, and save it in the SharedPreference for later usage.
class MigrationXamarinToFlutter {
  static final MigrationXamarinToFlutter _singleton =
      MigrationXamarinToFlutter._internal();

  factory MigrationXamarinToFlutter() {
    return _singleton;
  }

  MigrationXamarinToFlutter._internal();

  Future<void> start() async {
    // Check if file exists
    Tools.debugPrint('Migration of XamaringToFlutter started');
    Tools.debugPrint('Trying to check if the data has been already read');
    bool checkIfDataAlreadyRead = await _checkIfDataIsAlreadyReadFromWoofTraxDb();
    if (checkIfDataAlreadyRead) {
      Tools.debugPrint('User data has been already read from db, returning');
      return;
    }

    Tools.debugPrint('Trying to find the old db file');
    bool oldFileExists = await SqliteTools.checkIfOlderDBFileExists();
    if (!oldFileExists) {
      Tools.debugPrint('Could not find old db file, returning');
      return; // Old db file does not exist, this mean we have migrated already
    }

    Tools.debugPrint('Old db file found, trying to copy to new location');
    // Copy it to new location
    bool copyToNewPathSuccess = await SqliteTools.copyLegacyDbToSqflitePath();
    if (!copyToNewPathSuccess) return;

    Tools.debugPrint('Old db copied, trying to deleted the legacy db file');

    // Delete the old file only on Android because it is stored on a different path
    // hence we need to remove it from the old path
    if (Platform.isAndroid) {
      await SqliteTools.deleteLegacyDbFile();
    }

    Tools.debugPrint('Trying to read People data from the new file');
    // Start reading data
    List<Map<String, Object?>> people = await SqliteTools.getAllPeople();
    if (people.isEmpty) return;

    Map<String, Object?> myself = people.first;
    Object? email = myself["ConfirmedEmail"];
    Object? firstName = myself["FirstName"];
    Object? lastName = myself["LastName"];
    Object? objectId = myself["objectId"];

    SharedPreferences prefs = SettingsDelegate().prefs;

    if (email != null) {
      // Cast as string and save it in local storage
      Tools.debugPrint('Email read: $email now trying to save it Prefs');
      prefs.setString(wooftraxEmail, email as String);
      prefs.setBool(wooftraxDataRead, true);
    }
    if (firstName != null) {
      Tools.debugPrint(
          'First name read: $firstName now trying to save it Prefs');
      prefs.setString(wooftraxFirstName, firstName as String);
    }
    if (lastName != null) {
      Tools.debugPrint('Last name read: $lastName now trying to save it Prefs');
      prefs.setString(wooftraxLastName, lastName as String);
    }
    if (objectId != null) {
      Tools.debugPrint('Object Id read: $objectId now trying to save it Prefs');
      prefs.setString(wooftraxObjectId, objectId as String);
    }
  }

  Future<bool> _checkIfDataIsAlreadyReadFromWoofTraxDb() async {
    return SettingsDelegate().prefs.getBool(wooftraxDataRead) ?? false;
  }

  Future<Map<String, String?>> getWooftraxUserInfo() async {
    SharedPreferences prefs = SettingsDelegate().prefs;
    String? email = prefs.getString(wooftraxEmail);
    String? firstName = prefs.getString(wooftraxFirstName);
    String? lastName = prefs.getString(wooftraxLastName);
    String? objectId = prefs.getString(wooftraxObjectId);

    return Map.from({
      "email": email,
      "firstName": firstName,
      "lastName": lastName,
      "objectId": objectId
    });
  }

  Future<void> clearWooftraxUserInfo() async {
    SharedPreferences prefs = SettingsDelegate().prefs;
    await prefs.remove(wooftraxEmail);
    await prefs.remove(wooftraxFirstName);
    await prefs.remove(wooftraxLastName);
    await prefs.remove(wooftraxObjectId);
  }
}
