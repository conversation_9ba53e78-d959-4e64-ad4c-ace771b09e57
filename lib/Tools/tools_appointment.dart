import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/models/staff.dart';
import 'package:mybuddy/ui/templates/apt/add_appointment_request.dart';
import 'package:mybuddy/ui/templates/captainvet/cv_login.dart';

import 'tools_common.dart';

enum AptChoiceType { mbApt, externalApt, captainvetApt }

class AptChoice {
  final MBService service;
  final AptChoiceType aptChoiceType;

  AptChoice(this.service, this.aptChoiceType);
}

class AppointmentTools extends CommonTools {
  bool canMakeAppointmentSomeWhere(){
    return Data().get().services.where((s) => s.canMakeAppointment()).toList().isNotEmpty;
  }

  void appointmentAction(
      {MBService? service, Staff? staff, Pet? pet, required BuildContext context}) async {
    ///get choice list
    List<AptChoice> availableChoices = <AptChoice>[];
    List<MBService> services;
    if (service != null) {
      services = [service];
    } else {
      services = Data().get().services.where((s) => s.canMakeAppointment()).toList();
    }

    for (MBService currentService in services) {
      if (currentService.acceptAppointment) {
        availableChoices.add(AptChoice(currentService, AptChoiceType.mbApt));
      }
      if (currentService.getExternalAppointmentUrl() != null) {
        availableChoices.add(AptChoice(currentService, AptChoiceType.externalApt));
      }
      if (currentService.captainvetId != null && currentService.captainvetId != '') {
        availableChoices.add(AptChoice(currentService, AptChoiceType.captainvetApt));
      }
    }

    /// get choice
    AptChoice? aptChoice;
    if (availableChoices.length == 1) {
      aptChoice = availableChoices[0];
    } else if (availableChoices.length > 1) {
      aptChoice = await _appointmentDialogAction(context, availableChoices, services.length > 1);
    }

    if (aptChoice == null) {
      return;
    }

    ///action
    switch (aptChoice.aptChoiceType) {
      case AptChoiceType.captainvetApt:
        await Tools().navigatorPush(
          // todo trans
          CVPortal(clinic: aptChoice.service),
        );
        break;
      case AptChoiceType.externalApt:
        if(aptChoice.service.getExternalAppointmentUrl() != null) {
          launchURL(aptChoice.service.getExternalAppointmentUrl()!, context);
        }
        break;
      case AptChoiceType.mbApt:
        await Tools().navigatorPush(
          AddAppointmentRequestForm(
            title: 'APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_REQUEST_APPOINTMENT'.tr(),
            pet: pet,
            clinic: aptChoice.service,
            staff: staff,
          ),
        );
        break;
    }
  }

  Widget _optionWidget(AptChoice aptChoice, bool displayServiceName) {
    String text;
    switch (aptChoice.aptChoiceType) {
      case AptChoiceType.captainvetApt:
        text = 'APPLICATION_MOBILE_APT_CAPTAINVET_CHOICE'.tr();
        break;
      case AptChoiceType.externalApt:
        text = 'APPLICATION_MOBILE_APT_CUSTOM_APT'.tr();
        break;
      case AptChoiceType.mbApt:
        text = 'APPLICATION_MOBILE_APT_OFFICIAL_APT'.tr();
        break;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 2.5),
      decoration: BoxDecoration(
          border: Border(bottom: BorderSide(width: 1.0, color: Colors.blueGrey.shade100))),
      child: TextButton(
        onPressed: () {
          Tools().navigatorPop(value: aptChoice, removeLast: false);
        },
        child: Column(
          children: [
            Text(text, textAlign: TextAlign.center,),
            if(displayServiceName)
              Text('(${aptChoice.service.name ?? ''})',
                textAlign: TextAlign.center,
              ),
          ],
        ),
      ),
    );
  }

  Future<AptChoice?> _appointmentDialogAction(
      BuildContext context, List<AptChoice> availableChoices, bool displayServiceName) async {
    return await showModalBottomSheet<AptChoice>(
        context: context,
        builder: (BuildContext context) {
          return SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(10.0, 7.5, 10.0, 0.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: availableChoices.map((e) => _optionWidget(e, displayServiceName)).toList(),
            ),
          );
        });
  }
}
