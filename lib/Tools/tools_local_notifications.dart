import 'dart:async';
import 'dart:math';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';

class LocalNotificationsTools extends CommonTools {
  final AwesomeNotifications _awesomeNotification = AwesomeNotifications();

  /// Create a [NotificationChannel] for heads up notifications
  final NotificationChannel _channel = NotificationChannel(
    channelGroupKey: 'wooftrax_notification',
    channelKey: 'wooftrax_notification_channel',
    channelName: 'WoofTrax Notifications',
    channelDescription: 'This channel is used for wooftrax notifications.',
    channelShowBadge: true,
    importance: NotificationImportance.High,
    enableVibration: true,
  );

  /// Use this method to initialize the notification plugin
  Future<bool> initNotification() async {
    return await _awesomeNotification.initialize(
      'resource://drawable/wooftrax_notification_icon',
      [
        _channel,
      ],
      channelGroups: [
        NotificationChannelGroup(
          channelGroupKey: 'wooftrax_notification',
          channelGroupName: 'WoofTrax Notifications',
        )
      ],
    );
  }

  /// Use this method to show a notification which is received from firebase
  void showFirebaseNotification(Map notification) {
    _awesomeNotification.createNotification(
        content: NotificationContent(
      id: Random().nextInt(1000),
      channelKey: _channel.channelKey!,
      title: notification["title"],
      body: notification["body"],
      largeIcon: notification["image"],
      //large icon will be displayed at right side
      bigPicture: notification["image"],
      //big picture will be displayed in expanded view at bottom of the text
      notificationLayout: NotificationLayout.BigPicture,
      hideLargeIconOnExpand: true,
    ));
  }

  /// Use this method to set the notification events
  void setNotificationListener() {
    // Only after at least the action method is set, the notification events are delivered
    _awesomeNotification.setListeners(
        onActionReceivedMethod: _onActionReceivedMethod,
        onNotificationCreatedMethod: _onNotificationCreatedMethod,
        onNotificationDisplayedMethod: _onNotificationDisplayedMethod,
        onDismissActionReceivedMethod: _onDismissActionReceivedMethod);
  }

  /// Use this method to detect when a new notification or a schedule is created
  @pragma("vm:entry-point")
  static Future<void> _onNotificationCreatedMethod(
      ReceivedNotification receivedNotification) async {
    Tools.debugPrint("onNotificationCreatedMethod");
  }

  /// Use this method to detect every time that a new notification is displayed
  @pragma("vm:entry-point")
  static Future<void> _onNotificationDisplayedMethod(
      ReceivedNotification receivedNotification) async {
    Tools.debugPrint("onNotificationDisplayedMethod");
  }

  /// Use this method to detect if the user dismissed a notification
  @pragma("vm:entry-point")
  static Future<void> _onDismissActionReceivedMethod(
      ReceivedAction receivedAction) async {
    Tools.debugPrint("onDismissActionReceivedMethod");
  }

  /// Use this method to detect when the user taps on a notification or action button
  @pragma("vm:entry-point")
  static Future<void> _onActionReceivedMethod(
      ReceivedAction receivedAction) async {
    Tools.debugPrint("onActionReceivedMethod");
  }

  /// Use this method to cancel/remove a notification by using its id
  Future<void> cancelNotification(int id) async {
    await _awesomeNotification.dismiss(id);
  }

  /// Use this method to cancel/remove all notifications
  Future<void> cancelAllNotifications() async {
    await _awesomeNotification.dismissAllNotifications();
  }
}
