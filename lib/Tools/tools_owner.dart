import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';

import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/ui/templates/dynamiclink/dynamic_link_landing_page.dart';

class OwnerTools extends CommonTools {
  /// This method processes the dynamic links related to user account
  void processDynamicLinkData(
      String dynamicLinkType, PendingDynamicLinkData data) async {
    // Checking if user is logged in if yes then we try to add
    String? token = SettingsDelegate().prefs.getString('token');
    if (token != null) return;

    Tools().navigatorPush(
        DynamicLinkLandingPage(linkType: dynamicLinkType, data: data));
  }

  /// This method processes the dynamic links related to user account
  void processChangedEmailDynamicLinkData(
      String dynamicLinkType, PendingDynamicLinkData data) async {

    // Checking if user is logged in if yes then we try to add
    String? token = SettingsDelegate().prefs.getString('token');
    if (token == null) return;

    Tools().navigatorPush(
        DynamicLinkLandingPage(linkType: dynamicLinkType, data: data));
  }
}
