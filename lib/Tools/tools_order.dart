import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/service.dart';

import 'tools_common.dart';

enum OrderChoiceType { mbOrder, externalOrder }

class OrderChoice {
  final MBService service;
  final OrderChoiceType orderChoiceType;

  OrderChoice(this.service, this.orderChoiceType);
}

class OrderTools extends CommonTools {
  bool canMakeOrdersSomeWhere() {
    return Data()
        .get()
        .services
        .where((s) => s.canMakeOrder())
        .toList()
        .isNotEmpty;
  }

  void orderAction(
      {required MBService service, required BuildContext context}) async {
    int? choice;
    if (service.acceptOrder && service.getExternalOrderUrl() != null) {
      choice = await _orderDialogAction(context);
    } else if (service.acceptOrder) {
      choice = 0;
    } else if (service.getExternalOrderUrl() != null) {
      choice = 1;
    }
    switch (choice) {
      case 1:
        launchURL(service.getExternalOrderUrl()!, context);
        break;
      case 0:
        break;
      default:
        return;
    }
  }

  void orderAction2({MBService? service, required BuildContext context}) async {
    ///get choice list
    List<OrderChoice> availableChoices = [];
    List<MBService> services;
    if (service != null) {
      services = [service];
    } else {
      services = Data().get().services.where((s) => s.canMakeOrder()).toList();
    }

    for (MBService currentService in services) {
      if (currentService.acceptOrder) {
        availableChoices
            .add(OrderChoice(currentService, OrderChoiceType.mbOrder));
      }
      if (currentService.getExternalOrderUrl() != null) {
        availableChoices
            .add(OrderChoice(currentService, OrderChoiceType.externalOrder));
      }
    }

    /// get choice
    OrderChoice? orderChoice;
    if (availableChoices.length == 1) {
      orderChoice = availableChoices[0];
    } else if (availableChoices.length > 1) {
      orderChoice = await _orderDialogAction2(
          context, availableChoices, services.length > 1);
    }

    if (orderChoice == null) {
      return;
    }

    ///action
    switch (orderChoice.orderChoiceType) {
      case OrderChoiceType.externalOrder:
        launchURL(orderChoice.service.getExternalOrderUrl()!, context);
        break;
      case OrderChoiceType.mbOrder:
        break;
    }
  }

  Future<OrderChoice?> _orderDialogAction2(BuildContext context,
      List<OrderChoice> availableChoices, bool displayServiceName) async {
    return await showModalBottomSheet<OrderChoice>(
        context: context,
        builder: (BuildContext context) {
          return SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(10.0, 7.5, 10.0, 0.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: availableChoices
                  .map((e) => _optionWidget(e, displayServiceName))
                  .toList(),
            ),
          );
        });
  }

  Widget _optionWidget(OrderChoice orderChoice, bool displayServiceName) {
    String? text;
    switch (orderChoice.orderChoiceType) {
      case OrderChoiceType.externalOrder:
        text = 'APPLICATION_MOBILE_STORE_CUSTOM_STORE'.tr();
        break;
      case OrderChoiceType.mbOrder:
        text = 'APPLICATION_MOBILE_STORE_OFFICIAL_STORE'.tr();
        break;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 2.5),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 1.0, color: Colors.blueGrey.shade100),
        ),
      ),
      child: TextButton(
        onPressed: () {
          Tools().navigatorPop(value: orderChoice, removeLast: false);
        },
        child: Column(
          children: [
            Text(
              text,
              textAlign: TextAlign.center,
            ),
            if (displayServiceName)
              Text(
                '(${orderChoice.service.name ?? ''})',
                textAlign: TextAlign.center,
              )
          ],
        ),
      ),
    );
  }

  Future<int?> _orderDialogAction(BuildContext context) async {
    return await showModalBottomSheet<int?>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              TextButton(
                onPressed: () {
                  Tools().navigatorPop(removeLast: false, value: 0);
                },
                child: Text(
                  'APPLICATION_MOBILE_STORE_OFFICIAL_STORE'.tr(),
                ),
              ),
              const Divider(),
              TextButton(
                onPressed: () {
                  Tools().navigatorPop(removeLast: false, value: 1);
                },
                child: Text(
                  'APPLICATION_MOBILE_STORE_CUSTOM_STORE'.tr(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
