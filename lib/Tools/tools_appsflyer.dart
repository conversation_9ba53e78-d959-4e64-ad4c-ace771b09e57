import 'dart:io';

import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:flutter/foundation.dart';
import 'package:mybuddy/Tools/tools.dart';

class AppsFlyerTools {
  AppsFlyerTools();

  late AppsflyerSdk appsflyerSdk;

  Future<void> initSdk(String appsFlyerDevKey, String appsFlyerAppIdIos,
      String appsFlyerAppIdAndroid) async {

    AppsFlyerOptions appsFlyerOptions = AppsFlyerOptions(
        afDevKey: appsFlyerDevKey,
        appId: Platform.isAndroid ? appsFlyerAppIdAndroid : appsFlyerAppIdIos,
        showDebug: !kReleaseMode,
        disableAdvertisingIdentifier: true);

    appsflyerSdk = AppsflyerSdk(appsFlyerOptions);
    
    appsflyerSdk.initSdk();
  }

  Future<bool?> logEvent(String eventName, Map? eventValues) async {
    bool? result;
    try {
      result = await appsflyerSdk.logEvent(eventName, eventValues);
    } on Exception catch (e) {
      Tools.debugPrint("error: ${e.toString()}");
    }
    Tools.debugPrint("Result logEvent: $result");
  }
}
