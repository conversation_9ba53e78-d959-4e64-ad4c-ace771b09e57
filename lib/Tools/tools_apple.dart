import 'dart:async';

import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/class/app_domain_delegate.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/controllers/internet_controller.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:mybuddy/models/refs_data.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class AppleTools extends CommonTools {
  AppleTools();

  Future<void> login(BuildContext context) async {
    try {
      Tools().common.showDialogWithLoading();

      if (!(await InternetController.of().checkInternet(context: context))) {
        throw Exception("No Internet Connection");
      }

      final AuthorizationCredentialAppleID credential =
          await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        webAuthenticationOptions: WebAuthenticationOptions(
          clientId: 'com.wooftrax.wfadsignin',
          redirectUri: Uri.parse(
            AppDomainDelegate.signWithAppleCallback,
          ),
        ),
      );

      Tools.debugPrint(credential.toString());
      String? email, firstname, lastname, appleId, idToken;
      appleId = credential.userIdentifier;
      idToken = credential.identityToken;

      //store values given only the first time if it fails
      SharedPreferences prefs = SettingsDelegate().prefs;
      if (credential.email == null && appleId == prefs.get('apple_id')) {
        email = prefs.getString('apple_email');
        firstname = prefs.getString('apple_firstname');
        lastname = prefs.getString('apple_lastname');
      } else {
        unawaited(prefs.setString('apple_email', credential.email ?? ''));
        email = credential.email;
        unawaited(
            prefs.setString('apple_firstname', credential.givenName ?? ''));
        firstname = credential.givenName;
        unawaited(
            prefs.setString('apple_lastname', credential.familyName ?? ''));
        lastname = credential.familyName;
        unawaited(prefs.setString('apple_id', credential.userIdentifier ?? ''));
        appleId = credential.userIdentifier;
      }

      Owner _owner = Owner();
      Country country = Ref().get().getCountryByShortLabel("US");
      _owner.setCountry(country);
      _owner.lastName = lastname ?? '';
      _owner.firstName = firstname ?? '';
      _owner.email = email ?? '';
      _owner.phone = '';
      _owner.appleUserId = appleId;
      Tools.debugPrint('''
         Logged in!
         User id: $appleId
         ''');

      String? accessToken = appleId ??= idToken;

      MBResponse response = await MbApiOwner()
          .appleConnectRequest(context, _owner, accessToken, refresh: false);
      if (response.success) {
        await Tools().navigatorToHome();
      } else {
        // This pops the loading dialog
        Tools().navigatorPop();
      }
    } catch (error) {
      Tools.debugPrint('Error in Apple Sign in: ' + error.toString());
      // This pops the loading dialog
      Tools().navigatorPop();
    }
  }
}
