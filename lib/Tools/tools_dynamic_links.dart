import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:mybuddy/Tools/tools.dart';

const String LINK_TYPE_INVITE = "invite";
const String LINK_TYPE_VERIFY_EMAIL= "verify-email";
const String LINK_TYPE_FORGOT_PASSWORD = "forgot-password";
const String LINK_TYPE_VERIFY_CHANGE_EMAIL = "verify-changed-email";

class FirebaseDynamicLinkTools {
  Future<void> initDynamicLinks() async {
    FirebaseDynamicLinks dynamicLinks = FirebaseDynamicLinks.instance;
    PendingDynamicLinkData? initialData = await dynamicLinks.getInitialLink();
    if (initialData != null) {
      // Process initial link
      _processDynamicDataLink(initialData);
    }

    dynamicLinks.onLink.listen((dynamicLinkData) {
      // print(dynamicLinkData);
      _processDynamicDataLink(dynamicLinkData);
    }).onError((error) {
      // print('onLink error');
      // print(error.message);
    });
  }

  /// This method processes all the dynamic link received based on its type,
  /// it delegates to the appropriate handler that can process it
  void _processDynamicDataLink(PendingDynamicLinkData data) async {
    Uri link = data.link;
    String? dynamicLinkType = link.queryParameters['linkType'];
    switch (dynamicLinkType) {
      case LINK_TYPE_INVITE:
        await Tools().team.processDynamicLinkData(data);
        break;
      case LINK_TYPE_VERIFY_EMAIL:
      case LINK_TYPE_FORGOT_PASSWORD:
        Tools().owner.processDynamicLinkData(dynamicLinkType!, data);
        break;
      case LINK_TYPE_VERIFY_CHANGE_EMAIL:
        Future.delayed(const Duration(milliseconds: 500),(){
          Tools().owner.processChangedEmailDynamicLinkData(dynamicLinkType!, data);
        });
        break;
      default:
    }
  }

  /*Future<Uri> createDynamicLink(bool short, int packId) async {
    String dynamicLink = 'https://wooftrax.com/press-1?packId=$packId';
    FirebaseDynamicLinks dynamicLinks = FirebaseDynamicLinks.instance;
    final DynamicLinkParameters parameters = DynamicLinkParameters(
      uriPrefix: 'https://wooftraxwalkforadog.page.link',
      link: Uri.parse(dynamicLink),
      androidParameters: const AndroidParameters(
        packageName: 'com.actionxl.wooftrax.wfad',
        minimumVersion: 0,
      ),
      iosParameters: const IOSParameters(
        bundleId: 'com.wooftrax.wfad',
        minimumVersion: '0',
      ),
    );

    Uri url;
    if (short) {
      final ShortDynamicLink shortLink =
          await dynamicLinks.buildShortLink(parameters);
      url = shortLink.shortUrl;
    } else {
      url = await dynamicLinks.buildLink(parameters);
    }

    return url;
  }*/
}
