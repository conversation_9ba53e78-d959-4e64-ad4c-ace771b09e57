import 'dart:async';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:mybuddy/Api/base_api.dart';
import 'package:mybuddy/Api/mb_api_task.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/components/ext/data_connection_checker.dart';
import 'package:url_launcher/url_launcher.dart';

class CommonTools {
  Future<bool> isOnline({BuildContext? alertContext}) async {
    bool result = await DataConnectionChecker().hasConnection;
    if (result == true) return true;

    if (alertContext != null) {
      MbBaseApi().showMessage(
        alertContext,
        message: "You're offline! Check your internet connection.",
        showError: true,
      );
    }
    return false;
  }

  void launchURL(String url, BuildContext context,
      [int? id, bool? forceWebView]) async {
    if (await canLaunch(url)) {
      await launch(url, forceWebView: forceWebView ?? false);

      if (id != null) {
        Tools.debugPrint(
            'i am trying to call my favorite clinic so i will get some points');
        unawaited(MbApiTask().vetCall(id));
      }
    } else {
      await showSimpleDialog(context, 'Could not launch $url',
          title: 'APPLICATION_MOBILE_LABEL_ERROR'.tr());
    }
  }

  void showMessage(BuildContext context, String message,
      [Color color = const Color(0xff419563), Color? textColor]) {
    Fluttertoast.cancel();
    Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: color,
        textColor: textColor,
        fontSize: 13);
  }

  void showNotImplemented(BuildContext context) {
    showSimpleDialog(context, 'not implemented YET !');
  }

  Future<bool?> showSimpleDialog(BuildContext context, String? text,
      {String? title = '', List<Widget>? optionalButtons}) async {
    optionalButtons ??= [];
    return await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext ctx) {
        optionalButtons!.add(
          TextButton(
            onPressed: () {
              Tools().navigatorPop(value: true, removeLast: false);
            },
            child: Text('APPLICATION_MOBILE_BUTTON_LABEL_CLOSE'.tr()),
          ),
        );
        return AlertDialog(
            title: Text(title!),
            content: Text(text!),
            actions: optionalButtons);
      },
    );
  }

  void showValidationErrorMessage(BuildContext context) {
    showMessage(context,
        'APPLICATION_MOBILE_FORM_GENERAL_VALIDATION_ERROR'.tr(), Colors.red);
  }

  Future<bool?> showValidDialog(BuildContext context,
      {String? text = '',
      String? title,
      Function? onValid,
      Function? onReject}) async {
    if ((text == null || text.trim() == '') &&
        (title == null || title.trim() == '')) {
      text = 'APPLICATION_MOBILE_MESSAGE_CONFIRMATION'.tr();
    }
    return await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext ctx) {
        return AlertDialog(
          title: title != null && title.trim() != '' ? Text(title) : null,
          content: text != null && text.trim() != '' ? Text(text) : null,
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Tools().navigatorPop(value: true, removeLast: false);
                if (onValid != null) onValid();
              },
              child: Text('APPLICATION_MOBILE_BUTTON_LABEL_YES'.tr()),
            ),
            TextButton(
              onPressed: () {
                Tools().navigatorPop(removeLast: false, value: false);
                if (onReject != null) onReject();
              },
              child: Text('APPLICATION_MOBILE_BUTTON_LABEL_NO'.tr()),
            ),
          ],
        );
      },
    );
  }

  Future<bool?> showWarningDialog(BuildContext context,
      {String? text = '',
      String? title,
      String? validTitle,
      String? rejectTitle,
      Color? bgColor,
      Color? textColor,
      Function? onValid,
      Function? onReject,
      Widget? content}) async {
    if ((text == null || text.trim() == '') &&
        (title == null || title.trim() == '')) {
      text = 'APPLICATION_MOBILE_MESSAGE_CONFIRMATION'.tr();
    }
    return await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext ctx) {
        return AlertDialog(
          backgroundColor: bgColor,
          title: title != null && title.trim() != ''
              ? Text(
                  title,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: textColor),
                )
              : null,
          content: content ??
              (text != null && text.trim() != ''
                  ? Text(
                      text,
                      style: TextStyle(color: textColor),
                    )
                  : null),
          actions: <Widget>[
            TextButton(
                onPressed: () {
                  Tools().navigatorPop(removeLast: false, value: false);
                  if (onReject != null) onReject();
                },
                child: Text(
                  rejectTitle ?? 'APPLICATION_MOBILE_BUTTON_LABEL_NO'.tr(),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: textColor),
                )),
            TextButton(
                onPressed: () {
                  Tools().navigatorPop(value: true, removeLast: false);
                  if (onValid != null) onValid();
                },
                child: Text(
                  validTitle ?? 'APPLICATION_MOBILE_BUTTON_LABEL_YES'.tr(),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: textColor),
                )),
          ],
        );
      },
    );
  }

  Future<void> showCustomDialog(
      {String? description = '',
      String? title,
      String? validTitle,
      String? rejectTitle,
      Color? buttonColor,
      Function? onValid,
      Function? onReject,
      List<Widget>? prefixContent,
      List<Widget>? suffixContent}) async {
    return await Get.dialog(AlertDialog(
      backgroundColor: Colors.white,
      contentPadding: const EdgeInsets.symmetric(horizontal: 25, vertical: 15),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ...?prefixContent,
          if (title != null && title.isNotEmpty)
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  height: 1.5, fontWeight: FontWeight.w700, fontSize: 14),
            ),
          if (description != null && description.isNotEmpty)
            Text(
              description,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  fontWeight: FontWeight.w400, height: 1.5, fontSize: 14),
            ),
          ...?suffixContent,
          const SizedBox(
            height: 15,
          ),
          if (validTitle != null)
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: buttonColor,
                  padding: EdgeInsets.zero,
                  minimumSize: const Size(double.infinity, 40),
                  maximumSize: const Size(double.infinity, 40)),
              onPressed: () {
                Tools().navigatorPop();
                if (onValid != null) onValid();
              },
              child: Text(
                validTitle,
                style:
                    const TextStyle(fontWeight: FontWeight.w700, fontSize: 14),
              ),
            ),
          if (rejectTitle != null)
            TextButton(
                onPressed: () {
                  Tools().navigatorPop();
                  if (onReject != null) onReject();
                },
                child: Text(
                  rejectTitle,
                  style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                      color: const Color(0xff474747)),
                )),
        ],
      ),
    ));
  }

  /// This dialog is to be used when we want to show a custom dialog that has
  /// three buttons
  Future<void> showCustomDialogWithThreeButtons(
      {String? description = '',
      String? title,
      String? firstTitle,
      String? secondTitle,
      String? thirdTitle,
      Color? thirdButtonColor,
      Color? secondButtonColor,
      bool isSecondButtonFilled = false,
      Function? onFirst,
      Function? onSecond,
      Function? onThird,
      List<Widget>? prefixContent,
      List<Widget>? suffixContent}) async {
    return await Get.dialog(AlertDialog(
      backgroundColor: Colors.white,
      contentPadding: const EdgeInsets.symmetric(horizontal: 25, vertical: 15),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ...?prefixContent,
          if (title != null && title.isNotEmpty)
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  height: 1.5, fontWeight: FontWeight.w700, fontSize: 14),
            ),
          if (description != null && description.isNotEmpty)
            Text(
              description,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  fontWeight: FontWeight.w400, height: 1.5, fontSize: 14),
            ),
          ...?suffixContent,
          const SizedBox(
            height: 15,
          ),
          if (firstTitle != null)
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: thirdButtonColor,
                  padding: EdgeInsets.zero,
                  minimumSize: const Size(double.infinity, 40),
                  maximumSize: const Size(double.infinity, 40)),
              onPressed: () {
                Tools().navigatorPop();
                if (onFirst != null) onFirst();
              },
              child: Text(
                firstTitle,
                style:
                    const TextStyle(fontWeight: FontWeight.w700, fontSize: 14),
              ),
            ),
          if (secondTitle != null)
            OutlinedButton(
              style: OutlinedButton.styleFrom(
                  foregroundColor:
                      !isSecondButtonFilled ? secondButtonColor : Colors.white,
                  side: BorderSide(color: secondButtonColor!),
                  backgroundColor:
                      isSecondButtonFilled ? secondButtonColor : null,
                  padding: EdgeInsets.zero,
                  minimumSize: const Size(double.infinity, 40),
                  maximumSize: const Size(double.infinity, 40)),
              onPressed: () {
                Tools().navigatorPop();
                if (onSecond != null) onSecond();
              },
              child: Text(
                secondTitle,
                style:
                    const TextStyle(fontWeight: FontWeight.w700, fontSize: 14),
              ),
            ),
          if (thirdTitle != null)
            TextButton(
                onPressed: () {
                  Tools().navigatorPop();
                  if (onThird != null) onThird();
                },
                child: Text(
                  thirdTitle,
                  style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                      color: const Color(0xff474747)),
                )),
        ],
      ),
    ));
  }

  /// This dialog is to be used when we want to show a loading dialog
  Future<void> showDialogWithLoading(
      {String description = 'Loading...'}) async {
    return await Get.dialog(
        AlertDialog(
          backgroundColor: Colors.white,
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // The loading indicator
              const CircularProgressIndicator(),
              const SizedBox(width: 20),
              Text(description,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      fontWeight: FontWeight.w400, height: 1.5, fontSize: 14)),
              // Some text
            ],
          ),
        ),
        barrierDismissible: false);
  }

  Future<DateTimeRange?> showDateRangeDialog(
      {required BuildContext context,
      DateTimeRange? initialDateRange,
      required String helpText,
      DateTime? firstDate}) async {
    DateTimeRange? newRange = await showDateRangePicker(
        context: context,
        initialDateRange: initialDateRange,
        firstDate: firstDate ?? DateTime(1970),
        currentDate: DateTime.now(),
        lastDate: DateTime.now(),
        helpText: helpText,
        initialEntryMode: DatePickerEntryMode.calendarOnly,
        builder: (ctx, widget) {
          return Theme(
              data: ThemeData.light().copyWith(
                  colorScheme: ColorScheme(
                      brightness: Brightness.light,
                      primary: Theme.of(context).colorScheme.secondary,
                      onPrimary: Colors.white,
                      secondary: Theme.of(context).colorScheme.secondary,
                      onSecondary: Colors.white,
                      error: Colors.red,
                      onError: Colors.white,
                      background: Theme.of(context).colorScheme.secondary,
                      onBackground: Colors.white,
                      surface: Theme.of(context).colorScheme.secondary,
                      onSurface: Colors.black)),
              child: widget!);
        });

    return newRange;
  }

  Future<DateTime?> showDateDialog(
      {required BuildContext context,
      DateTime? initialDate,
      DateTime? lastDate,
      DateTime? currentDate,
      String? helpText,
      DateTime? firstDate}) async {
    DateTime? date = await showDatePicker(
        context: context,
        initialDate: initialDate ?? DateTime.now(),
        firstDate: firstDate ?? DateTime(1970),
        currentDate: currentDate ?? DateTime.now(),
        lastDate: lastDate ?? DateTime.now(),
        helpText: helpText,
        initialEntryMode: DatePickerEntryMode.calendarOnly,
        builder: (ctx, widget) {
          return Theme(
              data: ThemeData.light().copyWith(
                  colorScheme: ColorScheme(
                      brightness: Brightness.light,
                      primary: Theme.of(context).colorScheme.secondary,
                      onPrimary: Colors.white,
                      secondary: Theme.of(context).colorScheme.secondary,
                      onSecondary: Colors.white,
                      error: Colors.red,
                      onError: Colors.white,
                      background: Theme.of(context).colorScheme.secondary,
                      onBackground: Colors.white,
                      surface: Theme.of(context).colorScheme.secondary,
                      onSurface: Colors.black)),
              child: widget!);
        });

    return date;
  }
}
