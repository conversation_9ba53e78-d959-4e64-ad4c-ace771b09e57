import 'dart:async';

import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_owner.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/class/ref.dart';
import 'package:mybuddy/class/settings_delegate.dart';
import 'package:mybuddy/models/owner.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../controllers/internet_controller.dart';

class GoogleTools extends CommonTools {
  GoogleTools();

  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile', 'openid'],
  );

  Future<void> login(BuildContext context) async {
    try {
      Tools().common.showDialogWithLoading();

      if (!(await InternetController.of().checkInternet(context: context))) {
        throw Exception("No Internet Connection");
      }

      final GoogleSignInAccount? account = await _googleSignIn.signIn();
      if (account != null) {
        Owner _owner = Owner();
        _owner.email = account.email;
        _owner.googleUserId = account.id;
        _owner.setCountry(Ref().get().getCountryByShortLabel("US"));
        _owner.phone = '';

        final String displayName = account.displayName ?? '';
        if (displayName.isNotEmpty) {
          final int spaceIndex = displayName.trim().indexOf(' ');
          _owner.firstName = displayName.substring(0, spaceIndex);
          _owner.lastName = displayName.substring(spaceIndex).trim();
        }

        // only update prefs if this google_id isn't already set
        SharedPreferences prefs = SettingsDelegate().prefs;
        if (_owner.googleUserId != prefs.get('google_id')) {
          prefs.setString('google_email', account.email);
          prefs.setString('google_id', _owner.googleUserId ?? '');
          prefs.setString('google_firstName', _owner.firstName);
          prefs.setString('google_lastName', _owner.lastName);
        }

        final GoogleSignInAuthentication auth = await account.authentication;
        MBResponse res = await MbApiOwner().googleConnectRequest(
          context,
          _owner,
          auth.accessToken,
          // why refresh false ?
          refresh: false,
        );
        if (res.success) {
          await Tools().navigatorToHome();
        } else {
          // This pops the loading dialog
          Tools().navigatorPop();
        }
      }
      else {
        // This pops the loading dialog
        Tools().navigatorPop();
      }
    } catch (error) {
      Tools.debugPrint('Error in Google Sign in: ' + error.toString());
      // This pops the loading dialog
      Tools().navigatorPop();
    }
  }

  // don't await
  Future<void> logout() async {
    if (await _googleSignIn.isSignedIn()) {
      await _googleSignIn.signOut();
    }
  }
}
