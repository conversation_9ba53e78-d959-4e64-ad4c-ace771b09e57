import 'dart:io';

import 'package:path_provider/path_provider.dart';

import 'tools.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

/// legacy database name
const String dbName = "db.sql";

/// Helper class with methods to perform database related operations
class SqliteTools {
  static Future<Directory> _getLegacyDirectory() async {
    return await (Platform.isAndroid
        ? getApplicationSupportDirectory()
        : getApplicationDocumentsDirectory());
  }

  static Future<String> _copyDbIntoDatabasesPath(String path) async {
    try {
      File legacyFile = File(path);
      File dbFile =
          await legacyFile.copy(join(await getDatabasesPath(), dbName));

      return dbFile.path;
    } catch (e) {
      Tools.debugPrint('copying database has failed: $e');
      return "";
    }
  }

  static Future<bool> checkIfOlderDBFileExists() async {
    Directory legacyDirectory = await _getLegacyDirectory();
    String legacyPath = join(legacyDirectory.path, dbName);
    return File(legacyPath).existsSync();
  }

  static Future<bool> copyLegacyDbToSqflitePath() async {
    // copy legacy database into databases path in order to be able to read it
    Directory legacyDirectory = await _getLegacyDirectory();
    String legacyPath = join(legacyDirectory.path, dbName);
    String newDBPath = await _copyDbIntoDatabasesPath(legacyPath);
    return newDBPath != "";
  }

  static Future<bool> deleteLegacyDbFile() async {
    // copy legacy database into databases path in order to be able to read it
    Directory legacyDirectory = await _getLegacyDirectory();
    String legacyPath = join(legacyDirectory.path, dbName);
    try {
      File(legacyPath).deleteSync();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// This method returns the list of People from the People table
  static Future<List<Map<String, Object?>>> getAllPeople() async {
    // Get a location using getDatabasesPath
    String databasesPath = await getDatabasesPath();
    String path = join(databasesPath, dbName);

    Database database = await openDatabase(path);

    if (database.isOpen) {
      try {
        // query people: user information
        List<Map<String, Object?>> results = await database.query('People');
        Tools.debugPrint('query ended: ${results.length} results');
        return results;
      } on DatabaseException catch (e) {
        Tools.debugPrint('query to retrieve people has failed: $e');
      }
    }

    return Future.value(List.empty());
  }

  static Future<List<Map<String, Object?>>> fetchTables(Database db) async {
    // useful to debug database file
    List<Map<String, Object?>> tables =
        await db.query('sqlite_master', columns: ['type', 'name']);
    for (var t in tables) {
      for (var e in t.entries) {
        Tools.debugPrint('table ${e.key} => ${e.value}');
      }
    }

    return tables;
  }
}
