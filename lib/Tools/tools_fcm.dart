import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/Tools/tools_local_notifications.dart';

class FCMTools extends CommonTools {
  static final FCMTools _instance = FCMTools._internal();

  factory FCMTools() {
    return _instance;
  }

  FCMTools._internal();

  final LocalNotificationsTools _localNotifications = LocalNotificationsTools();
  late Stream<String> _tokenStream;

  /// Function to ask for notification permission
  Future<bool> requestPermission({Function? onDenied}) async {
    NotificationSettings settings =
        await FirebaseMessaging.instance.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      Tools.debugPrint('User granted permission');
      return true;
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      Tools.debugPrint('User granted provisional permission');

      return true;
    } else if (settings.authorizationStatus == AuthorizationStatus.denied) {
      if (onDenied != null) {
        await onDenied();
      }
    }

    return false;
  }

  /// Function to get the device token for push notification
  Future<void> getDeviceToken() async {
    final cioTool = Tools().cioTools;
    FirebaseMessaging.instance.getToken().then(cioTool.registerDeviceToken);
    _tokenStream = FirebaseMessaging.instance.onTokenRefresh;
    _tokenStream.listen(cioTool.registerDeviceToken);
  }

  void initializeNotifications() {
    //create a function to receive messages
    _receiveMessages();

    //create a function to receive messages when the app is in background
    _receiveMessageWhenTap();

    //create a function to receive messages when the app is terminated
    _receiveMessagesWhenTerminated();
  }

  /// Function to receive notification messages
  void _receiveMessages() {
    FirebaseMessaging.onMessage.listen((message) =>
        Tools().cioTools.showForegroundNotification(message.toMap()));
  }

  /// Function to receive messages when the user press on the notification
  void _receiveMessageWhenTap() {
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      Tools.debugPrint('A new onMessageOpenedApp event was published!');
      Tools.debugPrint('Message data: ${message.data}');

      if (message.notification != null) {
        Tools.debugPrint(
            'Message also contained a notification: ${message.notification}');
      }
    });
  }

  /// Function to receive messages when the app is terminated
  void _receiveMessagesWhenTerminated() {
    FirebaseMessaging.instance
        .getInitialMessage()
        .then((RemoteMessage? message) {
      if (message != null) {
        Tools.debugPrint('A new getInitialMessage event was published!');
        Tools.debugPrint('Message data: ${message.data}');

        if (message.notification != null) {
          Tools.debugPrint(
              'Message also contained a notification: ${message.notification}');
        }
      }
    });
  }

  void showFlutterNotification(RemoteMessage message) {
    if (message.data.isNotEmpty && message.notification == null) {
      _localNotifications.showFirebaseNotification(message.data);
    }
  }
}
