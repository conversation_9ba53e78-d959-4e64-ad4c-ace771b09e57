import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/class/location_manager.dart';
import 'package:mybuddy/class/power.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/workout/walk_page.dart';

class LocationTools extends CommonTools {
  Future<void> getLocationPermissions(
      {Function? onGranted, Function? onDenied}) async {
    try {
      bool allOk = await LocationManager().initLocationListening();
      if (allOk && onGranted != null) await onGranted();
    } catch (e) {
      if (onDenied != null) await onDenied();
    }
  }

  Future<bool> getBatterPowerSaverStatus() async {
    bool lpmEnabled = await Power.isLowPowerMode;
    Tools.debugPrint("Power mode $lpmEnabled");
    return lpmEnabled;
  }

  Future<void> openWorkoutPage(BuildContext context) async {
    await getLocationPermissions(
      onGranted: () async {
        if (await getBatterPowerSaverStatus()) {
          String batterySaver = Platform.isIOS ? "Low Power" : "Battery Saver";
          // Show dialog to user about power save mode
          await Tools().common.showCustomDialog(
              prefixContent: [
                Image.asset(
                  "assets/images/ic_battery_bolt.png",
                  height: 50,
                  width: 46,
                ),
              ],
              title: "$batterySaver Mode",
              description:
                  "We recommend turning off $batterySaver mode or your walks may not track accurately.",
              buttonColor: const Color(0xff419563),
              validTitle: 'APPLICATION_MOBILE_BUTTON_GO_TO_SETTINGS'
                  .tr("Go To Settings"),
              rejectTitle: 'APPLICATION_MOBILE_BUTTON_LABEL_WALK_ANYWAY'.tr("Walk Anyway"),
              onValid: () async {
                // Take user to battery settings
                await AppSettings.openDeviceSettings();
                return true;
              },
              onReject: () async {
                // Launch walk
                var mapRes = await Tools().navigatorPush(WorkoutActivityPage());
                if (mapRes != null) {
                  Tools().common.showMessage(context, mapRes as String);
                }

                return true;
              });
          return true;
        } else {
          var mapRes = await Tools().navigatorPush(WorkoutActivityPage());
          if (mapRes != null) {
            Tools().common.showMessage(context, mapRes as String);
          }
          return true;
        }
      },
      onDenied: () async {
        return await showDialog(
          barrierColor: Colors.transparent,
          context: context,
          builder: (BuildContext ctx) {
            return AlertDialog(
              title: Text(
                'WOOFTRAX_ALERT_PERMISSION_DENIED_TITLE'
                    .tr('WoofTrax needs your location to track your dog walk.'),
              ),
              content: Text('WOOFTRAX_ALERT_LOCATION_PERMISSION_DENIED_MESSAGE'
                  .tr('Go to Settings, WoofTrax, and set location services to "While Using the App" and turn ON Precise Location.')),
              actions: <Widget>[
                TextButton(
                  onPressed: () async {
                    await AppSettings.openAppSettings();
                    Tools().navigatorPop(removeLast: false, value: false);
                  },
                  child: Text(
                      'WOOFTRAX_ALERT_LOCATION_PERMISSION_DENIED_OPEN_SETTINGS'
                          .tr('Go to Settings')),
                ),
                TextButton(
                  onPressed: () {
                    Tools().navigatorPop(removeLast: false, value: false);
                  },
                  child: Text(
                      'APPLICATION_MOBILE_BUTTON_LABEL_CANCEL'.tr('Cancel')),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
