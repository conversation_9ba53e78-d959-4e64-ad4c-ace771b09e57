import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/pet/widget_gallery_image_chooser.dart';
import 'package:permission_handler/permission_handler.dart';

import '../class/permissions_handling.dart';

class ImageTools extends CommonTools {
  File? pickedFileToFile(XFile? pFile, BuildContext context) {
    if (pFile == null) return null;
    if (!(pFile.path.toUpperCase().endsWith('.JPG') ||
        pFile.path.toUpperCase().endsWith('.JPEG'))) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: const Duration(seconds: 4),
        content: Center(
            heightFactor: 1.25,
            child: Text(
                'APPLICATION_MOBILE_LABEL_JPG_ONLY'.tr())), //TODO warn message
      ));
      return null;
    }
    return File(pFile.path);
  }

  Future<File?> pickImage(BuildContext context,
      {bool myBuddyGallery = false}) async {
    FocusScope.of(context).unfocus();
    int? choice = await showImageSourceChoiceDialog(context,
        delete: false, myBuddyGallery: myBuddyGallery);
    ImageSource source;

    switch (choice) {
      case 0:
        source = ImageSource.camera;
        break;
      case 1:
        source = ImageSource.gallery;
        break;
      case 3:
        var url = await Tools().navigatorPush(const WidgetGalleryImage());
        if (url == null || url is! String) {
          return null;
        }
        FileInfo? fileInfo = await DefaultCacheManager().getFileFromCache(url);
        if (fileInfo != null) {
          return fileInfo.file;
        }
        File file = await DefaultCacheManager().getSingleFile(url);
        return file;
      default:
        return null;
    }

    FocusScope.of(context).requestFocus(FocusNode());
    return pickedFileToFile(
        await ImagePicker().pickImage(source: source), context);
  }

  Future<File?> pickImageOrDelete(BuildContext context,
      {bool delete = false}) async {
    FocusScope.of(context).unfocus();
    int? choice = await showImageSourceChoiceDialog(context, delete: delete);
    ImageSource source;
    switch (choice) {
      case 0:
        source = ImageSource.camera;
        break;
      case 1:
        source = ImageSource.gallery;
        break;
      case 3:
      default:
        return null;
    }
    return pickedFileToFile(
        await ImagePicker().pickImage(source: source), context);
  }

  Future<int?> showImageSourceChoiceDialog(BuildContext context,
      {delete = false, myBuddyGallery = false}) async {
    List<Widget> list = <Widget>[
      TextButton(
        onPressed: () {
          Tools().navigatorPop(removeLast: false, value: 1);
        },
        child: Text(
          'APPLICATION_MOBILE_BUTTON_LABEL_PICTURE_SELECT_FROM_DEVICE_GALLERY'
              .tr(),
        ),
      ),
      const Divider(),
      TextButton(
        onPressed: () {
          Tools().navigatorPop(removeLast: false, value: 0);
        },
        child: Text(
          'APPLICATION_MOBILE_BUTTON_LABEL_PICTURE_TAKE_PICTURE'.tr(),
        ),
      )
    ];

    if (delete) {
      list.add(const Divider());
      list.add(TextButton(
        onPressed: () {
          Tools().navigatorPop(removeLast: false, value: 2);
        },
        child: Stack(
          children: <Widget>[
            Text(
              'APPLICATION_MOBILE_BUTTON_LABEL_DELETE'.tr('Delete'),
              style: TextStyle(
                foreground: Paint()
                  ..style = PaintingStyle.stroke
                  ..strokeWidth = 1
                  ..color = Colors.white,
              ),
            ),
            Text(
              'APPLICATION_MOBILE_BUTTON_LABEL_DELETE'.tr('Delete'),
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ],
        ),
      ));
    }

    if (myBuddyGallery) {
      list.add(const Divider());
      list.add(TextButton(
        onPressed: () {
          Tools().navigatorPop(removeLast: false, value: 3);
        },
        child: Text(
          'APPLICATION_MOBILE_BUTTON_LABEL_PICTURE_SELECT_FROM_PET_GALLERY'
              .tr(),
        ),
      ));
    }
    return await showModalBottomSheet<int>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: list,
          ),
        );
      },
    );
  }

  Future<File?> pickAndCropAvatar(BuildContext context,
      {Object? entity,
      bool petGallery = false,
      titleOnCropper = "Crop your avatar"}) async {
    dynamic result = await _showModalBottomSheet(context,
        entity: entity, myBuddyGallery: petGallery);

    if (result != null) {
      Image image =
          result is String ? Image.network(result) : Image.file(result);

      File? croppedFile = await _cropImage(
          titleOnCropper, result is String ? result : result.path, context);

      if (croppedFile != null) {
        return croppedFile;
      }
      return null;
    }
    return null;
  }

  Future<File?> _cropImage(
      String titleOnCropper, String source, BuildContext context) async {
    CroppedFile? croppedFile = await ImageCropper().cropImage(
      aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
      sourcePath: source,
      aspectRatioPresets: [CropAspectRatioPreset.square],
      cropStyle: CropStyle.rectangle,
      maxHeight: 300,
      maxWidth: 300,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: titleOnCropper,
          toolbarColor: Theme.of(context).colorScheme.secondary,
          toolbarWidgetColor: Colors.white,
          activeControlsWidgetColor: Theme.of(context).colorScheme.secondary,
          backgroundColor: const Color(0xffededed),
          statusBarColor: Theme.of(context).colorScheme.secondary,
          initAspectRatio: CropAspectRatioPreset.square,
          lockAspectRatio: true,
          showCropGrid: false,
        ),
        IOSUiSettings(
          title: titleOnCropper,
          aspectRatioPickerButtonHidden: true,
          aspectRatioLockEnabled: true,
          resetAspectRatioEnabled: false,
        ),
      ],
    );

    if (croppedFile != null) {
      return File(croppedFile.path);
    }

    return null;
  }

  Future<dynamic> _showModalBottomSheet(BuildContext context,
      {Object? entity,
      bool myBuddyGallery = false,
      bool delete = false}) async {
    FocusScope.of(context).unfocus();

    PickChoice? choice = await showModalBottomSheet<PickChoice>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: PickChoice.values.where((c) {
              /// need to filter choices
              if ((c == PickChoice.deleteImage && false == delete)) {
                return false;
              }
              return true;
            }).map((PickChoice choice) {
              return _textButtonWidget(
                context,
                label: choice.toStr!,
                choice: choice,
              );
            }).toList(),
          ),
        );
      },
    );

    ImageSource? source;
    switch (choice) {
      case PickChoice.phoneCamera:
        source = ImageSource.camera;
        break;
      case PickChoice.phoneGallery:
        source = ImageSource.gallery;
        break;
      case PickChoice.deleteImage:
        return 0;
      default:
        return null;
    }

    FocusScope.of(context).requestFocus(FocusNode());

    try {
      return pickedFileToFile(
          await ImagePicker().pickImage(source: source), context);
    } catch (e) {
      handleInvalidPermissions(
          PermissionStatus.denied,
          'WOOFTRAX_ALERT_GALLERY_PERMISSION_DENIED_TITLE'.tr(
              "WoofTrax requires photos and camera permission. Kindly enable the permission from the settings."));
      return null;
    }
  }

  Future<PermissionStatus> _getImagePermission(ImageSource source) async {
    PermissionStatus permission = await Permission.storage.status;
    if (permission != PermissionStatus.granted) {
      PermissionStatus permissionStatus = await Permission.storage.request();
      return permissionStatus;
    } else {
      return permission;
    }
  }

  Future<bool> askGalleryOrCameraPermissions(ImageSource source) async {
    PermissionStatus permissionStatus = await _getImagePermission(source);
    if (permissionStatus == PermissionStatus.granted) {
      return true;
    } else {
      handleInvalidPermissions(
          permissionStatus,
          'WOOFTRAX_ALERT_GALLERY_PERMISSION_DENIED_TITLE'.tr(
              source == ImageSource.gallery
                  ? 'Media permission denied'
                  : 'Camera permission denied'));
      return false;
    }
  }
}

enum PickChoice { phoneGallery, phoneCamera, deleteImage }

extension PickChoiceExtension on PickChoice {
  String? get toStr {
    switch (this) {
      case PickChoice.phoneCamera:
        return 'APPLICATION_MOBILE_BUTTON_LABEL_PICTURE_TAKE_PICTURE'.tr();
      case PickChoice.phoneGallery:
        return 'APPLICATION_MOBILE_BUTTON_LABEL_PICTURE_SELECT_FROM_DEVICE_GALLERY'
            .tr();
      case PickChoice.deleteImage:
        return 'APPLICATION_MOBILE_BUTTON_LABEL_DELETE'.tr();
      default:
        return '';
    }
  }
}

Widget _textButtonWidget(BuildContext context,
    {PickChoice? choice, required String label}) {
  return Column(
    children: [
      if (choice != PickChoice.phoneGallery) const Divider(),

      ///endif
      TextButton(
        onPressed: () => Tools().navigatorPop(removeLast: false, value: choice),
        child: Text(label),
      ),
    ],
  );
}
