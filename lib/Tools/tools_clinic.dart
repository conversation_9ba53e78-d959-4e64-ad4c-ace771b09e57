import 'package:flutter/material.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/ui/templates/service/add_edit_service.dart';
import 'package:mybuddy/ui/templates/service/widget_code_clinic_dialog.dart';

import 'tools_common.dart';

class ClinicTools extends CommonTools {
  Future<void> addClinicAction(BuildContext context) async {
    int? choice = await _clinicDialogAction(context);
    switch (choice) {
      case 1:
//        String code =
        await _clinicCodeAction(context);
        break;
      case 2:
        await Tools().navigatorPush(AddEditServiceForm(
          title: 'APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_CLINIC'.tr(),
        ));
        break;
    }
  }

  Future<String?> _clinicCodeAction(BuildContext context) async {
    return await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext ctx) {
        return AlertDialog(
          title: Text(
            'APPLICATION_MOBILE_TEXT_CLINIC_ADD_MANUALLY'.tr(),
            style: const TextStyle(
                color: Colors.blue, fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
              child: WidgetCodeClinicDialog(
            onSubmit: () {},
          )),
        );
      },
    );
  }

  Future<int?> _clinicDialogAction(BuildContext context) async {
    return await showModalBottomSheet<int>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text('APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_CLINIC'.tr(),
                  style: Theme.of(context).textTheme.titleLarge),
              TextButton(
                onPressed: () {
                  Tools().navigatorPop(value: 1, removeLast: false);
                },
                child: Text(
                  'APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_BY_CODE'.tr(),
                  textAlign: TextAlign.center,
                ),
              ),
              const Divider(),
              TextButton(
                onPressed: () {
                  Tools().navigatorPop(value: 2, removeLast: false);
                },
                child: Text(
                  'APPLICATION_MOBILE_BUTTON_LABEL_SERVICE_ADD_MANUALLY'.tr(),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
