import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mybuddy/Api/mb_api_com.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/message.dart';
import 'package:mybuddy/models/service.dart';
import 'package:mybuddy/ui/templates/message/message_page.dart';

import 'tools_common.dart';

class MessageTools extends CommonTools {
  final _formKey = GlobalKey<FormState>();

  Future<void> addNewMessage(BuildContext context, {MBService? service}) async {
    MBMessage _message = MBMessage();
    MBService? _service = service ?? Data().getFavoriteClinic();
    if (_service != null && !_service.acceptMessage) {
      _service = null;
    }
    List<MBService> services = Data().getClinicsForMessage();
    return await showDialog(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          titlePadding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10.0,),
          contentPadding: const EdgeInsets.symmetric(horizontal: 15.0),
          actionsPadding: const EdgeInsets.only(bottom: 10.0),
          title: Text(
            'APPLICATION_MOBILE_MESSAGING_ADD_DISCUSSION'.tr(),
            maxLines: 2,
          ),
          content: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 5.0),
                  child: TextFormField(
                    keyboardType: TextInputType.multiline,
                    decoration: InputDecoration(
                      hintText: 'APPLICATION_MOBILE_FIELD_LABEL_TITLE'.tr(),
                      labelText: 'APPLICATION_MOBILE_FIELD_LABEL_TITLE'.tr(),
                    ),
                    initialValue: '',
                    inputFormatters: [LengthLimitingTextInputFormatter(4096)],
                    onSaved: (val) {
                      if (val != null) {
                        _message.title = val.trim();
                      }
                    },
                    validator: (val) => val == null || val.isEmpty || val.trim() == ''
                        ? 'APPLICATION_MOBILE_MESSAGE_ERROR_PRESENCE_TITLE'.tr()
                        : null,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 5.0),
                  child: DropdownButtonFormField<MBService>(
                    value: _service,
                    decoration: InputDecoration(
                      hintText:
                          'APPLICATION_MOBILE_LABEL_SERVICE_CLINIC'.tr() + '*',
                      labelText: 'APPLICATION_MOBILE_LABEL_SERVICE_CLINIC'.tr(),
                    ),
                    onChanged: (val) => _message.serviceId = val?.id,
                    isExpanded: true,
                    validator: (val) {
                      return val != null
                          ? null
                          : 'APPLICATION_MOBILE_MESSAGE_APPOINTMENT_ERROR_SERVICE'
                              .tr();
                    },
                    onSaved: (val) {
                      _message.serviceId = val?.id;
                    },
                    items: services.map((MBService service) {
                      return DropdownMenuItem<MBService>(
                        value: service,
                        child: Text(
                          service.name ?? '',
                          overflow: TextOverflow.visible,
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  await _submitForm(context, _message);
                },
                child: Text('APPLICATION_MOBILE_BUTTON_LABEL_SUBMIT'.tr()),
              ),
            )
          ],
        );
      },
    );
  }

  Future<void> _submitForm(BuildContext context, MBMessage message) async {
    final FormState? form = _formKey.currentState;

    if (form == null || !form.validate()) {
      showValidationErrorMessage(context);
      return;
    }
    form.save();

    MBResponse? response = await MbApiCom().addMessageRequest(context, message);

    if (response != null && response.success) {
      Tools().navigatorPop(value: true);
      MBMessage? m = response.entity as MBMessage?;
      if (m != null) {
        await Tools().navigatorPush(
          MessagePage(message: m),
        );
      }
    }
  }
}
