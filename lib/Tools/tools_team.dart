import 'dart:async';

import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/material.dart';
import 'package:mybuddy/Api/mb_api_community.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/community.dart';
import 'package:mybuddy/ui/components/mb_stackable_avatar.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../class/constants.dart';
import '../class/settings_delegate.dart';
import '../ui/templates/community/community_team_screen.dart';
import 'tools_common.dart';

class TeamTools extends CommonTools {
  Future<Team?> showJoiningTeamDialog(int? teamId) async {
    if (teamId == null) {
      return null;
    }
    Team? teamInData = Data().get().getTeam(teamId);
    if (teamInData != null) {
      return teamInData;
    }
    MBResponse response = await MbApiCommunity().getTeam(teamId);
    if (response.entity == null) {
      showMessage(
        Tools().homeContext,
        'APPLICATION_MOBILE_TEXT_EMPTY_DATA'.tr(),
      );
      return null;
    }
    Team team = response.entity as Team;

    bool joiningTeam = await showModalBottomSheet<bool>(
      context: Tools().homeContext,
      isDismissible: false,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(15.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 25.0),
                      child: MBAvatar(
                        size: 75.0,
                        imageUrl: team.imageUrl,
                        backgroundColor: Theme.of(context).colorScheme.surface,
                        noImageUrl: const Icon(Icons.image, size: 20.0),
                      ),
                    ),
                    Text(
                      team.name,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 15.0),
              RichText(
                text: TextSpan(
                  text: 'APPLICATION_MOBILE_LABEL_COMMUNITY_TEAM_CREATED_BY'
                      .tr('Created by'),
                  style: Theme.of(context).textTheme.bodyMedium,
                  children: [
                    const TextSpan(text: ' '),
                    TextSpan(text: team.adminFirstName),
                    const TextSpan(text: ' '),
                    TextSpan(text: team.adminLastName),
                  ],
                ),
              ),
              Text(
                team.description ?? '',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text(
                '${team.teamMembersCount} ' +
                    'APPLICATION_MOBILE_LABEL_COMMUNITY_TEAM_MEMBERS'
                        .tr('members'),
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(height: 15.0),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      Tools().navigatorPop(removeLast: false, value: false);
                    },
                    child: Text('APPLICATION_MOBILE_LABEL_REFUSE_CGU'.tr()),
                  ),
                  TextButton(
                    onPressed: () {
                      MbApiCommunity().joinTeam(context, team).then((res) {
                        Tools().navigatorPop(
                            removeLast: false, value: res.success);
                      });
                    },
                    child: Text(
                      'APPLICATION_MOBILE_BUTTON_JOIN_TEAM'.tr('Join team'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    ) as bool;
    if (joiningTeam) {
      return team;
    }

    return null;
  }

  Future<Team?> update(int? teamId) async {
    if (teamId == null) {
      return null;
    }

    MBResponse response = await MbApiCommunity().getTeam(teamId);
    if (response.entity == null) {
      showMessage(
        Tools().homeContext,
        'APPLICATION_MOBILE_TEXT_EMPTY_DATA'.tr(),
      );
      return null;
    }
    Team team = response.entity as Team;
    Data().get().teams
      ..removeWhere((t) => t.id == teamId)
      ..add(team);
    Data().storeData();

    return team;
  }

  Future<void> processDynamicLinkData(PendingDynamicLinkData data) async {
    // Save the dynamic link data in shared preferences
    SharedPreferences prefs = SettingsDelegate().prefs;

    List<String> linkList = prefs.getStringList(dynamicLinkData) ?? [];
    linkList.add(data.link.toString());

    prefs.setStringList(dynamicLinkData, linkList);

    Team? team = await addMeToTeams();
    if (team != null) {
      Tools().navigatorPush(TeamScreen(team: team), preventDuplicates: false);
    }
  }

  /// This method is responsible for adding the logged in user to the packs
  /// that he has received via Dynamic links.
  ///
  /// It reads the links visited by this user from the Shared Preferences and
  /// then it extracts the pack ids from the links and calls an api to add it
  /// in the pack.
  ///
  Future<Team?> addMeToTeams() async {
    SharedPreferences prefs = SettingsDelegate().prefs;

    // Checking if user is logged in if yes then we try to add
    String? token = prefs.getString('token');
    if (token == null) return null;

    // Retrieving the dynamic links that the user has visited
    List<String> linkList = prefs.getStringList(dynamicLinkData) ?? [];
    if (linkList.isEmpty) return null; // No links, return

    // Removing duplicate entries from the visited dynamic links
    List<String> distinctLinkList = linkList.toSet().toList();
    List<String> processedTeamIds = [];
    Uri uri;
    String teamId;
    Team? team;
    String audience;
    MbApiCommunity apiCommunity = MbApiCommunity();

    for (String link in distinctLinkList) {
      // print(link);
      uri = Uri.parse(link);
      // If the user is coming from a public dynamic link, then and only
      // we need to call this api to add this user to a particular pack
      audience = uri.queryParameters['audience'] ?? "";
      if (audience != "public") continue;

      teamId = uri.queryParameters['teamId'] ?? "";
      // Checking if pack id is not null and is not already processed
      if (teamId.isNotEmpty && !processedTeamIds.contains(teamId)) {
        MBResponse teamResponse = await apiCommunity.getTeam(int.parse(teamId));
        if (teamResponse.entity != null) {
          team = teamResponse.entity as Team;
          Map<String, dynamic> body = Map.from(uri.queryParameters);
          MBResponse addMeResponse =
              await apiCommunity.addMeToTeamFromDynamicLink(null, body);

          if (addMeResponse.success) {
            // All good
            processedTeamIds.add(teamId);
            linkList.remove(link);
          }
        }
      }
    }

    // If everything is good, we remove the links from SharedPreference because
    // we only want to perform this activity once per link
    await prefs.setStringList(dynamicLinkData, linkList.toSet().toList());
    return team;
  }
}
