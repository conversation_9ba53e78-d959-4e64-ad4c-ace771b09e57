import 'dart:async';

import 'package:mybuddy/Tools/tools_common.dart';

// ignore_for_file: constant_identifier_names
enum Event {
  WALK_START,
  WALK_END,
  WALK_PAUSE,
  WALK_RESUME,
  WAL<PERSON>_DISCARD,
  WRITE_GPX,
  INSERT_WORKOUT,
  INSERT_WORKOUT_DOG,
  UPLOAD_WALK,
  UPLOAD_WALK_SUCCESS,
  INITIALIZE_WALK,
  CLEAR_SHARED_PREF,
  SHARED_PREF_WALK,
  DUPLICATE_WORKOUT,
}

class CIOTools extends CommonTools {
  StreamSubscription? subscription;

  /// Function to initialize Customer.io SDK and identify the login user from the portal
  Future<void> initialize() async {
    // Temporarily disabled CustomerIO functionality to resolve build issues
    /*
    await CustomerIO.initialize(
      config: CustomerIOConfig(
        siteId: customerIOSiteID,
        apiKey: customerIOAPIKey,
        region: Region.us,
        enableInApp: true,
        backgroundQueueSecondsDelay: 5,
        // Region is optional, defaults to Region.US.
        // Use Region.EU for EU-based workspaces.
      ),
    );
    subscribeToInAppListener();
    */
  }

  /// Function to identify the login user from the portal
  Future identifyCIOUser() async {
    // Temporarily disabled CustomerIO functionality to resolve build issues
    /*
    String? userId = Data().get().owner.objectId;
    String? email = Data().get().owner.email;

    if (userId != null) {
      CustomerIO.identify(identifier: userId, attributes: {
        'id': userId,
        'email': email,
      });
    }
    */
  }

  /// Function to register the device token for push notification on Customer.io
  void registerDeviceToken(String? token) {
    // Temporarily disabled CustomerIO functionality to resolve build issues
    /*
    if (token != null) {
      CustomerIO.registerDeviceToken(deviceToken: token);
    }
    */
  }

  // function to log a debug event to CustomerIO
  void logDebugEvent(Event e, Map<String, dynamic> attributes) {
    // Temporarily disabled CustomerIO functionality to resolve build issues
    /*
    String eventName = 'DEBUG_LOG';

    if (e == Event.DUPLICATE_WORKOUT) {
      eventName = eventName + "_DUPLICATE";
    }

    attributes.addAll({'debug_event_name': e.name});
    CustomerIO.track(name: eventName, attributes: attributes);
    */
  }

  /// Function to clear the identify of the login user
  void clear() {
    // Temporarily disabled CustomerIO functionality to resolve build issues
    /*
    CustomerIO.clearIdentify();
    unsubscribeToInAppListener();
    */
  }

  /// Function to set screen event on Customer.io
  void setScreenEvent(String screenName) {
    // Temporarily disabled CustomerIO functionality to resolve build issues
    /*
    CustomerIO.screen(
      name: screenName,
    );
    */
  }

  /// Function to subscribe and track the in app messaging event on Customer.io
  void subscribeToInAppListener() {
    // Temporarily disabled CustomerIO functionality to resolve build issues
    /*
    // subscribe to stream
    subscription = CustomerIO.subscribeToInAppEventListener((InAppEvent event) {
      // complete cases for event.eventType
      switch (event.eventType) {
        case EventType.messageShown:
          Tools.debugPrint("messageShown: ${event.message}");
          break;
        case EventType.messageDismissed:
          Tools.debugPrint("messageDismissed: ${event.message}");
          break;
        case EventType.errorWithMessage:
          Tools.debugPrint("errorWithMessage: ${event.message}");
          break;
        case EventType.messageActionTaken:
          // event.actionValue => The type of action that triggered the event.
          // event.actionName => The name of the action specified when building the in-app message.
          Tools.debugPrint("messageActionTaken: ${event.message}");
          break;
      }
    });
    */
  }

  /// Function to unsubscribe in app messaging events
  void unsubscribeToInAppListener() {
    // Temporarily disabled CustomerIO functionality to resolve build issues
    /*
    // to unsubscribe from the event listener
    subscription?.cancel();
    */
  }

  /// function to redirect notification to CustomerIO SDK
  void showForegroundNotification(Map<String, dynamic> message) {
    // Temporarily disabled CustomerIO functionality to resolve build issues
    /*
    CustomerIO.messagingPush().onMessageReceived(message).then((handled) {
      // handled is true if notification was handled by Customer.io SDK; false otherwise
      return handled;
    });
    */
  }

  /// function to redirect notification to CustomerIO SDK
  void showBackgroundNotification(Map<String, dynamic> message) {
    // Temporarily disabled CustomerIO functionality to resolve build issues
    /*
    CustomerIO.messagingPush()
        .onBackgroundMessageReceived(message)
        .then((handled) {
      // handled is true if notification was handled by Customer.io SDK; false otherwise
      return handled;
    });
    */
  }
}
