import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:mybuddy/Tools/tools_common.dart';

class NativeSplashTools extends CommonTools {
  static void initialize() {
    WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
    FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  }

  static void hideSplashScreen() {
    FlutterNativeSplash.remove();
  }
}
