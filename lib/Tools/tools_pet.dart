import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:mybuddy/Api/mb_api_ref.dart';
import 'package:mybuddy/Api/mb_response.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/string_extensions.dart';
import 'package:mybuddy/models/healthbook.dart';
import 'package:mybuddy/models/pet.dart';
import 'package:mybuddy/models/pet_activity.dart';
import 'package:mybuddy/models/petcarepacks.dart';
import 'package:mybuddy/ui/templates/pet/add_edit_pet.dart';
import 'package:mybuddy/ui/templates/petcarepack/petcarepack_folder_page.dart';

import 'tools_common.dart';

class PetTools extends CommonTools {
  Future<void> addPetAction(BuildContext context, Pet? pet) async {
    //TODO HIGH ADD RECONCILIATE
    await Tools().navigatorPush(
      AddEditPetForm(
          title: pet == null
              ? 'APPLICATION_MOBILE_BUTTON_LABEL_DOG_ADD'.tr()
              : 'APPLICATION_MOBILE_BUTTON_LABEL_EDIT'.tr(),
          pet: pet),
    );
  }

  HealthBook getHealthBookData(Pet? pet) {
    HealthBook hb = HealthBook(pet);

    for (TreatmentActivity treatmentActivity in hb.treatments) {
      List<PetActivity> trPetActivities = Data().get().petActivities.where((pa) {
        return pa.activityId == 9 &&
            pa.forThisPet(pet: pet) &&
            pa.hasThisTreatment(treatment: treatmentActivity.treatment);
      }).toList();

      ///sort if it's not a neutering
      if (treatmentActivity.treatment.type != 3) {
        trPetActivities.sort((a, b) {
          DateTime? aDate = a.dateEnd ?? a.dateStart;
          DateTime? bDate = b.dateEnd ?? b.dateStart;
          if (aDate == null) {
            return 1;
          } else if (bDate == null) {
            return -1;
          } else {
            return aDate.compareTo(bDate);
          }
        });
      }
      if (trPetActivities.isNotEmpty) {
        treatmentActivity.petActivity = trPetActivities.last;
      }
    }

    ///debug only
//    for (TreatmentActivity trA in hb.treatments) {
//      if(trA.petActivity != null) {
//        print('${trA.treatment.type} ${trA.treatment.valence} ${trA.petActivity
//            .dateEnd}');
//      }else{
//        print('${trA.treatment.type} ${trA.treatment.valence}');
//      }
//    }
    return hb;
  }

  Future getPetCarePack(PetCarePack pcp, BuildContext context) async {
    MBResponse response = await MbApiRef().getPetCarePackRequest(context, pcp.id);
    if (response.success && response.dataString != null) {
      pcp = PetCarePack.fromJson(json.decode(response.dataString!)['petCarePack']);
      await Tools().navigatorPush(
        PetCarePackPageFolder(items: pcp.items, title: pcp.title, petCarePack: pcp),
      );
    }
  }

  String getPetListStr(List<Pet>? pets) {
    if (pets == null) return '';
    List<String> _pets = <String>[];
    for (var p in pets) {
      Pet? pet = Data().get().getPet(p.id);
      if (pet != null) {
        _pets.add(pet.name);
      }
    }
    return _pets.join(', ');
  }
}
