
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/Tools/tools_common.dart';
import 'package:mybuddy/class/ad_manager.dart';

class AdMobTools extends CommonTools {
  AdMobTools();

  InterstitialAd? _interstitialAd;

  Future<void> loadInterstitialAd() async {
    InterstitialAd.load(
      adUnitId: AdManager.interstitialAdUnitId, // TODO GMA Replace demo ad unit ID by AdManager adUnitId
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (InterstitialAd ad) {
          Tools.debugPrint('$ad loaded.');
          // Keep a reference to the ad so you can show it later.
          // this._rewardedAd = ad;
          _interstitialAd = ad;
        },
        onAdFailedToLoad: (LoadAdError error) {
          Tools.debugPrint('RewardedAd failed to load: $error');
          _interstitialAd = null;
        },
      ),
    );
  }

  Future<void> showInterstitialAd({Function? dismissedCallback}) async {
    try {
      await _interstitialAd?.show();
      if (dismissedCallback != null) {
        _interstitialAdDismissCallback(dismissedCallback: dismissedCallback);
      }
    } catch (e, trace) {
      Tools.debugPrint("Ads load failed: $e");
      Tools.sendErrorToCrashlytics(e, trace);
      if (dismissedCallback != null) dismissedCallback();
    }
  }

  void _interstitialAdDismissCallback({required Function dismissedCallback}) {
    _interstitialAd?.fullScreenContentCallback = FullScreenContentCallback(
      onAdDismissedFullScreenContent: (InterstitialAd ad) {
        dismissedCallback();
        ad.dispose();
      },
    );
  }

  void disposeInterstitialAd() {
    _interstitialAd?.dispose();
  }
}
