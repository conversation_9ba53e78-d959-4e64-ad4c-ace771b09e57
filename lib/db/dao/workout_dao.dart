import 'package:mybuddy/Tools/tools.dart';
import 'package:mybuddy/db/database_service.dart';
import 'package:mybuddy/db/models/workout_db.dart';
import 'package:sqflite/sqflite.dart';

class WorkoutDao {
  final Database db;

  const WorkoutDao({required this.db});

  Future<List<WorkoutDb>> getWorkoutsWithLimitAndOffset(
      String startDate,
      String endDate,
      String moodTypes,
      String dogIds,
      int recordLimit,
      int recordOffset) async {
    String sqlQuery = "";

    if (dogIds == "") {
      sqlQuery = "SELECT * FROM ${DatabaseService.TABLE_WORKOUT} "
          "WHERE createdAt >= '$startDate' AND createdAt <= '$endDate' "
          "AND moodType IN ($moodTypes) "
          "ORDER BY createdAt DESC "
          "LIMIT $recordLimit OFFSET $recordOffset";
    } else {
      sqlQuery = "SELECT * FROM ${DatabaseService.TABLE_WORKOUT} "
          "WHERE createdAt >= '$startDate' AND createdAt <= '$endDate' "
          "AND moodType IN ($moodTypes) "
          "AND id IN (SELECT workoutId FROM ${DatabaseService.TABLE_WORKOUT_DOGS} WHERE dogId IN ($dogIds)) "
          "ORDER BY createdAt DESC "
          "LIMIT $recordLimit OFFSET $recordOffset";
    }

    final List<Map<String, dynamic>> maps = await db.rawQuery(sqlQuery);

    // Convert the List<Map<String, dynamic> into a List<WorkoutDb>.
    return List.generate(maps.length, (i) {
      return WorkoutDb.fromMap(maps[i]);
    });
  }

  /// This method fetches the workout history stats with applied filters
  Future<Map<String, dynamic>> getWorkoutsSummaryWithFilters(
      String startDate, String endDate, String moodTypes, String dogIds) async {
    String sqlQuery = "";

    if (dogIds == "") {
      sqlQuery =
          "SELECT SUM(distance) as totalDistance, SUM(duration) as totalDuration, SUM(points) as totalPoints, COUNT(*) as totalWalks "
          "FROM ${DatabaseService.TABLE_WORKOUT} "
          "WHERE createdAt >= '$startDate' AND createdAt <= '$endDate' "
          "AND moodType IN ($moodTypes)";
    } else {
      sqlQuery =
          "SELECT SUM(distance) as totalDistance, SUM(duration) as totalDuration, SUM(points) as totalPoints, COUNT(*) as totalWalks "
          "FROM ${DatabaseService.TABLE_WORKOUT} "
          "WHERE createdAt >= '$startDate' AND createdAt <= '$endDate' "
          "AND moodType IN ($moodTypes) "
          "AND id IN (SELECT workoutId FROM ${DatabaseService.TABLE_WORKOUT_DOGS} WHERE dogId IN ($dogIds))";
    }

    final List<Map<String, dynamic>> maps = await db.rawQuery(sqlQuery);

    int totalWalks = 0;
    double totalDuration = 0;
    double totalDistance = 0;
    int totalPoints = 0;

    if (maps.isNotEmpty) {
      totalWalks = maps[0]['totalWalks'] ?? 0;
      totalDuration = maps[0]['totalDuration']?.toDouble() ?? 0.0;
      totalDistance = maps[0]['totalDistance']?.toDouble() ?? 0.0;
      totalPoints = maps[0]['totalPoints'] ?? 0;
    }

    return {
      'walks': totalWalks,
      'duration': totalDuration,
      'distance': totalDistance,
      'points': totalPoints
    };
  }

  Future<int?> insertWorkout(WorkoutDb workout) async {
    // Insert the Workout into the correct table. You might also specify the
    // `conflictAlgorithm` to use in case the same dog is inserted twice.
    //
    // In this case, replace any previous data.
    try {
      int rowId = await db.insert(
        DatabaseService.TABLE_WORKOUT,
        workout.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      return rowId;
    } catch (exception, stackTrace) {
      Tools.sendErrorToCrashlytics(exception, stackTrace);
      return null;
    }
  }

  Future<void> insertWorkouts(List<WorkoutDb> workouts) async {
    // Insert the Workout into the correct table. You might also specify the
    // `conflictAlgorithm` to use in case the same dog is inserted twice.
    //
    // In this case, replace any previous data.
    try {
      Batch batch = db.batch();

      for (var element in workouts) {
        batch.insert(
          DatabaseService.TABLE_WORKOUT,
          element.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      List<Object?> ids = await batch.commit(noResult: false);
      for (var i = 0; i < workouts.length; i++) {
        workouts[i].id = int.parse(ids[i].toString());
      }
    } catch (e, trace) {
      Tools.sendErrorToCrashlytics(e, trace);
    }
  }

  Future<int> updateWorkout(WorkoutDb workout) async {
    // Update the given Workout.
    int updatedId = await db.update(
      DatabaseService.TABLE_WORKOUT,
      workout.toMap(),
      // Ensure that the Workout has a matching id.
      where: 'id = ?',
      // Pass the Workout's id as a whereArg to prevent SQL injection.
      whereArgs: [workout.id],
    );

    return updatedId;
  }

  Future<int> updateGpxFileName(int workoutId, String fileName) async {
    // Update workout with new gpx filename
    return await db.rawUpdate('''
    UPDATE ${DatabaseService.TABLE_WORKOUT}
    SET gpxFileName = ?
    WHERE workoutId = ?
    ''', [fileName, workoutId]);
  }

  Future<void> deleteWorkout(WorkoutDb workout) async {
    // Remove the Workout from the database.
    await db.delete(
      DatabaseService.TABLE_WORKOUT,
      // Use a `where` clause to delete a specific dog.
      where: 'id = ?',
      // Pass the Workout's id as a whereArg to prevent SQL injection.
      whereArgs: [workout.id],
    );
  }

  Future<void> clear() async {
    // Remove the Workout from the database.
    await db.delete(DatabaseService.TABLE_WORKOUT);
  }

  Future<List<WorkoutDb>> getUnSyncedWorkouts() async {
    final List<Map<String, dynamic>> maps = await db.query(
        DatabaseService.TABLE_WORKOUT,
        where: 'isUploaded = ?',
        whereArgs: [0]);

    // Convert the List<Map<String, dynamic> into a List<WorkoutDb>.
    return maps.map((e) => WorkoutDb.fromMap(e)).toList();
  }

  Future<WalkStats> getTodaysStats() async {
    // we use first 10 characters of createdAt which gives us YYYY-MM-DD
    // we compare that to current localTime to get Today's workouts only
    final res = await db.rawQuery('''
      SELECT 
      ifnull(count(id), 0) as totalWalks, 
      ifnull(sum(distance), 0.0) * 0.621 as totalDistance
      FROM ${DatabaseService.TABLE_WORKOUT}
      WHERE substr(createdAt, 1, 10)
            =date('now', 'localtime')
      ''');
    return WalkStats.fromJson(res.single);
  }

  Future<WalkStats> getThisWeeksStats() async {
    // stats of all walks from last sunday to today
    final daysFromSunday = DateTime.now().weekday;
    final res = await db.rawQuery('''
      SELECT 
      ifnull(count(id), 0) as totalWalks, 
      ifnull(sum(distance), 0.0) * 0.621 as totalDistance
      FROM ${DatabaseService.TABLE_WORKOUT}
      WHERE distance * 0.621 >= 0.25
      AND substr(createdAt, 1, 10)>= substr(datetime('now', '-$daysFromSunday days','localtime'), 1, 10)
      AND substr(createdAt, 1, 10)<= substr(datetime('now','localtime'), 1, 10)
      ''');
    return WalkStats.fromJson(res.single);
  }

  Future<WorkoutDb?> getWorkoutByID(int workoutID) async {
    try {
      String sqlQuery =
          "SELECT * FROM ${DatabaseService.TABLE_WORKOUT} WHERE workoutId=$workoutID";

      final List<Map<String, dynamic>> maps = await db.rawQuery(sqlQuery);

      if (maps.isEmpty) return null;

      return WorkoutDb.fromMap(maps.first);
    } catch (e, trace) {
      Tools.sendErrorToCrashlytics(e, trace);
      return null;
    }
  }
}
