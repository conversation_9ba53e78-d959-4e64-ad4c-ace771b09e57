import 'package:mybuddy/db/database_service.dart';
import 'package:mybuddy/db/models/home_summary_db.dart';
import 'package:mybuddy/models/home_summary.dart';
import 'package:sqflite/sqflite.dart';

class HomeSummaryDao {
  final Database db;

  const HomeSummaryDao({required this.db});

  Future<void> setHomeSummary(HomeSummaryDb homeSummaryDb) async =>
      await db.insert(
        DatabaseService.TABLE_HOME_SUMMARY,
        homeSummaryDb.toJson(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

  Future<HomeSummaryDb> getAggregatedHomeSummary() async {
    final res = await db.rawQuery('''
      SELECT 
      totalWalks+newWalks AS totalWalks,
      totalMinutes+newMinutes AS totalMinutes,
      totalDistance+newDistance AS totalDistance,
      totalPoints+newPoints AS totalPoints,
      totalMigratedCharityPoints
      FROM
      (
      SELECT 
        count(id) AS newWalks, 
        ifnull(sum(duration), 0) AS newMinutes, 
        ifnull(sum(distance), 0) * 0.621 AS newDistance,
        ifnull(sum(points), 0) AS newPoints
      FROM ${DatabaseService.TABLE_WORKOUT} 
      WHERE isUploaded = 0
      ), home_summary
      ''');

    if(res.isEmpty) {
      return HomeSummaryDb();
    }

    return HomeSummaryDb.fromDbJson(res.single);
  }
}
