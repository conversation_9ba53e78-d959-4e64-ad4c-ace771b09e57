import 'package:mybuddy/db/database_service.dart';
import 'package:mybuddy/db/models/workout_db.dart';
import 'package:mybuddy/db/models/workout_dogs_db.dart';
import 'package:sqflite/sqflite.dart';

class WorkoutDogDao {
  final Database db;

  const WorkoutDogDao({required this.db});

  Future<List<WorkoutDogDb>> getWorkoutDogs(WorkoutDb workout) async {
    final List<Map<String, dynamic>> maps = await db.query(
        DatabaseService.TABLE_WORKOUT_DOGS,
        where: 'workoutId = ?',
        whereArgs: [workout.id]);

    // Convert the List<Map<String, dynamic> into a List<Dog>.
    return List.generate(maps.length, (i) {
      return WorkoutDogDb.fromMap(maps[i]);
    });
  }

  Future<void> insertWorkoutDogs(List<WorkoutDogDb> workoutDogs) async {
    // Insert the Workout into the correct table. You might also specify the
    // `conflictAlgorithm` to use in case the same dog is inserted twice.
    //
    // In this case, replace any previous data.
    Batch batch = db.batch();

    for (var element in workoutDogs) {
      batch.insert(
        DatabaseService.TABLE_WORKOUT_DOGS,
        element.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    await batch.commit(noResult: true);
  }

  Future<void> clear() async {
    // Remove the Workout from the database.
    await db.delete(DatabaseService.TABLE_WORKOUT_DOGS);
  }
}
