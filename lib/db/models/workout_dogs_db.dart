/// Table WorkoutDogs
class WorkoutDogDb {
  int? id;
  final int workoutId;
  final int dogId;

  WorkoutDogDb({
    this.id,
    required this.workoutId,
    required this.dogId,
  });

  // Convert a WorkoutDog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() => {
    'id': id,
    'workoutId': workoutId,
    'dogId': dogId,
  };


  // Convert a WorkoutDog into a Map. The keys must correspond to the names of the
  // columns in the database.
  factory WorkoutDogDb.fromMap(Map<String, dynamic> json) => WorkoutDogDb(
        id: json['id'],
        workoutId: json['workoutId'],
        dogId: json['dogId'],
      );

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  @override
  String toString() {
    return 'WorkoutDogDb{id: $id, workoutId: $workoutId, dogId: $dogId}';
  }
}