import 'dart:convert';
import 'dart:io';

import 'package:device_info/device_info.dart';
import 'package:ios_utsname_ext/extension.dart';
import 'package:mybuddy/class/data.dart';
import 'package:mybuddy/extensions/number_extensions.dart';
import 'package:mybuddy/models/unit.dart';

import '../../Tools/tools.dart';
import '../../class/app_storage.dart';
import '../../class/settings_delegate.dart';

/// Table Workouts
class WorkoutDb {
  int? id;
  int? workoutId;
  final int ownerId;
  final int status;
  final double distance;
  late int points;
  final double duration;
  final double speed;
  final int moodType;
  final int charityId;
  final String charityName;
  final double startLat;
  final double startLong;
  final double endLat;
  final double endLong;
  final String gpxFileName;
  late int isUploaded;
  final int? autoEndReason;
  final DateTime createdAt;
  final DateTime updatedAt;

  WorkoutDb({
    this.id,
    this.workoutId,
    required this.ownerId,
    required this.status,
    required this.distance,
    required this.points,
    required this.duration,
    required this.speed,
    required this.moodType,
    required this.charityId,
    required this.charityName,
    required this.startLat,
    required this.startLong,
    required this.endLat,
    required this.endLong,
    required this.gpxFileName,
    required this.isUploaded,
    this.autoEndReason,
    required this.createdAt,
    required this.updatedAt,
  });

  // Convert a WorkoutDb into a Map. The keys must correspond to the names of the
  // columns in the database.

  //toMap method
  Map<String, dynamic> toMap() => {
        'id': id,
        'workoutId': workoutId,
        'ownerId': ownerId,
        'status': status,
        'distance': distance,
        'points': points,
        'duration': duration,
        'speed': speed,
        'moodType': moodType,
        'charityId': charityId,
        'charityName': charityName,
        'startLat': startLat,
        'startLong': startLong,
        'endLat': endLat,
        'endLong': endLong,
        'gpxFileName': gpxFileName,
        'isUploaded': isUploaded,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
      };

  //fromMap method
  factory WorkoutDb.fromMap(Map<String, dynamic> json) => WorkoutDb(
        id: json['id'],
        workoutId: json['workoutId'],
        ownerId: json['ownerId'],
        status: json['status'],
        distance: json['distance'].toDouble(),
        points: json['points'],
        duration: json['duration'].toDouble(),
        speed: json['speed'].toDouble(),
        moodType: json['moodType'],
        charityId: json['charityId'],
        charityName: json['charityName'],
        startLat: json['startLat'].toDouble(),
        startLong: json['startLong'].toDouble(),
        endLat: json['endLat'].toDouble(),
        endLong: json['endLong'].toDouble(),
        gpxFileName: json['gpxFileName'],
        isUploaded: json['isUploaded'],
        autoEndReason: json['autoEndReason'],
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
      );

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  @override
  String toString() {
    return 'WorkoutDb{id: $id, workoutId: $workoutId, status: $status}';
  }

  //Implement toWalkJson to convert to json for walk api
  Future<Map<String, dynamic>> toWalkJson() async {
    Map<String, dynamic> data = {
      'status': status,
      'duration': duration.minuteToMillisecond(),
      'averageSpeed': speed,
      'type': moodType,
      'autoEndReason': autoEndReason,
      'shelter': {"id": charityId, "name": charityName}
    };

    if (workoutId != null) {
      data['id'] = workoutId;
    }

    data['distance'] = distance
        .toUserLength(src: LengthSource.kilometers)
        .toLocalStringAsFixed();
    data['api'] = SettingsDelegate().prefs.getString('version');
    data['phone_os'] = Platform.operatingSystemVersion;
    int phoneType = 0;
    String phoneModel = '';
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      phoneType = 1;
      AndroidDeviceInfo phone = await deviceInfo.androidInfo;
      phoneModel = phone.model;
      data['phone_os'] = phone.version.release;
    } else if (Platform.isIOS) {
      phoneType = 3;
      IosDeviceInfo phone = await deviceInfo.iosInfo;
      phoneModel = phone.utsname.machine.iOSProductName;
    }
    data['phone_model'] = phoneModel;
    data['phone_type'] = phoneType;
    data['app_version'] = Tools().appVersion;
    data['gpx'] = base64Encode(
        utf8.encode(await AppStorage().readGpx(gpxFileName) ?? ""));
    data['createdAt'] = Data.dateTimeToApiDateTimeStr(createdAt);
    data['updatedAt'] = Data.dateTimeToApiDateTimeStr(updatedAt);

    return data;
  }
}

class WalkStats {
  const WalkStats({
    required this.totalWalks,
    required this.totalDistance,
  });

  /// Creates a TodaysStats from Json map
  factory WalkStats.fromJson(Map<String, dynamic> json) => WalkStats(
        totalWalks: json['totalWalks'] as int,
        totalDistance: json['totalDistance'] as double,
      );

  final int totalWalks;
  final double totalDistance;
}
