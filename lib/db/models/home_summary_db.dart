import 'package:mybuddy/extensions/mybuddy_extensions.dart';

class HomeSummaryDb {
  HomeSummaryDb({
    this.totalPoints = 0,
    this.totalWalks = 0,
    this.totalDistance = 0,
    this.totalMinutes = 0,
    this.totalMigratedCharityPoints = 0,
  });

  late int totalPoints;
  late int totalWalks;
  late double totalDistance;
  late double totalMinutes;
  late int totalMigratedCharityPoints;

  /// Creates a HomeSummaryDb from HomeSummary
  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': 1,
        'totalPoints': totalPoints,
        'totalWalks': totalWalks,
        'totalDistance': totalDistance,
        'totalMinutes': totalMinutes,
        'totalMigratedCharityPoints': totalMigratedCharityPoints
      };

  factory HomeSummaryDb.fromDbJson(Map<String, dynamic> json) => HomeSummaryDb(
        totalWalks: json['totalWalks'],
        totalDistance: double.parse(json['totalDistance'].toString()),
        totalPoints: json['totalPoints'],
        totalMinutes: double.parse(json['totalMinutes'].toString()),
        totalMigratedCharityPoints: json['totalMigratedCharityPoints'],
      );

  String get totalDistanceWithOneDecimal => totalDistance.toStringAsFixed(1);
  String get totalPointsInUSFormat => totalPoints.toPointsLong();
}
