import 'package:mybuddy/db/dao/workout_dao.dart';
import 'package:mybuddy/db/dao/workout_dog_dao.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

import 'dao/home_summary_dao.dart';

class DatabaseService {
  static const String TABLE_WORKOUT = "workouts";
  static const String TABLE_WORKOUT_DOGS = "workout_dogs";
  static const String TABLE_HOME_SUMMARY = "home_summary";
  static const String TABLE_USER_STATS = "user_stats";

  static WorkoutDao? workoutDao;
  static WorkoutDogDao? workoutDogDao;
  static HomeSummaryDao? _homeSummaryDao;

  // Singleton pattern
  static final DatabaseService _databaseService = DatabaseService._internal();
  factory DatabaseService() => _databaseService;
  DatabaseService._internal();

  static Database? _database;
  Future<Database> get database async {
    if (_database != null) return _database!;
    // Initialize the DB first time it is accessed
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasePath = await getDatabasesPath();

    // Set the path to the database. Note: Using the `join` function from the
    // `path` package is best practice to ensure the path is correctly
    // constructed for each platform.
    final path = join(databasePath, 'wooftrax_database.db');

    // Set the version. This executes the onCreate function and provides a
    // path to perform database upgrades and downgrades.
    return await openDatabase(
      path,
      onCreate: _onCreate,
      version: 1,
      onConfigure: (db) async => await db.execute('PRAGMA foreign_keys = ON'),
    );
  }

  // When the database is first created, create a table to store breeds
  // and a table to store dogs.
  Future<void> _onCreate(Database db, int version) async {
    // Run the CREATE {Workout} TABLE statement on the database.
    await db.execute(
      'CREATE TABLE $TABLE_WORKOUT('
      'id INTEGER PRIMARY KEY AUTOINCREMENT, '
      'workoutId INTEGER UNIQUE, '
      'ownerId INTEGER, '
      'status INTEGER, '
      'distance NUMERIC, '
      'speed NUMERIC, '
      'duration NUMERIC, '
      'moodType INTEGER, '
      'charityId INTEGER, '
      'charityName TEXT, '
      'points INTEGER, '
      'startLat NUMERIC, '
      'startLong NUMERIC, '
      'endLat NUMERIC, '
      'endLong NUMERIC, '
      'gpxFileName TEXT, '
      'isUploaded INTEGER, '
      'autoEndReason INTEGER, '
      'createdAt TEXT, '
      'updatedAt TEXT '
      ')',
    );
    // Run the CREATE {WorkoutDogs} TABLE statement on the database.
    await db.execute(
      'CREATE TABLE $TABLE_WORKOUT_DOGS('
      'id INTEGER PRIMARY KEY AUTOINCREMENT, '
      'workoutId INTEGER, '
      'dogId INTEGER, '
      'FOREIGN KEY(workoutId) REFERENCES workouts(id)'
      ')',
    );
    // Run the CREATE {HomeSummary} TABLE statement on the database.
    await db.execute('''
      create table $TABLE_HOME_SUMMARY (
        id integer primary key,
        totalPoints integer,
        totalWalks integer,
        totalDistance numeric,
        totalMinutes numeric,
        totalMigratedCharityPoints integer
      )
    ''');
  }

  HomeSummaryDao get homeSummaryDao =>
      _homeSummaryDao ?? HomeSummaryDao(db: _database!);

  WorkoutDao getWorkoutDao() {
    workoutDao ??= WorkoutDao(db: _database!);
    return workoutDao!;
  }

  WorkoutDogDao getWorkoutDogDao() {
    workoutDogDao ??= WorkoutDogDao(db: _database!);
    return workoutDogDao!;
  }
}
