# mybuddy2
myBuddy pet app ® Flutter

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://flutter.io/docs/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://flutter.io/docs/cookbook)

For help getting started with Flutter, view our 
[online documentation](https://flutter.io/docs), which offers tutorials, 
samples, guidance on mobile development, and a full API reference.

## Fastlane release guideline

Install Fastlane:
 - Run command "gem install fastlane" or "brew install fastlane"

To initiate the release process make sure you have updated the version name

For Android:
 - It is enough to update the version in pubspec.yaml

For iOS: (Version will be updated in two places)
 - Xcode -> Runner -> General -> Targets (Runner) -> Version & Build
 - Xcode (Side Pane) -> Runner -> Flutter -> wooftraxRelease -> FLUTTER_BUILD_NAME & FLUTTER_BUILD_NUMBER

Android release to App Center:
 - Run command "sh scripts/build_and_upload_android_app_center.sh" from the project directory

iOS release to App Center:
 - Run command "sh scripts/build_and_upload_ios_app_center.sh" from the project directory